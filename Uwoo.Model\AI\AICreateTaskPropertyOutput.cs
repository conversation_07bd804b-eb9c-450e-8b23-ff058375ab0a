﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI创建项目化实践阶段任务相关属性输出
    /// </summary>
    public class AICreateTaskPropertyOutput
    {
        /// <summary>
        /// 评分标准
        /// </summary>
        public string? ScoreStandard { get; set; }

        /// <summary>
        /// 情景对话要求
        /// </summary>
        public string? Demand { get; set; }

        /// <summary>
        /// 问答范围
        /// </summary>
        public string? Scope { get; set; }

        /// <summary>
        /// 评估角色设定
        /// </summary>
        public string? RoleSetting { get; set; }

        /// <summary>
        /// 高频问题集合
        /// </summary>
        public List<AICreateTaskPropertyQuestionOutput> QuestionInfos { get; set; } = new List<AICreateTaskPropertyQuestionOutput>();
    }

    /// <summary>
    /// 高频问题集合
    /// </summary>
    public class AICreateTaskPropertyQuestionOutput
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Describe { get; set; }
    }
}
