﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mime;
using System.Net.Security;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Uwoo.Core.Extensions;
using Uwoo.Util;
using System.Net.Http;
using MSHttpContext= Microsoft.AspNetCore.Http.HttpContext;
using System.IO.Compression;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http.Headers;

namespace Uwoo.Core.Utilities
{
	public static class HttpHelper
	{
		#region 构造函数

		/// <summary>
		/// 静态构造函数
		/// </summary>
		static HttpHelper()
		{
			ServicePointManager.SecurityProtocol =
				SecurityProtocolType.Tls12
				| SecurityProtocolType.Tls11
				| SecurityProtocolType.Tls;

			ServicePointManager.DefaultConnectionLimit = int.MaxValue;
			ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback((sender, certificate, chain, sslPolicyErrors) => true);
		}

		#endregion

		#region 外部接口

		/// <summary>
		/// 记录日志
		/// </summary>
		public static Action<string> HandleLog { get; set; }

		/// <summary>
		/// 发起GET请求
		/// 注：若使用证书,推荐使用X509Certificate2的pkcs12证书
		/// </summary>
		/// <param name="url">地址</param>
		/// <param name="paramters">参数</param>
		/// <param name="headers">请求头</param>
		/// <param name="cerFile">证书</param>
		/// <param name="timeOut">超时时间(单位毫秒)</param>
		/// <returns></returns>
		public static string GetData(string url, Dictionary<string, object> paramters = null, Dictionary<string, string> headers = null, X509Certificate cerFile = null, int timeOut = 0)
		{
			return RequestData(HttpMethod.Get, url, paramters, headers, ContentType.Form, cerFile, timeOut);
		}

		/// <summary>
		/// 发起POST请求
		/// 注：若使用证书,推荐使用X509Certificate2的pkcs12证书
		/// </summary>
		/// <param name="url">地址</param>
		/// <param name="paramters">参数</param>
		/// <param name="headers">请求头</param>
		/// <param name="contentType">请求的ContentType</param>
		/// <param name="cerFile">证书</param>
		/// <returns></returns>
		public static string PostData(string url, Dictionary<string, object> paramters = null, Dictionary<string, string> headers = null, ContentType contentType = ContentType.Form, X509Certificate cerFile = null)
		{
			Dictionary<ContentType, string> mapping = new Dictionary<ContentType, string>();
			mapping.Add(ContentType.Form, "application/x-www-form-urlencoded");
			mapping.Add(ContentType.Json, "application/json");

			string body = BuildBody(paramters, contentType);
			return PostData(url, body, mapping[contentType], headers, cerFile);
		}

		/// <summary>
		/// POST请求
		/// </summary>
		/// <param name="url"></param>
		/// <param name="obj"></param>
		/// <param name="contentType">application/xml、application/json、application/text、application/x-www-form-urlencoded</param>
		/// <param name="charset"></param>
		/// <returns></returns>       
		public static string HttpPostAsync(string url, object obj, string contentType = "", string charset = "UTF-8")
		{
			string result = "";
			var serviceProvider = new ServiceCollection().AddHttpClient().BuildServiceProvider();
			IHttpClientFactory _httpClientFactory = serviceProvider.GetService<IHttpClientFactory>();
			var _httpClient = _httpClientFactory.CreateClient("CTCCMonitor");

			try
			{

				_httpClient.DefaultRequestHeaders.Accept.Clear();
				_httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(contentType));
				string content = JsonConvert.SerializeObject(obj);
				var httpContent = new StringContent(content, Encoding.UTF8, contentType);
				var response = _httpClient.PostAsync(url, httpContent).Result;
				if (response.IsSuccessStatusCode)
				{
					Task<string> t = response.Content.ReadAsStringAsync();
					if (t != null)
					{
						result = t.Result;
					}
				}
			}
			catch
			{
				throw;
			}
			finally
			{
				_httpClient.Dispose();
			}
			return result;
		}

		public static string PostVideoData(string url, IFormFile file)
		{
			using var client = new HttpClient();
			using var content = new MultipartFormDataContent();
			using var streamContent = new StreamContent(file.OpenReadStream());
			
			streamContent.Headers.Add("Content-Type", "application/octet-stream");
			content.Add(streamContent, "file", file.FileName);

			var response = client.PostAsync(url, content).Result;
			response.EnsureSuccessStatusCode();
			return response.Content.ReadAsStringAsync().Result;
		}

		/// <summary>
		/// 发起POST请求
		/// 注：若使用证书,推荐使用X509Certificate2的pkcs12证书
		/// </summary>
		/// <param name="url">地址</param>
		/// <param name="body">请求体</param>
		/// <param name="contentType">请求的ContentType</param>
		/// <param name="headers">请求头</param>
		/// <param name="cerFile">证书</param>
		/// <returns></returns>
		public static string PostData(string url, string body, string contentType, Dictionary<string, string> headers, X509Certificate cerFile)
		{
			return RequestData(HttpMethod.Post, url, body, contentType, headers, cerFile);
		}

		/// <summary>
		/// 请求数据
		/// 注：若使用证书,推荐使用X509Certificate2的pkcs12证书
		/// </summary>
		/// <param name="method">请求方法</param>
		/// <param name="url">URL地址</param>
		/// <param name="paramters">参数</param>
		/// <param name="headers">请求头信息</param>
		/// <param name="contentType">请求数据类型</param>
		/// <param name="cerFile">证书</param>
		/// <param name="timeOut">超时时间(单位毫秒)</param>
		/// <returns></returns>
		public static string RequestData(HttpMethod method, string url, Dictionary<string, object> paramters = null, Dictionary<string, string> headers = null, ContentType contentType = ContentType.Form, X509Certificate cerFile = null, int timeOut = 0)
		{
			if (string.IsNullOrEmpty(url))
				throw new Exception("请求地址不能为NULL或空！");

			string newUrl = url;
			if (method == HttpMethod.Get)
			{
				StringBuilder paramBuilder = new StringBuilder();
				var paramList = new List<KeyValuePair<string, object>>();
				paramList = paramters?.ToList() ?? new List<KeyValuePair<string, object>>();
				for (int i = 0; i < paramList.Count; i++)
				{
					var theParamter = paramList[i];
					string key = theParamter.Key;
					string value = theParamter.Value.ToString();

					string head = string.Empty;
					if (i == 0 && !UrlHaveParam(url))
						head = "?";
					else
						head = "&";

					paramBuilder.Append($@"{head}{key}={value}");
				}

				newUrl = url + paramBuilder.ToString();
			}

			string body = BuildBody(paramters, contentType);
			return RequestData(method, newUrl, body, GetContentTypeStr(contentType), headers, cerFile, timeOut);
		}

		/// <summary>
		/// 请求数据
		/// 注：若使用证书,推荐使用X509Certificate2的pkcs12证书
		/// </summary>
		/// <param name="method">请求方法</param>
		/// <param name="url">请求地址</param>
		/// <param name="body">请求的body内容</param>
		/// <param name="contentType">请求数据类型</param>
		/// <param name="headers">请求头</param>
		/// <param name="cerFile">证书</param>
		/// <param name="timeOut">超时时间(单位毫秒)</param>
		/// <returns></returns>
		public static string RequestData(HttpMethod method, string url, string body, string contentType, Dictionary<string, string> headers = null, X509Certificate cerFile = null, int timeOut = 0)
		{
			if (string.IsNullOrEmpty(url))
				throw new Exception("请求地址不能为NULL或空！");

			using var client = new HttpClient();
			using var request = new HttpRequestMessage(method, url);
			request.Content = new StringContent(body, Encoding.UTF8, contentType);

			if (timeOut > 0)
				client.Timeout = TimeSpan.FromMilliseconds(timeOut);

			if (headers != null)
			{
				foreach (var header in headers)
				{
					request.Headers.Add(header.Key, header.Value);
				}
			}

			if (cerFile != null)
			{
				var handler = new HttpClientHandler();
				handler.ClientCertificates.Add(cerFile);
				using var clientWithCert = new HttpClient(handler);
				var response = clientWithCert.SendAsync(request).Result;
				response.EnsureSuccessStatusCode();


				return response.Content.ReadAsStringAsync().Result;
			}
			else
			{
				var response = client.SendAsync(request).Result;
				response.EnsureSuccessStatusCode();
				return response.Content.ReadAsStringAsync().Result;
			}
		}

        /// <summary>
        /// 请求数据
        /// 注：若使用证书,推荐使用X509Certificate2的pkcs12证书
        /// </summary>
        /// <param name="method">请求方法</param>
        /// <param name="url">请求地址</param>
        /// <param name="body">请求的body内容</param>
        /// <param name="contentType">请求数据类型</param>
        /// <param name="headers">请求头</param>
        /// <param name="cerFile">证书</param>
        /// <param name="timeOut">超时时间(单位毫秒)</param>
        /// <returns></returns>
        public static byte[] RequestDataMemoryStream(string method, string url, string body, string contentType, Dictionary<string, string> headers = null, X509Certificate cerFile = null, int timeOut = 0)
        {
            if (string.IsNullOrEmpty(url))
            {
                throw new Exception("请求地址不能为NULL或空！");
            }

            string newUrl = url;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(newUrl);
            if (timeOut > 0)
            {
                request.Timeout = timeOut;
            }
            request.Method = method.ToUpper();
            request.ContentType = contentType;
            headers?.ForEach(aHeader =>
            {
                request.Headers.Add(aHeader.Key, aHeader.Value);
            });

            //HTTPS证书
            if (cerFile != null)
            {
                request.ClientCertificates.Add(cerFile);
            }
            if (method.ToUpper() != "GET")
            {
                byte[] data = Encoding.UTF8.GetBytes(body);
                request.ContentLength = data.Length;

                using (Stream requestStream = request.GetRequestStream())
                {
                    requestStream.Write(data, 0, data.Length);
                }
            }

            string resData = string.Empty;
            DateTime startTime = DateTime.Now;
            try
            {
                using (MemoryStream memory = new MemoryStream())
                {
                    //获取响应
                    using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                    {
                        //检查响应状态码是否为OK
                        if (response.StatusCode == HttpStatusCode.OK)
                        {
                            //获取响应流
                            using (Stream responseStream = response.GetResponseStream())
                            {
                                responseStream.CopyTo(memory);
                            }
                        }
                    }
                    memory.Position = 0;
                    byte[] zipBytes = memory.ToArray();

                    return zipBytes;
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                var time = DateTime.Now - startTime;
                if (resData?.Length > 1000)
                {
                    resData = new string(resData.Copy(0, 1000).ToArray());
                    resData += "......";
                }
                string log = $@"方向:请求外部接口url:{url}method:{method}contentType:{contentType}body:{body}耗时:{(int)time.TotalMilliseconds}ms返回:{resData}";
                HandleLog?.Invoke(log);
            }
        }


        /// <summary>
        /// 获取所有请求的参数（包括get参数和post参数）
        /// </summary>
        /// <param name="context">请求上下文</param>
        /// <returns></returns>
        public static Dictionary<string, object> GetAllRequestParams(MSHttpContext context)
		{
			Dictionary<string, object> allParams = new Dictionary<string, object>();

			var request = context.Request;
			List<string> paramKeys = new List<string>();
			var getParams = request.Query.Keys.ToList();
			var postParams = new List<string>();
			try
			{
				if (request.Method.ToLower() != "get")
					postParams = request.Form.Keys.ToList();
			}
			catch
			{

			}
			paramKeys.AddRange(getParams);
			paramKeys.AddRange(postParams);

			paramKeys.ForEach(aParam =>
			{
				object value = null;
				if (request.Query.ContainsKey(aParam))
					value = request.Query[aParam].ToString();
				else if (request.Form.ContainsKey(aParam))
					value = request.Form[aParam].ToString();

				allParams.Add(aParam.ToLower(), value);
			});

			string contentType = request.ContentType?.ToLower() ?? "";

			//若为POST的application/json
			if (contentType.Contains("application/json"))
			{
				//context.Request.Body.Position = 0; // 确保可以读取内容
				//using (var reader = new StreamReader(request.Body,  Encoding.UTF8, leaveOpen: true))
				//{
				//	// 进行异步读取
				//	string jsonString =  reader.ReadToEndAsync().Result;
				//	context.Request.Body.Position = 0; 

				//	try
				//	{
				//		// 解析 JSON 数据
				//		var jsonParams = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonString);
				//		foreach (var kvp in jsonParams)
				//		{
				//			allParams[kvp.Key.ToLower()] = kvp.Value;
				//		}
				//	}
				//	catch (JsonException ex)
				//	{
				//		// 处理 JSON 解析错误，可以记录日志或抛出异常等
				//		Console.WriteLine($"Error while parsing JSON parameters: {ex.Message}");
				//	}
				//	catch (Exception ex)
				//	{
				//		// 处理其他可能的异常
				//		Console.WriteLine($"Error while retrieving post parameters: {ex.Message}");
				//	}
				//}
				var stream = request.Body;
				string str = stream.ReadToString(Encoding.UTF8);
				if (!str.IsNullOrEmpty())
				{
					var obj = str.ToJObject();
					foreach (var aProperty in obj)
					{
						allParams[aProperty.Key.ToLower()] = aProperty.Value;
					}
				}
			}
			return allParams;
		}

		/// <summary>
		/// 构建完全Url
		/// </summary>
		/// <param name="url">Url</param>
		/// <param name="parameters">参数</param>
		/// <returns></returns>
		public static string BuildGetFullUrl(string url, Dictionary<string, object> parameters = null)
		{
			StringBuilder paramBuilder = new StringBuilder();
			var paramList = parameters?.ToList() ?? new List<KeyValuePair<string, object>>();
			for (int i = 0; i < paramList.Count; i++)
			{
				var theParamter = paramList[i];
				string key = theParamter.Key;
				string value = theParamter.Value.ToString();

				string head = string.Empty;
				if (i == 0 && !UrlHaveParam(url))
					head = "?";
				else
					head = "&";

				paramBuilder.Append($@"{head}{key}={value}");
			}

			return url + paramBuilder.ToString();
		}

		/// <summary>
		/// 从URL获取html文档
		/// </summary>
		/// <param name="url"></param>
		/// <returns></returns>
		public static string GetHtml(string url)
		{
			string htmlCode = "";
			for (int i = 0; i < 3; i++)
			{
				try
				{
					using var client = new HttpClient();
					using var request = new HttpRequestMessage(HttpMethod.Get, url);
					request.Headers.Add("Accept-Encoding", "gzip, deflate");
					request.Headers.Add("User-Agent", "Mozilla/4.0");

					using var response = client.SendAsync(request).Result;
					response.EnsureSuccessStatusCode();

					using var stream = response.Content.ReadAsStreamAsync().Result;
					
					// 获取目标网站的编码格式
					string contentype = response.Content.Headers.ContentType?.ToString() ?? "";
					Regex regex = new Regex("charset\\s*=\\s*[\\W]?\\s*([\\w-]+)", RegexOptions.IgnoreCase);

					if (response.Content.Headers.ContentEncoding.Contains("gzip"))
					{
						using var ms = new MemoryStream();
						stream.CopyTo(ms);
						ms.Seek(0, SeekOrigin.Begin);
						using var zipStream = new GZipStream(ms, CompressionMode.Decompress);

						if (regex.IsMatch(contentype))
						{
							Encoding encoding = Encoding.GetEncoding(regex.Match(contentype).Groups[1].Value.Trim());
							using var sr = new StreamReader(zipStream, encoding);
							htmlCode = sr.ReadToEnd();
						}
						else
						{
							using var sr = new StreamReader(zipStream, Encoding.UTF8);
							htmlCode = sr.ReadToEnd();
							string subStr = htmlCode.Substring(0, 2000);
							string pattern = "charset=(.*?)\"";
							Encoding encoding;
							foreach (Match match in Regex.Matches(subStr, pattern))
							{
								if (match.Groups[1].ToString().ToLower() == "utf-8")
									break;
								else
								{
									encoding = Encoding.GetEncoding(match.Groups[1].ToString().ToLower());
									ms.Seek(0, SeekOrigin.Begin);
									using var zipStream2 = new GZipStream(ms, CompressionMode.Decompress);
									using var sr2 = new StreamReader(zipStream2, encoding);
									htmlCode = sr2.ReadToEnd();
								}
							}
						}
					}
					else
					{
						using var sr = new StreamReader(stream, Encoding.Default);
						htmlCode = sr.ReadToEnd();
					}
					return htmlCode;
				}
				catch (Exception e)
				{
					Console.WriteLine(e);
					Console.WriteLine("重试中....................");
				}
			}
			return "";
		}

		/// <summary>
		/// 发起安全签名请求
		/// 注：使用本框架签名算法,ContentType为application/json
		/// </summary>
		/// <param name="url">地址</param>
		/// <param name="body">请求body</param>
		/// <param name="appId">应用Id</param>
		/// <param name="appSecret">应用密钥</param>
		/// <returns></returns>
		public static string SafeSignRequest(string url, string body, string appId, string appSecret)
		{
			string time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
			string guid = Guid.NewGuid().ToString();
			Dictionary<string, string> headers = new Dictionary<string, string>
			{
				{"appId",appId },
				{"time",time },
				{"guid",guid },
				{"sign",BuildApiSign(appId,appSecret,guid,time.ToDateTime().Value,body) }
			};

			return RequestData(HttpMethod.Post, url, body, "application/json", headers);
		}

		/// <summary>
		/// 生成接口签名sign
		/// 注：md5(appId+time+guid+body+appSecret)
		/// </summary>
		/// <param name="appId">应用Id</param>
		/// <param name="appSecret">应用密钥</param>
		/// <param name="guid">唯一GUID</param>
		/// <param name="time">时间</param>
		/// <param name="body">请求体</param>
		/// <returns></returns>
		public static string BuildApiSign(string appId, string appSecret, string guid, DateTime time, string body)
		{
			return $"{appId}{time.ToString("yyyy-MM-dd HH:mm:ss")}{guid}{body}{appSecret}".ToMD5String();
		}

		#endregion

		#region 内部成员

		private static string BuildBody(Dictionary<string, object> parameters, ContentType contentType)
		{
			StringBuilder bodyBuilder = new StringBuilder();
			switch (contentType)
			{
				case ContentType.Form:
					{
						var paramList = parameters?.ToList() ?? new List<KeyValuePair<string, object>>();
						for (int i = 0; i < paramList.Count; i++)
						{
							var theParamter = paramList[i];
							string key = theParamter.Key;
							string value = theParamter.Value?.ToString();

							string head = string.Empty;
							if (i != 0)
								head = "&";

							bodyBuilder.Append($@"{head}{key}={value}");
						}
					}; break;
				case ContentType.Json:
					{
						bodyBuilder.Append(JsonConvert.SerializeObject(parameters));
					}; break;
				default: break;
			}

			return bodyBuilder.ToString();
		}

		private static bool UrlHaveParam(string url)
		{
			return url.Contains("?");
		}

		private static string GetContentTypeStr(ContentType contentType)
		{
			string contentTypeStr = string.Empty;
			switch (contentType)
			{
				case ContentType.Form: contentTypeStr = "application/x-www-form-urlencoded"; break;
				case ContentType.Json: contentTypeStr = "application/json"; break;
				default: break;
			}

			return contentTypeStr;
		}

		#endregion
	}

	#region 类型定义

	public enum ContentType
	{
		/// <summary>
		/// 传统Form表单,即application/x-www-form-urlencoded
		/// </summary>
		Form,
		/// <summary>
		/// 使用Json,即application/json
		/// </summary>
		Json
	}

	#endregion
}
