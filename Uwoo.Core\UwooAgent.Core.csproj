﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName></SccProjectName>
    <SccProvider></SccProvider>
    <SccAuxPath></SccAuxPath>
    <SccLocalPath></SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ApplicationIcon />
    <OutputType>Library</OutputType>
    <StartupObject />
  </PropertyGroup>
    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;NU1902;NU1903;NU1904;</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;NU1902;NU1903;NU1904;</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="IRepositories\**" />
    <Compile Remove="IServices\**" />
    <Compile Remove="Repositories\**" />
    <Compile Remove="Services\API\**" />
    <Compile Remove="Services\Base\**" />
    <EmbeddedResource Remove="IRepositories\**" />
    <EmbeddedResource Remove="IServices\**" />
    <EmbeddedResource Remove="Repositories\**" />
    <EmbeddedResource Remove="Services\API\**" />
    <EmbeddedResource Remove="Services\Base\**" />
    <None Remove="IRepositories\**" />
    <None Remove="IServices\**" />
    <None Remove="Repositories\**" />
    <None Remove="Services\API\**" />
    <None Remove="Services\Base\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Autofac" Version="6.0.0" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="AutoMapper" Version="6.2.2" />
    <PackageReference Include="Confluent.Kafka" Version="1.8.2" />
    <PackageReference Include="CSRedisCore" Version="3.6.5" />
    <PackageReference Include="EPPlus" Version="7.1.0" />
    <PackageReference Include="HuaweiCloud.OBS.SDK" Version="3.20.7" />
    <PackageReference Include="IdHelper" Version="1.4.1" />
    <PackageReference Include="Mapster" Version="7.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Formatters.Json" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Redis.Core" Version="1.0.3" />
    <PackageReference Include="Microsoft.Extensions.DependencyModel" Version="6.0.0" />
    <PackageReference Include="Net.Codecrete.QrCodeGenerator" Version="2.0.6" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.15" />
    <PackageReference Include="Npgsql" Version="6.0.0" />
    <!--<PackageReference Include="MySql.Data" Version="8.0.13" />-->
    <PackageReference Include="Quartz" Version="3.4.0" />
    <PackageReference Include="RabbitMQ.Client" Version="6.5.0" />
    <PackageReference Include="SkiaSharp" Version="2.88.9" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.88.7" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.112" />
    <PackageReference Include="System.Data.Common" Version="4.3.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.35.0" />
    <PackageReference Include="ZKWeb.System.Drawing" Version="4.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Uwoo.Entity\UwooAgent.Entity.csproj" />
    <ProjectReference Include="..\Uwoo.Model\UwooAgent.Model.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Spire.License">
      <HintPath>..\Uwoo.WebApi\lib\Spire.License.dll</HintPath>
    </Reference>
    <Reference Include="Spire.Pdf">
      <HintPath>..\Uwoo.WebApi\lib\Spire.Pdf.dll</HintPath>
    </Reference>
    <Reference Include="Spire.Presentation">
      <HintPath>..\Uwoo.WebApi\lib\Spire.Presentation.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
