﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Question
{
    /// <summary>
    /// 题库_题目信息实体
    /// </summary>
    [Table("Exam_Question")]
    public class Exam_Question
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 题干
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 题目编号
        /// </summary>
        public string QuestionCode { get; set; }

        /// <summary>
        /// 题目类型Id
        /// </summary>
        public int? QuestionTypeId { get; set; }

        /// <summary>
        /// 学年(届)
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// 学期（1上、2下）
        /// </summary>
        public int? Semester { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public int? GradeId { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public string SubjectId { get; set; }

        /// <summary>
        /// 章节Id
        /// </summary>
        public string ChapterId { get; set; }

        /// <summary>
        /// 单元目标Id
        /// </summary>
        public string UnitTargetId { get; set; }

        /// <summary>
        /// 章节目标Id
        /// </summary>
        public string ChapterTargetId { get; set; }

        /// <summary>
        /// 题目目标Id
        /// </summary>
        public string QuestionTargetId { get; set; }

        /// <summary>
        /// 课程版本Id
        /// </summary>
        public string CourseVersionsId { get; set; }

        /// <summary>
        /// 教材Id
        /// </summary>
        public string TextbookId { get; set; }

        /// <summary>
        /// 作答提示
        /// </summary>
        public string Hint { get; set; }

        /// <summary>
        /// 学习指导
        /// </summary>
        public string LearningGuidance { get; set; }

        /// <summary>
        /// 错误归因
        /// </summary>
        public string Misattribution { get; set; }

        /// <summary>
        /// 题目解析
        /// </summary>
        public string Analysis { get; set; }

        /// <summary>
        /// 参考答案
        /// </summary>
        public string ReferenceAnswer { get; set; }

        /// <summary>
        /// 预计完成时间(单位分钟)
        /// </summary>
        public int? FinishTime { get; set; }

        /// <summary>
        /// 答题形式( 1:图片  2:音频  3:视频)
        /// </summary>
        public int? ResponseFormat { get; set; }

        /// <summary>
        /// 拍照设置（1不上传、2必须上传、3可选上传、4核心素养上传）默认1
        /// </summary>
        public int? PhotographSet { get; set; }

        /// <summary>
        /// 共享设置(1不共享、2同校共享)默认1
        /// </summary>
        public int? ShareSet { get; set; }

        /// <summary>
        /// 区市共享设置(1不共享、2愿意全区共享、3愿意全市共享)默认1
        /// </summary>
        public int? ShareAreaCity { get; set; }

        /// <summary>
        /// 区市共享审核（1未审核、2已审核）默认1
        /// </summary>
        public int? ShareAreaCityAudit { get; set; }

        /// <summary>
        /// 状态（1未发布(未组卷)、2已发布(已组卷)）
        /// </summary>
        public int? State { get; set; }

        /// <summary>
        /// 来源：试卷名称
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 来源类型（1市、2区、3校、4班）
        /// </summary>
        public int? SourceType { get; set; }

        /// <summary>
        /// 资源类型（1教材资源(来源于教材练习册)）
        /// </summary>
        public int? ResourcesType { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public string SchoolId { get; set; }

        /// <summary>
        /// 区Id
        /// </summary>
        public int? AreaId { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public string CreatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// 平台来源（1专课专练、2三个助手）
        /// </summary>
        public int? PlatformSource { get; set; }

        /// <summary>
        /// 字数（作文题）
        /// </summary>
        public int? Words { get; set; }
    }
}
