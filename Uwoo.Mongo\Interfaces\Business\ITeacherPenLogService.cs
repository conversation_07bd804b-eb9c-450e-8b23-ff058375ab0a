﻿//  -- Function：ITeacherPenLogService.cs
//  --- Project：X.PenServer.Interfaces
//  ---- Remark：
//  ---- Author：Lucifer
//  ------ Date：2023/10/25 01:29:17

using Uwoo.Mongo.Models;
using Uwoo.Mongo.ViewModels;

namespace Uwoo.Mongo.Interfaces.Business;

/// <summary>
/// 教师笔迹
/// </summary>
public interface ITeacherPenLogService : IMongoAutoService<TeacherPenLog>
{
	/// <summary>
	/// 获取笔迹列表
	/// </summary>
	/// <param name="colname">集合名称</param>
	/// <param name="page">页码</param>
	/// <param name="userid">学生id</param>
	/// <param name="year"></param>
	/// <returns></returns>
	List<TeacherPenLog> GetAll(string colname, string userid, int page,int? year= null);

    /// <summary>
    /// 获取已作答笔迹数据
    /// </summary>
    /// <param name="colname">集合名称</param>
    /// <param name="page">页码集合</param>
    /// <param name="userid">用户id集合</param>
    /// <returns></returns>
    List<TeacherPenLog> GetAll(string colname, List<int> page, List<string> userid);

    /// <summary>
    /// 获取已作答页面列表
    /// </summary>
    /// <param name="colname">集合名称</param>
    /// <param name="page">页码集合</param>
    /// <param name="userid">用户id集合</param>
    /// <returns></returns>
    List<PenLogUserInfo> GetUserPageList(string colname, List<int> page, List<string> userid);

    /// <summary>
    /// 获取已作答页面列表
    /// </summary>
    /// <param name="colname">集合名称</param>
    /// <param name="page">页码集合</param>
    /// <param name="userid">学生id</param>
    /// <returns></returns>
    List<PenLogUserInfo> GetUserPageList(string colname, List<int> page, string userid);

    /// <summary>
    /// 删除批改学生笔迹
    /// </summary>
    /// <param name="colname">集合名称</param>
    /// <param name="page">页码集合</param>
    /// <param name="userid">用户id集合</param>
    void DeletePenLog(string colname, List<int> page, List<string> userid);
}