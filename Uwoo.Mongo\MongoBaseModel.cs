﻿// -- Function：MongoBaseModel.cs
// --- Project：X.PenServer.Models
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/24 14:47

namespace Uwoo.Mongo;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

/// <summary>
/// 数据基类
/// </summary>
public abstract class MongoBaseModel
{
    /// <summary>
    /// mongodb主键id
    /// </summary>
    [BsonId]
    [BsonRepresentation(BsonType.Int64)]
    public long Mid { get; set; }

    /// <summary>
    /// 入库时间
    /// </summary>
    [BsonRepresentation(BsonType.DateTime)]
    [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
    public DateTime AddTime { get; set; } = DateTime.Now;
}