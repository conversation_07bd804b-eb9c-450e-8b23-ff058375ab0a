﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 文多多创建token入参
    /// </summary>
    public class WeDuoDuoCreateTokenInputDto
    {
        /// <summary>
        /// 用户ID（自定义用户ID，非必填，建议不超过32位字符串）
        /// 第三方用户ID，不同uid创建的token数据会相互隔离，主要用于数据隔离
        /// </summary>
        public string? uid { get; set; }

        /// <summary>
        /// 限制 token 最大生成PPT次数（数字，为空则不限制，为0时不允许生成PPT，大于0时限制生成PPT次数）
        /// UI iframe 接入时强烈建议传 limit 参数，避免 token 泄露照成损失！
        /// </summary>
        public int limit { get; set; }

        /// <summary>
        /// 过期时间，单位：小时
        /// 默认两小时过期，最大可设置为48小时
        /// </summary>
        public int timeOfHours { get; set; }
    }
}
