﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 学生端项目化实践阶段任务作品评估接口入参
    /// </summary>
    public class StudentAssessInput
    {
        /// <summary>
        /// 项目化实践阶段任务ID
        /// </summary>
        public string? ProjectStageTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 文件信息
        /// </summary>
        public List<StudentAssessFileInfoInput> Files { get; set; } = new List<StudentAssessFileInfoInput>();
    }

    /// <summary>
    /// 学生端项目化实践阶段任务作品评估接口入参
    /// </summary>
    public class StudentAssessFileInfoInput
    {
        /// <summary>
        /// 文件地址
        /// </summary>
        public string? FileUrl { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }
    }
}
