﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.System.IServices;
using UwooAgent.Entity.DomainModels.Base;
using UwooAgent.Model.SemesterTime;

namespace UwooAgent.WebApi.Controllers.Base
{
    /// <summary>
    /// 学年学期控制器
    /// </summary>
    [Route("/SemesterTime/[controller]/[action]")]
    [ApiController]
    public class SemesterTimeController : ApiBaseController<IBase_SemesterTimeService>
    {
        #region DI

        private IBase_SemesterTimeService _semesterTimeService;
        public SemesterTimeController(IBase_SemesterTimeService semesterTimeService)
        {
            _semesterTimeService = semesterTimeService;
        }

        #endregion

        /// <summary>
        /// 获取当前学年学期时间
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<NowSemesterTime> GetNowYearSemesterTime()
        {
            return await _semesterTimeService.GetNowYearSemesterTime();
        }
    }
}
