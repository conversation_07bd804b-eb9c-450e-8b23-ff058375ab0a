﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Middleware
{
	public static class WebSocketManagerExtensions
	{
		public static IServiceCollection AddWebSocketManager(this IServiceCollection services)
		{
			services.AddSingleton<WebSocketManager>();
			return services;
		}
	}
}
