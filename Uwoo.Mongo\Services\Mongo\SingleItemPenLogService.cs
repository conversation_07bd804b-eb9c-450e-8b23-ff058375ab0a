﻿// -- Function：SingleItemPenLogService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2024/04/04 15:04:05

using MongoDB.Bson;
using MongoDB.Driver;
using Uwoo.Contracts.Config;
using Uwoo.Mongo.Infrastructure;
using Uwoo.Mongo.Interfaces.Business;
using Uwoo.Mongo.Models;

namespace Uwoo.Mongo.Services.Mongo;

/// <inheritdoc cref="ISingleItemPenLogService" />
public class SingleItemPenLogService : MongoAutoService<SingleItemPenLog>, ISingleItemPenLogService
{
    /// <inheritdoc />
    public SingleItemPenLogService(IMongoConfig config) : base(config)
    {
    }

    /// <inheritdoc />
    protected override void CreateIndex(IMongoCollection<SingleItemPenLog> collection)
    {
	    var userid_builder = Builders<SingleItemPenLog>.IndexKeys
		    .Ascending(x => x.UserId)
		    .Ascending(x => x.ItemId)
		    .Ascending(x => x.PageId)
		    .Ascending(x => x.Mac)
		    .Ascending(x => x.PaperId)
		    .Ascending(x => x.Page);
	    collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");

	    var pageid_builder = Builders<SingleItemPenLog>.IndexKeys
		    .Ascending(x => x.PageId)
		    .Ascending(x => x.UserId)
		    .Ascending(x => x.ItemId)
		    .Ascending(x => x.Mac)
		    .Ascending(x => x.PaperId);
	    collection.Indexes.CreateIndex(pageid_builder, collection.CollectionNamespace.CollectionName + "_PageId_Key");

	    var itemid_builder = Builders<SingleItemPenLog>.IndexKeys
		    .Ascending(x => x.ItemId)
		    .Ascending(x => x.UserId)
		    .Ascending(x => x.PageId)
		    .Ascending(x => x.Mac)
		    .Ascending(x => x.PaperId);
	    collection.Indexes.CreateIndex(itemid_builder, collection.CollectionNamespace.CollectionName + "_ItemId_Key");
    }


	/// <inheritdoc />
	public List<string> GetUserList(string colname, int pageId, string itemId, List<string> userid)
	{
		var mongo = GetConnection(colname);
		if (!mongo.AsQueryable().Any(x => x.PageId == pageId && x.ItemId.Equals(itemId, StringComparison.CurrentCultureIgnoreCase) && userid.Contains(x.UserId)))
		{
			return new List<string>();
		}

		var builder = Builders<SingleItemPenLog>.Filter;
		var filter = builder.Eq(x => x.PageId, pageId) & builder.Eq(x => x.ItemId, itemId) & builder.In(x => x.UserId, userid);
		var result = mongo.Distinct(x => x.UserId, filter).ToList();
		return result;
	}

	/// <inheritdoc />
	public List<string> GetUserList(string colname, List<int> page, List<string> userid, int? year = null)
	{
		var mongo = GetConnection(colname, year);
		if (!mongo.AsQueryable().Any(x => page.Contains(x.PageId) && userid.Contains(x.UserId)))
		{
			return new List<string>();
		}

		var builder = Builders<SingleItemPenLog>.Filter;
		var filter = builder.In(x => x.PageId, page) & builder.In(x => x.UserId, userid);
		var result = mongo.Distinct(x => x.UserId, filter).ToList();
		return result;
	}


	/// <inheritdoc />
	public List<SingleItemPenLog> GetAll(string colname, string userid, int page,string itemId)
	{
		var mongo = GetConnection(colname);
		var list = mongo.Find(x => x.PageId == page && x.UserId == userid && x.ItemId== itemId).ToList();
		return list.OrderBy(x => x.Mid).ToList();
	}

	/// <summary>
	/// 获取学生笔迹列表
	/// </summary>
	/// <param name="colname">集合名称</param>
	/// <param name="page">页码集合</param>
	/// <param name="userid">用户id</param>
	/// <param name="year">学年</param>
	/// <returns></returns>
	public List<SingleItemPenLog> GetAll(string colname, List<int> page, string userid,int? year= null)
	{
		var mongo = GetConnection(colname,year);
		var list = mongo.Find(x => page.Contains(x.PageId) && x.UserId == userid).ToList();
		return list.OrderBy(x => x.Mid).ToList();
	}

	/// <inheritdoc />
	public void DeleteSinglePenLog(string colname, string paperId, List<string> userid)
	{
		var mongo = GetConnection(colname);
		mongo.DeleteMany(x => paperId.Contains(x.PaperId) && userid.Contains(x.UserId));
	}


	/// <inheritdoc />
	public async Task CorrectMDLog(string colname, int page, string user_id, int move_x, int move_y)
	{
		var mongo = GetConnection(colname);
		var result = mongo.AsQueryable().Where(x => x.PageId == page && x.UserId == user_id);
		if (move_x == 0 && move_y == 0)
		{
			return;
		}

		await Parallel.ForEachAsync(result, async (item, token) =>
		{
			foreach (var dot in item.Dots)
			{
				dot.X += (move_x != 0 ? move_x : 0);
				dot.Y += (move_y != 0 ? move_y : 0);
			}

			await mongo.FindOneAndReplaceAsync(x => x.Mid == item.Mid, item, cancellationToken: token);
		});
	}
}