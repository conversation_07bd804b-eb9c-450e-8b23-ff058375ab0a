﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Entity.DomainModels.System
{
    /// <summary>
    /// 
    /// </summary>
    [Table("Sys_Menu")]
    [Entity(TableCnName = "菜单配置")]
    public class Sys_Menu : BaseEntity
    {
        /// <summary>
        ///ID
        /// </summary>
        [Key]
        [Display(Name = "ID")]
        [Column(TypeName = "int")]
        [Editable(true)]
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        [Required(AllowEmptyStrings = false)]
        public int Menu_Id { get; set; }

        /// <summary>
        ///父级ID
        /// </summary>
        [Display(Name = "父级ID")]
        [DisplayFormat(DataFormatString = "10,0")]
        [Column(TypeName = "int")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public int ParentId { get; set; }


        /// <summary>
        ///菜单名称
        /// </summary>
        [Display(Name = "菜单名称")]
        [MaxLength(50)]
        [Column(TypeName = "nvarchar(50)")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public string MenuName { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "TableName")]
        [MaxLength(200)]
        [Column(TypeName = "nvarchar(200)")]
        [Editable(true)]
        public string TableName { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "Url")]
        [MaxLength(10000)]
        [Column(TypeName = "nvarchar(10000)")]
        [Editable(true)]
        public string Url { get; set; }

        /// <summary>
        ///权限
        /// </summary>
        [Display(Name = "权限")]
        [MaxLength(10000)]
        [Column(TypeName = "nvarchar(10000)")]
        [Editable(true)]
        public string Auth { get; set; }


        /// <summary>
        ///
        /// </summary>
        [Display(Name = "Description")]
        [MaxLength(200)]
        [Column(TypeName = "nvarchar(200)")]
        [Editable(true)]
        public string Description { get; set; }


        /// <summary>
        ///图标
        /// </summary>
        [Display(Name = "图标")]
        [MaxLength(50)]
        [Column(TypeName = "nvarchar(50)")]
        [Editable(true)]
        public string Icon { get; set; }

        /// <summary>
        ///排序号
        /// </summary>
        [Display(Name = "排序号")]
        [DisplayFormat(DataFormatString = "10,0")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? OrderNo { get; set; }

        /// <summary>
        ///创建人
        /// </summary>
        [Display(Name = "创建人")]
        [MaxLength(50)]
        [Column(TypeName = "nvarchar(50)")]
        [Editable(true)]
        public string Creator { get; set; }

        /// <summary>
        ///创建时间
        /// </summary>
        [Display(Name = "创建时间")]
        [Column(TypeName = "datetime")]
        [Editable(true)]
        public DateTime? CreateDate { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "Modifier")]
        [MaxLength(50)]
        [Column(TypeName = "nvarchar(50)")]
        [Editable(true)]
        public string Modifier { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "ModifyTime")]
        [Column(TypeName = "datetime")]
        [Editable(true)]
        public DateTime? ModifyDate { get; set; }

        /// <summary>
        ///是否启用
        /// </summary>
        [Display(Name = "是否启用")]
        [Column(TypeName = "tinyint")]
        [Editable(true)]
        public byte? Enable { get; set; }


        /// <summary>
        /// 路径参数
        /// </summary>
        [Display(Name = "路径参数")]
        [Column(TypeName = "nvarchar(4000)")]
        [Editable(true)]
        public string Parameters { get; set; }

        /// <summary>
        /// 布局
        /// </summary>
        [Display(Name = "布局")]
        [Column(TypeName = "nvarchar(200)")]
        [Editable(true)]
        public string Layout { get; set; }

        /// <summary>
        /// 2022.03.26
        /// 菜单类型1:移动端，0:PC端
        /// </summary>
        [Display(Name = "是否启用")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? MenuType { get; set; }

        /// <summary>
        /// 图标类型
        /// </summary>
        [Display(Name = "图标类型")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int IconType { get; set; }

        /// <summary>
        /// 是否缓存路由
        /// </summary>
        [Display(Name = "是否缓存路由")]
        [Column(TypeName = "bit")]
        [Editable(true)]
        public bool IsCacheRouting { get; set; }

        /// <summary>
        /// 是否常量路由
        /// </summary>
        [Display(Name = "是否常量路由")]
        [Column(TypeName = "bit")]
        [Editable(true)]
        public bool IsConstRouting { get; set; }

        /// <summary>
        /// 外链
        /// </summary>
        [Display(Name = "外链")]
        [Column(TypeName = "nvarchar(500)")]
        [Editable(true)]
        public string Links { get; set; }

        /// <summary>
        /// 是否隐藏
        /// </summary>
        [Display(Name = "是否隐藏")]
        [Column(TypeName = "bit")]
        [Editable(true)]
        public bool IsHide { get; set; }

        /// <summary>
        /// 是否多标签
        /// </summary>
        [Display(Name = "是否多标签")]
        [Column(TypeName = "bit")]
        [Editable(true)]
        public bool IsMultiLabel { get; set; }

        /// <summary>
        /// 固定在页签中的序号
        /// </summary>
        [Display(Name = "固定在页签中的序号")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int TabNum { get; set; }

        /// <summary>
        /// 路由参数
        /// </summary>
        [Display(Name = "路由参数")]
        [Column(TypeName = "nvarchar(4000)")]
        [Editable(true)]
        public string RoutingParameters { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(Menu_Id), nameof(Menu_Id))]
        public List<Sys_Actions> Actions { get; set; }
    }
}
