﻿// -- Function：SingleItemPenLog.cs
// --- Project：X.PenServer.Models
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2024/04/04 15:04:03

using System.Text.Json.Serialization;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Uwoo.Mongo.Models;

/// <summary>
/// 单题模式笔迹
/// </summary>
public class SingleItemPenLog : PenLog
{
    /// <summary>
    /// 试卷id
    /// </summary>
    [BsonElement(nameof(PaperId))]
    [BsonRepresentation(BsonType.String)]
    [JsonPropertyName(nameof(PaperId))]
    [JsonInclude]
    public string PaperId { get; set; }

    /// <summary>
    /// 题目id
    /// </summary>
    [BsonElement(nameof(ItemId))]
    [BsonRepresentation(BsonType.String)]
    [JsonPropertyName(nameof(ItemId))]
    [JsonInclude]
    public string ItemId { get; set; }
}