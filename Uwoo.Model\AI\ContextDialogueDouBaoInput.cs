﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 上下文对话豆包入参
    /// </summary>
    public class ContextDialogueDouBaoInput
    {
        /// <summary>
        /// 上下文缓存的ID，用于关联缓存的信息。
        /// </summary>
        public string? context_id { get; set; }

        /// <summary>
        /// 推理接入点（Endpoint）ID
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 响应内容是否流式返回：false：模型生成完所有内容后一次性返回结果、true：按 SSE 协议逐块返回模型生成内容，并以一条 data: [DONE] 消息结束
        /// </summary>
        public bool stream { get; set; }

        /// <summary>
        /// 消息体
        /// </summary>
        public List<ContextDialogueDouBaoInputMessages> messages { get; set; } = new List<ContextDialogueDouBaoInputMessages>();

        /// <summary>
        /// 流式输出扩展参数
        /// </summary>
        public ContextDialogueDouBaoInputStreamOptions stream_options { get; set; }=new ContextDialogueDouBaoInputStreamOptions();
    }

    /// <summary>
    /// 消息体
    /// </summary>
    public class ContextDialogueDouBaoInputMessages
    {
        /// <summary>
        /// 角色
        /// </summary>
        public string? role { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string? content { get; set; }
    }

    /// <summary>
    /// 流式输出扩展参数
    /// </summary>
    public class ContextDialogueDouBaoInputStreamOptions
    {
        /// <summary>
        /// 是否包含本次请求的 token 用量统计信息
        /// </summary>
        public bool include_usage { get; set; }
    }
}
