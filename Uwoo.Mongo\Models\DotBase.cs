﻿// -- Function: DotBase.cs
// --- Project: Uwoo.Mongo
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/07/02 11:07

using System.Text.Json.Serialization;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Uwoo.Mongo.Models;

/// <inheritdoc />
public class DotBase : MongoBaseModel
{
    /// <summary>
    /// X坐标
    /// </summary>
    [BsonElement(nameof(X))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(X))]
    [JsonInclude]
    public int X { get; set; }

    /// <summary>
    /// Y坐标
    /// </summary>
    [BsonElement(nameof(Y))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(Y))]
    [JsonInclude]
    public int Y { get; set; }

    /// <summary>
    /// 点位类型: 1.开始 2.结束
    /// </summary>
    [BsonElement(nameof(Type))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(Type))]
    [JsonInclude]
    public int Type { get; set; }

    /// <summary>
    /// BookNo
    /// </summary>
    [BsonElement(nameof(BookNo))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(BookNo))]
    [JsonInclude]
    public int BookNo { get; set; }

    /// <summary>
    /// Pressure
    /// </summary>
    [BsonElement(nameof(Pressure))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(Pressure))]
    [JsonInclude]
    public int Pressure { get; set; }
}