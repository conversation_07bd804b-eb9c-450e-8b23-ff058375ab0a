﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 智能体_学生端口语交际提交入参
    /// </summary>
    public class AgentStudentOralCommunicationSubmitInput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string? AgentTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 是否预览(默认否)
        /// </summary>
        public bool IsPreview { get; set; } = false;

        /// <summary>
        /// 预览临时Id
        /// </summary>
        public string? TemporaryId { get; set; }
    }
}
