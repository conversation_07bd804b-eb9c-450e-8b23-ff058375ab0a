﻿using Microsoft.AspNetCore.Http;
using OBS;
using OBS.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.Model.Upload;
using Uwoo.Util.Helper;

namespace Uwoo.Core.Utilities
{
	public class UploadHuaWeiObs
	{
		/// <summary>
		/// 上传对象
		/// </summary>
		/// <param name="file">图片</param>
		/// <param name="bucketName">桶名称</param>
		/// <returns>地址</returns>
		public string Execute(IFormFile file, string bucketName)
		{
			var suff = file.FileName.Substring(file.FileName.LastIndexOf('.'));
			var responseUrl = Upload(file.OpenReadStream(), suff, bucketName);
			return new UploadFileModel { ObjectUrl = responseUrl }.ToJsonString();
		}

		public string Execute(byte[] fileBytes, string contentType,string bucketName)
		{
			try
			{
				// 创建ObsClient实例
				ObsClient obsClient = new ObsClient(AppSetting.HuaweiObs.AK, AppSetting.HuaweiObs.SK, AppSetting.HuaweiObs.Endpoint);

				// 生成唯一文件名
				string fileName = $"{Guid.NewGuid()}.{contentType}";
				string objectKey = $"{contentType}/{fileName}";

				using (var ms = new MemoryStream(fileBytes))
				{
					// 上传文件
					var request = new PutObjectRequest
					{
						BucketName = bucketName,
						ObjectKey = objectKey,
						InputStream = ms
					};

					PutObjectResponse response = obsClient.PutObject(request);

					if (response.StatusCode == System.Net.HttpStatusCode.OK)
					{
						return response.ObjectUrl;
					}
				}

				throw new Exception("Upload failed");
			}
			catch (Exception ex)
			{
				throw new Exception($"Upload failed：{ex.StackTrace}");
			}
		}

		/// <summary>
		/// 异步上传
		/// </summary>
		/// <param name="file"></param>
		/// <param name="bucket_name"></param>
		/// <returns></returns>
		[Obsolete]
		public async Task<string> ExecuteAsync(IFormFile file, string bucket_name)
		{
			//var ext = Path.GetExtension(file.FileName);
			//var files = file.OpenReadStream().ReadToBytes();
			//var request = new HttpRequestClient();
			//var bites = new List<byte[]>();
			//request.SetFieldValue("bucketName", bucket_name, bites);
			//var name = Guid.NewGuid().ToString("N") + ext;
			//request.SetFieldValue("uploadFile", name, "multipart/form-data", files, bites);
			//var result = await request.UploadObjectAsync(_uploadWordUrl, bites);
			//return result;
			await Task.CompletedTask;
			return "废弃";
		}

		/// <summary>
		/// 批量上传多个文件
		/// </summary>
		/// <param name="files"></param>
		/// <param name="bucketName"></param>
		/// <returns></returns>
		public List<string> Execute(IFormFileCollection files, string bucketName)
		{
			List<string> paperImgUrls = new List<string>();
			foreach (var f in files)
			{
				var uploadFileModel = Execute(f, bucketName).ToObject<UploadFileModel>();
				if (!string.IsNullOrEmpty(uploadFileModel.ObjectUrl))
				{
					paperImgUrls.Add(uploadFileModel.ObjectUrl);
				}
			}

			return paperImgUrls;
		}

		/// <summary>
		/// Form上传文件
		/// </summary>
		/// <param name="path">本地文件路径</param>
		/// <param name="contentType"></param>
		/// <param name="bucketName">桶名称</param>
		/// <param name="isDeleteFile"></param>
		/// <param name="isFileName"></param>
		/// <returns>Item1:文件地址  Item2:文件大小</returns>
		public (string, long) Upload(string path, string contentType, string bucketName, bool isDeleteFile = false, bool isFileName = false)
		{
			if (!File.Exists(path))
			{
				return (string.Empty, 0);
			}

			try
			{
				var fileStream = File.Open(path, FileMode.Open, FileAccess.Read, FileShare.Read);

				var length = fileStream.Length;
				// 读取文件的 byte[]
				byte[] bytes = new byte[fileStream.Length];
				_ = fileStream.Read(bytes, 0, bytes.Length);
				fileStream.Close();
				// 把 byte[] 转换成 Stream
				Stream stream = new MemoryStream(bytes);
				var url = Upload(stream, contentType, bucketName, isFileName ? fileStream.Name.Substring(fileStream.Name.LastIndexOf("\\") + 1) : "");

				if (isDeleteFile)
					File.Delete(path);
				return (url, length);

			}
			catch(Exception ex)
			{
				throw ex;
			}
		}

		/// <summary>
		/// 流式上传文件
		/// </summary>
		/// <param name="stream">文件流</param>
		/// <param name="contentType">桶名称</param>
		/// <param name="bucketName">桶名称</param>
		/// <param name="fileName">桶名称</param>
		/// <returns></returns>
		public string Upload(Stream stream, string contentType, string bucketName, string fileName = "")
		{
			// 创建ObsClient实例
			ObsClient client = new ObsClient(AppSetting.HuaweiObs.AK, AppSetting.HuaweiObs.SK, AppSetting.HuaweiObs.Endpoint);
			// 上传流
			try
			{
				PutObjectRequest request = new PutObjectRequest()
				{
					BucketName = bucketName,
					ObjectKey = fileName.IsNullOrEmpty() ? Guid.NewGuid().ToString("N") + contentType : fileName,
					InputStream = stream
				};

				if ("png".Equals(contentType, StringComparison.OrdinalIgnoreCase))
				{
					request.ContentType = "image/png";
				}
				PutObjectResponse response = client.PutObject(request);
				return response.ObjectUrl;
			}
			catch(Exception ex)
			{
				throw ex;
			}
		}
	}
}
