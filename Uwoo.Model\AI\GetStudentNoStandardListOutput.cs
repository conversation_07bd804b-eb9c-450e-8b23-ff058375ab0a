﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取学生未达标列表输出
    /// </summary>
    public class GetStudentNoStandardListOutput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 项目化实践Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目化实践名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目化实践Logo
        /// </summary>
        public string? ProjectLogo { get; set; }

        /// <summary>
        /// 项目化实践背景
        /// </summary>
        public string? ProjectIntroduce { get; set; }

        /// <summary>
        /// 项目化实践阶段
        /// </summary>
        public List<GetStudentNoStandardListStageOutput> ProjectStageInfos { get; set; } = new List<GetStudentNoStandardListStageOutput>();
    }

    /// <summary>
    /// 获取学生未达标列表阶段信息输出
    /// </summary>
    public class GetStudentNoStandardListStageOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 项目化实践阶段任务
        /// </summary>
        public List<GetStudentNoStandardListStageTaskOutput> ProjectStageTaskInfos { get; set; } = new List<GetStudentNoStandardListStageTaskOutput>();
    }

    /// <summary>
    /// 获取学生未达标列表阶段任务信息输出
    /// </summary>
    public class GetStudentNoStandardListStageTaskOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 项目化实践阶段Id
        /// </summary>
        public string? ProjectStageId { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 任务提交记录Id
        /// </summary>
        public List<string> TaskSubmitId { get; set; } = new List<string>();

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }
    }
}
