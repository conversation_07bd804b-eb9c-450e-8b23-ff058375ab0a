﻿// -- Function：CardPenLogService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2024/1/31 16:19
namespace Uwoo.Mongo.Services.Mongo;

using MongoDB.Driver;
using Uwoo.Contracts.Config;
using Uwoo.Mongo.Infrastructure;
using Uwoo.Mongo.Interfaces.Business;
using Uwoo.Mongo.Models;

/// <inheritdoc cref="ICardPenLogService" />
public class CardPenLogService : MongoAutoService<CardPenLog>, ICardPenLogService
{
	/// <inheritdoc />
	public CardPenLogService(IMongoConfig config) : base(config)
	{
	}

	/// <inheritdoc />
	protected override void CreateIndex(IMongoCollection<CardPenLog> collection)
	{
		var userid_builder = Builders<CardPenLog>.IndexKeys
			.Ascending(x => x.UserId)
			.Ascending(x => x.PageId)
			.Ascending(x => x.ItemNo)
			.Ascending(x => x.PaperId)
			.Ascending(x => x.Mac)
			.Ascending(x => x.Page)
			.Ascending(x => x.AddTime);
		collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");

		var pageid_builder = Builders<CardPenLog>.IndexKeys
			.Ascending(x => x.PageId)
			.Ascending(x => x.UserId)
			.Ascending(x => x.Mac)
			.Ascending(x => x.AddTime);
		collection.Indexes.CreateIndex(pageid_builder, collection.CollectionNamespace.CollectionName + "_PageId_Key");
	}

	/// <summary>
	/// 获取主观题的答题笔迹
	/// </summary>
	/// <param name="colname"></param>
	/// <param name="userId"></param>
	/// <param name="paperId"></param>
	/// <param name="year">学年</param>
	/// <returns></returns>
	public List<CardPenLog> GetAll(string colname, string userId, string paperId, int? year= null)
	{
		var mongo = GetConnection(colname, year);
		var list = mongo.Find(x => x.PaperId.Equals(paperId, StringComparison.OrdinalIgnoreCase) && x.UserId == userId).ToList();
		return list.OrderBy(x => x.Mid).ToList();
	}

	/// <inheritdoc />
	public List<CardPenLog> GetPenLogsByItemNo(string colname,string userId,string paperId,int itemNo)
	{
		var mongo = GetConnection(colname);
		var list = mongo.Find(x => x.PaperId.Equals(paperId, StringComparison.OrdinalIgnoreCase) && x.UserId == userId && x.ItemNo == itemNo).ToList();
		return list.OrderBy(x => x.Mid).ToList();
	}

	/// <inheritdoc />
	public void DeleteCardPenLog(string colname, string paperId, List<string> userid)
	{
		var mongo = GetConnection(colname);
		mongo.DeleteMany(x => paperId.Contains(x.PaperId) && userid.Contains(x.UserId));
	}
}