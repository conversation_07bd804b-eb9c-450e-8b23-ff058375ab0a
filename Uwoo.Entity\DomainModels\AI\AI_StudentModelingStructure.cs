﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// 智能体_学生建模构建
	/// </summary>
	[Table("AI_StudentModelingStructure")]
    public class AI_StudentModelingStructure
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 建模Id
        /// </summary>
        public string ModelingId { get; set; }

        /// <summary>
        /// 建模阶段Id
        /// </summary>
        public string ModelingStageId { get; set; }

        /// <summary>
        /// 建模阶段任务Id
        /// </summary>
        public string ModelingStageTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string StudentId { get; set; }

        /// <summary>
        /// 学生做建模阶段任务提交记录Id
        /// </summary>
        public string StudentDoModelingTaskId { get; set; }

        /// <summary>
        /// 变量定义
        /// </summary>
        public string Variable { get; set; }

        /// <summary>
        /// 表达式
        /// </summary>
        public string Expression { get; set; }

        /// <summary>
        /// 是否属于优化
        /// </summary>
        public bool IsOptimize { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string Versions { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
