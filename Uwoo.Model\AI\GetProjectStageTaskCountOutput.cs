﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取项目化实践阶段任务统计输出
    /// </summary>
    public class GetProjectStageTaskCountOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 任务统计信息
        /// </summary>
        public List<GetProjectStageTaskInfoOutput> Tasks { get; set; } = new List<GetProjectStageTaskInfoOutput>();
    }

    /// <summary>
    /// 获取项目化实践阶段任务统计信息输出
    /// </summary>
    public class GetProjectStageTaskInfoOutput
    {
        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:思维导图、7:选词填空）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 参与率/查询率
        /// </summary>
        public decimal Participation { get; set; }

        /// <summary>
        /// 平均对话/平均查询次数
        /// </summary>
        public decimal AvgDialogue { get; set; }

        /// <summary>
        /// 平均分
        /// </summary>
        public decimal? AvgScore { get; set; }

        /// <summary>
        /// 上传率
        /// </summary>
        public decimal Submit { get; set; }

        /// <summary>
        /// 等第
        /// </summary>
        public List<GetProjectStageTaskLevelOutput> TaskLevels { get; set; } = new List<GetProjectStageTaskLevelOutput>();

        /// <summary>
        /// 查询趋势
        /// </summary>
        public List<GetProjectStageTaskQueryCountOutput> QueryCount { get; set; } = new List<GetProjectStageTaskQueryCountOutput>();

        /// <summary>
        /// 主题
        /// </summary>
        public List<string> Themes { get; set; } = new List<string>();
    }

    /// <summary>
    /// 获取项目化实践阶段任务统计等第输出
    /// </summary>
    public class GetProjectStageTaskLevelOutput
    {
        /// <summary>
        /// 等第名称
        /// </summary>
        public string? LevelName { get; set; }

        /// <summary>
        /// 等第数量
        /// </summary>
        public int LevelCount { get; set; }
    }

    /// <summary>
    /// 获取项目化实践阶段任务查询统计输出
    /// </summary>
    public class GetProjectStageTaskQueryCountOutput
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 统计数量
        /// </summary>
        public int Count { get; set; }
    }

    /// <summary>
    /// 获取做项目化实践阶段任务主题
    /// </summary>
    public class GetDoProjectStageTaskThemes
    {
        /// <summary>
        /// 阶段任务Id
        /// </summary>
        public string? ProjectStageTaskId { get; set; }

        /// <summary>
        /// 主题名称
        /// </summary>
        public string? Name { get; set; }
    }
}
