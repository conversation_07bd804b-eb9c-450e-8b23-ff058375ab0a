﻿//   -- Function：RealtimeBatteryModel.cs
//   --- Project：Uwoo.ContractModels
//   ---- Remark：
//   ---- Author：Lucifer
//   ------ Date：2023/09/19 16:28

using System.Text.Json.Serialization;

// ReSharper disable once CheckNamespace
namespace X.PenServer.Contracts.Socket
{
	/// <summary>
	/// 实时电量
	/// </summary>
	public class RealtimeBatteryModel : RealtimeMessageBase
	{
		/// <summary>
		/// 电量
		/// </summary>
		/// <remarks>电量百分比: 0 ~ 10</remarks>
		[JsonPropertyName(nameof(Battery))]
		[JsonInclude]
		public int Battery { get; set; }
	}
}