﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 文件分析（保存分析结果）接口入参
    /// </summary>
    public class SaveFileAnalysisInput
    {
        /// <summary>
        /// 文件
        /// </summary>
        public List<SaveFileAnalysisFileInfoInput> Files { get; set; } = new List<SaveFileAnalysisFileInfoInput>();

        /// <summary>
        /// 项目化实践阶段任务Id
        /// </summary>
        public string? ProjectStageTaskId { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 1学生端项目化实践
        /// </summary>
        public int Type { get; set; }
    }

    /// <summary>
    /// 文件分析（保存分析结果）接口入参
    /// </summary>
    public class SaveFileAnalysisFileInfoInput
    {
        /// <summary>
        /// 文件地址
        /// </summary>
        public string? FileUrl { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public int Length { get; set; }
    }
}
