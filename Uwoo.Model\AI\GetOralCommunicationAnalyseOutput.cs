﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取口语交际分析输出
    /// </summary>
    public class GetOralCommunicationAnalyseOutput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 智能体Logo
        /// </summary>
        public string? Logo { get; set; }

        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string? AgentTaskId { get; set; }

        /// <summary>
        /// 智能体任务名称
        /// </summary>
        public string? AgentTaskName { get; set; }

        /// <summary>
        /// 智能体任务项目背景介绍
        /// </summary>
        public string? AgentTaskIntroduce { get; set; }

        /// <summary>
        /// 互动模式
        /// </summary>
        public string? InteractiveMode { get; set; }

        /// <summary>
        /// 提交人数
        /// </summary>
        public int SubmitCount { get; set; }

        /// <summary>
        /// 平均等第
        /// </summary>
        public string? AvgLevel { get; set; }

        /// <summary>
        /// 等第分布
        /// </summary>
        public List<GetOralCommunicationAnalyseLevelOutput> LevelList { get; set; } = new List<GetOralCommunicationAnalyseLevelOutput>();

        /// <summary>
        /// 成绩分布
        /// </summary>
        public List<GetOralCommunicationAnalyseScoreOutput> ScoreList { get; set; } = new List<GetOralCommunicationAnalyseScoreOutput>();
    }

    /// <summary>
    /// 获取口语交际分析等第分布输出
    /// </summary>
    public class GetOralCommunicationAnalyseLevelOutput
    {
        /// <summary>
        /// 等第名称
        /// </summary>
        public string? LevelName { get; set; }

        /// <summary>
        /// 等第数量
        /// </summary>
        public int LevelCount { get; set; }
    }

    /// <summary>
    /// 获取口语交际分析成绩分布输出
    /// </summary>
    public class GetOralCommunicationAnalyseScoreOutput
    {
        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生学号
        /// </summary>
        public string? StudentNum { get; set; }

        /// <summary>
        /// 学生姓名
        /// </summary>
        public string? StudentName { get; set; }

        /// <summary>
        /// 学生等第
        /// </summary>
        public string? StudentLevel { get; set; }
    }
}
