﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.CacheManager.IService;

namespace UwooAgent.Core.CacheManager.IBusinessCacheService
{
    /// <summary>
    /// 智能体通用缓存
    /// </summary>
    public interface IAgentCommonCacheService : IRedisService
    {
        /// <summary>
        /// 缓存锁
        /// </summary>
        /// <param name="key">key</param>
        /// <returns></returns>
        bool SetRedisLock(string key);

        /// <summary>
        /// 释放缓存锁
        /// </summary>
        /// <param name="key">key</param>
        /// <returns></returns>
        void DelRedisLock(string key);
    }
}
