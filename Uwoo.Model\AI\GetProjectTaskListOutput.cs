﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取项目化实践任务列表输出
    /// </summary>
    public class GetProjectTaskListOutput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 项目化实践Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目化实践名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目化实践背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 项目化实践图标
        /// </summary>
        public string? TaskLogo { get; set; }

        /// <summary>
        /// 发布状态（1已发布、2未发布）
        /// </summary>
        public int PublishStatus { get; set; }

        /// <summary>
        /// 发布的班级信息
        /// </summary>
        public List<GetProjectTaskListClassInfoOutput> ClassInfos { get; set; } = new List<GetProjectTaskListClassInfoOutput>();
    }

    /// <summary>
    /// 项目化实践发布的班级
    /// </summary>
    public class GetProjectTaskListClassInfoOutput
    {
        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 班级名称
        /// </summary>
        public string? ClassName { get; set; }
    }
}
