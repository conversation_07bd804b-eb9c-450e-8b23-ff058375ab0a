﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 教师端阅读理解_下载学生评估结果Dto
    /// </summary>
    public class ReadingDownloadStudentResultDto
    {
        /// <summary>
        /// 阅读理解阶段任务Id
        /// </summary>
        public string? ReadingProjectStageTaskId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? TaskName { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生名称
        /// </summary>
        public string? RealName { get; set; }

        /// <summary>
        /// 评估结果
        /// </summary>
        public string? AssessmentResult { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
}
