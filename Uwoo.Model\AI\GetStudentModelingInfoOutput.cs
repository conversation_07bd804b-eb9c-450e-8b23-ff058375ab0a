﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取学生端建模信息输出
    /// </summary>
    public class GetStudentModelingInfoOutput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 建模Id
        /// </summary>
        public string? ModelingId { get; set; }

        /// <summary>
        /// 建模名称
        /// </summary>
        public string? ModelingName { get; set; }

        /// <summary>
        /// 建模Logo
        /// </summary>
        public string? ModelingLogo { get; set; }

        /// <summary>
        /// 建模背景
        /// </summary>
        public string? ModelingIntroduce { get; set; }

        /// <summary>
        /// 建模任务状态（1进行中、2已结束）
        /// </summary>
        public int ModelingTaskState { get; set; }

        /// <summary>
        /// 进度
        /// </summary>
        public decimal ProgressBar { get; set; }

        /// <summary>
        /// 是否公布评分
        /// </summary>
        public bool IsScorePublish { get; set; }

        /// <summary>
        /// 评分公布类型（1提交后、2截至后、3指定时间）
        /// </summary>
        public int? ScorePublishType { get; set; }

        /// <summary>
        /// 评分公布时间（评分公布类型指定时间）
        /// </summary>
        public DateTime? ScorePublishTime { get; set; }

        /// <summary>
        /// 建模阶段
        /// </summary>
        public List<GetStudentModelingStageInfoOutput> ModelingStageInfos { get; set; } = new List<GetStudentModelingStageInfoOutput>();
    }

    /// <summary>
    /// 获取学生端建模阶段信息输出
    /// </summary>
    public class GetStudentModelingStageInfoOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string? Describe { get; set; }

        /// <summary>
        /// 是否锁
        /// </summary>
        public bool IsLock { get; set; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 建模阶段任务
        /// </summary>
        public List<GetStudentModelingStageTaskInfoOutput> ModelingStageTaskInfos { get; set; } = new List<GetStudentModelingStageTaskInfoOutput>();
    }

    /// <summary>
    /// 获取学生端建模阶段任务信息输出
    /// </summary>
    public class GetStudentModelingStageTaskInfoOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 建模阶段Id
        /// </summary>
        public string? ModelingStageId { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:问题理解、7:模型构建、8:模型假设、9:模型评价）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 开场白
        /// </summary>
        public string? Prologue { get; set; }

        /// <summary>
        /// 音色Id
        /// </summary>
        public string? ToneId { get; set; }

        /// <summary>
        /// 角色名称（用于AI展示名称）
        /// </summary>
        public string? RoleName { get; set; }

        /// <summary>
        /// 是否开启提交条件（任务点设置）
        /// </summary>
        public bool TaskIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（任务点设置）
        /// </summary>
        public bool TaskIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（任务点设置）
        /// </summary>
        public decimal TaskAssessmentScore { get; set; }

        /// <summary>
        /// 是否观看视频（任务点设置）
        /// </summary>
        public bool TaskIsWatchVideo { get; set; }

        /// <summary>
        /// 是否开启视频观看时长条件（任务点设置）
        /// </summary>
        public bool TaskIsVideoWatchDuration { get; set; }

        /// <summary>
        /// 视频观看时长（分钟）（任务点设置）
        /// </summary>
        public int TaskVideoWatchDuration { get; set; }

        /// <summary>
        /// 是否需要阅读全部文档（任务点设置）
        /// </summary>
        public bool TaskIsReadAllDocuments { get; set; }

        /// <summary>
        /// 是否开启提交条件（组间任务设置）
        /// </summary>
        public bool GroupIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（组间任务设置）
        /// </summary>
        public bool GroupIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（组间任务设置）
        /// </summary>
        public decimal GroupAssessmentScore { get; set; }

        /// <summary>
        /// 是否观看视频（组间任务设置）
        /// </summary>
        public bool GroupIsWatchVideo { get; set; }

        /// <summary>
        /// 是否开启视频观看时长条件（组间任务设置）
        /// </summary>
        public bool GroupIsVideoWatchDuration { get; set; }

        /// <summary>
        /// 视频观看时长（分钟）（组间任务设置）
        /// </summary>
        public int GroupVideoWatchDuration { get; set; }

        /// <summary>
        /// 是否需要阅读全部文档（组间任务设置）
        /// </summary>
        public bool GroupIsReadAllDocuments { get; set; }

        /// <summary>
        /// 是否锁
        /// </summary>
        public bool IsLock { get; set; }

        /// <summary>
        /// 完成状态（1未开始、2进行中、3已完成）
        /// </summary>
        public int State { get; set; }

        /// <summary>
        /// 是否已提交但未达标
        /// </summary>
        public bool IsSubmitNoStandard { get; set; }

        /// <summary>
        /// 任务提交记录Id
        /// </summary>
        public string? TaskSubmitId { get; set; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 任务视频
        /// </summary>
        public List<GetStudentModelingStageTaskVideoInfoOutput> TaskVideos { get; set; } = new List<GetStudentModelingStageTaskVideoInfoOutput>();

        /// <summary>
        /// 任务文档
        /// </summary>
        public List<GetStudentModelingStageTaskDocumentInfoOutput> TaskDocuments { get; set; } = new List<GetStudentModelingStageTaskDocumentInfoOutput>();
    }

    /// <summary>
    /// 获取学生端建模阶段任务视频信息输出
    /// </summary>
    public class GetStudentModelingStageTaskVideoInfoOutput
    {
        /// <summary>
        /// 视频Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 建模阶段任务Id
        /// </summary>
        public string? ModelingStageTaskId { get; set; }

        /// <summary>
        /// 视频名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 视频地址
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// 视频大小
        /// </summary>
        public int Size { get; set; }

        /// <summary>
        /// 视频时长（秒）
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 是否观看
        /// </summary>
        public bool IsWatch { get; set; }

        /// <summary>
        /// 观看累计时长（秒）
        /// </summary>
        public int TotalWatchDuration { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Order { get; set; }
    }

    /// <summary>
    /// 获取学生端建模阶段任务文档信息输出
    /// </summary>
    public class GetStudentModelingStageTaskDocumentInfoOutput
    {
        /// <summary>
        /// 文档Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 建模阶段任务Id
        /// </summary>
        public string? ModelingStageTaskId { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 文件地址
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public int Size { get; set; }

        /// <summary>
        /// 是否阅读
        /// </summary>
        public bool IsRead { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Order { get; set; }
    }
}
