﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取建模列表输出
    /// </summary>
    public class GetModelingListOutput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 建模Id
        /// </summary>
        public string? ModelingId { get; set; }

        /// <summary>
        /// 建模名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 建模背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 建模图标
        /// </summary>
        public string? TaskLogo { get; set; }

        /// <summary>
        /// 发布状态（1已发布、2未发布）
        /// </summary>
        public int PublishStatus { get; set; }

        /// <summary>
        /// 发布的班级信息
        /// </summary>
        public List<GetModelingListClassInfoOutput> ClassInfos { get; set; } = new List<GetModelingListClassInfoOutput>();

        /// <summary>
        /// 发布的学生信息
        /// </summary>
        public List<GetModelingListStudentInfoOutput> StudentInfos { get; set; } = new List<GetModelingListStudentInfoOutput>();
    }

    /// <summary>
    /// 建模发布的班级信息
    /// </summary>
    public class GetModelingListClassInfoOutput
    {
        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 班级名称
        /// </summary>
        public string? ClassName { get; set; }
    }

    /// <summary>
    /// 建模发布的学生信息
    /// </summary>
    public class GetModelingListStudentInfoOutput
    {
        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生姓名
        /// </summary>
        public string? StudentName { get; set; }
    }

    /// <summary>
    /// 建模发布信息
    /// </summary>
    public class GetModelingPublishInfoDto
    {
        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string? AgentTaskId { get; set; }

        /// <summary>
        /// 发布类型（1班、2学生）
        /// </summary>
        public int? PublishType { get; set; }

        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 班级名称
        /// </summary>
        public string? ClassName { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生姓名
        /// </summary>
        public string? StudentName { get; set; }
    }
}
