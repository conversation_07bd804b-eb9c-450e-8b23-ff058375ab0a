﻿using AutoMapper;
using Coldairarrow.Util;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Core.Utilities;
using Uwoo.Model.CustomException;
using UwooAgent.Core.CacheManager.IBusinessCacheService;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.Base;
using UwooAgent.Model.AI;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_学生端口语交际
    /// </summary>
    public class AgentStudentOralCommunicationService : ServiceBase<AI_OralCommunicationTask, IAgenStudentOralCommunicationRepository>, IAgentStudentOralCommunicationService, IDependency
    {
        #region DI

        private readonly IAgentCommonService _agentCommonService;
        private readonly IClassCacheService _classCacheService;

        public AgentStudentOralCommunicationService(
            IAgentCommonService agentCommonService,
            IClassCacheService classCacheService)
        {
            _agentCommonService = agentCommonService;
            _classCacheService = classCacheService;
        }

        #endregion

        /// <summary>
        /// 智能体_学生端口语交际对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        public async Task AgentStudentOralCommunicationDialogue(AgentStudentOralCommunicationDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取智能体信息
                AI_AgentBaseInfo agentBaseInfo = await DBSqlSugar.Queryable<AI_AgentBaseInfo>().Where(p => p.Id == input.AgentId && p.IsDeleted == false).FirstAsync();
                if (agentBaseInfo == null)
                {
                    throw new BusException("智能体Id异常!");
                }

                //获取模型Id
                AI_ModelBaseInfo modelBaseInfo = await DBSqlSugar.Queryable<AI_ModelBaseInfo>().Where(p => p.Id == agentBaseInfo.ModelId && p.IsDeleted == false).FirstAsync();
                if (modelBaseInfo == null || string.IsNullOrEmpty(modelBaseInfo.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                //获取口语交际Key
                string oralCommunicationKey = string.Empty;
                if (input.IsPreview)
                {
                    oralCommunicationKey = AIAgentKeys.GetPreviewOralCommunicationKey(input.AgentId, input.AgentTaskId, input.TemporaryId);
                }
                else
                {
                    oralCommunicationKey = AIAgentKeys.GetStudentOralCommunicationKey(input.AgentId, input.AgentTaskId, input.StudentId);
                }
                if (string.IsNullOrEmpty(oralCommunicationKey))
                {
                    throw new BusException("获取交际Key异常!");
                }

                //获取豆包上下文缓存Id
                bool isCreate = false;
                bool isTimeOut = false;
                AI_ContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ContextCacheKey>().Where(p => p.CacheKey == oralCommunicationKey && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                if (contextCacheKey != null)
                {
                    //验证是否过期
                    if (contextCacheKey.ExpirationTime <= DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                        isTimeOut = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                //创建上下文缓存
                if (isCreate)
                {
                    contextCacheKey = await AgentStudentOralCommunicationCreateContext(new AgentStudentOralCommunicationCreateContextInput()
                    {
                        AgentTaskId = input.AgentTaskId,
                        ClassId = input.ClassId,
                        OralCommunicationKey = oralCommunicationKey
                    });
                }

                //缓存过期逻辑
                if (isTimeOut && !input.IsPreview)
                {
                    //获取历史对话记录
                    List<AI_DialogueContentRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_DialogueContentRecord>().Where(p => p.Key == oralCommunicationKey && p.IsDeleted == false).OrderBy(p => p.CreateTime).With(SqlWith.NoLock).ToListAsync();
                    if (dialogueContentRecords.Count > 0)
                    {
                        //历史对话记录
                        string dialogueData = "学生历史问答记录如下:\n";
                        foreach (var dialogueContentRecord in dialogueContentRecords)
                        {
                            AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                            dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                             + $"学生问:{ask.AskText}。\n"
                                             + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                        }
                        dialogueData += "要求：后续学生问答结合历史记录产出你的回答。（注意:当前对话你只需要输出'收到'）";

                        //上下文对话（历史对话记录）
                        Func<string, Task> emptyDataHandler = async (data) =>
                        {
                            await Task.CompletedTask;
                        };
                        await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                        {
                            context_id = contextCacheKey.CacheId,
                            content = dialogueData,
                            role = "user",
                            modelId = modelBaseInfo.Modelkey
                        }, async (data) =>
                        {
                            if (cancellationToken.IsCancellationRequested)
                            {
                                return;
                            }
                            await emptyDataHandler(data);
                        }, cancellationToken);
                    }
                }

                //上下文对话
                string msg = string.Empty;
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = input.Msg,
                    role = "user",
                    modelId = modelBaseInfo.Modelkey
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);

                    string json = data;
                    int startIndex = json.IndexOf('{');
                    json = json.Substring(startIndex);
                    JObject jsonObject = JObject.Parse(json);
                    string content = jsonObject["Content"]?.Value<string>();
                    if (content != null)
                    {
                        msg += content;
                    }
                }, cancellationToken);

                //更新过期时间
                contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                if (!input.IsPreview)
                {
                    //保存会话记录
                    AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                    {
                        AskText = input.Msg,
                        AudioFile = new AIDialogueASKAudioFileInfo()
                        {
                            AudioUrl = input.AudioUrl,
                            Duration = input.Duration,
                        }
                    };
                    AI_DialogueContentRecord aI_DialogueContentRecord = new AI_DialogueContentRecord()
                    {
                        Id = IdHelper.GetId(),
                        Key = oralCommunicationKey,
                        Ask = aIDialogueASKDto.ToJsonString(),
                        Answer = msg,
                        CreateTime = DateTime.Now,
                        MessageType = 2,
                        BusinessId = input.AgentTaskId,
                        IsDeleted = false
                    };
                    await DBSqlSugar.Insertable(aI_DialogueContentRecord).ExecuteCommandAsync();
                }

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
        }

        /// <summary>
        /// 智能体_学生端口语交际提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AgentStudentOralCommunicationSubmitOutput> AgentStudentOralCommunicationSubmit(AgentStudentOralCommunicationSubmitInput input)
        {
            try
            {
                //验证是否存在提交记录
                if (!input.IsPreview)
                {
                    bool isExists = await DBSqlSugar.Queryable<AI_StudentDoOralCommunicationTask>().With(SqlWith.NoLock).AnyAsync(p => p.StudentId == input.StudentId && p.AgentTaskId == input.AgentTaskId && p.IsDeleted == false);
                    if (isExists)
                    {
                        return new AgentStudentOralCommunicationSubmitOutput()
                        {
                            AssessmentResult = "已存在提交记录!"
                        };
                    }
                }

                //获取智能体信息
                AI_AgentBaseInfo agentBaseInfo = await DBSqlSugar.Queryable<AI_AgentBaseInfo>().Where(p => p.Id == input.AgentId && p.IsDeleted == false).FirstAsync();
                if (agentBaseInfo == null)
                {
                    throw new BusException("智能体Id异常!");
                }

                //获取模型Id
                AI_ModelBaseInfo modelBaseInfo = await DBSqlSugar.Queryable<AI_ModelBaseInfo>().Where(p => p.Id == agentBaseInfo.ModelId && p.IsDeleted == false).FirstAsync();
                if (modelBaseInfo == null || string.IsNullOrEmpty(modelBaseInfo.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                //获取口语交际Key
                string oralCommunicationKey = string.Empty;
                if (input.IsPreview)
                {
                    oralCommunicationKey = AIAgentKeys.GetPreviewOralCommunicationKey(input.AgentId, input.AgentTaskId, input.TemporaryId);
                }
                else
                {
                    oralCommunicationKey = AIAgentKeys.GetStudentOralCommunicationKey(input.AgentId, input.AgentTaskId, input.StudentId);
                }
                if (string.IsNullOrEmpty(oralCommunicationKey))
                {
                    throw new BusException("获取交际Key异常!");
                }

                //获取口语交际提交指令
                AI_Directive submitDirective = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentOralCommunicationSubmit_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                if (submitDirective == null)
                {
                    throw new BusException("无法获取口语交际提交系统提示词,请联系管理员!");
                }

                //获取豆包上下文缓存Id
                bool isCreate = false;
                bool isTimeOut = false;
                AI_ContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ContextCacheKey>().Where(p => p.CacheKey == oralCommunicationKey && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                if (contextCacheKey != null)
                {
                    //验证是否过期
                    if (contextCacheKey.ExpirationTime < DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                        isTimeOut = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                //创建上下文缓存
                if (isCreate)
                {
                    contextCacheKey = await AgentStudentOralCommunicationCreateContext(new AgentStudentOralCommunicationCreateContextInput()
                    {
                        AgentTaskId = input.AgentTaskId,
                        ClassId = input.ClassId,
                        OralCommunicationKey = oralCommunicationKey
                    });
                }

                //对话信息
                string msg = string.Empty;

                //缓存过期逻辑
                if (isTimeOut && !input.IsPreview)
                {
                    //获取历史对话记录
                    List<AI_DialogueContentRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_DialogueContentRecord>().Where(p => p.Key == oralCommunicationKey && p.IsDeleted == false).OrderBy(p => p.CreateTime).With(SqlWith.NoLock).ToListAsync();
                    if (dialogueContentRecords.Count > 0)
                    {
                        //历史对话记录
                        string dialogueData = "学生历史问答记录如下:\n";
                        foreach (var dialogueContentRecord in dialogueContentRecords)
                        {
                            AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                            dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                             + $"学生问:{ask.AskText}。\n"
                                             + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                        }
                        msg = submitDirective.Directive + dialogueData;
                    }
                }
                else
                {
                    msg = submitDirective.Directive;
                }

                string resultData = string.Empty;
                //上下文对话
                Func<string, Task> emptyDataHandler = async (data) =>
                {
                    await Task.CompletedTask;
                };
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = msg,
                    role = "user",
                    modelId = modelBaseInfo.Modelkey
                }, async (data) =>
                {
                    await emptyDataHandler(data);

                    string json = data;
                    int startIndex = json.IndexOf('{');
                    json = json.Substring(startIndex);
                    JObject jsonObject = JObject.Parse(json);
                    string content = jsonObject["Content"]?.Value<string>();
                    if (content != null)
                    {
                        resultData += content;
                    }
                });

                if (!string.IsNullOrEmpty(resultData))
                {
                    Regex JsonBlockRegex = new Regex(@"\{(?:[^\{\}]+|(?<Open>\{)|(?<-Open>\}))+(?(Open)(?!))\}", RegexOptions.Compiled | RegexOptions.Singleline);
                    var matches = JsonBlockRegex.Matches(resultData);
                    foreach (Match match in matches)
                    {
                        resultData = match.Value;
                        break;
                    }
                    AI_StudentDoOralCommunicationTask doAgentTaskInfo = JsonConvert.DeserializeObject<AI_StudentDoOralCommunicationTask>(resultData);
                    if (!input.IsPreview)
                    {
                        doAgentTaskInfo.Id = IdHelper.GetId();
                        doAgentTaskInfo.AgentTaskId = input.AgentTaskId;
                        doAgentTaskInfo.Level = doAgentTaskInfo.Score.HasValue ? BusinessUtil.GetAvgLevel(doAgentTaskInfo.Score.Value) : "D";
                        doAgentTaskInfo.StudentId = input.StudentId;
                        doAgentTaskInfo.CreateTime = DateTime.Now;
                        doAgentTaskInfo.Creator = input.StudentId;
                        doAgentTaskInfo.ModifyTime = DateTime.Now;
                        doAgentTaskInfo.Modifier = input.StudentId;
                        doAgentTaskInfo.IsDeleted = false;
                        await DBSqlSugar.Insertable(doAgentTaskInfo).ExecuteCommandAsync();
                    }
                    return new AgentStudentOralCommunicationSubmitOutput()
                    {
                        AssessmentResult = doAgentTaskInfo.AssessmentResult
                    };
                }
                else
                {
                    throw new Exception("提交异常!");
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 智能体_学生端口语交际创建上下文
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<AI_ContextCacheKey> AgentStudentOralCommunicationCreateContext(AgentStudentOralCommunicationCreateContextInput input)
        {
            try
            {
                //上下文信息
                string contextMsg = string.Empty;

                //获取班级信息
                Exam_Class classInfo = _classCacheService.GetClassInfo(input.ClassId);
                if (classInfo == null)
                {
                    classInfo = await DBSqlSugar.Queryable<Exam_Class>().Where(p => p.Id == input.ClassId).With(SqlWith.NoLock).FirstAsync();
                    if (classInfo == null)
                    {
                        throw new BusException("班级信息异常!");
                    }
                    else
                    {
                        _classCacheService.SaveClassInfo(classInfo);
                    }
                }

                string gradeName = BusinessUtil.GradeName(classInfo.GradeId);

                //获取口语交际任务基本信息
                string sql = @"SELECT
                                	ora.Id AS OralCommunicationId,
                                	ora.InteractiveMode,
                                	ora.Target,
                                	ora.Scene,
                                	ora.SceneDetail,
                                	sub.Name AS SubjectName,
                                	cha.ChapterName,
                                	model.Modelkey 
                                FROM
                                	AI_AgentTask task WITH ( NOLOCK )
                                	INNER JOIN AI_AgentBaseInfo agent WITH ( NOLOCK ) ON agent.Id= task.AgentId 
                                	AND agent.IsDeleted= 0
                                	INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON model.Id= agent.ModelId 
                                	AND model.IsDeleted= 0
                                	INNER JOIN AI_OralCommunicationTask ora WITH ( NOLOCK ) ON task.Id= ora.AgentTaskId 
                                	AND ora.IsDeleted= 0
                                	LEFT JOIN Exam_Subject sub WITH ( NOLOCK ) ON task.SubjectId= sub.Id
                                	LEFT JOIN Exam_SubjectChapter cha WITH ( NOLOCK ) ON ora.ChapterId= cha.Id 
                                 WHERE
                                    task.Id= @agentTaskId 
                                    AND task.IsDeleted=0";
                StudentOralCommunicationBaseInfoDto oralCommunicationBaseInfo = await DBSqlSugar.SqlQueryable<StudentOralCommunicationBaseInfoDto>(sql)
                    .AddParameters(new { agentTaskId = input.AgentTaskId }).FirstAsync();

                if (oralCommunicationBaseInfo.InteractiveMode == 1)
                {
                    //获取指令式任务指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentOralCommunicationInstruct_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取口语交际指令式任务系统提示词,请联系管理员!");
                    }

                    //获取指令式任务
                    List<AI_OralCommunicationInstructTask> oralCommunicationInstructTasks = await DBSqlSugar.Queryable<AI_OralCommunicationInstructTask>().Where(p => p.OralCommunicationId == oralCommunicationBaseInfo.OralCommunicationId && p.IsDeleted == false).OrderBy(p => p.OrderId).With(SqlWith.NoLock).ToListAsync();
                    if (oralCommunicationInstructTasks.Count <= 0)
                    {
                        throw new BusException("暂无任务信息!");
                    }

                    List<string> fileUrls = oralCommunicationInstructTasks.Where(p => p.VerificationMode == 2).Select(p => p.HtmlFileUrl).ToList();
                    //获取文件信息
                    List<AIFileInfoDto> fileInfoDtos = new List<AIFileInfoDto>();
                    if (fileUrls.Count > 0)
                    {
                        fileInfoDtos = await DBSqlSugar.Queryable<AI_FileInfo>()
                            .Where(p => fileUrls.Contains(p.FileUrl) && p.IsDeleted == false)
                            .Select(p => new AIFileInfoDto()
                            {
                                Name = p.FileName,
                                Url = p.FileUrl,
                                CreateTime = p.CreateTime
                            }).ToListAsync();
                    }

                    //指令式任务
                    string taskJson = string.Empty;
                    foreach (var oralCommunicationInstructTask in oralCommunicationInstructTasks)
                    {
                        if (oralCommunicationInstructTask.VerificationMode == 1)
                        {
                            //图片地址
                            List<ImgInput> imgInputs = new List<ImgInput>();
                            if (!string.IsNullOrEmpty(oralCommunicationInstructTask.ImgUrl))
                            {
                                imgInputs = JsonConvert.DeserializeObject<List<ImgInput>>(oralCommunicationInstructTask.ImgUrl);
                            }

                            string imgInfo = string.Empty;
                            foreach (var item in imgInputs)
                            {
                                imgInfo += $"![图片地址]({item.ImgUrl})";
                            }

                            taskJson += $"\n===第{oralCommunicationInstructTasks.IndexOf(oralCommunicationInstructTask) + 1}个任务:{oralCommunicationInstructTask.Name}===\n"
                                            + $"指令内容:{oralCommunicationInstructTask.InstructContent}。\n"
                                            + $"验证方式:图片圈选。\n"
                                            + $"图片信息:{imgInfo}。\n"
                                            + $"具体成果要求:{oralCommunicationInstructTask.Result}。\n"
                                            + $"注意:若执行到该任务，图片信息里面的所有内容必须原样输出禁止改变任何数据（如:{imgInfo}）。\n\n";
                        }
                        else if (oralCommunicationInstructTask.VerificationMode == 2)
                        {
                            //获取文件信息
                            AIFileInfoDto fileInfoDto = fileInfoDtos.Where(p => p.Url == oralCommunicationInstructTask.HtmlFileUrl).FirstOrDefault();
                            string fileInfoJson = fileInfoDto != null ? $"```AnimationCard\r\noption={fileInfoDto.ToJsonString()}\r\n```" : "暂无动画";

                            taskJson += $"\n===第{oralCommunicationInstructTasks.IndexOf(oralCommunicationInstructTask) + 1}个任务:{oralCommunicationInstructTask.Name}===\n"
                                            + $"指令内容:{oralCommunicationInstructTask.InstructContent}。\n"
                                            + $"验证方式:动画效果。\n"
                                            + $"动画信息:{fileInfoJson}。\n"
                                            + $"注意:若执行到该任务，动画信息里面的所有内容必须原样输出禁止改变任何数据（如:{fileInfoJson}）。\n\n";
                        }
                        else if (oralCommunicationInstructTask.VerificationMode == 3)
                        {
                            taskJson += $"\n===第{oralCommunicationInstructTasks.IndexOf(oralCommunicationInstructTask) + 1}个任务:{oralCommunicationInstructTask.Name}===\n"
                                            + $"指令内容:{oralCommunicationInstructTask.InstructContent}。\n"
                                            + $"验证方式:文字描述。\n"
                                            + $"具体成果要求:{oralCommunicationInstructTask.Result}。\n\n";
                        }
                    }

                    contextMsg = directive.Directive.Replace("{学段}", gradeName)
                        .Replace("{学科}", oralCommunicationBaseInfo.SubjectName)
                        .Replace("{教材章节}", oralCommunicationBaseInfo.ChapterName)
                        .Replace("{教学目标}", oralCommunicationBaseInfo.Target)
                        .Replace("{场景类型}", oralCommunicationBaseInfo.Scene)
                        .Replace("{场景细节描述}", oralCommunicationBaseInfo.SceneDetail)
                        .Replace("{任务消息}", taskJson);
                }
                else if (oralCommunicationBaseInfo.InteractiveMode == 2)
                {
                    //获取对话式任务指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentOralCommunicationDialogue_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取口语交际对话式任务系统提示词,请联系管理员!");
                    }

                    //获取对话式任务
                    List<AI_OralCommunicationDialogueTask> oralCommunicationDialogueTasks = await DBSqlSugar.Queryable<AI_OralCommunicationDialogueTask>().Where(p => p.OralCommunicationId == oralCommunicationBaseInfo.OralCommunicationId && p.IsDeleted == false).OrderBy(p => p.OrderId).With(SqlWith.NoLock).ToListAsync();
                    if (oralCommunicationDialogueTasks.Count <= 0)
                    {
                        throw new BusException("暂无任务信息!");
                    }

                    //对话式任务
                    string taskJson = string.Empty;
                    foreach (var oralCommunicationDialogueTask in oralCommunicationDialogueTasks)
                    {
                        taskJson += $"\n===第{oralCommunicationDialogueTasks.IndexOf(oralCommunicationDialogueTask) + 1}个任务:{oralCommunicationDialogueTask.Name}===\n"
                                        + $"对话目标:{oralCommunicationDialogueTask.DialogueTarget}。\n"
                                        + $"有效回应标准:{oralCommunicationDialogueTask.ValidRespond}。\n"
                                        + $"追问话术:{oralCommunicationDialogueTask.Asked}。\n\n";
                    }

                    contextMsg = directive.Directive.Replace("{学段}", gradeName)
                        .Replace("{学科}", oralCommunicationBaseInfo.SubjectName)
                        .Replace("{教材章节}", oralCommunicationBaseInfo.ChapterName)
                        .Replace("{教学目标}", oralCommunicationBaseInfo.Target)
                        .Replace("{场景类型}", oralCommunicationBaseInfo.Scene)
                        .Replace("{场景细节描述}", oralCommunicationBaseInfo.SceneDetail)
                        .Replace("{任务消息}", taskJson);
                }

                //创建上下文缓存
                string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                {
                    Msg = contextMsg,
                    TimeOut = 604800,
                    modelId = oralCommunicationBaseInfo.Modelkey
                });
                if (string.IsNullOrEmpty(contextId))
                {
                    throw new Exception("创建上下文缓存异常!");
                }

                AI_ContextCacheKey contextCacheKey = new AI_ContextCacheKey()
                {
                    Id = IdHelper.GetId(),
                    CacheKey = input.OralCommunicationKey,
                    CacheId = contextId,
                    BusinessId = input.AgentTaskId,
                    CreateTime = DateTime.Now,
                    TimeOut = 604800,
                    Explain = "学生端口语交际Key",
                    ExpirationTime = DateTime.Now.AddSeconds(604800),
                    IsDeleted = false
                };
                await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                return contextCacheKey;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }
    }
}
