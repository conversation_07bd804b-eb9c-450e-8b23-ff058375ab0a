﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// AI_教案和点阵笔试卷Word草稿本记录关联
    /// </summary>
    [Table("AI_TeachingPlanDZBWordImportHistoryMapping")]
    public class AI_TeachingPlanDZBWordImportHistoryMapping
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 教案Id
        /// </summary>
        public string TeachingPlanId { get; set; }

        /// <summary>
        /// Word草稿本记录Id
        /// </summary>
        public string DZBWordImportHistoryId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }
    }
}
