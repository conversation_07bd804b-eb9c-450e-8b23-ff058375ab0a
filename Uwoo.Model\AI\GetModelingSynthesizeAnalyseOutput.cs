﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取建模综合分析输出
    /// </summary>
    public class GetModelingSynthesizeAnalyseOutput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 建模Id
        /// </summary>
        public string? ModelingId { get; set; }

        /// <summary>
        /// 建模名称
        /// </summary>
        public string? ModelingName { get; set; }

        /// <summary>
        /// 建模Logo
        /// </summary>
        public string? ModelingLogo { get; set; }

        /// <summary>
        /// 建模背景
        /// </summary>
        public string? ModelingIntroduce { get; set; }

        /// <summary>
        /// 平均等第
        /// </summary>
        public string? AvgLevel { get; set; }

        /// <summary>
        /// 平均等第基于多少份首次提交计算
        /// </summary>
        public int AvgLevelCount { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal Finish { get; set; }

        /// <summary>
        /// 提交率
        /// </summary>
        public decimal Submit { get; set; }

        /// <summary>
        /// 需提交数量
        /// </summary>
        public decimal SubmitCount { get; set; }

        /// <summary>
        /// 参与率
        /// </summary>
        public decimal Participation { get; set; }

        /// <summary>
        /// 参与人数
        /// </summary>
        public int ParticipationCount { get; set; }

        /// <summary>
        /// 班级学生人数
        /// </summary>
        public int StudentCount { get; set; }

        /// <summary>
        /// 阶段完成情况
        /// </summary>
        public List<GetModelingSynthesizeAnalyseStageOutput> StageInfo { get; set; } = new List<GetModelingSynthesizeAnalyseStageOutput>();

        /// <summary>
        /// 阶段任务平均分
        /// </summary>
        public List<GetModelingSynthesizeAnalyseScoreOutput> StageTaskAvgScore { get; set; } = new List<GetModelingSynthesizeAnalyseScoreOutput>();
    }

    /// <summary>
    /// 获取建模综合分析阶段输出
    /// </summary>
    public class GetModelingSynthesizeAnalyseStageOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 任务数量
        /// </summary>
        public int TaskCount { get; set; }

        /// <summary>
        /// 完成数量
        /// </summary>
        public decimal FinishCount { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal Finish { get; set; }
    }

    /// <summary>
    /// 获取建模综合分析平均分输出
    /// </summary>
    public class GetModelingSynthesizeAnalyseScoreOutput
    {
        /// <summary>
        /// 阶段任务名称
        /// </summary>
        public string? StageTaskName { get; set; }

        /// <summary>
        /// 平均分
        /// </summary>
        public decimal? AvgScore { get; set; }
    }
}
