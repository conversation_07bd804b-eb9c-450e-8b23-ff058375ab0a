using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;
using Uwoo.Core.BaseProvider;
using Uwoo.Model.CustomException;
using Coldairarrow.Util;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.System.IRepositories.AI;
using Uwoo.Core.Configuration;
using UwooAgent.Core.Utilities;
using UwooAgent.Core.CacheManager.IBusinessCacheService;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 阅读理解智能体学生端服务实现
    /// </summary>
    public partial class ReadingComprehensionStudentService : ServiceBase<RC_StudentTaskRecord, IAgentStudentReadingComprehensionRepository>, IReadingComprehensionStudentService, IDependency
    {
        private IAgentCommonCacheService _agentCommonCacheService;
        private IAgentCommonService _agentCommonService;

        public ReadingComprehensionStudentService(IAgentCommonCacheService agentCommonCacheService, IAgentCommonService agentCommonService)
        {
            _agentCommonCacheService = agentCommonCacheService;
            _agentCommonService = agentCommonService;
        }
        //public ReadingComprehensionStudentService(ISqlSugarClient db, IConfiguration configuration)
        //{
        //    _db = db;
        //    _configuration = configuration;
        //}

        /// <summary>
        /// 获取学生的阅读理解项目列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentReadingTaskListOutput> GetStudentReadingTaskList(StudentReadingTaskListInput input)
        {
            // 获取学生可见的项目（通过AI_AgentTaskPublish表关联）
            var query = DBSqlSugar.Queryable<AI_AgentTask, AI_AgentTaskPublish>((p, pub) => new JoinQueryInfos(
                JoinType.Inner, p.Id == pub.AgentTaskId
            ))
            .Where((p, pub) => p.IsDeleted == false && pub.IsDeleted == false && pub.PublishType == 1);

            // 这里需要根据学生ID获取班级ID，然后筛选项目
            var classId = DBSqlSugar.Ado.GetString(@"select top 1 ClassId from Exam_Student(nolock) where Id= @studentId", new { studentId = input.StudentId });
            query = query.Where((p, pub) => pub.PublishBusinessId == classId);

            // 总数量
            int totalCount = await query.CountAsync();

            // 分页查询
            var projects = await query
                .OrderByDescending((p, pub) => pub.CreateTime)
                .Select((p, pub) => new
                {
                    Project = p,
                    PublishTime = pub.CreateTime
                })
                .Skip((input.PageIndex - 1) * input.PageSize)
                .Take(input.PageSize)
                .ToListAsync();

            List<StudentReadingTaskListItemOutput> taskList = new List<StudentReadingTaskListItemOutput>();

            foreach (var item in projects)
            {
                // 获取项目统计信息
                var projectStats = await GetStudentProjectStats(item.Project.Id, input.StudentId);

                StudentReadingTaskListItemOutput projectItem = new StudentReadingTaskListItemOutput
                {
                    TaskId = item.Project.Id,
                    Name = item.Project.Name,
                    Introduce = item.Project.Introduce,
                    TaskTypeName = await GetProjectTaskTypesName(item.Project.Id),
                    TaskStatus = projectStats.ProjectStatus,
                    TaskStatusName = GetTaskStatusName(projectStats.ProjectStatus),
                    //PublishTime = item.Project.PublishTime,
                    StageCount = projectStats.StageCount,
                    TotalTaskCount = projectStats.TotalTaskCount,
                    CompletedTaskCount = projectStats.CompletedTaskCount,
                    Progress = projectStats.Progress,
                    Score = projectStats.AverageScore,
                    IsStandard = projectStats.IsStandard,
                    AIGrade = projectStats.AIGrade
                };

                taskList.Add(projectItem);
            }

            return new StudentReadingTaskListOutput
            {
                TaskList = taskList,
                TotalCount = totalCount,
                PageIndex = input.PageIndex,
                PageSize = input.PageSize
            };
        }

        /// <summary>
        /// 获取阅读理解项目详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentReadingTaskDetailsOutput> GetReadingTaskDetails(StudentReadingTaskDetailsInput input)
        {
            // 获取项目基本信息
            AI_AgentTask project = await DBSqlSugar.Queryable<AI_AgentTask>()
                .Where(p => p.Id == input.TaskId && p.IsDeleted == false)
                .FirstAsync();

            if (project == null)
            {
                throw new BusException("项目不存在或未发布！");
            }

            // 获取学生项目统计信息
            var projectStats = await GetStudentProjectStats(input.TaskId, input.StudentId);

            StudentReadingTaskDetailsOutput output = new StudentReadingTaskDetailsOutput
            {
                TaskId = project.Id,
                Name = project.Name,
                Introduce = project.Introduce,
                ProjectLogo = project.TaskLogo,
                TaskStatus = projectStats.ProjectStatus,
                Score = projectStats.AverageScore,
                IsStandard = projectStats.IsStandard,
                AIGrade = projectStats.AIGrade,
                StageCount = projectStats.StageCount,
                TotalTaskCount = projectStats.TotalTaskCount,
                CompletedTaskCount = projectStats.CompletedTaskCount,
                Progress = projectStats.Progress,
                TaskTypeName = await GetProjectTaskTypesName(project.Id),
                //PublishTime = project.PublishTime,
                CreateTime = project.CreateTime
            };

            // 加载项目阶段和任务详情
            await LoadStudentProjectStages(output, input.StudentId);

            return output;
        }

        /// <summary>
        /// 加载学生项目阶段和任务
        /// </summary>
        /// <param name="output"></param>
        /// <param name="studentId"></param>
        /// <returns></returns>
        private async Task LoadStudentProjectStages(StudentReadingTaskDetailsOutput output, string studentId)
        {
            // 获取项目阶段
            var stages = await DBSqlSugar.Queryable<RC_ReadingProjectStage>()
                .Where(p => p.ReadingProjectId == output.TaskId && p.IsDeleted == false)
                .OrderBy(p => p.StageOrder)
                .ToListAsync();

            foreach (var stage in stages)
            {
                var stageOutput = new StudentProjectStageOutput
                {
                    StageId = stage.Id,
                    Name = stage.StageName,
                    Describe = stage.StageDescribe,
                    StageOrder = stage.StageOrder
                };

                // 加载阶段任务
                await LoadStudentStageTasks(stageOutput, studentId);

                output.Stages.Add(stageOutput);
            }

            // 计算阶段和任务的锁定状态（参考项目化实践逻辑）
            CalculateStageAndTaskLockStatus(output.Stages);
        }

        /// <summary>
        /// 加载学生阶段任务
        /// </summary>
        /// <param name="stageOutput"></param>
        /// <param name="studentId"></param>
        /// <returns></returns>
        private async Task LoadStudentStageTasks(StudentProjectStageOutput stageOutput, string studentId)
        {
            // 获取阶段任务
            var tasks = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                .Where(p => p.ReadingProjectStageId == stageOutput.StageId && p.IsDeleted == false)
                .OrderBy(p => p.TaskOrder)
                .ToListAsync();

            foreach (var task in tasks)
            {
                // 获取学生任务记录
                var studentRecord = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                    .Where(p => p.ReadingProjectStageTaskId == task.Id && p.StudentId == studentId && p.IsDeleted == false)
                    .OrderByDescending(x => x.CreateTime)
                    .FirstAsync();
                if (task.Id == "1963113619428622336")
                {

                }
                var taskOutput = new StudentProjectStageTaskOutput
                {
                    Id = task.Id,
                    TaskType = task.TaskType,
                    Name = task.TaskName,
                    Target = task.Target,
                    ScoreStandard = task.ScoreStandard,
                    Demand = task.Demand,
                    Scope = task.Scope,
                    RoleSetting = task.RoleSetting,
                    ToneId = task.ToneId,
                    RoleName = task.RoleName,
                    Prologue = task.Prologue,
                    Order = task.TaskOrder,
                    State = studentRecord?.TaskStatus ?? 1,
                    CompleteTime = studentRecord?.CreateTime,
                    Score = studentRecord?.Score ?? 0,
                    IsStandard = studentRecord?.IsStandard ?? false,
                    AIGrade = studentRecord?.AIGrade,

                    // 组间任务设置
                    GroupIsSubmit = task.GroupIsSubmit,
                    GroupIsAssessment = task.GroupIsAssessment,
                    GroupAssessmentScore = task.GroupAssessmentScore,
                    TaskIsWatchVideo = task.TaskIsWatchAllVideos,
                    TaskIsReadAllDocuments = task.TaskIsReadAllDocuments,
                    // 任务点设置
                    TaskIsSubmit = task.TaskIsSubmit,
                    TaskIsAssessment = task.TaskIsAssessment,
                    TaskAssessmentScore = task.TaskAssessmentScore,

                    // 任务状态相关
                    TaskSubmitId = studentRecord?.Id
                };

                // 计算提交状态
                taskOutput.IsSubmitNoStandard = studentRecord != null && studentRecord.IsStandard;

                // 加载任务特殊配置
                await LoadStudentTaskConfig(taskOutput, task, studentId);

                stageOutput.Tasks.Add(taskOutput);
            }
        }

        /// <summary>
        /// 加载学生任务特殊配置
        /// </summary>
        /// <param name="taskOutput"></param>
        /// <param name="task"></param>
        /// <returns></returns>
        private async Task LoadStudentTaskConfig(StudentProjectStageTaskOutput taskOutput, RC_ReadingProjectStageTask task, string studentId)
        {
            var config = new StudentTaskConfigOutput();

            switch (task.TaskType)
            {
                case 4: // 视频任务
                    config.GroupTotalWatchDurationLimit = task.GroupTotalWatchDurationLimit;
                    config.GroupIsWatchAllVideos = task.GroupIsWatchAllVideos;
                    config.TaskTotalWatchDurationLimit = task.TaskTotalWatchDurationLimit;
                    config.TaskIsWatchAllVideos = task.TaskIsWatchAllVideos;
                    await LoadStudentVideoResources(config, task.Id, taskOutput.Id, studentId);
                    break;
                case 5: // 文档任务
                    config.GroupIsReadAllDocuments = task.GroupIsReadAllDocuments;
                    config.TaskIsReadAllDocuments = task.TaskIsReadAllDocuments;
                    await LoadStudentDocumentResources(config, task.Id, taskOutput.Id, studentId);
                    break;
                case 7: // 选词填空任务
                    config.QuestionContent = task.QuestionContent;
                    config.CorrectAnswers = !string.IsNullOrEmpty(task.CorrectAnswers)
                        ? JsonConvert.DeserializeObject<List<string>>(task.CorrectAnswers) ?? new List<string>()
                        : new List<string>();
                    config.DistractorWords = !string.IsNullOrEmpty(task.DistractorWords)
                        ? JsonConvert.DeserializeObject<List<string>>(task.DistractorWords) ?? new List<string>()
                        : new List<string>();
                    config.CustomBackgroundImage = task.CustomBackgroundImage;
                    break;
            }

            taskOutput.TaskConfig = config;
        }

        /// <summary>
        /// 加载学生视频资源
        /// </summary>
        /// <param name="config"></param>
        /// <param name="taskId"></param>
        /// <param name="studentTaskId"></param>
        /// <returns></returns>
        private async Task LoadStudentVideoResources(StudentTaskConfigOutput config, string taskId, string studentTaskId, string studentId)
        {
            var videos = await DBSqlSugar.Queryable<RC_VideoResource>()
                .Where(p => p.ReadingProjectStageTaskId == taskId && p.IsDeleted == false)
                .OrderBy(p => p.VideoOrder)
                .ToListAsync();

            foreach (var video in videos)
            {
                // 获取学生观看记录
                var watchRecord = await DBSqlSugar.Queryable<RC_StudentVideoWatchRecord>()
                    .Where(p => p.ReadingProjectStageTaskId == taskId && p.StudentId == studentId && p.VideoId == video.Id && p.IsDeleted == false)
                    .FirstAsync();

                config.VideoResources.Add(new StudentVideoResourceOutput
                {
                    Id = video.Id,
                    VideoTitle = video.VideoTitle,
                    VideoDescription = video.VideoDescription,
                    VideoUrl = video.VideoUrl,
                    Duration = video.Duration,
                    VideoOrder = video.VideoOrder,
                    HasWatched = watchRecord != null,
                    TotalWatchDuration = watchRecord?.TotalWatchDuration ?? 0
                });
            }
        }

        /// <summary>
        /// 加载学生文档资源
        /// </summary>
        /// <param name="config"></param>
        /// <param name="taskId"></param>
        /// <param name="studentTaskId"></param>
        /// <returns></returns>
        private async Task LoadStudentDocumentResources(StudentTaskConfigOutput config, string taskId, string studentTaskId, string studentId)
        {
            var documents = await DBSqlSugar.Queryable<RC_DocumentResource>()
                .Where(p => p.ReadingProjectStageTaskId == taskId && p.IsDeleted == false)
                .OrderBy(p => p.DocumentOrder)
                .ToListAsync();

            foreach (var document in documents)
            {
                // 获取学生阅读记录
                var readRecord = await DBSqlSugar.Queryable<RC_StudentDocumentReadRecord>()
                    .Where(p => p.ReadingProjectStageTaskId == taskId && p.StudentId == studentId && p.DocumentId == document.Id && p.IsDeleted == false)
                    .FirstAsync();

                config.DocumentResources.Add(new StudentDocumentResourceOutput
                {
                    Id = document.Id,
                    DocumentTitle = document.DocumentTitle,
                    DocumentDescription = document.DocumentDescription,
                    DocumentUrl = document.DocumentUrl,
                    DocumentOrder = document.DocumentOrder,
                    HasRead = readRecord != null,
                    ReadTime = readRecord?.ReadTime
                });
            }
        }

        /// <summary>
        /// 获取任务类型名称
        /// </summary>
        /// <param name="taskType"></param>
        /// <returns></returns>
        private string GetTaskTypeName(int taskType)
        {
            return taskType switch
            {
                1 => "成果评估",
                2 => "情景对话",
                3 => "知识问答",
                4 => "视频任务",
                5 => "文档任务",
                6 => "思维导图",
                7 => "选词填空",
                _ => "未知类型"
            };
        }

        /// <summary>
        /// 获取任务状态名称
        /// </summary>
        /// <param name="taskStatus"></param>
        /// <returns></returns>
        private string GetTaskStatusName(int taskStatus)
        {
            return taskStatus switch
            {
                1 => "未开始",
                2 => "进行中",
                3 => "已完成",
                _ => "未知状态"
            };
        }

        #region 视频任务相关方法

        /// <summary>
        /// 记录视频观看状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<VideoWatchStatusOutput> RecordVideoWatchStatus(VideoWatchStatusInput input)
        {
            try
            {
                // 检查是否已存在观看记录
                RC_StudentVideoWatchRecord existingRecord = await DBSqlSugar.Queryable<RC_StudentVideoWatchRecord>()
                    .Where(p => p.StudentId == input.StudentId
                        && p.ReadingProjectStageTaskId == input.TaskId
                        && p.VideoId == input.VideoId
                        && p.IsDeleted == false)
                    .FirstAsync();

                if (existingRecord != null)
                {
                    // 更新最后观看时间
                    existingRecord.LastWatchTime = DateTime.Now;
                    existingRecord.ModifyTime = DateTime.Now;
                    existingRecord.Modifier = input.StudentId;
                    await DBSqlSugar.Updateable(existingRecord).ExecuteCommandAsync();

                    return new VideoWatchStatusOutput
                    {
                        Success = true,
                        Message = "观看状态更新成功",
                        HasWatched = true
                    };
                }
                else
                {
                    // 创建新的观看记录
                    RC_StudentVideoWatchRecord newRecord = new RC_StudentVideoWatchRecord
                    {
                        Id = IdHelper.GetId(),
                        StudentId = input.StudentId,
                        ReadingProjectStageTaskId = input.TaskId,
                        VideoId = input.VideoId,
                        TotalWatchDuration = 0,
                        HasWatched = true,
                        FirstWatchTime = DateTime.Now,
                        LastWatchTime = DateTime.Now,
                        CreateTime = DateTime.Now,
                        Creator = input.StudentId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.StudentId,
                        IsDeleted = false
                    };

                    await DBSqlSugar.Insertable(newRecord).ExecuteCommandAsync();

                    return new VideoWatchStatusOutput
                    {
                        Success = true,
                        Message = "观看状态记录成功",
                        HasWatched = false
                    };
                }
            }
            catch (Exception ex)
            {
                return new VideoWatchStatusOutput
                {
                    Success = false,
                    Message = $"记录观看状态失败：{ex.Message}",
                    HasWatched = false
                };
            }
        }

        /// <summary>
        /// 记录视频观看时长
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<VideoWatchDurationOutput> RecordVideoWatchDuration(VideoWatchDurationInput input)
        {
            try
            {
                // 获取任务信息
                RC_ReadingProjectStageTask taskInfo = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                    .Where(p => p.Id == input.TaskId && p.IsDeleted == false)
                    .FirstAsync();

                if (taskInfo == null)
                {
                    throw new BusException("任务不存在！");
                }

                // 验证任务类型
                if (taskInfo.TaskType != 4)
                {
                    throw new BusException("该任务不是视频任务！");
                }

                var projectId = DBSqlSugar.Ado.GetString(@"
select ps.ReadingProjectId from RC_ReadingProjectStageTask(nolock) pst
join RC_ReadingProjectStage(nolock) ps on ps.Id= pst.ReadingProjectStageId
where pst.Id=@taskId", new { taskId = input.TaskId });
                if (string.IsNullOrEmpty(projectId))
                {
                    throw new BusException("该任务找不到关联的项目Id", 801);
                }

                // 获取或创建观看记录
                RC_StudentVideoWatchRecord watchRecord = await DBSqlSugar.Queryable<RC_StudentVideoWatchRecord>()
                    .Where(p => p.StudentId == input.StudentId
                        && p.ReadingProjectStageTaskId == input.TaskId
                        && p.VideoId == input.VideoId
                        && p.IsDeleted == false)
                    .FirstAsync();

                if (watchRecord == null)
                {
                    // 如果没有观看记录，创建一个
                    watchRecord = new RC_StudentVideoWatchRecord
                    {
                        Id = IdHelper.GetId(),
                        StudentId = input.StudentId,
                        ReadingProjectStageTaskId = input.TaskId,
                        VideoId = input.VideoId,
                        TotalWatchDuration = input.WatchDuration,
                        HasWatched = true,
                        FirstWatchTime = DateTime.Now,
                        LastWatchTime = DateTime.Now,
                        CreateTime = DateTime.Now,
                        Creator = input.StudentId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.StudentId,
                        IsDeleted = false
                    };

                    await DBSqlSugar.Insertable(watchRecord).ExecuteCommandAsync();
                }
                else
                {
                    // 累加观看时长
                    watchRecord.TotalWatchDuration += input.WatchDuration;
                    watchRecord.LastWatchTime = DateTime.Now;
                    watchRecord.ModifyTime = DateTime.Now;
                    watchRecord.Modifier = input.StudentId;

                    await DBSqlSugar.Updateable(watchRecord).ExecuteCommandAsync();
                }

                // 计算完成状态
                bool taskCompleted = await CheckVideoTaskCompletion(input.TaskId, input.StudentId, taskInfo);

                // 更新学生任务记录状态
                if (taskCompleted)
                {
                    await UpdateStudentTaskStatus(input.TaskId, input.StudentId, 3, projectId); // 已完成
                }
                else
                {
                    await UpdateStudentTaskStatus(input.TaskId, input.StudentId, 2, projectId); // 进行中
                }

                // 使用任务点解锁条件的观看时长要求
                int? requiredDuration = taskInfo.TaskTotalWatchDurationLimit;
                bool isCompleted = false;
                decimal progress = 0;

                if (requiredDuration.HasValue && requiredDuration.Value > 0)
                {
                    // 获取总观看时长
                    int totalWatchDuration = await DBSqlSugar.Queryable<RC_StudentVideoWatchRecord>()
                        .Where(p => p.StudentId == input.StudentId && p.ReadingProjectStageTaskId == input.TaskId && p.IsDeleted == false)
                        .SumAsync(p => p.TotalWatchDuration);

                    progress = Math.Min(100, (decimal)totalWatchDuration / (requiredDuration.Value * 60) * 100);
                    isCompleted = totalWatchDuration >= (requiredDuration.Value * 60);
                }
                else
                {
                    // 如果没有时长要求，只要观看就算完成
                    isCompleted = watchRecord.HasWatched;
                    progress = isCompleted ? 100 : 0;
                }

                return new VideoWatchDurationOutput
                {
                    Success = true,
                    Message = "观看时长记录成功",
                    TotalWatchDuration = watchRecord.TotalWatchDuration,
                    RequiredDuration = requiredDuration.HasValue ? requiredDuration.Value * 60 : null,
                    IsCompleted = isCompleted,
                    Progress = Math.Round(progress, 1),
                    TaskCompleted = taskCompleted
                };
            }
            catch (Exception ex)
            {
                return new VideoWatchDurationOutput
                {
                    Success = false,
                    Message = $"记录观看时长失败：{ex.Message}",
                    TotalWatchDuration = 0,
                    RequiredDuration = null,
                    IsCompleted = false,
                    Progress = 0,
                    TaskCompleted = false
                };
            }
        }

        /// <summary>
        /// 检查视频任务完成状态
        /// </summary>
        /// <param name="taskId"></param>
        /// <param name="studentId"></param>
        /// <param name="taskInfo"></param>
        /// <returns></returns>
        private async Task<bool> CheckVideoTaskCompletion(string taskId, string studentId, RC_ReadingProjectStageTask taskInfo)
        {
            List<RC_StudentVideoWatchRecord> watchRecords = await DBSqlSugar.Queryable<RC_StudentVideoWatchRecord>()
                .Where(p => p.StudentId == studentId && p.ReadingProjectStageTaskId == taskId && p.IsDeleted == false)
                .ToListAsync();

            // 检查观看所有视频的要求（使用任务点解锁条件）
            if (taskInfo.TaskIsWatchAllVideos == true)
            {
                int totalVideos = await DBSqlSugar.Queryable<RC_VideoResource>()
                    .Where(p => p.ReadingProjectStageTaskId == taskId && p.IsDeleted == false)
                    .CountAsync();

                int watchedVideos = watchRecords.Count(w => w.HasWatched);
                if (watchedVideos < totalVideos)
                {
                    return false;
                }
            }

            // 检查总观看时长要求（使用任务点解锁条件）
            if (taskInfo.TaskTotalWatchDurationLimit.HasValue && taskInfo.TaskTotalWatchDurationLimit.Value > 0)
            {
                int totalWatchDuration = watchRecords.Sum(w => w.TotalWatchDuration);
                if (totalWatchDuration < taskInfo.TaskTotalWatchDurationLimit.Value * 60)
                {
                    return false;
                }
            }

            return true;
        }

        #endregion

        #region 文档任务相关方法

        /// <summary>
        /// 记录文档阅读状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<DocumentReadStatusOutput> RecordDocumentReadStatus(DocumentReadStatusInput input)
        {
            try
            {
                // 获取任务信息（修正：从 RC_ReadingProjectStageTask 表获取配置）
                RC_ReadingProjectStageTask taskInfo = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                    .Where(p => p.Id == input.TaskId && p.IsDeleted == false)
                    .FirstAsync();

                if (taskInfo == null)
                {
                    throw new BusException("任务不存在！");
                }

                // 验证任务类型
                if (taskInfo.TaskType != 5)
                {
                    throw new BusException("该任务不是文档任务！");
                }

                var projectId = DBSqlSugar.Ado.GetString(@"
select ps.ReadingProjectId from RC_ReadingProjectStageTask(nolock) pst
join RC_ReadingProjectStage(nolock) ps on ps.Id= pst.ReadingProjectStageId
where pst.Id=@taskId", new { taskId = input.TaskId });
                if (string.IsNullOrEmpty(projectId))
                {
                    throw new BusException("该任务找不到关联的项目Id", 801);
                }

                // 记录文档阅读状态
                foreach (var documentId in input.ReadDocumentIds)
                {
                    // 检查是否已存在阅读记录
                    RC_StudentDocumentReadRecord existingRecord = await DBSqlSugar.Queryable<RC_StudentDocumentReadRecord>()
                        .Where(p => p.StudentId == input.StudentId
                            && p.ReadingProjectStageTaskId == input.TaskId
                            && p.DocumentId == documentId
                            && p.IsDeleted == false)
                        .FirstAsync();

                    if (existingRecord == null)
                    {
                        // 创建新的阅读记录
                        RC_StudentDocumentReadRecord newRecord = new RC_StudentDocumentReadRecord
                        {
                            Id = IdHelper.GetId(),
                            StudentId = input.StudentId,
                            ReadingProjectStageTaskId = input.TaskId,
                            DocumentId = documentId,
                            HasRead = true,
                            ReadTime = DateTime.Now,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false
                        };

                        await DBSqlSugar.Insertable(newRecord).ExecuteCommandAsync();
                    }
                }

                // 统计阅读情况
                List<RC_StudentDocumentReadRecord> readRecords = await DBSqlSugar.Queryable<RC_StudentDocumentReadRecord>()
                    .Where(p => p.StudentId == input.StudentId
                        && p.ReadingProjectStageTaskId == input.TaskId
                        && p.IsDeleted == false)
                    .ToListAsync();

                int totalDocuments = await DBSqlSugar.Queryable<RC_DocumentResource>()
                    .Where(p => p.ReadingProjectStageTaskId == input.TaskId && p.IsDeleted == false)
                    .CountAsync();

                int readDocuments = readRecords.Count;
                decimal progress = totalDocuments > 0 ? (decimal)readDocuments / totalDocuments * 100 : 0;

                // 判断是否完成任务（修正：使用正确的字段名和逻辑）
                bool isCompleted = false;

                // 使用任务点解锁条件来判断任务是否完成
                if (taskInfo.TaskIsReadAllDocuments == true)
                {
                    // 需要阅读所有文档才算完成
                    isCompleted = readDocuments >= totalDocuments;
                }
                else
                {
                    // 只要阅读了任意文档就算完成
                    isCompleted = readDocuments > 0;
                }

                // 更新学生任务记录状态
                if (isCompleted)
                {
                    await UpdateStudentTaskStatus(input.TaskId, input.StudentId, 3, projectId); // 已完成
                }
                else
                {
                    await UpdateStudentTaskStatus(input.TaskId, input.StudentId, 2, projectId); // 进行中
                }

                // 获取未读文档列表
                List<string> allDocumentIds = await DBSqlSugar.Queryable<RC_DocumentResource>()
                    .Where(p => p.ReadingProjectStageTaskId == input.TaskId && p.IsDeleted == false)
                    .Select(p => p.Id)
                    .ToListAsync();

                List<string> readDocumentIds = readRecords.Select(r => r.DocumentId).ToList();
                List<string> unreadDocuments = allDocumentIds.Except(readDocumentIds).ToList();

                return new DocumentReadStatusOutput
                {
                    Success = true,
                    Message = "文档阅读状态记录成功",
                    IsCompleted = isCompleted,
                    TotalDocuments = totalDocuments,
                    ReadDocuments = readDocuments,
                    Progress = Math.Round(progress, 1),
                    UnreadDocuments = unreadDocuments
                };
            }
            catch (Exception ex)
            {
                return new DocumentReadStatusOutput
                {
                    Success = false,
                    Message = $"记录文档阅读状态失败：{ex.Message}",
                    IsCompleted = false,
                    TotalDocuments = 0,
                    ReadDocuments = 0,
                    Progress = 0,
                    UnreadDocuments = new List<string>()
                };
            }
        }

        #endregion

        #region 思维导图任务相关方法

        /// <summary>
        /// 提交思维导图任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<MindMapSubmitOutput> SubmitMindMap(MindMapSubmitInput input)
        {
            try
            {
                // 检查是否已有达标提交记录
                RC_StudentTaskRecord existingRecord = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                    .Where(p => p.ReadingProjectStageTaskId == input.TaskId
                        && p.StudentId == input.StudentId
                        && p.IsStandard == true
                        && p.IsDeleted == false)
                    .FirstAsync();

                if (existingRecord != null)
                {
                    throw new BusException("已完成，无法重复提交!");
                }

                // 获取任务信息
                var taskInfo = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask, RC_ReadingProjectStage, AI_AgentTask>((t, s, p) => new JoinQueryInfos(
                    JoinType.Inner, t.ReadingProjectStageId == s.Id,
                    JoinType.Inner, s.ReadingProjectId == p.Id
                ))
                .Where((t, s, p) => t.Id == input.TaskId && t.IsDeleted == false && s.IsDeleted == false && p.IsDeleted == false)
                .Select((t, s, p) => new
                {
                    Task = t,
                    Stage = s,
                    Project = p
                })
                .FirstAsync();

                if (taskInfo == null)
                {
                    throw new BusException("任务信息不存在！");
                }
                // 获取学生端阅读理解阶段任务key（参考项目化实践）
                string studentProjectTaskKey = AIAgentKeys.GetStudentProjecKey(taskInfo.Project.AgentId, taskInfo.Task.Id, input.StudentId);
                if (string.IsNullOrEmpty(studentProjectTaskKey))
                {
                    throw new BusException("无法获取阅读理解阶段任务Key!");
                }
                // 获取思维导图任务配置
                RC_MindMapTaskConfig mindMapConfig = await DBSqlSugar.Queryable<RC_MindMapTaskConfig>()
                    .Where(p => p.ReadingProjectStageTaskId == input.TaskId && p.IsDeleted == false)
                    .FirstAsync();

                var modelkey = DBSqlSugar.Ado.GetString($@" select model.ModelKey from  AI_AgentTask project WITH ( NOLOCK ) 
         	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON project.AgentId= agentBase.Id AND agentBase.IsDeleted= 0
            INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
            where       project.IsDeleted = 0 and project.Id='{taskInfo.Project.Id}'");

                // 调用豆包AI评估
                var assessmentResult = await ProcessMindMapAssessment(taskInfo.Task, input, modelkey);

                // 验证是否达标
                bool taskPass = taskInfo.Task.TaskIsAssessment ? assessmentResult.Score >= taskInfo.Task.TaskAssessmentScore : true;
                bool groupPass = taskInfo.Task.GroupIsAssessment ? assessmentResult.Score >= taskInfo.Task.GroupAssessmentScore : true;
                bool isStandard = taskPass && groupPass;

                // 计算任务状态：如果达标则为已完成(3)，否则为进行中(2)
                int taskStatus = isStandard ? 3 : 2;

                // 计算项目状态：需要查询该学生在整个项目中的完成情况
                int projectStatus = await CalculateProjectStatus(taskInfo.Project.Id, input.StudentId, input.TaskId, isStandard);

                // 创建提交记录
                RC_StudentTaskRecord taskRecord = new RC_StudentTaskRecord
                {
                    Id = IdHelper.GetId(),
                    StudentId = input.StudentId,
                    ReadingProjectId = taskInfo.Project.Id,
                    ReadingProjectStageTaskId = input.TaskId,
                    TaskStatus = taskStatus,
                    ProjectStatus = projectStatus,
                    SubmitContent = input.MindMapContent,
                    Score = assessmentResult.Score,
                    IsStandard = isStandard,
                    AssessmentResult = assessmentResult.AssessmentResult,
                    AIGrade = assessmentResult.AIGrade,
                    MatchedQuestions = assessmentResult.MatchedQuestions?.Count > 0
                        ? JsonConvert.SerializeObject(assessmentResult.MatchedQuestions)
                        : null,
                    CreateTime = DateTime.Now,
                    Creator = input.StudentId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.StudentId,
                    IsDeleted = false
                };

                await DBSqlSugar.Insertable(taskRecord).ExecuteCommandAsync();

                // 保存对话记录（参考项目化实践）
                AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                {
                    AskText = input.MindMapContent,
                };
                AI_DialogueContentRecord aI_DialogueContentRecord = new AI_DialogueContentRecord()
                {
                    Id = IdHelper.GetId(),
                    Key = studentProjectTaskKey,
                    Ask = JsonConvert.SerializeObject(aIDialogueASKDto),
                    Answer = assessmentResult.AssessmentResult,
                    CreateTime = DateTime.Now,
                    MessageType = 2,
                    BusinessId = input.TaskId,
                    IsDeleted = false
                };
                await DBSqlSugar.Insertable(aI_DialogueContentRecord).ExecuteCommandAsync();

                return new MindMapSubmitOutput
                {
                    IsStandard = isStandard,
                    AssessmentResult = assessmentResult.AssessmentResult,
                    Score = assessmentResult.Score,
                    SubmitId = taskRecord.Id,
                    AIGrade = assessmentResult.AIGrade
                    // 不返回 Dimensions
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        #endregion

        /// <summary>
        /// 更新学生任务状态
        /// </summary>
        /// <param name="taskId"></param>
        /// <param name="studentId"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        private async Task UpdateStudentTaskStatus(string taskId, string studentId, int status, string projectId)
        {
            RC_StudentTaskRecord studentRecord = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                .Where(p => p.ReadingProjectStageTaskId == taskId && p.StudentId == studentId && p.IsDeleted == false)
                .FirstAsync();

            if (studentRecord == null)
            {
                // 创建新记录
                studentRecord = new RC_StudentTaskRecord
                {
                    Id = IdHelper.GetId(),
                    StudentId = studentId,
                    ReadingProjectId = projectId,
                    ReadingProjectStageTaskId = taskId,
                    TaskStatus = status,
                    Score = 0,
                    IsStandard = false,
                    CreateTime = DateTime.Now,
                    Creator = studentId,
                    ModifyTime = DateTime.Now,
                    Modifier = studentId,
                    IsDeleted = false
                };

                await DBSqlSugar.Insertable(studentRecord).ExecuteCommandAsync();
            }
            else
            {
                // 更新状态
                studentRecord.TaskStatus = status;
                studentRecord.ModifyTime = DateTime.Now;
                studentRecord.Modifier = studentId;

                await DBSqlSugar.Updateable(studentRecord).ExecuteCommandAsync();
            }
        }

        #region 选词填空任务相关方法

        /// <summary>
        /// 提交选词填空任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<WordFillSubmitOutput> SubmitWordFillTask(WordFillSubmitInput input)
        {
            try
            {
                // 检查是否已有达标提交记录
                RC_StudentTaskRecord existingRecord = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                    .Where(p => p.ReadingProjectStageTaskId == input.TaskId
                        && p.StudentId == input.StudentId
                        && p.IsStandard == true
                        && p.IsDeleted == false)
                    .FirstAsync();

                if (existingRecord != null)
                {
                    throw new BusException("已完成，无法重复提交!");
                }

                // 获取任务信息
                var taskInfo = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask, RC_ReadingProjectStage, AI_AgentTask>((t, s, p) => new JoinQueryInfos(
                    JoinType.Inner, t.ReadingProjectStageId == s.Id,
                    JoinType.Inner, s.ReadingProjectId == p.Id
                ))
                .Where((t, s, p) => t.Id == input.TaskId && t.IsDeleted == false && s.IsDeleted == false && p.IsDeleted == false)
                .Select((t, s, p) => new
                {
                    Task = t,
                    Stage = s,
                    Project = p
                })
                .FirstAsync();

                if (taskInfo == null)
                {
                    throw new BusException("任务信息不存在！");
                }

                // 获取学生端阅读理解阶段任务key（参考项目化实践）
                string studentProjectTaskKey = AIAgentKeys.GetStudentProjecKey(taskInfo.Project.AgentId, taskInfo.Task.Id, input.StudentId);
                if (string.IsNullOrEmpty(studentProjectTaskKey))
                {
                    throw new BusException("无法获取阅读理解阶段任务Key!");
                }

                // 保存操作记录
                foreach (var operation in input.OperationRecords)
                {
                    RC_StudentWordFillOperationRecord operationRecord = new RC_StudentWordFillOperationRecord
                    {
                        Id = IdHelper.GetId(),
                        StudentId = input.StudentId,
                        ReadingProjectStageTaskId = input.TaskId,
                        OperationOrder = operation.OperationOrder,
                        QuestionIndex = operation.QuestionIndex,
                        SelectedWord = operation.SelectedWord,
                        //BlankId = operation.BlankId,
                        //   OperationType = operation.OperationType,
                        OperationTime = DateTimeOffset.FromUnixTimeMilliseconds(operation.OperationTimestamp).DateTime,
                        CreateTime = DateTime.Now,
                        Creator = input.StudentId,
                        IsDeleted = false
                    };
                    await DBSqlSugar.Insertable(operationRecord).ExecuteCommandAsync();
                }

                var modelkey = DBSqlSugar.Ado.GetString($@" select model.ModelKey from  AI_AgentTask project WITH ( NOLOCK ) 
         	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON project.AgentId= agentBase.Id AND agentBase.IsDeleted= 0
            INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
            where       project.IsDeleted = 0 and project.Id='{taskInfo.Project.Id}'");
                // 调用AI评估
                var assessmentResult = await ProcessWordFillAssessment(taskInfo.Task, input, modelkey);

                // 验证是否达标
                bool taskPass = taskInfo.Task.TaskIsAssessment ? assessmentResult.Score >= taskInfo.Task.TaskAssessmentScore : true;
                bool groupPass = taskInfo.Task.GroupIsAssessment ? assessmentResult.Score >= taskInfo.Task.GroupAssessmentScore : true;
                bool isStandard = taskPass && groupPass;

                // 如果答案不正确或未达标，也保存记录但返回未达标状态
                if (!assessmentResult.AllCorrect || !isStandard)
                {
                    // 计算任务状态：未达标为进行中(2)
                    int taskStatus = 2;
                    int projectStatus = await CalculateProjectStatus(taskInfo.Project.Id, input.StudentId, input.TaskId, false);

                    // 保存提交记录
                    RC_StudentTaskRecord studentTaskRecord = new RC_StudentTaskRecord()
                    {
                        Id = IdHelper.GetId(),
                        StudentId = input.StudentId,
                        ReadingProjectStageTaskId = input.TaskId,
                        ReadingProjectId = taskInfo.Project.Id,
                        TaskStatus = taskStatus,
                        ProjectStatus = projectStatus,
                        SubmitContent = JsonConvert.SerializeObject(input.Answers),
                        Score = assessmentResult.Score,
                        IsStandard = false,
                        AssessmentResult = assessmentResult.AssessmentResult,
                        AIGrade = assessmentResult.AIGrade,
                        MatchedQuestions = JsonConvert.SerializeObject(assessmentResult.MatchedQuestions),
                        CreateTime = DateTime.Now,
                        Creator = input.StudentId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.StudentId,
                        IsDeleted = false
                    };

                    await DBSqlSugar.Insertable(studentTaskRecord).ExecuteCommandAsync();

                    return new WordFillSubmitOutput
                    {
                        IsStandard = false,
                        AssessmentResult = assessmentResult.AllCorrect ? "答案正确，但未达到评估标准，请继续努力！" : "别灰心呀，再试一下呢！"
                    };
                }

                // 计算任务状态：如果达标则为已完成(3)，否则为进行中(2)
                int taskStatusFinal = isStandard ? 3 : 2;

                // 计算项目状态：需要查询该学生在整个项目中的完成情况
                int projectStatusFinal = await CalculateProjectStatus(taskInfo.Project.Id, input.StudentId, input.TaskId, isStandard);

                // 保存提交记录
                RC_StudentTaskRecord studentTaskRecordFinal = new RC_StudentTaskRecord()
                {
                    Id = IdHelper.GetId(),
                    StudentId = input.StudentId,
                    ReadingProjectStageTaskId = input.TaskId,
                    ReadingProjectId = taskInfo.Project.Id,
                    TaskStatus = taskStatusFinal,
                    ProjectStatus = projectStatusFinal,
                    SubmitContent = JsonConvert.SerializeObject(input.Answers),
                    Score = assessmentResult.Score,
                    IsStandard = isStandard,
                    AssessmentResult = assessmentResult.AssessmentResult,
                    AIGrade = assessmentResult.AIGrade,
                    MatchedQuestions = JsonConvert.SerializeObject(assessmentResult.MatchedQuestions),
                    CreateTime = DateTime.Now,
                    Creator = input.StudentId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.StudentId,
                    IsDeleted = false
                };

                await DBSqlSugar.Insertable(studentTaskRecordFinal).ExecuteCommandAsync();

                AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                {
                    AskText = JsonConvert.SerializeObject(input.Answers),
                };
                AI_DialogueContentRecord aI_DialogueContentRecord = new AI_DialogueContentRecord()
                {
                    Id = IdHelper.GetId(),
                    Key = studentProjectTaskKey,
                    Ask = JsonConvert.SerializeObject(aIDialogueASKDto),
                    Answer = "---",
                    CreateTime = DateTime.Now,
                    MessageType = 2,
                    BusinessId = input.TaskId,
                    IsDeleted = false
                };
                await DBSqlSugar.Insertable(aI_DialogueContentRecord).ExecuteCommandAsync();

                // 返回结果
                return new WordFillSubmitOutput
                {
                    IsStandard = isStandard,
                    AssessmentResult = assessmentResult.AssessmentResult
                };
            }
            catch (Exception ex)
            {
                throw new BusException($"提交失败：{ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// 获取学生任务完成历史
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentTaskHistoryOutput> GetStudentTaskHistory(StudentTaskHistoryInput input)
        {
            // 使用项目化实践的表结构查询
            var query = DBSqlSugar.Queryable<RC_StudentTaskRecord, RC_ReadingProjectStageTask, RC_ReadingProjectStage, AI_AgentTask>((s, t, st, p) => new JoinQueryInfos(
                JoinType.Inner, s.ReadingProjectStageTaskId == t.Id,
                JoinType.Inner, t.ReadingProjectStageId == st.Id,
                JoinType.Inner, st.ReadingProjectId == p.Id
            ))
            .Where((s, t, st, p) => s.StudentId == input.StudentId && s.TaskStatus == 3 && s.IsDeleted == false && t.IsDeleted == false && st.IsDeleted == false && p.IsDeleted == false);

            // 任务类型筛选
            if (input.TaskType.HasValue)
            {
                query = query.Where((s, t, st, p) => t.TaskType == input.TaskType.Value);
            }

            // 总数量
            int totalCount = await query.CountAsync();

            // 分页查询
            var historyData = await query
                .OrderByDescending((s, t, st, p) => s.CreateTime)
                .Select((s, t, st, p) => new
                {
                    StudentRecord = s,
                    Task = t,
                    Stage = st,
                    Project = p
                })
                .Skip((input.PageIndex - 1) * input.PageSize)
                .Take(input.PageSize)
                .ToListAsync();

            List<StudentTaskHistoryItemOutput> taskHistoryList = historyData.Select(item => new StudentTaskHistoryItemOutput
            {
                TaskId = item.Task.Id,
                Name = item.Task.TaskName,
                TaskType = item.Task.TaskType,
                TaskTypeName = GetTaskTypeName(item.Task.TaskType),
                CompleteTime = item.StudentRecord.CreateTime,
                Score = item.StudentRecord.Score,
                IsStandard = item.StudentRecord.IsStandard,
                AIGrade = item.StudentRecord.AIGrade,
                AssessmentResult = item.StudentRecord.AssessmentResult,
                SubmitContent = item.StudentRecord.SubmitContent
            }).ToList();

            return new StudentTaskHistoryOutput
            {
                TaskHistoryList = taskHistoryList,
                TotalCount = totalCount,
                PageIndex = input.PageIndex,
                PageSize = input.PageSize
            };
        }

        /// <summary>
        /// 获取学生项目统计信息
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="studentId"></param>
        /// <returns></returns>
        private async Task<StudentProjectStatsOutput> GetStudentProjectStats(string projectId, string studentId)
        {
            // 获取项目下的所有任务
            var allTasks = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask, RC_ReadingProjectStage>((t, s) => new JoinQueryInfos(
                JoinType.Inner, t.ReadingProjectStageId == s.Id
            ))
            .Where((t, s) => s.ReadingProjectId == projectId && t.IsDeleted == false && s.IsDeleted == false)
            .Select((t, s) => new { TaskId = t.Id, StageOrder = s.StageOrder, TaskOrder = t.TaskOrder })
            .ToListAsync();

            // 在内存中排序
            allTasks = allTasks.OrderBy(x => x.StageOrder).ThenBy(x => x.TaskOrder).ToList();

            // 获取学生的任务完成记录
            var studentRecords = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                .Where(p => p.ReadingProjectId == projectId && p.StudentId == studentId && p.IsDeleted == false)
                .ToListAsync();

            // 统计信息
            int totalTaskCount = allTasks.Count;
            int completedTaskCount = studentRecords.Count(r => r.TaskStatus == 3); // 已完成
            int stageCount = allTasks.Select(t => t.StageOrder).Distinct().Count();

            // 计算项目状态
            int projectStatus = 1; // 未开始

            if (studentRecords.Any())
            {
                if (completedTaskCount == totalTaskCount)
                {
                    projectStatus = 3; // 已完成
                }
                else
                {
                    projectStatus = 2; // 进行中
                }
            }

            // 计算进度和平均分
            decimal progress = totalTaskCount > 0 ? (decimal)completedTaskCount / totalTaskCount * 100 : 0;
            decimal averageScore = studentRecords.Any() ? studentRecords.Average(r => r.Score) : 0;
            bool isStandard = studentRecords.Any() && studentRecords.All(r => r.IsStandard);
            string aiGrade = studentRecords.Any() ? studentRecords.FirstOrDefault()?.AIGrade : null;

            return new StudentProjectStatsOutput
            {
                ProjectStatus = projectStatus,
                StageCount = stageCount,
                TotalTaskCount = totalTaskCount,
                CompletedTaskCount = completedTaskCount,
                Progress = progress,
                AverageScore = averageScore,
                IsStandard = isStandard,
                AIGrade = aiGrade
            };
        }

        /// <summary>
        /// 获取项目包含的任务类型名称
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        private async Task<string> GetProjectTaskTypesName(string projectId)
        {
            var taskTypes = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask, RC_ReadingProjectStage>((t, s) => new JoinQueryInfos(
                JoinType.Inner, t.ReadingProjectStageId == s.Id
            ))
            .Where((t, s) => s.ReadingProjectId == projectId && t.IsDeleted == false && s.IsDeleted == false)
            .Select(t => t.TaskType)
            .Distinct()
            .ToListAsync();

            if (taskTypes == null || taskTypes.Count == 0)
            {
                return "无任务";
            }

            var typeNames = taskTypes.Select(GetTaskTypeName).ToList();
            return string.Join("、", typeNames);
        }

        /// <summary>
        /// 阅读理解情景对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task ReadingDialogue(ReadingDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                // 获取阅读理解项目阶段任务基础信息（完全参考项目化实践的SQL查询）
                string projectStageTaskSql = @"SELECT
                                                task.Id AS ProjectStageTaskId,
                                                stage.Id AS ProjectStageId,
                                                project.Id AS ProjectId,
                                                agentBase.Id AS AgentId,
                                                task.Demand AS Demand,
                                                task.TaskName AS Name,
                                                task.Target,
                                                model.Modelkey
                                            FROM
                                                RC_ReadingProjectStageTask task WITH ( NOLOCK )
                                                INNER JOIN RC_ReadingProjectStage stage WITH ( NOLOCK ) ON task.ReadingProjectStageId = stage.Id
                                                AND stage.IsDeleted = 0
                                                INNER JOIN AI_AgentTask project WITH ( NOLOCK ) ON stage.ReadingProjectId = project.Id
                                                AND project.IsDeleted = 0
                                                INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON project.AgentId = agentBase.Id
                                                AND agentBase.IsDeleted = 0
                                                INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId = model.Id
                                                AND model.IsDeleted = 0
                                            WHERE
                                                task.Id = @projectStageTaskId
                                                AND task.IsDeleted = 0";
                ProjectStageTaskBaseInfoDto projectStageTask = await DBSqlSugar.SqlQueryable<ProjectStageTaskBaseInfoDto>(projectStageTaskSql)
                    .AddParameters(new { projectStageTaskId = input.ProjectStageTaskId })
                    .FirstAsync();
                if (projectStageTask == null)
                {
                    throw new BusException("阅读理解项目阶段任务不存在!");
                }
                if (string.IsNullOrEmpty(projectStageTask.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                // 获取学生端阅读理解阶段任务key（参考项目化实践）
                string studentReadingTaskKey = AIAgentKeys.GetStudentProjecKey(projectStageTask.AgentId, projectStageTask.ProjectStageTaskId, input.StudentId);
                if (string.IsNullOrEmpty(studentReadingTaskKey))
                {
                    throw new BusException("无法获取阅读理解阶段任务Key!");
                }

                // 获取或创建上下文缓存
                bool isCreate = false;
                bool isTimeOut = false;
                AI_ContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ContextCacheKey>()
                    .Where(p => p.CacheKey == studentReadingTaskKey && p.IsDeleted == false)
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (contextCacheKey != null)
                {
                    // 验证是否过期
                    if (contextCacheKey.ExpirationTime <= DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                        isTimeOut = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                // 创建上下文缓存
                if (isCreate)
                {
                    // 获取阅读理解阶段任务的高频问题（参考项目化实践）
                    List<RC_ReadingProjectStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<RC_ReadingProjectStageTaskQuestion>()
                        .Where(p => p.ReadingProjectStageTaskId == projectStageTask.ProjectStageTaskId && p.IsDeleted == false)
                        .Select(p => new RC_ReadingProjectStageTaskQuestion()
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Describe = p.Describe
                        }).ToListAsync();
                    string taskQuestionsText = string.Empty;
                    foreach (var taskQuestion in taskQuestions)
                    {
                        taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                    }
                    if (string.IsNullOrEmpty(taskQuestionsText))
                    {
                        taskQuestionsText = "暂无";
                    }

                    //获取阅读理解情景对话系统指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentReadingDialogue_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null || string.IsNullOrEmpty(directive.Directive))
                    {
                        throw new BusException("无法获取情景对话系统指令,请联系管理员!");
                    }

                    string directiveStr = directive.Directive.Replace("{任务名称}", projectStageTask.Name)
                        .Replace("{任务目标}", projectStageTask.Target)
                        .Replace("{主题}", taskQuestionsText);
                    if (string.IsNullOrEmpty(projectStageTask.Demand))
                    {
                        directiveStr = directiveStr.Replace("{情景对话要求}", "暂无");
                    }
                    else
                    {
                        directiveStr = directiveStr.Replace("{情景对话要求}", projectStageTask.Demand);
                    }

                    string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                    {
                        Msg = directiveStr,
                        TimeOut = 604800,
                        modelId = projectStageTask.Modelkey
                    });
                    if (string.IsNullOrEmpty(contextId))
                    {
                        throw new BusException("创建上下文缓存异常!");
                    }

                    contextCacheKey = new AI_ContextCacheKey()
                    {
                        Id = IdHelper.GetId(),
                        CacheKey = studentReadingTaskKey,
                        CacheId = contextId,
                        BusinessId = projectStageTask.ProjectStageTaskId,
                        CreateTime = DateTime.Now,
                        TimeOut = 604800,
                        Explain = "学生端阅读理解阶段任务情景对话Key",
                        ExpirationTime = DateTime.Now.AddSeconds(604800),
                        IsDeleted = false
                    };
                    await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                }

                // 检查是否首次对话（参考项目化实践）
                List<AI_DialogueContentRecord> dialogueRecords = await DBSqlSugar.Queryable<AI_DialogueContentRecord>()
                    .Where(p => p.Key == studentReadingTaskKey && p.MessageType == 2 && p.IsDeleted == false)
                    .OrderBy(p => p.CreateTime)
                    .ToListAsync();

                // 如果是首次对话或者上下文过期重新创建，需要发送任务要求
                if (isCreate || isTimeOut)
                {
                    // 首次对话，发送任务要求（参考项目化实践）
                    string taskRequirement = $"一、任务名称:{projectStageTask.Name}。\n二、任务目标:{projectStageTask.Target}。\n三、对话要求:{projectStageTask.Demand}。";

                    // 上下文对话（任务要求）
                    Func<string, Task> emptyDataHandler = async (data) =>
                    {
                        await Task.CompletedTask;
                    };
                    await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                    {
                        context_id = contextCacheKey.CacheId,
                        content = taskRequirement,
                        role = "user",
                        modelId = projectStageTask.Modelkey
                    }, emptyDataHandler, cancellationToken);
                }

                // 构建对话消息
                string message = input.Msg;

                // 上下文对话
                string msg = string.Empty;
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = message,
                    role = "user",
                    modelId = projectStageTask.Modelkey
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);

                    // 解析返回的数据，提取content内容
                    try
                    {
                        string json = data;
                        int startIndex = json.IndexOf('{');
                        if (startIndex >= 0)
                        {
                            json = json.Substring(startIndex);
                            JObject jsonObject = JObject.Parse(json);
                            string content = jsonObject["Content"]?.Value<string>();
                            if (content != null)
                            {
                                msg += content;
                            }
                        }
                    }
                    catch
                    {
                        // 解析失败时直接使用原始数据
                        msg += data;
                    }
                }, cancellationToken);

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);

                // 更新过期时间
                contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                // 保存对话记录（参考项目化实践）
                AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                {
                    AskText = input.Msg,
                    AudioFile = new AIDialogueASKAudioFileInfo()
                    {
                        AudioUrl = input.AudioUrl,
                        Duration = 0, // 阅读理解暂时不处理音频时长
                    }
                };
                AI_DialogueContentRecord aI_DialogueContentRecord = new AI_DialogueContentRecord()
                {
                    Id = IdHelper.GetId(),
                    Key = studentReadingTaskKey,
                    Ask = JsonConvert.SerializeObject(aIDialogueASKDto),
                    Answer = msg,
                    CreateTime = DateTime.Now,
                    MessageType = 2, // 用户消息
                    BusinessId = projectStageTask.ProjectStageTaskId,
                    IsDeleted = false
                };
                await DBSqlSugar.Insertable(aI_DialogueContentRecord).ExecuteCommandAsync();

                // 更新任务状态为进行中（情景对话任务只要有对话就标记为进行中）
                await UpdateStudentTaskStatus(input.ProjectStageTaskId, input.StudentId, 2, projectStageTask.ProjectId);

            }
            catch (Exception ex)
            {
                throw new BusException($"阅读理解情景对话失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 阅读理解情景对话提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ReadingDialogueSubmitOutput> ReadingDialogueSubmit(ReadingDialogueSubmitInput input)
        {
            // 缓存锁（参考项目化实践）
            string key = input.StudentId + "|" + input.ProjectStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    // 检查是否已存在达标提交记录
                    RC_StudentTaskRecord existingRecord = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                        .Where(p => p.ReadingProjectStageTaskId == input.ProjectStageTaskId
                            && p.StudentId == input.StudentId
                            && p.IsStandard == true
                            && p.IsDeleted == false)
                        .FirstAsync();
                    if (existingRecord != null)
                    {
                        throw new BusException("已完成，无法重复提交!", 801);
                    }

                    // 获取阅读理解阶段任务基础信息（参考项目化实践的SQL查询）
                    string readingStageTaskSql = @"SELECT
                                                    task.Id AS ProjectStageTaskId,
                                                    stage.Id AS ProjectStageId,
                                                    project.Id AS ProjectId,
                                                    project.AgentId AS AgentId,
                                                    task.RoleSetting,
                                                    task.Demand,
                                                    task.ScoreStandard,
         	                                        model.Modelkey,
                                                    task.TaskIsSubmit,
                                                    task.TaskIsAssessment,
                                                    task.TaskAssessmentScore,
                                                    task.GroupIsSubmit,
                                                    task.GroupIsAssessment,
                                                    task.GroupAssessmentScore,
                                                    task.TaskName AS Name,
                                                    task.Target
                                                FROM
                                                    RC_ReadingProjectStageTask task WITH ( NOLOCK )
                                                    INNER JOIN RC_ReadingProjectStage stage WITH ( NOLOCK ) ON task.ReadingProjectStageId = stage.Id
                                                    AND stage.IsDeleted = 0
                                                    INNER JOIN AI_AgentTask project WITH ( NOLOCK ) ON stage.ReadingProjectId = project.Id
                                                    AND project.IsDeleted = 0
         	                                        INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON project.AgentId= agentBase.Id 
                                                	AND agentBase.IsDeleted= 0
                                                    INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	AND model.IsDeleted= 0 
                                                WHERE
                                                    task.IsDeleted = 0
                                                    AND task.TaskType = 2
                                                    AND task.Id = @projectStageTaskId";
                    ReadingStageTaskBaseInfoDto readingStageTask = await DBSqlSugar.SqlQueryable<ReadingStageTaskBaseInfoDto>(readingStageTaskSql)
                        .AddParameters(new { projectStageTaskId = input.ProjectStageTaskId }).FirstAsync();
                    if (readingStageTask == null)
                    {
                        throw new BusException("阅读理解阶段任务Id异常!");
                    }

                    // 获取学生端阅读理解阶段任务key（参考项目化实践）
                    string studentProjectTaskKey = AIAgentKeys.GetStudentProjecKey(readingStageTask.AgentId, readingStageTask.ProjectStageTaskId, input.StudentId);
                    if (string.IsNullOrEmpty(studentProjectTaskKey))
                    {
                        throw new BusException("无法获取阅读理解阶段任务Key!");
                    }

                    // 获取豆包上下文缓存Id（参考项目化实践）
                    bool isCreate = false;
                    bool isTimeOut = false;
                    AI_ContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ContextCacheKey>()
                        .Where(p => p.CacheKey == studentProjectTaskKey && p.IsDeleted == false)
                        .With(SqlWith.NoLock)
                        .FirstAsync();

                    if (contextCacheKey != null)
                    {
                        // 验证是否过期
                        if (contextCacheKey.ExpirationTime <= DateTime.Now)
                        {
                            contextCacheKey.IsDeleted = true;
                            await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                            isCreate = true;
                            isTimeOut = true;
                        }
                    }
                    else
                    {
                        isCreate = true;
                    }

                    // 创建上下文缓存（参考项目化实践）
                    if (isCreate)
                    {
                        // 获取阅读理解阶段任务的高频问题
                        List<RC_ReadingProjectStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<RC_ReadingProjectStageTaskQuestion>()
                            .Where(p => p.ReadingProjectStageTaskId == readingStageTask.ProjectStageTaskId && p.IsDeleted == false)
                            .Select(p => new RC_ReadingProjectStageTaskQuestion()
                            {
                                Id = p.Id,
                                Name = p.Name,
                                Describe = p.Describe
                            }).ToListAsync();
                        string taskQuestionsText = string.Empty;
                        foreach (var taskQuestion in taskQuestions)
                        {
                            taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                        }
                        if (string.IsNullOrEmpty(taskQuestionsText))
                        {
                            taskQuestionsText = "暂无";
                        }

                        //获取阅读理解情景对话系统指令
                        AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentReadingDialogue_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                        if (directive == null || string.IsNullOrEmpty(directive.Directive))
                        {
                            throw new BusException("无法获取情景对话系统指令,请联系管理员!");
                        }

                        string directiveStr = directive.Directive.Replace("{任务名称}", readingStageTask.Name)
                            .Replace("{任务目标}", readingStageTask.Target)
                            .Replace("{主题}", taskQuestionsText);
                        if (string.IsNullOrEmpty(readingStageTask.Demand))
                        {
                            directiveStr = directiveStr.Replace("{情景对话要求}", "暂无");
                        }
                        else
                        {
                            directiveStr = directiveStr.Replace("{情景对话要求}", readingStageTask.Demand);
                        }

                        string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                        {
                            Msg = directiveStr,
                            TimeOut = 604800,
                            modelId = readingStageTask.Modelkey
                        });
                        if (string.IsNullOrEmpty(contextId))
                        {
                            throw new Exception("创建上下文缓存异常!");
                        }

                        contextCacheKey = new AI_ContextCacheKey()
                        {
                            Id = IdHelper.GetId(),
                            CacheKey = studentProjectTaskKey,
                            CacheId = contextId,
                            BusinessId = readingStageTask.ProjectStageTaskId,
                            CreateTime = DateTime.Now,
                            TimeOut = 604800,
                            Explain = "学生端阅读理解阶段任务情景对话Key",
                            ExpirationTime = DateTime.Now.AddSeconds(604800),
                            IsDeleted = false
                        };
                        await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                    }

                    // 获取提交指令（参考项目化实践）
                    AI_Directive submitDirective = await DBSqlSugar.Queryable<AI_Directive>()
                        .Where(p => p.Key == AIAgentKeys.StudentProjectDialogueSubmit_Directive && p.IsDeleted == false)
                        .With(SqlWith.NoLock)
                        .FirstAsync();
                    if (submitDirective == null)
                    {
                        throw new BusException("无法获取提交系统提示词,请联系管理员!");
                    }

                    // 提交信息
                    string submitMsg = submitDirective.Directive;
                    submitMsg = submitMsg.Replace("{评分标准}", readingStageTask.ScoreStandard).Replace("{评估角色设定}", readingStageTask.RoleSetting);

                    // 缓存过期逻辑（参考项目化实践）
                    if (isTimeOut)
                    {
                        // 获取历史对话记录
                        List<AI_DialogueContentRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_DialogueContentRecord>()
                            .Where(p => p.Key == studentProjectTaskKey && p.IsDeleted == false)
                            .OrderBy(p => p.CreateTime)
                            .With(SqlWith.NoLock)
                            .ToListAsync();
                        if (dialogueContentRecords.Count > 0)
                        {
                            // 历史对话记录
                            string dialogueData = "\n5.学生历史问答记录如下:\n";
                            foreach (var dialogueContentRecord in dialogueContentRecords)
                            {
                                AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                                dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                                 + $"学生问:{ask.AskText}。\n"
                                                 + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                            }
                            submitMsg = submitMsg + dialogueData;
                        }
                    }

                    // 获取模型配置
                    //string model = await DBSqlSugar.Queryable<AI_ModelConfig>().Where(p => p.SceneType == 6).Select(p => p.Model).FirstAsync();

                    // 上下文对话（参考项目化实践）
                    string resultData = string.Empty;
                    Func<string, Task> emptyDataHandler = async (data) =>
                    {
                        await Task.CompletedTask;
                    };
                    await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                    {
                        context_id = contextCacheKey.CacheId,
                        content = submitMsg,
                        role = "user",
                        modelId = readingStageTask.Modelkey
                    }, async (data) =>
                    {
                        await emptyDataHandler(data);

                        string json = data;
                        int startIndex = json.IndexOf('{');
                        json = json.Substring(startIndex);
                        JObject jsonObject = JObject.Parse(json);
                        string content = jsonObject["Content"]?.Value<string>();
                        if (content != null)
                        {
                            resultData += content;
                        }
                    });

                    // 豆包输出解析（参考项目化实践）
                    StudentProjectSubmitDouBaoOutput dialogueSubmitDouBaoOutput = StudentProjectSubmitAnalysisJson(resultData);
                    if (dialogueSubmitDouBaoOutput != null)
                    {
                        // 验证是否达标
                        bool taskPass = readingStageTask.TaskIsAssessment ? dialogueSubmitDouBaoOutput.Score >= readingStageTask.TaskAssessmentScore : true;
                        bool groupPass = readingStageTask.GroupIsAssessment ? dialogueSubmitDouBaoOutput.Score >= readingStageTask.GroupAssessmentScore : true;
                        bool isStandard = taskPass && groupPass;

                        // 计算任务状态：如果达标则为已完成(3)，否则为进行中(2)
                        int taskStatus = isStandard ? 3 : 2;

                        // 计算项目状态：需要查询该学生在整个项目中的完成情况
                        int projectStatus = await CalculateProjectStatus(readingStageTask.ProjectId, input.StudentId, input.ProjectStageTaskId, isStandard);

                        // 保存提交记录
                        RC_StudentTaskRecord studentTaskRecord = new RC_StudentTaskRecord()
                        {
                            Id = IdHelper.GetId(),
                            StudentId = input.StudentId,
                            ReadingProjectStageTaskId = readingStageTask.ProjectStageTaskId,
                            ReadingProjectId = readingStageTask.ProjectId,
                            TaskStatus = taskStatus,
                            ProjectStatus = projectStatus,
                            SubmitContent = "情景对话已完成", // 实际应该存储对话记录
                            Score = dialogueSubmitDouBaoOutput.Score,
                            IsStandard = isStandard,
                            AssessmentResult = dialogueSubmitDouBaoOutput.AssessmentResult,
                            AIGrade = BusinessUtil.GetAvgLevel(dialogueSubmitDouBaoOutput.Score),
                            MatchedQuestions = dialogueSubmitDouBaoOutput.QuestionIds != null && dialogueSubmitDouBaoOutput.QuestionIds.Count > 0
                                ? JsonConvert.SerializeObject(dialogueSubmitDouBaoOutput.QuestionIds)
                                : null,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false
                        };

                        await DBSqlSugar.Insertable(studentTaskRecord).ExecuteCommandAsync();

                        _agentCommonCacheService.DelRedisLock(key);
                        return new ReadingDialogueSubmitOutput
                        {
                            IsStandard = studentTaskRecord.IsStandard,
                            AssessmentResult = studentTaskRecord.AssessmentResult,
                            // Score = studentTaskRecord.Score,
                            //  AIGrade = studentTaskRecord.AIGrade
                        };
                    }
                    else
                    {
                        throw new Exception("提交异常!");
                    }
                }
                catch (Exception ex)
                {
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
            else
            {
                throw new BusException("数据处理中!", 801);
            }
        }

        /// <summary>
        /// 阅读理解知识问答
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task ReadingKnowledge(ReadingKnowledgeInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                // 获取阅读理解项目阶段任务基础信息（参考项目化实践的完整SQL查询）
                string projectStageTaskSql = @"SELECT
                                                task.Id AS ProjectStageTaskId,
                                                stage.Id AS ProjectStageId,
                                                project.Id AS ProjectId,
                                                agentBase.Id AS AgentId,
                                                task.RoleSetting,
                                                task.Scope,
                                                model.Modelkey
                                            FROM
                                                RC_ReadingProjectStageTask task WITH ( NOLOCK )
                                                INNER JOIN RC_ReadingProjectStage stage WITH ( NOLOCK ) ON task.ReadingProjectStageId = stage.Id
                                                AND stage.IsDeleted = 0
                                                INNER JOIN AI_AgentTask project WITH ( NOLOCK ) ON stage.ReadingProjectId = project.Id
                                                AND project.IsDeleted = 0
                                                INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON project.AgentId = agentBase.Id
                                                AND agentBase.IsDeleted = 0
                                                INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId = model.Id
                                                AND model.IsDeleted = 0
                                            WHERE
                                                task.Id = @projectStageTaskId
                                                AND task.IsDeleted = 0";

                ProjectStageTaskBaseInfoDto projectStageTask = await DBSqlSugar.SqlQueryable<ProjectStageTaskBaseInfoDto>(projectStageTaskSql)
                    .AddParameters(new { projectStageTaskId = input.ProjectStageTaskId })
                    .FirstAsync();

                if (projectStageTask == null)
                {
                    throw new BusException("阅读理解项目阶段任务不存在!");
                }
                if (string.IsNullOrEmpty(projectStageTask.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                // 获取学生端阅读理解阶段任务key（参考项目化实践）
                string studentReadingTaskKey = AIAgentKeys.GetStudentProjecKey(projectStageTask.AgentId, projectStageTask.ProjectStageTaskId, input.StudentId);
                if (string.IsNullOrEmpty(studentReadingTaskKey))
                {
                    throw new BusException("无法获取阅读理解阶段任务Key!");
                }

                // 获取或创建上下文缓存（参考项目化实践）
                bool isCreate = false;
                bool isTimeOut = false;
                AI_ContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ContextCacheKey>()
                    .Where(p => p.CacheKey == studentReadingTaskKey && p.IsDeleted == false)
                    .With(SqlWith.NoLock)
                    .FirstAsync();

                if (contextCacheKey != null)
                {
                    // 验证是否过期
                    if (contextCacheKey.ExpirationTime <= DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                        isTimeOut = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                // 创建上下文缓存
                if (isCreate)
                {
                    string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                    {
                        Msg = $"一、角色设定:{projectStageTask.RoleSetting}。\n二、问答范围:{projectStageTask.Scope}。",
                        TimeOut = 604800,
                        modelId = projectStageTask.Modelkey
                    });
                    if (string.IsNullOrEmpty(contextId))
                    {
                        throw new BusException("创建上下文缓存异常!");
                    }

                    contextCacheKey = new AI_ContextCacheKey()
                    {
                        Id = IdHelper.GetId(),
                        CacheKey = studentReadingTaskKey,
                        CacheId = contextId,
                        BusinessId = projectStageTask.ProjectStageTaskId,
                        CreateTime = DateTime.Now,
                        TimeOut = 604800,
                        Explain = "学生端阅读理解阶段任务知识问答Key",
                        ExpirationTime = DateTime.Now.AddSeconds(604800),
                        IsDeleted = false
                    };
                    await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                }

                // 缓存过期逻辑（参考项目化实践）
                if (isTimeOut)
                {
                    // 获取历史对话记录
                    List<AI_DialogueContentRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_DialogueContentRecord>()
                        .Where(p => p.Key == studentReadingTaskKey && p.IsDeleted == false)
                        .OrderBy(p => p.CreateTime)
                        .With(SqlWith.NoLock)
                        .ToListAsync();

                    if (dialogueContentRecords.Count > 0)
                    {
                        // 历史对话记录
                        string dialogueData = "学生历史问答记录如下:\n";
                        foreach (var dialogueContentRecord in dialogueContentRecords)
                        {
                            AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                            dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                         + $"学生问:{ask.AskText}。\n"
                                         + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                        }
                        dialogueData += "要求：后续学生问答结合历史记录产出你的回答。（注意:当前对话你只需要输出'收到'）";

                        // 上下文对话（历史对话记录）
                        Func<string, Task> emptyDataHandler = async (data) =>
                        {
                            await Task.CompletedTask;
                        };
                        await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                        {
                            context_id = contextCacheKey.CacheId,
                            content = dialogueData,
                            role = "user",
                            modelId = projectStageTask.Modelkey
                        }, async (data) =>
                        {
                            await emptyDataHandler(data);
                        }, cancellationToken);

                        // 更新过期时间
                        contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }
                }

                // 如果不是过期重建，也要更新过期时间（每次对话都延长缓存有效期）
                //if (!isTimeOut)
                //{
                //    contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                //    await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                //}

                // 构建对话消息
                string message = input.Msg;

                // 上下文对话（参考项目化实践）
                string msg = string.Empty;
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = message,
                    role = "user",
                    modelId = projectStageTask.Modelkey
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);

                    // 解析返回的数据，提取content内容
                    try
                    {
                        string json = data;
                        int startIndex = json.IndexOf('{');
                        if (startIndex >= 0)
                        {
                            json = json.Substring(startIndex);
                            JObject jsonObject = JObject.Parse(json);
                            string content = jsonObject["Content"]?.Value<string>();
                            if (content != null)
                            {
                                msg += content;
                            }
                        }
                    }
                    catch
                    {
                        // 解析失败时直接使用原始数据
                        msg += data;
                    }
                }, cancellationToken);

                // 保存对话记录（参考项目化实践）
                AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                {
                    AskText = input.Msg,
                    AudioFile = new AIDialogueASKAudioFileInfo()
                    {
                        AudioUrl = input.AudioUrl,
                        Duration = input.Duration,
                    }
                };
                AI_DialogueContentRecord aI_DialogueContentRecord = new AI_DialogueContentRecord()
                {
                    Id = IdHelper.GetId(),
                    Key = studentReadingTaskKey,
                    Ask = JsonConvert.SerializeObject(aIDialogueASKDto),
                    Answer = msg,
                    CreateTime = DateTime.Now,
                    MessageType = 2, // 用户消息
                    BusinessId = projectStageTask.ProjectStageTaskId,
                    IsDeleted = false
                };
                await DBSqlSugar.Insertable(aI_DialogueContentRecord).ExecuteCommandAsync();

                // 更新任务状态为进行中（知识问答任务只要有对话就标记为进行中）
                await UpdateStudentTaskStatus(input.ProjectStageTaskId, input.StudentId, 2, projectStageTask.ProjectId);

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
            catch (Exception ex)
            {
                ReadingComprehensionSSEOutput errorOutput = new ReadingComprehensionSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                string errorData = "data: " + JsonConvert.SerializeObject(errorOutput) + "\n\n";
                await dataHandler(errorData);
            }
        }

        /// <summary>
        /// 获取学生做任务结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentDoTaskResultOutput> GetStudentDoTaskResult(GetStudentDoTaskResultInput input)
        {
            try
            {
                // 根据任务提交记录ID获取评估结果
                RC_StudentTaskRecord taskRecord = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                    .Where(p => p.Id == input.TaskSubmitId && p.StudentId == input.StudentId && p.IsDeleted == false)
                    .FirstAsync();

                if (taskRecord == null)
                {
                    throw new BusException("任务提交记录不存在！");
                }

                return new GetStudentDoTaskResultOutput()
                {
                    AssessmentResult = taskRecord.AssessmentResult ?? "暂无评估结果"
                };
            }
            catch (Exception ex)
            {
                throw new BusException($"获取任务结果失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 查询任务提交记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetTaskSubmitRecordsOutput> GetTaskSubmitRecords(GetTaskSubmitRecordsInput input)
        {
            try
            {
                // 构建查询条件
                var query = DBSqlSugar.Queryable<RC_StudentTaskRecord, RC_ReadingProjectStageTask, RC_ReadingProjectStage, AI_AgentTask>(
                    (r, t, s, p) => new JoinQueryInfos(
                        JoinType.Inner, r.ReadingProjectStageTaskId == t.Id,
                        JoinType.Inner, t.ReadingProjectStageId == s.Id,
                        JoinType.Inner, s.ReadingProjectId == p.Id
                    ))
                    .Where((r, t, s, p) => r.StudentId == input.StudentId && r.IsDeleted == false && t.IsDeleted == false && s.IsDeleted == false && p.IsDeleted == false);

                // 项目筛选
                if (!string.IsNullOrEmpty(input.ProjectId))
                {
                    query = query.Where((r, t, s, p) => p.Id == input.ProjectId);
                }

                // 阶段筛选
                if (!string.IsNullOrEmpty(input.StageId))
                {
                    query = query.Where((r, t, s, p) => s.Id == input.StageId);
                }

                // 任务筛选
                if (!string.IsNullOrEmpty(input.TaskId))
                {
                    query = query.Where((r, t, s, p) => t.Id == input.TaskId);
                }

                // 任务状态筛选
                if (input.TaskStatuses != null && input.TaskStatuses.Count > 0)
                {
                    query = query.Where((r, t, s, p) => input.TaskStatuses.Contains(r.TaskStatus));
                }

                // 是否达标筛选
                if (input.IsStandard.HasValue)
                {
                    query = query.Where((r, t, s, p) => r.IsStandard == input.IsStandard.Value);
                }

                // 时间范围筛选
                if (input.StartTime.HasValue)
                {
                    query = query.Where((r, t, s, p) => r.CreateTime >= input.StartTime.Value);
                }
                if (input.EndTime.HasValue)
                {
                    query = query.Where((r, t, s, p) => r.CreateTime <= input.EndTime.Value);
                }

                // 获取总数
                int totalCount = await query.CountAsync();

                // 排序
                switch (input.OrderBy?.ToLower())
                {
                    case "score":
                        query = input.OrderDirection?.ToUpper() == "ASC"
                            ? query.OrderBy((r, t, s, p) => r.Score)
                            : query.OrderByDescending((r, t, s, p) => r.Score);
                        break;
                    case "taskorder":
                        query = input.OrderDirection?.ToUpper() == "ASC"
                            ? query.OrderBy((r, t, s, p) => t.TaskOrder)
                            : query.OrderByDescending((r, t, s, p) => t.TaskOrder);
                        break;
                    default: // CreateTime
                        query = input.OrderDirection?.ToUpper() == "ASC"
                            ? query.OrderBy((r, t, s, p) => r.CreateTime)
                            : query.OrderByDescending((r, t, s, p) => r.CreateTime);
                        break;
                }

                // 分页查询
                var records = await query
                    .Select((r, t, s, p) => new TaskSubmitRecordItem
                    {
                        Id = r.Id,
                        StudentId = r.StudentId,
                        ReadingProjectStageTaskId = r.ReadingProjectStageTaskId,
                        ReadingProjectId = r.ReadingProjectId,
                        TaskStatus = r.TaskStatus,
                        SubmitContent = r.SubmitContent,
                        Score = r.Score,
                        IsStandard = r.IsStandard,
                        AssessmentResult = r.AssessmentResult,
                        AIGrade = r.AIGrade,
                        MatchedQuestions = r.MatchedQuestions,
                        CreateTime = r.CreateTime,
                        Creator = r.Creator,
                        ProjectName = p.Name,
                        StageName = s.StageName,
                        StageOrder = s.StageOrder,
                        TaskName = t.TaskName,
                        TaskType = t.TaskType,
                        TaskOrder = t.TaskOrder
                    })
                    .ToPageListAsync(input.PageIndex, input.PageSize);

                // 计算总页数
                int totalPages = (int)Math.Ceiling((double)totalCount / input.PageSize);

                return new GetTaskSubmitRecordsOutput
                {
                    Records = records,
                    TotalCount = totalCount,
                    PageIndex = input.PageIndex,
                    PageSize = input.PageSize,
                    TotalPages = totalPages
                };
            }
            catch (Exception ex)
            {
                throw new BusException($"查询任务提交记录失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 阅读理解成果评估
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ReadingAssessmentOutput> ReadingAssessment(ReadingAssessmentInput input)
        {
            // 缓存锁（参考项目化实践）
            string key = input.StudentId + "|" + input.ProjectStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    // 是否存在达标提交记录
                    RC_StudentTaskRecord isDo = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                        .Where(p => p.ReadingProjectStageTaskId == input.ProjectStageTaskId
                                    && p.StudentId == input.StudentId
                                    && p.IsStandard == true
                                    && p.IsDeleted == false)
                        .FirstAsync();

                    if (isDo != null)
                    {
                        throw new BusException("已完成，无法重复提交!", 801);
                    }

                    // 获取阅读理解阶段任务基础信息（参考项目化实践的SQL查询）
                    string readingStageTaskSql = @"SELECT
                                                    task.Id AS ProjectStageTaskId,
                                                    stage.Id AS ProjectStageId,
                                                    project.Id AS ProjectId,
                                                    project.AgentId AS AgentId,
                                                    task.RoleSetting,
                                                    task.ScoreStandard,
                                                    task.TaskIsSubmit,
                                                    task.TaskIsAssessment,
                                                    task.TaskAssessmentScore,
                                                    task.GroupIsSubmit,
                                                    task.GroupIsAssessment,
                                                    task.GroupAssessmentScore
                                                FROM
                                                    RC_ReadingProjectStageTask task WITH ( NOLOCK )
                                                    INNER JOIN RC_ReadingProjectStage stage WITH ( NOLOCK ) ON task.ReadingProjectStageId = stage.Id
                                                    AND stage.IsDeleted = 0
                                                    INNER JOIN AI_AgentTask project WITH ( NOLOCK ) ON stage.ReadingProjectId = project.Id
                                                    AND project.IsDeleted = 0
                                                WHERE
                                                    task.IsDeleted = 0
                                                    AND task.TaskType = 1
                                                    AND task.Id = @projectStageTaskId";

                    ReadingStageTaskBaseInfoDto readingStageTask = await DBSqlSugar.SqlQueryable<ReadingStageTaskBaseInfoDto>(readingStageTaskSql)
                        .AddParameters(new { projectStageTaskId = input.ProjectStageTaskId }).FirstAsync();

                    if (readingStageTask == null)
                    {
                        throw new BusException("阅读理解阶段任务Id异常!");
                    }

                    // 获取阅读理解阶段任务的高频问题
                    List<RC_ReadingProjectStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<RC_ReadingProjectStageTaskQuestion>()
                        .Where(p => p.ReadingProjectStageTaskId == readingStageTask.ProjectStageTaskId && p.IsDeleted == false)
                        .ToListAsync();

                    string taskQuestionsText = "";
                    if (taskQuestions.Count > 0)
                    {
                        taskQuestionsText = string.Join("、", taskQuestions.Select(p => p.Name));
                    }
                    else
                    {
                        taskQuestionsText = "暂无";
                    }

                    // 获取学生端阅读理解阶段任务key（参考项目化实践）
                    string studentProjectTaskKey = AIAgentKeys.GetStudentProjecKey(readingStageTask.AgentId, readingStageTask.ProjectStageTaskId, input.StudentId);
                    if (string.IsNullOrEmpty(studentProjectTaskKey))
                    {
                        throw new BusException("无法获取阅读理解阶段任务Key!");
                    }

                    // 构建文件提交内容
                    var fileSubmission = new
                    {
                        Files = input.Files,
                        SubmitTime = DateTime.Now
                    };

                    string submitContent = JsonConvert.SerializeObject(fileSubmission);

                    // 调用AI评估服务对作品进行评估
                    var assessmentResult = await ProcessWorkAssessment(readingStageTask, input.Files, taskQuestionsText);

                    // 验证是否达标
                    bool taskPass = readingStageTask.TaskIsAssessment ? assessmentResult.Score >= readingStageTask.TaskAssessmentScore : true;
                    bool groupPass = readingStageTask.GroupIsAssessment ? assessmentResult.Score >= readingStageTask.GroupAssessmentScore : true;
                    bool isStandard = taskPass && groupPass;

                    // 计算任务状态：如果达标则为已完成(3)，否则为进行中(2)
                    int taskStatus = isStandard ? 3 : 2;

                    // 计算项目状态：需要查询该学生在整个项目中的完成情况
                    int projectStatus = await CalculateProjectStatus(readingStageTask.ProjectId, input.StudentId, readingStageTask.ProjectStageTaskId, isStandard);

                    // 保存提交记录
                    RC_StudentTaskRecord studentTaskRecord = new RC_StudentTaskRecord()
                    {
                        Id = IdHelper.GetId(),
                        StudentId = input.StudentId,
                        ReadingProjectStageTaskId = readingStageTask.ProjectStageTaskId,
                        ReadingProjectId = readingStageTask.ProjectId,
                        TaskStatus = taskStatus,
                        ProjectStatus = projectStatus,
                        SubmitContent = submitContent,
                        Score = assessmentResult.Score,
                        IsStandard = isStandard,
                        AssessmentResult = assessmentResult.AssessmentResult,
                        AIGrade = assessmentResult.AIGrade,
                        MatchedQuestions = assessmentResult.MatchedQuestions != null && assessmentResult.MatchedQuestions.Count > 0
                            ? JsonConvert.SerializeObject(assessmentResult.MatchedQuestions)
                            : null,
                        CreateTime = DateTime.Now,
                        Creator = input.StudentId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.StudentId,
                        IsDeleted = false
                    };

                    await DBSqlSugar.Insertable(studentTaskRecord).ExecuteCommandAsync();

                    // 保存会话记录（参考项目化实践的格式）
                    List<AIDialogueASKFileInfo> fileInfos = new List<AIDialogueASKFileInfo>();
                    foreach (var file in input.Files)
                    {
                        fileInfos.Add(new AIDialogueASKFileInfo()
                        {
                            FileName = file.FileName,
                            FileUrl = file.FileUrl
                        });
                    }
                    AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                    {
                        Files = fileInfos
                    };
                    AI_DialogueContentRecord aI_DialogueContentRecord = new AI_DialogueContentRecord()
                    {
                        Id = IdHelper.GetId(),
                        Key = studentProjectTaskKey,
                        Ask = JsonConvert.SerializeObject(aIDialogueASKDto),
                        Answer = "---",
                        CreateTime = DateTime.Now,
                        MessageType = 2,
                        BusinessId = readingStageTask.ProjectStageTaskId,
                        IsDeleted = false
                    };
                    await DBSqlSugar.Insertable(aI_DialogueContentRecord).ExecuteCommandAsync();

                    _agentCommonCacheService.DelRedisLock(key);
                    return new ReadingAssessmentOutput()
                    {
                        IsStandard = studentTaskRecord.IsStandard,
                        AssessmentResult = studentTaskRecord.AssessmentResult
                    };
                }
                catch (Exception ex)
                {
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
            else
            {
                throw new BusException("数据处理中!", 801);
            }
        }

        /// <summary>
        /// 处理作品评估（调用豆包AI，参考项目化实践）
        /// </summary>
        /// <param name="readingStageTask"></param>
        /// <param name="files"></param>
        /// <param name="taskQuestionsText"></param>
        /// <returns></returns>
        private async Task<WorkAssessmentResult> ProcessWorkAssessment(ReadingStageTaskBaseInfoDto readingStageTask, List<StudentAssessFileInfoInput> files, string taskQuestionsText)
        {
            try
            {
                // 获取豆包AI配置（参考项目化实践）
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string model = await DBSqlSugar.Queryable<AI_ModelConfig>().Where(p => p.SceneType == 6).Select(p => p.Model).FirstAsync();
                string url = AppSetting.DouBaoAI.MyAppUrl;
                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(model) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("豆包AI配置异常!");
                }

                // 参数处理（参考项目化实践）
                string fileUrlStr = string.Empty;
                foreach (var file in files)
                {
                    fileUrlStr += $"第{files.IndexOf(file) + 1}个作品地址:{file.FileUrl}。\n";
                }

                // 构建豆包AI请求（参考项目化实践）
                DouBaoMyAppDto fileAnalysisDouBaoDto = new DouBaoMyAppDto()
                {
                    model = model,
                    stream = true,
                    messages = new List<DouBaoMyAppMessages>()
                    {
                        new DouBaoMyAppMessages()
                        {
                            role = "user",
                            content = $"作品地址:{fileUrlStr}、评分标准:{readingStageTask.ScoreStandard}、用户提示词:{readingStageTask.RoleSetting}、主题:{taskQuestionsText}"
                        }
                    }
                };
                string jsonData = JsonConvert.SerializeObject(fileAnalysisDouBaoDto);

                string resDataJson = string.Empty;
                // HTTP请求（参考项目化实践）
                using (var httpClient = new HttpClient())
                {
                    var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Authorization", apiKey);
                    request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead))
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            using (var stream = await response.Content.ReadAsStreamAsync())
                            {
                                using (var reader = new StreamReader(stream, Encoding.UTF8))
                                {
                                    string line;
                                    while ((line = await reader.ReadLineAsync()) != "data:[DONE]")
                                    {
                                        // 处理SSE数据行
                                        if (line.StartsWith("data:"))
                                        {
                                            // 去除(data: )进行解析数据
                                            var data = line.Substring("data:".Length).Trim();
                                            if (!string.IsNullOrEmpty(data))
                                            {
                                                // 解析请求结果
                                                DouBaoMyAppStreamOutput chatResponse = JsonConvert.DeserializeObject<DouBaoMyAppStreamOutput>(data);
                                                if (chatResponse != null && chatResponse.choices.Count > 0 && chatResponse.choices[0].delta != null)
                                                {
                                                    resDataJson += chatResponse.choices[0].delta.content;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            throw new BusException("豆包AI评估请求失败!");
                        }
                    }
                }

                // 豆包评估输出解析（参考项目化实践）
                StudentProjectSubmitDouBaoOutput studentAssessDouBaoOutput = StudentProjectSubmitAnalysisJson(resDataJson);
                if (studentAssessDouBaoOutput != null)
                {
                    // 转换为WorkAssessmentResult格式
                    return new WorkAssessmentResult
                    {
                        Score = studentAssessDouBaoOutput.Score,
                        AssessmentResult = studentAssessDouBaoOutput.AssessmentResult ?? "评估完成",
                        AIGrade = BusinessUtil.GetAvgLevel(studentAssessDouBaoOutput.Score),
                        MatchedQuestions = studentAssessDouBaoOutput.QuestionIds ?? new List<string>()
                    };
                }
                else
                {
                    throw new Exception("豆包AI评估结果解析失败!");
                }
            }
            catch (Exception ex)
            {
                // 评估失败时返回默认结果
                return new WorkAssessmentResult
                {
                    Score = 60m,
                    AssessmentResult = $"评估过程中出现异常：{ex.Message}。给予基础分数，建议重新提交。",
                    AIGrade = "合格",
                    MatchedQuestions = new List<string>()
                };
            }
        }

        /// <summary>
        /// 解析项目化实践阶段任务提交后返回的Json（参考项目化实践）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="JsonException"></exception>
        /// <exception cref="Exception"></exception>
        public static StudentProjectSubmitDouBaoOutput StudentProjectSubmitAnalysisJson(string input)
        {
            try
            {
                var result = new StudentProjectSubmitDouBaoOutput();

                if (string.IsNullOrWhiteSpace(input))
                {
                    return result;
                }

                //先尝试直接解析
                try
                {
                    var jsonResult = JsonConvert.DeserializeObject<StudentProjectSubmitDouBaoOutput>(input);
                    if (jsonResult != null)
                    {
                        return jsonResult;
                    }
                }
                catch
                {

                }

                //尝试JSON解析（优先处理标准格式）
                try
                {
                    int startIndex = input.IndexOf('{');
                    int endIndex = input.LastIndexOf('}');
                    if (startIndex != -1 && endIndex != -1 && startIndex < endIndex)
                    {
                        string jsonPart = input.Substring(startIndex, endIndex - startIndex + 1);
                        // 清理JSON中的特殊引号
                        jsonPart = jsonPart.Replace("\u2018", "'")
                                           .Replace("\u2019", "'")
                                           .Replace("\u201C", "\"")
                                           .Replace("\u201D", "\"");

                        // 尝试 deserialization
                        var jsonResult = JsonConvert.DeserializeObject<StudentProjectSubmitDouBaoOutput>(jsonPart);
                        if (jsonResult != null)
                        {
                            // 验证是否有效解析（至少有一个字段有值）
                            if (jsonResult.Score > 0 || !string.IsNullOrEmpty(jsonResult.Level) ||
                                !string.IsNullOrEmpty(jsonResult.AssessmentResult) ||
                                jsonResult.QuestionIds.Count > 0)
                            {
                                return jsonResult;
                            }
                        }
                    }
                }
                catch (Exception)
                {

                }

                // 正则提取 - 适配带双引号的字段名
                var inputCopy = input;

                // 提取分数
                var scorePattern = @"[""']?[Ss]core[""']?\s*[:：]\s*(\d+(?:\.\d+)?)";
                var scoreMatch = Regex.Match(inputCopy, scorePattern);
                if (scoreMatch.Success)
                {
                    if (decimal.TryParse(scoreMatch.Groups[1].Value, out decimal score))
                    {
                        result.Score = score;
                    }
                }

                // 提取等第
                var levelPattern = @"[""']?[Ll]evel[""']?\s*[:：]\s*[""']?([^""',\n\r}]+)[""']?";
                var levelMatch = Regex.Match(inputCopy, levelPattern);
                if (levelMatch.Success)
                {
                    result.Level = levelMatch.Groups[1].Value.Trim();
                }

                // 提取评估结果
                var assessmentPattern = @"[""']?[Aa]ssessment[Rr]esult[""']?\s*[:：]\s*[""']?([^""'\n\r}]+)[""']?";
                var assessmentMatch = Regex.Match(inputCopy, assessmentPattern);
                if (assessmentMatch.Success)
                {
                    result.AssessmentResult = assessmentMatch.Groups[1].Value.Trim();
                }

                // 提取问题ID列表
                var questionIdsPattern = @"[""']?[Qq]uestion[Ii]ds[""']?\s*[:：]\s*\[([^\]]*)\]";
                var questionIdsMatch = Regex.Match(inputCopy, questionIdsPattern);
                if (questionIdsMatch.Success)
                {
                    var questionIdsStr = questionIdsMatch.Groups[1].Value;
                    var questionIds = questionIdsStr.Split(',')
                        .Select(id => id.Trim().Trim('"', '\''))
                        .Where(id => !string.IsNullOrEmpty(id))
                        .ToList();
                    result.QuestionIds = questionIds;
                }

                return result;
            }
            catch (Exception ex)
            {
                // 解析失败时返回默认结果
                return new StudentProjectSubmitDouBaoOutput
                {
                    Score = 60m,
                    Level = "合格",
                    AssessmentResult = $"解析评估结果时出现异常：{ex.Message}",
                    QuestionIds = new List<string>()
                };
            }
        }

        /// <summary>
        /// 计算项目状态
        /// </summary>
        /// <param name="projectId">项目ID</param>
        /// <param name="studentId">学生ID</param>
        /// <param name="currentTaskId">当前任务ID</param>
        /// <param name="currentTaskIsStandard">当前任务是否达标</param>
        /// <returns>项目状态：1-未开始，2-进行中，3-已完成</returns>
        private async Task<int> CalculateProjectStatus(string projectId, string studentId, string currentTaskId, bool currentTaskIsStandard)
        {
            try
            {
                // 获取项目的所有任务（排除对话类型任务 TaskType != 3）
                var allProjectTasks = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                    .InnerJoin<RC_ReadingProjectStage>((task, stage) => task.ReadingProjectStageId == stage.Id && stage.IsDeleted == false)
                    .Where((task, stage) => stage.ReadingProjectId == projectId && task.IsDeleted == false && task.TaskType != 3)
                    .Select((task, stage) => new { task.Id })
                    .ToListAsync();

                if (!allProjectTasks.Any())
                {
                    return 1; // 没有任务，未开始
                }

                // 获取学生在该项目中的所有任务记录
                var studentTaskRecords = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                    .Where(p => p.ReadingProjectId == projectId && p.StudentId == studentId && p.IsDeleted == false)
                    .ToListAsync();

                // 计算已达标完成的任务数量
                int completedTaskCount = 0;
                foreach (var task in allProjectTasks)
                {
                    if (task.Id == currentTaskId)
                    {
                        // 当前任务，使用传入的达标状态
                        if (currentTaskIsStandard)
                        {
                            completedTaskCount++;
                        }
                    }
                    else
                    {
                        // 其他任务，查询是否已达标
                        var taskRecord = studentTaskRecords.FirstOrDefault(r => r.ReadingProjectStageTaskId == task.Id);
                        if (taskRecord != null && taskRecord.IsStandard)
                        {
                            completedTaskCount++;
                        }
                    }
                }

                // 判断项目状态
                if (completedTaskCount == 0 && !studentTaskRecords.Any())
                {
                    return 1; // 未开始：没有任何任务记录
                }
                else if (completedTaskCount == allProjectTasks.Count)
                {
                    return 3; // 已完成：所有任务都达标完成
                }
                else
                {
                    return 2; // 进行中：有任务记录但未全部完成
                }
            }
            catch (Exception)
            {
                // 异常情况下返回进行中状态
                return 2;
            }
        }

        /// <summary>
        /// 获取任务高频问题标签
        /// </summary>
        /// <param name="taskId"></param>
        /// <returns></returns>
        private async Task<string> GetTaskQuestionTags(string taskId)
        {
            try
            {
                var questionTags = await DBSqlSugar.Queryable<RC_ReadingProjectStageTaskQuestion>()
                    .Where(p => p.ReadingProjectStageTaskId == taskId && p.IsDeleted == false)
                    .ToListAsync();
                if (questionTags.Any())
                {
                    //高频问题Id，高频问题名称、高频问题描述组成的字符串
                    return string.Join("；", questionTags.Select(q => $"问题Id:{q.Id}、问题名称：{q.Name}、问题描述：{q.Describe}").ToList());
                }
                return "无";
            }
            catch
            {
                return "无";
            }
        }

        /// <summary>
        /// 计算阶段和任务的锁定状态（参考项目化实践逻辑）
        /// </summary>
        /// <param name="stages">阶段列表</param>
        private void CalculateStageAndTaskLockStatus(List<StudentProjectStageOutput> stages)
        {
            // 下一个阶段是否锁定
            bool isStagesLocked = false;

            // 处理阶段信息
            foreach (var stage in stages.OrderBy(s => s.StageOrder))
            {
                // 获取当前阶段的任务
                var stageTasks = stage.Tasks.OrderBy(t => t.Order).ToList();

                if (stageTasks.Count <= 0)
                {
                    stage.IsLock = false;
                    continue;
                }

                // 设置当前阶段锁定状态
                stage.IsLock = isStagesLocked;

                if (stage.IsLock)
                {
                    // 当前阶段锁定，下面的所有任务都锁定
                    foreach (var task in stageTasks)
                    {
                        task.IsLock = true;
                    }
                }
                else
                {
                    // 任务是否锁定
                    bool isTaskLocked = false;

                    foreach (var task in stageTasks)
                    {
                        // 知识问答类型不锁定
                        if (task.TaskType == 3)
                        {
                            task.IsLock = false;
                            continue;
                        }

                        // 设置当前任务锁定状态
                        task.IsLock = isTaskLocked;

                        // 检查任务点解锁条件，决定下一个任务是否锁定
                        bool taskUnlocked = CheckTaskPointUnlockConditions(task);

                        // 设置下一个任务是否锁定
                        if (taskUnlocked)
                        {
                            isTaskLocked = false;
                        }
                        else
                        {
                            isTaskLocked = true;
                        }
                    }
                }

                // 验证下一个阶段是否锁定
                // 如果当前阶段所有任务都是知识问答类型，则下一个阶段不锁定
                if (stageTasks.Count == stageTasks.Count(t => t.TaskType == 3))
                {
                    isStagesLocked = false;
                }
                else
                {
                    // 检查当前阶段是否有未完成的非知识问答任务
                    var nonKnowledgeTasks = stageTasks.Where(t => t.TaskType != 3).ToList();
                    bool allNonKnowledgeTasksCompleted = nonKnowledgeTasks.All(t =>
                    {
                        // 检查组间解锁条件
                        return CheckGroupUnlockConditions(t);
                    });

                    isStagesLocked = !allNonKnowledgeTasksCompleted;
                }
            }
        }

        /// <summary>
        /// 检查任务点解锁条件（任务间解锁）
        /// </summary>
        /// <param name="task">任务信息</param>
        /// <returns>是否满足解锁条件</returns>
        private bool CheckTaskPointUnlockConditions(StudentProjectStageTaskOutput task)
        {
            // 通用任务点解锁条件
            bool submitPass = task.TaskIsSubmit ? task.State == 3 : true; // 3表示已完成
            bool assessmentPass = task.TaskIsAssessment ? task.IsStandard && task.Score >= task.TaskAssessmentScore : true;

            // 特殊任务类型的任务点解锁条件
            bool specialConditionPass = true;
            switch (task.TaskType)
            {
                case 4: // 视频任务
                    specialConditionPass = CheckVideoTaskPointConditions(task);
                    break;
                case 5: // 文档任务
                    specialConditionPass = CheckDocumentTaskPointConditions(task);
                    break;
                    // 其他任务类型使用通用条件
            }

            return submitPass && assessmentPass && specialConditionPass;
        }

        /// <summary>
        /// 检查组间解锁条件（阶段间解锁）
        /// </summary>
        /// <param name="task">任务信息</param>
        /// <returns>是否满足解锁条件</returns>
        private bool CheckGroupUnlockConditions(StudentProjectStageTaskOutput task)
        {
            // 通用组间解锁条件
            bool submitPass = task.GroupIsSubmit ? task.State == 3 : true; // 3表示已完成
            bool assessmentPass = task.GroupIsAssessment ? task.IsStandard && task.Score >= task.GroupAssessmentScore : true;

            // 特殊任务类型的组间解锁条件
            bool specialConditionPass = true;
            switch (task.TaskType)
            {
                case 4: // 视频任务
                    specialConditionPass = CheckVideoGroupConditions(task);
                    break;
                case 5: // 文档任务
                    specialConditionPass = CheckDocumentGroupConditions(task);
                    break;
                    // 其他任务类型使用通用条件
            }

            return submitPass && assessmentPass && specialConditionPass;
        }

        /// <summary>
        /// 检查视频任务的任务点解锁条件
        /// </summary>
        /// <param name="task">任务信息</param>
        /// <returns>是否满足条件</returns>
        private bool CheckVideoTaskPointConditions(StudentProjectStageTaskOutput task)
        {
            if (task.TaskConfig == null) return true;

            // 检查观看时长条件
            if (task.TaskConfig.TaskTotalWatchDurationLimit.HasValue)
            {
                // TODO: 这里需要查询学生的实际观看时长
                // var actualWatchDuration = await GetStudentVideoWatchDuration(task.Id, studentId);
                // if (actualWatchDuration < task.TaskConfig.TaskTotalWatchDurationLimit.Value)
                //     return false;
            }

            // 检查是否观看所有视频条件
            if (task.TaskConfig.TaskIsWatchAllVideos == true)
            {
                // TODO: 这里需要查询学生是否观看了所有视频
                // var watchedVideoCount = await GetStudentWatchedVideoCount(task.Id, studentId);
                // var totalVideoCount = task.TaskConfig.VideoResources?.Count ?? 0;
                // if (watchedVideoCount < totalVideoCount)
                //     return false;
            }

            return true;
        }

        /// <summary>
        /// 检查视频任务的组间解锁条件
        /// </summary>
        /// <param name="task">任务信息</param>
        /// <returns>是否满足条件</returns>
        private bool CheckVideoGroupConditions(StudentProjectStageTaskOutput task)
        {
            if (task.TaskConfig == null) return true;

            // 检查观看时长条件
            if (task.TaskConfig.GroupTotalWatchDurationLimit.HasValue)
            {
                // TODO: 这里需要查询学生的实际观看时长
                // var actualWatchDuration = await GetStudentVideoWatchDuration(task.Id, studentId);
                // if (actualWatchDuration < task.TaskConfig.GroupTotalWatchDurationLimit.Value)
                //     return false;
            }

            // 检查是否观看所有视频条件
            if (task.TaskConfig.GroupIsWatchAllVideos == true)
            {
                // TODO: 这里需要查询学生是否观看了所有视频
                // var watchedVideoCount = await GetStudentWatchedVideoCount(task.Id, studentId);
                // var totalVideoCount = task.TaskConfig.VideoResources?.Count ?? 0;
                // if (watchedVideoCount < totalVideoCount)
                //     return false;
            }

            return true;
        }

        /// <summary>
        /// 检查文档任务的任务点解锁条件
        /// </summary>
        /// <param name="task">任务信息</param>
        /// <returns>是否满足条件</returns>
        private bool CheckDocumentTaskPointConditions(StudentProjectStageTaskOutput task)
        {
            if (task.TaskConfig == null) return true;

            // 检查是否阅读所有文档条件
            if (task.TaskConfig.TaskIsReadAllDocuments == true)
            {
                // TODO: 这里需要查询学生是否阅读了所有文档
                // var readDocumentCount = await GetStudentReadDocumentCount(task.Id, studentId);
                // var totalDocumentCount = task.TaskConfig.DocumentResources?.Count ?? 0;
                // if (readDocumentCount < totalDocumentCount)
                //     return false;
            }

            return true;
        }

        /// <summary>
        /// 检查文档任务的组间解锁条件
        /// </summary>
        /// <param name="task">任务信息</param>
        /// <returns>是否满足条件</returns>
        private bool CheckDocumentGroupConditions(StudentProjectStageTaskOutput task)
        {
            if (task.TaskConfig == null) return true;

            // 检查是否阅读所有文档条件
            if (task.TaskConfig.GroupIsReadAllDocuments == true)
            {
                // TODO: 这里需要查询学生是否阅读了所有文档
                // var readDocumentCount = await GetStudentReadDocumentCount(task.Id, studentId);
                // var totalDocumentCount = task.TaskConfig.DocumentResources?.Count ?? 0;
                // if (readDocumentCount < totalDocumentCount)
                //     return false;
            }

            return true;
        }

        /// <summary>
        /// 计算任务锁定状态
        /// </summary>
        /// <param name="currentTask">当前任务</param>
        /// <param name="previousTasks">前面的任务列表</param>
        /// <returns>是否锁定</returns>
        private bool CalculateTaskLockStatus(StudentProjectStageTaskOutput currentTask, List<StudentProjectStageTaskOutput> previousTasks)
        {
            // 如果是第一个任务，不锁定
            if (previousTasks.Count == 0)
            {
                return false;
            }

            // 检查前面的任务是否都满足解锁条件
            foreach (var prevTask in previousTasks.OrderBy(t => t.Order))
            {
                // 如果前面的任务顺序大于等于当前任务，跳过
                if (prevTask.Order >= currentTask.Order)
                {
                    continue;
                }

                // 检查组间任务设置的提交条件
                if (prevTask.GroupIsSubmit && prevTask.State != 3) // 3表示已完成
                {
                    return true; // 前面的任务未完成，当前任务锁定
                }

                // 检查组间任务设置的AI评估条件
                if (prevTask.GroupIsAssessment && (!prevTask.IsStandard || prevTask.Score < prevTask.GroupAssessmentScore))
                {
                    return true; // 前面的任务未达标，当前任务锁定
                }

                // 检查任务点设置的提交条件
                if (prevTask.TaskIsSubmit && prevTask.State != 3)
                {
                    return true; // 前面的任务未完成，当前任务锁定
                }

                // 检查任务点设置的AI评估条件
                if (prevTask.TaskIsAssessment && (!prevTask.IsStandard || prevTask.Score < prevTask.TaskAssessmentScore))
                {
                    return true; // 前面的任务未达标，当前任务锁定
                }
            }

            return false; // 所有前置条件都满足，不锁定
        }

        #region 未达标相关接口

        /// <summary>
        /// 获取学生端未达标列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentNoStandardListOutput> GetStudentNoStandardList(GetStudentNoStandardListInput input)
        {
            try
            {
                // 获取阅读理解项目基础信息
                GetStudentNoStandardListOutput projectInfoOutput = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentNoStandardListOutput()
                    {
                        AgentId = p.AgentId,
                        ProjectId = p.Id,
                        ProjectName = p.Name,
                        ProjectIntroduce = p.Introduce,
                        ProjectLogo = p.TaskLogo
                    })
                    .FirstAsync();

                if (projectInfoOutput == null)
                {
                    throw new BusException("阅读理解项目Id异常!");
                }

                // 获取学生未达标的任务记录（排除视频任务和文档任务，因为它们不存在提交操作）
                List<RC_StudentTaskRecord> studentTaskRecords = await DBSqlSugar.Queryable<RC_StudentTaskRecord, RC_ReadingProjectStageTask>((r, t) => new JoinQueryInfos(
                    JoinType.Inner, r.ReadingProjectStageTaskId == t.Id
                ))
                .Where((r, t) => r.StudentId == input.StudentId
                            && r.ReadingProjectId == input.ProjectId
                            && r.IsStandard == false
                            && r.IsDeleted == false
                            && t.IsDeleted == false
                            && t.TaskType != 4  // 排除视频任务
                            && t.TaskType != 5) // 排除文档任务
                .Select((r, t) => r)
                .ToListAsync();

                if (studentTaskRecords.Count > 0)
                {
                    // 通过关联查询获取阶段和任务信息（已在上一步过滤了视频和文档任务）
                    var stageTaskInfo = await DBSqlSugar.Queryable<RC_StudentTaskRecord, RC_ReadingProjectStageTask, RC_ReadingProjectStage>((r, t, s) => new JoinQueryInfos(
                        JoinType.Inner, r.ReadingProjectStageTaskId == t.Id,
                        JoinType.Inner, t.ReadingProjectStageId == s.Id
                    ))
                    .Where((r, t, s) => r.StudentId == input.StudentId
                                        && r.ReadingProjectId == input.ProjectId
                                        && r.IsStandard == false
                                        && r.IsDeleted == false
                                        && t.IsDeleted == false
                                        && s.IsDeleted == false
                                        && t.TaskType != 4  // 再次确保排除视频任务
                                        && t.TaskType != 5) // 再次确保排除文档任务
                    .Select((r, t, s) => new
                    {
                        StageId = s.Id,
                        StageName = s.StageName,
                        StageOrder = s.StageOrder,
                        TaskId = t.Id,
                        TaskName = t.TaskName,
                        TaskTarget = t.Target,
                        TaskType = t.TaskType,
                        TaskOrder = t.TaskOrder,
                        RecordId = r.Id,
                        RecordCreateTime = r.CreateTime
                    })
                    .ToListAsync();

                    // 按阶段分组
                    var stageGroups = stageTaskInfo.GroupBy(x => new { x.StageId, x.StageName, x.StageOrder })
                        .OrderBy(g => g.Key.StageOrder)
                        .ToList();

                    List<GetStudentNoStandardListStageOutput> projectStages = new List<GetStudentNoStandardListStageOutput>();

                    foreach (var stageGroup in stageGroups)
                    {
                        var stage = new GetStudentNoStandardListStageOutput
                        {
                            Id = stageGroup.Key.StageId,
                            Name = stageGroup.Key.StageName,
                            Order = stageGroup.Key.StageOrder,
                            ProjectStageTaskInfos = new List<GetStudentNoStandardListStageTaskOutput>()
                        };

                        // 按任务分组
                        var taskGroups = stageGroup.GroupBy(x => new { x.TaskId, x.TaskName, x.TaskTarget, x.TaskType, x.TaskOrder })
                            .OrderBy(g => g.Key.TaskOrder)
                            .ToList();

                        foreach (var taskGroup in taskGroups)
                        {
                            var task = new GetStudentNoStandardListStageTaskOutput
                            {
                                Id = taskGroup.Key.TaskId,
                                ProjectStageId = stageGroup.Key.StageId,
                                Name = taskGroup.Key.TaskName,
                                Target = taskGroup.Key.TaskTarget,
                                TaskType = taskGroup.Key.TaskType,
                                Order = taskGroup.Key.TaskOrder,
                                TaskSubmitId = taskGroup.OrderBy(x => x.RecordCreateTime).Select(x => x.RecordId).ToList()
                            };

                            stage.ProjectStageTaskInfos.Add(task);
                        }

                        projectStages.Add(stage);
                    }

                    projectInfoOutput.ProjectStageInfos = projectStages;
                    return projectInfoOutput;
                }
                else
                {
                    return projectInfoOutput;
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 学生端阅读理解阶段任务提交未达标对话记录备份
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task StudentSubmitNoStandardBackups(StudentSubmitNoStandardBackupsInput input)
        {
            try
            {
                // 获取阅读理解项目的智能体ID
                var projectInfo = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.IsDeleted == false)
                    .InnerJoin<RC_ReadingProjectStage>((p, s) => p.Id == s.ReadingProjectId && s.IsDeleted == false)
                    .InnerJoin<RC_ReadingProjectStageTask>((p, s, t) => s.Id == t.ReadingProjectStageId && t.Id == input.ProjectStageTaskId && t.IsDeleted == false)
                    .Select((p, s, t) => new { p.AgentId })
                    .FirstAsync();

                if (projectInfo == null)
                {
                    throw new BusException("无法获取阅读理解项目信息!");
                }

                // 获取学生端阅读理解阶段任务key
                string studentProjectTaskKey = AIAgentKeys.GetStudentProjecKey(projectInfo.AgentId, input.ProjectStageTaskId, input.StudentId);
                if (string.IsNullOrEmpty(studentProjectTaskKey))
                {
                    throw new BusException("无法获取阅读理解阶段任务Key!");
                }

                // 获取学生端阅读理解阶段任务未达标key
                string studentProjectTaskNoStandardKey = AIAgentKeys.GetStudentProjectNoStandardKey(projectInfo.AgentId, input.ProjectStageTaskId, input.StudentId, input.TaskSubmitId);
                if (string.IsNullOrEmpty(studentProjectTaskNoStandardKey))
                {
                    throw new BusException("无法获取阅读理解阶段任务未达标Key!");
                }

                // 对话记录备份
                await DBSqlSugar.Updateable<AI_DialogueContentRecord>()
                    .SetColumns(it => new AI_DialogueContentRecord()
                    {
                        Key = studentProjectTaskNoStandardKey
                    })
                    .Where(p => p.Key == studentProjectTaskKey && p.IsDeleted == false)
                    .ExecuteCommandAsync();

                //当前缓存Key替换为未达标Key
                await DBSqlSugar.Updateable<AI_ContextCacheKey>()
                    .SetColumns(it => new AI_ContextCacheKey() { CacheKey = studentProjectTaskNoStandardKey })
                    .Where(p => p.CacheKey == studentProjectTaskKey)
                    .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        #endregion

        /// <summary>
        /// 处理思维导图AI评估
        /// </summary>
        /// <param name="task"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<MindMapAssessmentResult> ProcessMindMapAssessment(RC_ReadingProjectStageTask task, MindMapSubmitInput input, string modelId)
        {
            try
            {
                // 获取豆包AI配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string url = AppSetting.DouBaoAI.DialogueUrl;

                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("豆包AI配置异常!");
                }

                // 获取高频问题标签
                string questionTags = await GetTaskQuestionTags(task.Id);

                // 构建系统提示词
                string systemPrompt = $"角色设定：{task.RoleSetting}\n";
                systemPrompt += $"任务名称：{task.TaskName}\n";
                systemPrompt += $"任务描述：{task.Demand}\n";
                systemPrompt += $"任务目标：{task.Target}\n";
                systemPrompt += $"高频问题标签：{questionTags}\n";
                systemPrompt += $"评分标准：{task.ScoreStandard}\n\n";
                systemPrompt += "请根据用户提交的思维导图内容进行评估，并从上述高频问题标签中筛选出与用户作答内容相符的问题。\n";
                systemPrompt += "返回格式要求：请以JSON格式返回评估结果，包含以下字段：\n";
                systemPrompt += "{\n";
                systemPrompt += "  \"Score\": 评估分数(0-100),\n";
                systemPrompt += "  \"AssessmentResult\": \"评估结果描述\",\n";
                systemPrompt += "  \"MatchedQuestions\": [\"问题Id\"]\n";
                systemPrompt += "}";
                systemPrompt += @"AssessmentResult字段中需要包含评估结果和思维导图和统计图表。
    评估结果是字符串格式（文字需连贯详实，禁止出现分数。输出列表层级格式加粗/换行）。
    以下是思维导图和统计图表输出格式：
	思维导图输出实例（注意：一个#表示顶级标题，两个#表示二级标题以此类推，-表示条目）：
       ```mindmap
        # 顶级标题
        
        ## 二级标题1
        - 条目1
        - 条目2
        
        ## 二级标题2
        - 条目1
          - 条目1.1
          - 条目1.2
        - 条目2
          - 条目2.1
          - 条目2.2
        ```
    统计图表请按照echarts的json格式来输出（注意：格式开头必须是```echarts_json结尾必须是```，只需要输出这个格式的Json不需要输出其它文本）。";

                // 构建豆包AI请求
                var douBaoDto = new DouBaoMyAppDto()
                {
                    model = modelId,
                    stream = true,
                    messages = new List<DouBaoMyAppMessages>()
                    {
                        new DouBaoMyAppMessages()
                        {
                            role = "system",
                            content = systemPrompt
                        },
                        new DouBaoMyAppMessages()
                        {
                            role = "user",
                            content = input.MindMapContent ?? ""
                        }
                    }
                };

                string jsonData = JsonConvert.SerializeObject(douBaoDto);
                string resDataJson = string.Empty;

                // 调用豆包AI
                using (var httpClient = new HttpClient())
                {
                    var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Authorization", apiKey);
                    request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead))
                    {
                        response.EnsureSuccessStatusCode();
                        using (var stream = await response.Content.ReadAsStreamAsync())
                        using (var reader = new StreamReader(stream))
                        {
                            string line;
                            while ((line = await reader.ReadLineAsync()) != null)
                            {
                                if (line.StartsWith("data: "))
                                {
                                    string data = line.Substring(6);
                                    if (data != "[DONE]")
                                    {
                                        try
                                        {
                                            var jsonResponse = JsonConvert.DeserializeObject<dynamic>(data);
                                            if (jsonResponse?.choices != null && jsonResponse.choices.Count > 0)
                                            {
                                                var delta = jsonResponse.choices[0].delta;
                                                if (delta?.content != null)
                                                {
                                                    resDataJson += delta.content.ToString();
                                                }
                                            }
                                        }
                                        catch
                                        {
                                            // 忽略解析错误
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 解析豆包AI返回结果
                var assessmentOutput = ParseDouBaoAssessmentResult(resDataJson);
                if (assessmentOutput != null)
                {
                    return new MindMapAssessmentResult
                    {
                        Score = assessmentOutput.Score,
                        AssessmentResult = assessmentOutput.AssessmentResult,
                        AIGrade = BusinessUtil.GetAvgLevel(assessmentOutput.Score),
                        MatchedQuestions = assessmentOutput.QuestionIds ?? new List<string>()
                    };
                }
            }
            catch (Exception ex)
            {
                // AI评估失败时使用默认评估
                Console.WriteLine($"豆包AI评估失败: {ex.Message}");
            }

            // 默认评估结果
            return new MindMapAssessmentResult
            {
                Score = 75.0m,
                AssessmentResult = "思维导图制作完成，基本符合要求。",
                AIGrade = "良好"
            };
        }

        /// <summary>
        /// 处理选词填空AI评估
        /// </summary>
        /// <param name="task"></param>
        /// <param name="input"></param>
        /// <param name="modelId"></param>
        /// <returns></returns>
        private async Task<WordFillAssessmentResult> ProcessWordFillAssessment(RC_ReadingProjectStageTask task, WordFillSubmitInput input, string modelId)
        {
            try
            {
                // 获取正确答案进行验证
                List<string> correctAnswers = new List<string>();
                if (!string.IsNullOrEmpty(task.CorrectAnswers))
                {
                    correctAnswers = JsonConvert.DeserializeObject<List<string>>(task.CorrectAnswers) ?? new List<string>();
                }

                int correctCount = 0;
                int totalCount = input.Answers.Count;
                List<string> wrongAnswers = new List<string>();

                // 验证答案正确性
                foreach (var answer in input.Answers)
                {
                    if (answer.QuestionIndex > 0 && answer.QuestionIndex <= correctAnswers.Count)
                    {
                        string correctAnswer = correctAnswers[answer.QuestionIndex - 1];
                        bool isCorrect = string.Equals(answer.SelectedWord?.Trim(), correctAnswer?.Trim(), StringComparison.OrdinalIgnoreCase);
                        if (isCorrect)
                        {
                            correctCount++;
                        }
                        else
                        {
                            wrongAnswers.Add($"第{answer.QuestionIndex}题");
                        }
                    }
                }

                bool allCorrect = correctCount == totalCount;
                decimal accuracyRate = totalCount > 0 ? (decimal)correctCount / totalCount * 100 : 0;

                // 如果答案不全对，直接返回基础评估
                if (!allCorrect)
                {
                    return new WordFillAssessmentResult
                    {
                        Score = accuracyRate,
                        AssessmentResult = $"答对了{correctCount}题，共{totalCount}题。{string.Join("、", wrongAnswers)}答错了，请仔细检查后重新作答。",
                        AIGrade = BusinessUtil.GetAvgLevel(accuracyRate),
                        AllCorrect = false,
                        CorrectCount = correctCount,
                        TotalCount = totalCount,
                        MatchedQuestions = new List<string>()
                    };
                }

                // 答案全对，调用豆包AI进行深度评估
                return await ProcessWordFillAIAssessment(task, input, correctCount, totalCount, modelId);
            }
            catch (Exception ex)
            {
                // 评估失败时使用默认评估
                Console.WriteLine($"选词填空评估失败: {ex.Message}");
                return new WordFillAssessmentResult
                {
                    Score = 75.0m,
                    AssessmentResult = "选词填空任务完成，基本符合要求。",
                    AIGrade = "良好",
                    AllCorrect = true,
                    CorrectCount = input.Answers.Count,
                    TotalCount = input.Answers.Count,
                    MatchedQuestions = new List<string>()
                };
            }
        }

        /// <summary>
        /// 处理选词填空豆包AI深度评估
        /// </summary>
        /// <param name="task"></param>
        /// <param name="input"></param>
        /// <param name="correctCount"></param>
        /// <param name="totalCount"></param>
        ///<param name="modelId"></param>
        /// <returns></returns>
        private async Task<WordFillAssessmentResult> ProcessWordFillAIAssessment(RC_ReadingProjectStageTask task, WordFillSubmitInput input, int correctCount, int totalCount, string modelId)
        {
            try
            {
                // 获取豆包AI配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string url = AppSetting.DouBaoAI.DialogueUrl;

                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("豆包AI配置异常!");
                }

                // 获取任务的高频问题
                var taskQuestions = await DBSqlSugar.Queryable<RC_ReadingProjectStageTaskQuestion>()
                    .Where(p => p.ReadingProjectStageTaskId == task.Id && p.IsDeleted == false)
                    .OrderBy(p => p.Order)
                    .ToListAsync();

                // 构建评估提示词
                string systemPrompt = $@"1.角色设定：{task.RoleSetting}
2.任务名称：{task.TaskName}
3.任务要求：{task.Demand ?? "无"}
4.任务目标：{task.Target ?? "完成选词填空练习"}
5.评分标准：{task.ScoreStandard ?? "准确性、理解能力、学习态度"}
6.题目内容：{task.QuestionContent}
7.高频问题标签（如果学生表现涉及这些问题，请在questionIds中返回对应的问题Id）：
{string.Join("\n", taskQuestions.Select((q, index) => $"第{index + 1}个问题. 问题Id：{q.Id}、问题名称：{q.Name}、问题描述：{q.Describe}；"))}

8.请返回JSON格式：
{{
    ""Score"": 分数(0-100),
    ""AssessmentResult"": ""详细评估结果"",
    ""MatchedQuestions"": [匹配的问题Id数组，如[1,3]]
}}
8.1.AssessmentResult字段中需要包含评估结果和思维导图和统计图表。
  8.1.1.评估结果是字符串格式（文字需连贯详实，禁止出现分数。输出列表层级格式加粗/换行）。
  8.1.2.以下是思维导图和统计图表输出格式：
    8.1.2.1.思维导图输出实例（注意：一个#表示顶级标题，两个#表示二级标题以此类推，-表示条目）：
       ```mindmap
        # 顶级标题
        
        ## 二级标题1
        - 条目1
        - 条目2
        
        ## 二级标题2
        - 条目1
          - 条目1.1
          - 条目1.2
        - 条目2
          - 条目2.1
          - 条目2.2
        ```
    8.1.2.2.统计图表请按照echarts的json格式来输出（注意：格式开头必须是```echarts_json结尾必须是```，只需要输出这个格式的Json不需要输出其它文本）。
";

                string userPrompt = $@"学生提交的答案详情：
{JsonConvert.SerializeObject(input.Answers, Formatting.Indented)}

学生操作记录：
{JsonConvert.SerializeObject(input.OperationRecords?.Take(10), Formatting.Indented)}";

                // 调用豆包AI
                var requestData = new DouBaoMyAppDto
                {
                    model = modelId,
                    stream = false,
                    messages = new List<DouBaoMyAppMessages>()
                        {
                            new DouBaoMyAppMessages()
                            {
                                role="system",
                                content= systemPrompt
                            },
                            new DouBaoMyAppMessages()
                            {
                                role="user",
                                content= userPrompt
                            }
                        }
                };

                string requestJson = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(requestJson, Encoding.UTF8, "application/json");

                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Add("Authorization", apiKey);
                    var response = await httpClient.PostAsync(url, content);
                    string responseContent = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var responseData = JsonConvert.DeserializeObject<dynamic>(responseContent);
                        string resDataJson = responseData?.choices?[0]?.message?.content?.ToString();

                        if (!string.IsNullOrEmpty(resDataJson))
                        {
                            // 解析豆包AI返回结果
                            var assessmentOutput = ParseDouBaoAssessmentResult(resDataJson);
                            if (assessmentOutput != null)
                            {
                                // 将问题序号转换为问题内容
                                var matchedQuestions = new List<string>();
                                if (assessmentOutput.QuestionIds != null)
                                {
                                    matchedQuestions = assessmentOutput.QuestionIds;
                                    //foreach (var questionIdObj in assessmentOutput.QuestionIds)
                                    //{
                                    //    if (int.TryParse(questionIdObj.ToString(), out int questionId))
                                    //    {
                                    //        if (questionId > 0 && questionId <= taskQuestions.Count)
                                    //        {
                                    //            matchedQuestions.Add(taskQuestions[questionId - 1].Name);
                                    //        }
                                    //    }
                                    //}
                                }

                                return new WordFillAssessmentResult
                                {
                                    Score = assessmentOutput.Score,

                                    AssessmentResult = assessmentOutput.AssessmentResult,
                                    AIGrade = BusinessUtil.GetAvgLevel(assessmentOutput.Score),
                                    AllCorrect = true,
                                    CorrectCount = correctCount,
                                    TotalCount = totalCount,
                                    MatchedQuestions = matchedQuestions
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"豆包AI选词填空评估失败: {ex.Message}");
            }

            // AI评估失败时使用默认评估
            return new WordFillAssessmentResult
            {
                Score = 95.0m,
                AssessmentResult = "恭喜你！答案全部正确。你的答题过程体现了良好的逻辑思维能力，能够准确理解题意并选择正确答案。继续保持这种认真的学习态度！",
                AIGrade = "优秀",
                AllCorrect = true,
                CorrectCount = correctCount,
                TotalCount = totalCount,
                MatchedQuestions = new List<string>()
            };
        }

        /// <summary>
        /// AI评估结果临时解析类
        /// </summary>
        private class AIAssessmentResult
        {
            public decimal Score { get; set; }
            public string AssessmentResult { get; set; }
            public List<string> MatchedQuestions { get; set; } = new List<string>();
        }

        /// <summary>
        /// 解析豆包AI评估结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private StudentProjectSubmitDouBaoOutput ParseDouBaoAssessmentResult(string input)
        {
            try
            {
                var result = new StudentProjectSubmitDouBaoOutput();

                if (string.IsNullOrWhiteSpace(input))
                {
                    return result;
                }

                // 先尝试解析新的AI响应格式（包含MatchedQuestions）
                try
                {
                    var aiResult = JsonConvert.DeserializeObject<AIAssessmentResult>(input);
                    if (aiResult != null && aiResult.Score > 0)
                    {
                        return new StudentProjectSubmitDouBaoOutput
                        {
                            Score = aiResult.Score,
                            AssessmentResult = aiResult.AssessmentResult,
                            Level = BusinessUtil.GetAvgLevel(aiResult.Score),
                            QuestionIds = aiResult.MatchedQuestions
                        };
                    }
                }
                catch
                {
                    // 忽略解析错误
                }

                // 再尝试直接解析原格式
                try
                {
                    var jsonResult = JsonConvert.DeserializeObject<StudentProjectSubmitDouBaoOutput>(input);
                    if (jsonResult != null)
                    {
                        return jsonResult;
                    }
                }
                catch
                {
                    // 忽略解析错误
                }

                // 尝试JSON解析（优先处理新格式）
                try
                {
                    int startIndex = input.IndexOf('{');
                    int endIndex = input.LastIndexOf('}');
                    if (startIndex != -1 && endIndex != -1 && startIndex < endIndex)
                    {
                        string jsonPart = input.Substring(startIndex, endIndex - startIndex + 1);
                        // 清理JSON中的特殊引号
                        jsonPart = jsonPart.Replace("\u2018", "'")
                                           .Replace("\u2019", "'")
                                           .Replace("\u201C", "\"")
                                           .Replace("\u201D", "\"");

                        // 先尝试解析新格式（包含MatchedQuestions）
                        try
                        {
                            var aiResult = JsonConvert.DeserializeObject<AIAssessmentResult>(jsonPart);
                            if (aiResult != null && aiResult.Score > 0)
                            {
                                return new StudentProjectSubmitDouBaoOutput
                                {
                                    Score = aiResult.Score,
                                    AssessmentResult = aiResult.AssessmentResult,
                                    Level = BusinessUtil.GetAvgLevel(aiResult.Score),
                                    QuestionIds = aiResult.MatchedQuestions
                                };
                            }
                        }
                        catch
                        {
                            // 忽略解析错误，继续尝试原格式
                        }

                        // 再尝试解析原格式
                        var jsonResult = JsonConvert.DeserializeObject<StudentProjectSubmitDouBaoOutput>(jsonPart);
                        if (jsonResult != null)
                        {
                            // 验证是否有效解析（至少有一个字段有值）
                            if (jsonResult.Score > 0 || !string.IsNullOrEmpty(jsonResult.Level) ||
                                !string.IsNullOrEmpty(jsonResult.AssessmentResult))
                            {
                                return jsonResult;
                            }
                        }
                    }
                }
                catch
                {
                    // 忽略解析错误
                }

                // 如果JSON解析失败，尝试正则表达式提取
                try
                {
                    // 提取分数
                    var scoreMatch = Regex.Match(input, @"分数[：:]\s*(\d+(?:\.\d+)?)");
                    if (scoreMatch.Success)
                    {
                        if (decimal.TryParse(scoreMatch.Groups[1].Value, out decimal score))
                        {
                            result.Score = score;
                        }
                    }

                    // 提取评估结果
                    var assessmentMatch = Regex.Match(input, @"评估结果[：:]\s*(.+?)(?=\n|$)");
                    if (assessmentMatch.Success)
                    {
                        result.AssessmentResult = assessmentMatch.Groups[1].Value.Trim();
                    }

                    return result;
                }
                catch
                {
                    // 忽略解析错误
                }

                return result;
            }
            catch
            {
                return new StudentProjectSubmitDouBaoOutput();
            }
        }
    }

    /// <summary>
    /// 学生项目统计输出
    /// </summary>
    public class StudentProjectStatsOutput
    {
        public int ProjectStatus { get; set; }
        public int StageCount { get; set; }
        public int TotalTaskCount { get; set; }
        public int CompletedTaskCount { get; set; }
        public decimal Progress { get; set; }
        public decimal AverageScore { get; set; }
        public bool IsStandard { get; set; }
        public string AIGrade { get; set; }
    }

    /// <summary>
    /// 作品评估结果
    /// </summary>
    public class WorkAssessmentResult
    {
        public decimal Score { get; set; }
        public string AssessmentResult { get; set; }
        public string AIGrade { get; set; }
        public string WorkEvaluationDetail { get; set; }
        public List<string> MatchedQuestions { get; set; } = new List<string>();
    }

    /// <summary>
    /// 思维导图评估结果
    /// </summary>
    public class MindMapAssessmentResult
    {
        public decimal Score { get; set; }
        public string AssessmentResult { get; set; }
        public string AIGrade { get; set; }
        /// <summary>
        /// 匹配的高频问题列表
        /// </summary>
        public List<string> MatchedQuestions { get; set; } = new List<string>();
    }

    /// <summary>
    /// 选词填空评估结果
    /// </summary>
    public class WordFillAssessmentResult
    {
        public decimal Score { get; set; }
        public string AssessmentResult { get; set; }
        public string AIGrade { get; set; }
        public List<string> MatchedQuestions { get; set; } = new List<string>();
        public bool AllCorrect { get; set; }
        public int CorrectCount { get; set; }
        public int TotalCount { get; set; }
    }

    /// <summary>
    /// 阅读理解阶段任务基础信息DTO
    /// </summary>
    public class ReadingStageTaskBaseInfoDto
    {
        /// <summary>
        /// 任务名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string Target { get; set; }
        public string ProjectStageTaskId { get; set; }
        public string ProjectStageId { get; set; }
        public string ProjectId { get; set; }
        public string AgentId { get; set; }
        public string RoleSetting { get; set; }
        public string Demand { get; set; }
        public string ScoreStandard { get; set; }
        public string Modelkey { get; set; }
        public bool TaskIsSubmit { get; set; }
        public bool TaskIsAssessment { get; set; }
        public decimal TaskAssessmentScore { get; set; }
        public bool GroupIsSubmit { get; set; }
        public bool GroupIsAssessment { get; set; }
        public decimal GroupAssessmentScore { get; set; }
    }
}
