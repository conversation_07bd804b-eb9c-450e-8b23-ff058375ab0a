using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI生成题目接口入参
    /// </summary>
    public class AIGenerateQuestionsInput
    {
        /// <summary>
        /// 提示词
        /// </summary>
        public string? Prompt { get; set; }

        /// <summary>
        /// 模型ID
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 是否是知识点出题（用于AI判断是否需要返回知识点信息）
        /// </summary>
        public bool IsKnowledgePointMode { get; set; } = false;

        /// <summary>
        /// 可选的知识点列表（知识点出题时传入，供AI参考）
        /// </summary>
        public List<string>? AvailableKnowledgePoints { get; set; }

        /// <summary>
        /// 知识点ID和内容的映射关系（知识点出题时传入，供AI返回正确的ID）
        /// </summary>
        public Dictionary<string, string>? KnowledgePointMapping { get; set; }

        /// <summary>
        /// 是否是章节出题（用于AI判断是否需要返回章节信息）
        /// </summary>
        public bool IsChapterMode { get; set; } = false;

        /// <summary>
        /// 可选的章节列表（章节出题时传入，供AI参考）
        /// </summary>
        public List<string>? AvailableChapters { get; set; }

        /// <summary>
        /// 章节ID和名称的映射关系（章节出题时传入，供AI返回正确的ID）
        /// </summary>
        public Dictionary<string, string>? ChapterMapping { get; set; }
    }
}
