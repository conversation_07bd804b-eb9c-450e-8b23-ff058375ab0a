using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 知识库创建输入参数
    /// </summary>
    public class CreateKnowledgeInput
    {
        /// <summary>
        /// 知识库名称（用户端显示名称）
        /// </summary>
        [Required(ErrorMessage = "知识库名称不能为空")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 知识库描述信息
        /// 长度要求：[1, 65535]
        /// </summary>
        [StringLength(65535, MinimumLength = 1, ErrorMessage = "知识库描述长度必须在1-65535个字符之间")]
        public string? Description { get; set; }

        /// <summary>
        /// 知识库内的数据类型
        /// unstructured_data：非结构化数据（同时支持上传结构化文件，如 pdf、 doc、csv 等）
        /// structured_data：结构化数据（仅支持上传结构化文件，如 csv、xlsx、jsonl）
        /// </summary>
        [Required(ErrorMessage = "数据类型不能为空")]
        public string DataType { get; set; } = "unstructured_data";

        /// <summary>
        /// 非结构化文档处理策略
        /// 当 data_type 为 "unstructured_data" 时生效
        /// </summary>
        public PreprocessingConfig? Preprocessing { get; set; }

        /// <summary>
        /// 向量化模型名称及其参数
        /// </summary>
        public string? VectorModel { get; set; }

        /// <summary>
        /// 索引参数（类型、维度、量化方式、chunk 长度）
        /// </summary>
        public IndexConfig? IndexConfig { get; set; }
    }

    /// <summary>
    /// 知识库更新输入参数
    /// </summary>
    public class UpdateKnowledgeInput
    {
        /// <summary>
        /// 知识库ID
        /// </summary>
        [Required(ErrorMessage = "知识库ID不能为空")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 知识库名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 知识库描述信息
        /// </summary>
        [StringLength(65535, MinimumLength = 1, ErrorMessage = "知识库描述长度必须在1-65535个字符之间")]
        public string? Description { get; set; }
    }

    /// <summary>
    /// 知识库查询输入参数
    /// </summary>
    public class KnowledgeQueryInput
    {
        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 搜索关键词（搜索名称和描述）
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 数据类型筛选
        /// </summary>
        public string? DataType { get; set; }

        /// <summary>
        /// 创建人筛选
        /// </summary>
        public string? Creator { get; set; }

        /// <summary>
        /// 排序字段（CreateTime、Name）
        /// </summary>
        public string SortField { get; set; } = "CreateTime";

        /// <summary>
        /// 排序方向（asc、desc）
        /// </summary>
        public string SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// 知识库列表DTO
    /// </summary>
    public class KnowledgeListDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 知识库名称（用户端显示名称）
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 豆包知识库名称（系统自动创建）
        /// </summary>
        public string DoubaoName { get; set; } = string.Empty;

        /// <summary>
        /// 知识库描述信息
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// 数据类型显示名称
        /// </summary>
        public string DataTypeDisplayName => DataType switch
        {
            "unstructured_data" => "非结构化数据",
            "structured_data" => "结构化数据",
            _ => "未知类型"
        };

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建时间显示文本
        /// </summary>
        public string CreateTimeText => CreateTime.ToString("yyyy-MM-dd HH:mm:ss");
    }

    /// <summary>
    /// 知识库详情DTO
    /// </summary>
    public class KnowledgeDetailDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 知识库名称（用户端显示名称）
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 豆包知识库名称（系统自动创建）
        /// </summary>
        public string DoubaoName { get; set; } = string.Empty;

        /// <summary>
        /// 知识库描述信息
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// 数据类型显示名称
        /// </summary>
        public string DataTypeDisplayName => DataType switch
        {
            "unstructured_data" => "非结构化数据",
            "structured_data" => "结构化数据",
            _ => "未知类型"
        };

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建时间显示文本
        /// </summary>
        public string CreateTimeText => CreateTime.ToString("yyyy-MM-dd HH:mm:ss");
    }

    /// <summary>
    /// 知识库分页查询结果
    /// </summary>
    public class KnowledgePageResult
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<KnowledgeListDto> Items { get; set; } = new List<KnowledgeListDto>();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)Total / PageSize);
    }

    /// <summary>
    /// 知识库操作结果
    /// </summary>
    public class KnowledgeOperationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 数据（创建或更新后的知识库信息）
        /// </summary>
        public KnowledgeDetailDto? Data { get; set; }

        /// <summary>
        /// VikingDB返回的collection_id（如果有）
        /// </summary>
        public string? CollectionId { get; set; }
    }

    /// <summary>
    /// 预处理配置
    /// </summary>
    public class PreprocessingConfig
    {
        /// <summary>
        /// 分块策略，默认为 "custom_balance"
        /// </summary>
        public string ChunkingStrategy { get; set; } = "custom_balance";

        /// <summary>
        /// 分块长度
        /// </summary>
        public int? ChunkLength { get; set; }
    }

    /// <summary>
    /// 索引配置
    /// </summary>
    public class IndexConfig
    {
        /// <summary>
        /// 索引类型（如 HNSW、IVF_FLAT 等）
        /// </summary>
        public string Type { get; set; } = "HNSW";

        /// <summary>
        /// 维度数量
        /// </summary>
        public int Dimension { get; set; } = 768;

        /// <summary>
        /// 量化策略
        /// </summary>
        public string? QuantizationStrategy { get; set; }
    }

    /// <summary>
    /// VikingDB API请求参数
    /// </summary>
    public class VikingDBCreateRequest
    {
        /// <summary>
        /// 知识库名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 项目ID
        /// </summary>
        public string ProjectId { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// 预处理配置
        /// </summary>
        public PreprocessingConfig? Preprocessing { get; set; }

        /// <summary>
        /// 向量模型
        /// </summary>
        public string? VectorModel { get; set; }

        /// <summary>
        /// 索引配置
        /// </summary>
        public IndexConfig? IndexConfig { get; set; }
    }

    /// <summary>
    /// VikingDB API响应结果
    /// </summary>
    public class VikingDBCreateResponse
    {
        /// <summary>
        /// 状态码
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 数据
        /// </summary>
        public VikingDBData? Data { get; set; }

        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; } = string.Empty;
    }

    /// <summary>
    /// VikingDB返回数据
    /// </summary>
    public class VikingDBData
    {
        /// <summary>
        /// 集合ID
        /// </summary>
        public string CollectionId { get; set; } = string.Empty;

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
    }
}
