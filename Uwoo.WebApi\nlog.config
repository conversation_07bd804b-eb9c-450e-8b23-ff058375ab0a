<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      throwExceptions="true">

  <targets>
   <target name="logfile" xsi:type="File"
			fileName="${basedir}/logs/${level}/nlog-${shortdate}.log"
            archiveFileName="${basedir}/logs/${level}/nlog-${shortdate}-{#####}.log"
            archiveNumbering="Sequence"
            archiveEvery="None"
            archiveAboveSize="20971520"
            maxArchiveFiles="12"
            concurrentWrites="true"
            keepFileOpen="false"
            encoding="utf-8">
      <layout xsi:type="JsonLayout">
        <attribute name="time" layout="${longdate}" />
        <attribute name="level" layout="${level:uppercase=true}" />
        <attribute name="logger" layout="${logger}" />
        <attribute name="message" layout="${message}" />
        <attribute name="exception" layout="${exception:format=tostring}" />
      </layout>
    </target>

    <target xsi:type="File"
		    name="accessLog"
		    fileName="${basedir}/accesslogs/access.log"
		    archiveFileName="${basedir}/accesslogs/${shortdate}_{####}.log"
		    archiveNumbering="Sequence"
		    maxArchiveFiles="10"
		    archiveAboveSize="52428800"
		    layout="${message}" />
  </targets>

  <rules>
	<logger name="Microsoft.*" minlevel="Info"  final="true" />
    <logger name="*" minlevel="Info" writeTo="logfile" />
	<logger name="Uwoo.Core.LogManager.NLogAccessLogger" writeTo="accessLog" minlevel="Info" maxlevel="Info" final="true" />
  </rules>
</nlog>
