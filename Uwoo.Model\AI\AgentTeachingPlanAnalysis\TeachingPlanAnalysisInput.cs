﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI.AgentTeachingPlanAnalysis
{

    public  class TeachingPlanAnalysisModel
    {
        /// <summary>
        /// 教师ID
        /// </summary>
        public string TeacherId { get; set; }
        /// <summary>
        /// 班级ID
        /// </summary>
        public string ClassId { get; set; }
        /// <summary>
        /// 教案选择模型
        /// </summary>
        public TeachingPlanSelectionModel PlanSelection { get; set; } = new();

        /// <summary>
        /// 前测数据选择模型
        /// </summary>
        public PreTestSelectionModel PreTestSelection { get; set; } = new();
    }
    /// <summary>
    /// 教案选择模型
    /// </summary>
    public class TeachingPlanSelectionModel
    {
        /// <summary>
        /// 选择的教案ID（从教案库中选择时使用）
        /// </summary>
        public int? SelectedPlanId { get; set; }

        /// <summary>
        /// 自主上传的教案文件（自主上传时使用）
        /// </summary>
        public IFormFile UploadedFile { get; set; }
    }
    /// <summary>
    /// 前测数据选择模型
    /// </summary>
    public class PreTestSelectionModel
    {
        /// <summary>
        /// 选择的作业ID列表（1-5个）
        /// </summary>
        [Required]
        [MaxLength(5, ErrorMessage = "最多只能选择5份前测数据")]
        public List<int> ExerciseIds { get; set; } = new List<int>();
    }
}
