﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName></SccProjectName>
    <SccProvider></SccProvider>
    <SccAuxPath></SccAuxPath>
    <SccLocalPath></SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ApplicationIcon />
    <OutputType>Library</OutputType>
    <StartupObject />
  </PropertyGroup>
    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;NU1902;NU1903;NU1904;</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;NU1902;NU1903;NU1904;</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Aspose.Words" Version="25.8.0" />
    <PackageReference Include="AutoMapper" Version="6.2.2" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.3.0" />
    <PackageReference Include="DocXCore" Version="1.0.10" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.112" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Uwoo.Core\UwooAgent.Core.csproj" />
    <ProjectReference Include="..\Uwoo.Entity\UwooAgent.Entity.csproj" />
    <ProjectReference Include="..\Uwoo.Mongo\UwooAgent.Mongo.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="cn.jpush.api">
      <HintPath>..\Uwoo.WebApi\lib\cn.jpush.api.dll</HintPath>
    </Reference>
    <Reference Include="Spire.Pdf">
      <HintPath>..\Uwoo.WebApi\lib\Spire.Pdf.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
