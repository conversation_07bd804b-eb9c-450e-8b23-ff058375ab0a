﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model
{
    /// <summary>
    /// 分页输出
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class PageReturn<T>
    {
        /// <summary>
        /// 数据总量
        /// </summary>
        public long TotalCount { get; set; }

        /// <summary>
        /// 数据集
        /// </summary>
        public List<T> Datas { get; set; } = new List<T>();
    }
}
