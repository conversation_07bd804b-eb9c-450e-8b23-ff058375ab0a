﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI生成图片豆包输出
    /// </summary>
    public class AIGenerateImageDouBaoOutput
    {
        /// <summary>
        /// 本次请求使用的模型 ID
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 本次请求创建时间的 Unix 时间戳（秒）
        /// </summary>
        public long created { get; set; }

        /// <summary>
        /// 输出图像的信息，包括图像下载的 URL 或 Base64。
        /// 当指定返回生成图像的格式为url时，则相应参数的子字段为url。为确保信息安全，该链接将在生成后 24 小时内失效，请务必及时保存图像。
        /// 当指定返回生成图像的格式为b64_json时，则相应参数的子字段为b64_json。
        /// </summary>
        public List<AIGenerateImageDouBaoData> data { get; set; } = new List<AIGenerateImageDouBaoData>();

        /// <summary>
        /// 本次请求的用量信息。
        /// </summary>
        public AIGenerateImageDouBaoUsage usage { get; set; } = new AIGenerateImageDouBaoUsage();
    }

    /// <summary>
    /// 输出图像的信息，包括图像下载的 URL 或 Base64。
    /// </summary>
    public class AIGenerateImageDouBaoData
    {
        /// <summary>
        /// 当指定返回生成图像的格式为url时，则相应参数的子字段为url。为确保信息安全，该链接将在生成后 24 小时内失效，请务必及时保存图像。
        /// 当指定返回生成图像的格式为b64_json时，则相应参数的子字段为b64_json。
        /// </summary>
        public string? url { get; set; }
    }

    /// <summary>
    /// 本次请求的用量信息。
    /// </summary>
    public class AIGenerateImageDouBaoUsage
    {
        /// <summary>
        /// 模型生成的图片张数
        /// </summary>
        public int generated_images { get; set; }

        /// <summary>
        /// 模型生成的图片所用的token数量。
        /// </summary>
        public int output_tokens { get; set; }

        /// <summary>
        /// 本次请求消耗的总token数量
        /// </summary>
        public int total_tokens { get; set; }
    }
}
