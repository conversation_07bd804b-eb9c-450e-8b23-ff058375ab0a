﻿using Coldairarrow.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.CustomException;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;
using UwooAgent.System.Services.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_学生端建模
    /// </summary>
    [Route("/AgentStudentModeling/[controller]/[action]")]
    [ApiController]
    public class AgentStudentModelingController : ApiBaseController<IAgentStudentModelingService>
    {
        #region DI
        private readonly IAgentStudentModelingService _agentStudentModelingService;
        private readonly IAgentCommonService _agentCommonService;
        public AgentStudentModelingController(
            IAgentStudentModelingService agentStudentModelingService,
            IAgentCommonService agentCommonService)
        {
            _agentStudentModelingService = agentStudentModelingService;
            _agentCommonService = agentCommonService;
        }
        #endregion

        /// <summary>
        /// 获取学生端建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentModelingInfoOutput> GetStudentModelingInfo(GetStudentModelingInfoInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId))
            {
                throw new BusException("建模Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.ClassId))
            {
                throw new BusException("班级Id不能为空!");
            }
            return await _agentStudentModelingService.GetStudentModelingInfo(input);
        }

        /// <summary>
        /// 学生阅读建模文档
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task StudentReadModelingDocument(StudentReadModelingDocumentInput input)
        {
            if (string.IsNullOrEmpty(input.StudentId)
                || string.IsNullOrEmpty(input.DocumentId)
                || string.IsNullOrEmpty(input.ModelingId)
                || string.IsNullOrEmpty(input.ModelingStageId)
                || string.IsNullOrEmpty(input.ModelingStageTaskId))
            {
                throw new BusException("参数异常!");
            }

            await _agentStudentModelingService.StudentReadModelingDocument(input);
        }

        /// <summary>
        /// 学生观看建模视频
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task StudentWatchModelingVideo(StudentWatchModelingVideoInput input)
        {
            if (string.IsNullOrEmpty(input.StudentId)
                    || string.IsNullOrEmpty(input.VideoId)
                    || string.IsNullOrEmpty(input.ModelingId)
                    || string.IsNullOrEmpty(input.ModelingStageId)
                    || string.IsNullOrEmpty(input.ModelingStageTaskId)
                    || input.Duration <= 0)
            {
                throw new BusException("参数异常!");
            }

            await _agentStudentModelingService.StudentWatchModelingVideo(input);
        }

        /// <summary>
        /// 学生端建模作品评估
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentModelingAssessOutput> StudentModelingAssess(StudentModelingAssessInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingStageTaskId))
            {
                throw new BusException("建模阶段任务Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            if (input.Files == null || input.Files.Count <= 0)
            {
                throw new BusException("文件信息不能为空!");
            }
            else
            {
                foreach (var file in input.Files)
                {
                    if (string.IsNullOrEmpty(file.FileUrl))
                    {
                        throw new BusException($"第{input.Files.IndexOf(file) + 1}个文件地址不能为空!");
                    }
                    if (string.IsNullOrEmpty(file.FileName))
                    {
                        throw new BusException($"第{input.Files.IndexOf(file) + 1}个文件名称不能为空!");
                    }
                }
            }

            return await _agentStudentModelingService.StudentModelingAssess(input);
        }

        /// <summary>
        /// 学生端建模情景对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task StudentModelingDialogue(StudentModelingDialogueInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.ModelingStageTaskId)
                    || string.IsNullOrEmpty(input.StudentId)
                    || (string.IsNullOrEmpty(input.Msg) && string.IsNullOrEmpty(input.AudioUrl)))
                {
                    throw new Exception("参数异常!");
                }
                if (!string.IsNullOrEmpty(input.AudioUrl))
                {
                    string fileExtension = Path.GetExtension(input.AudioUrl);
                    if (fileExtension.ToLower() != ".wav" && fileExtension.ToLower() != ".mp3")
                    {
                        throw new Exception("非常抱歉，目前我们仅支持 wav、mp3 音频格式，还请您谅解!");
                    }
                    input.Msg = await _agentCommonService.AudioFileChangeText(new AudioFileChangeTextInput()
                    {
                        FileUrl = input.AudioUrl,
                    });
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentStudentModelingService.StudentModelingDialogue(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 学生端建模情景对话提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentModelingDialogueSubmitOutput> StudentModelingDialogueSubmit(StudentModelingDialogueSubmitInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingStageTaskId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new Exception("参数异常!");
            }

            return await _agentStudentModelingService.StudentModelingDialogueSubmit(input);
        }

        /// <summary>
        /// 学生端建模知识问答
        /// </summary>
        /// <param name="input"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task StudentModelingKnowledge(StudentModelingKnowledgeInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.ModelingStageTaskId)
                    || string.IsNullOrEmpty(input.StudentId)
                    || (string.IsNullOrEmpty(input.Msg) && string.IsNullOrEmpty(input.AudioUrl)))
                {
                    throw new Exception("参数异常!");
                }
                if (!string.IsNullOrEmpty(input.AudioUrl))
                {
                    string fileExtension = Path.GetExtension(input.AudioUrl);
                    if (fileExtension.ToLower() != ".wav" && fileExtension.ToLower() != ".mp3")
                    {
                        throw new Exception("非常抱歉，目前我们仅支持 wav、mp3 音频格式，还请您谅解!");
                    }
                    input.Msg = await _agentCommonService.AudioFileChangeText(new AudioFileChangeTextInput()
                    {
                        FileUrl = input.AudioUrl,
                    });
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentStudentModelingService.StudentModelingKnowledge(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 学生端建模问题理解
        /// </summary>
        /// <param name="input"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task StudentModelingComprehend(StudentModelingComprehendInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.ModelingStageTaskId)
                    || string.IsNullOrEmpty(input.StudentId)
                    || (string.IsNullOrEmpty(input.Msg) && string.IsNullOrEmpty(input.AudioUrl)))
                {
                    throw new Exception("参数异常!");
                }
                if (!string.IsNullOrEmpty(input.AudioUrl))
                {
                    string fileExtension = Path.GetExtension(input.AudioUrl);
                    if (fileExtension.ToLower() != ".wav" && fileExtension.ToLower() != ".mp3")
                    {
                        throw new Exception("非常抱歉，目前我们仅支持 wav、mp3 音频格式，还请您谅解!");
                    }
                    input.Msg = await _agentCommonService.AudioFileChangeText(new AudioFileChangeTextInput()
                    {
                        FileUrl = input.AudioUrl,
                    });
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentStudentModelingService.StudentModelingComprehend(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 学生端建模问题理解提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentModelingComprehendSubmitOutput> StudentModelingComprehendSubmit(StudentModelingComprehendSubmitInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingStageTaskId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new Exception("参数异常!");
            }

            return await _agentStudentModelingService.StudentModelingComprehendSubmit(input);
        }

        /// <summary>
        /// 学生端建模构建图片识别
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task<StudentModelingStructureImgOcrOutput> StudentModelingStructureImgOcr(StudentModelingStructureImgOcrInput input)
        {
            if (string.IsNullOrEmpty(input.ImgUrl))
            {
                throw new Exception("参数异常!");
            }

            return await _agentStudentModelingService.StudentModelingStructureImgOcr(input);
        }

        /// <summary>
        /// 学生端建模模型构建提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentModelingStructureSubmitOutput> StudentModelingStructureSubmit(StudentModelingStructureSubmitInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingStageTaskId)
                || string.IsNullOrEmpty(input.StudentId)
                || string.IsNullOrEmpty(input.Variable)
                || string.IsNullOrEmpty(input.Expression))
            {
                throw new Exception("参数异常!");
            }

            return await _agentStudentModelingService.StudentModelingStructureSubmit(input);
        }

        /// <summary>
        /// 学生端建模模型假设
        /// </summary>
        /// <param name="input"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task StudentModelingHypothesis(StudentModelingHypothesisInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.ModelingStageTaskId)
                    || string.IsNullOrEmpty(input.StudentId)
                    || (string.IsNullOrEmpty(input.Msg) && string.IsNullOrEmpty(input.AudioUrl)))
                {
                    throw new Exception("参数异常!");
                }
                if (!string.IsNullOrEmpty(input.AudioUrl))
                {
                    string fileExtension = Path.GetExtension(input.AudioUrl);
                    if (fileExtension.ToLower() != ".wav" && fileExtension.ToLower() != ".mp3")
                    {
                        throw new Exception("非常抱歉，目前我们仅支持 wav、mp3 音频格式，还请您谅解!");
                    }
                    input.Msg = await _agentCommonService.AudioFileChangeText(new AudioFileChangeTextInput()
                    {
                        FileUrl = input.AudioUrl,
                    });
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentStudentModelingService.StudentModelingHypothesis(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 学生端建模模型假设提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentModelingHypothesisSubmitOutput> StudentModelingHypothesisSubmit(StudentModelingHypothesisSubmitInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingStageTaskId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new Exception("参数异常!");
            }

            return await _agentStudentModelingService.StudentModelingHypothesisSubmit(input);
        }

        /// <summary>
        /// 学生端建模模型评价
        /// </summary>
        /// <param name="input"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task StudentModelingEvaluate(StudentModelingEvaluateInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.ModelingStageTaskId)
                    || string.IsNullOrEmpty(input.StudentId)
                    || (string.IsNullOrEmpty(input.Msg) && string.IsNullOrEmpty(input.AudioUrl)))
                {
                    throw new Exception("参数异常!");
                }
                if (!string.IsNullOrEmpty(input.AudioUrl))
                {
                    string fileExtension = Path.GetExtension(input.AudioUrl);
                    if (fileExtension.ToLower() != ".wav" && fileExtension.ToLower() != ".mp3")
                    {
                        throw new Exception("非常抱歉，目前我们仅支持 wav、mp3 音频格式，还请您谅解!");
                    }
                    input.Msg = await _agentCommonService.AudioFileChangeText(new AudioFileChangeTextInput()
                    {
                        FileUrl = input.AudioUrl,
                    });
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentStudentModelingService.StudentModelingEvaluate(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 学生端建模模型评价提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentModelingEvaluateSubmitOutput> StudentModelingEvaluateSubmit(StudentModelingEvaluateSubmitInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingStageTaskId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new Exception("参数异常!");
            }

            return await _agentStudentModelingService.StudentModelingEvaluateSubmit(input);
        }

        /// <summary>
        /// 获取学生模型构建最新版本模型构建信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentModelingStructureNewestOutput> StudentModelingStructureNewest(StudentModelingStructureNewestInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new Exception("参数异常!");
            }

            return await _agentStudentModelingService.StudentModelingStructureNewest(input);
        }

        /// <summary>
        /// 学生端建模模型构建优化
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task StudentModelingStructureOptimize(StudentModelingStructureOptimizeInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId)
                || string.IsNullOrEmpty(input.StudentId)
                || string.IsNullOrEmpty(input.Variable)
                || string.IsNullOrEmpty(input.Expression))
            {
                throw new Exception("参数异常!");
            }

            await _agentStudentModelingService.StudentModelingStructureOptimize(input);
        }

        /// <summary>
        /// 获取学生建模相关任务对话内容记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageReturn<StudentModelingDialogueRecordOutput>> StudentModelingDialogueRecord(StudentModelingDialogueRecordInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId) || string.IsNullOrEmpty(input.ModelingStageId) || string.IsNullOrEmpty(input.ModelingStageTaskId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new Exception("参数异常!");
            }
            if (input.IsBackups)
            {
                if (string.IsNullOrEmpty(input.StudentDoModelingTaskId))
                {
                    throw new Exception("参数异常!");
                }
            }
            return await _agentStudentModelingService.StudentModelingDialogueRecord(input);
        }

        /// <summary>
        /// 获取学生建模模型构建记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentModelingStructureRecordOutput> StudentModelingStructureRecord(StudentModelingStructureRecordInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId)
                || string.IsNullOrEmpty(input.ModelingStageId)
                || string.IsNullOrEmpty(input.ModelingStageTaskId)
                || string.IsNullOrEmpty(input.StudentDoModelingTaskId)
                || string.IsNullOrEmpty(input.StudentId))
            {
                throw new Exception("参数异常!");
            }
            return await _agentStudentModelingService.StudentModelingStructureRecord(input);
        }

        /// <summary>
        /// 学生端建模提交未达标对话记录备份
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task StudentModelingSubmitNoStandardBackups(StudentModelingSubmitNoStandardBackupsInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId)
                    || string.IsNullOrEmpty(input.ModelingStageId)
                    || string.IsNullOrEmpty(input.ModelingStageTaskId)
                    || string.IsNullOrEmpty(input.TaskSubmitId)
                    || string.IsNullOrEmpty(input.StudentId))
            {
                throw new Exception("参数异常!");
            }
            await _agentStudentModelingService.StudentModelingSubmitNoStandardBackups(input);
        }

        /// <summary>
        /// 获取学生做建模阶段任务结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentDoModelingTaskResultOutput> GetStudentDoModelingTaskResult(GetStudentDoModelingTaskResultInput input)
        {
            if (string.IsNullOrEmpty(input.TaskSubmitId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new Exception("参数异常!");
            }
            return await _agentStudentModelingService.GetStudentDoModelingTaskResult(input);
        }

        /// <summary>
        /// 获取学生做建模未达标列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentDoModelingNoStandardListOutput> GetStudentDoModelingNoStandardList(GetStudentDoModelingNoStandardListInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new Exception("参数异常!");
            }
            return await _agentStudentModelingService.GetStudentDoModelingNoStandardList(input);
        }
    }
}
