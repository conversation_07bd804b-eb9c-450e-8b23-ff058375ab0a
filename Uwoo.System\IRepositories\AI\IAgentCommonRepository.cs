﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IRepositories.AI
{
    /// <summary>
    /// 智能体_通用功能
    /// </summary>
    public interface IAgentCommonRepository : IDependency, IRepository<AI_AgentBaseInfo>
    {
        
    }
}
