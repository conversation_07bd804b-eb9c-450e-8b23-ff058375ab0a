﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取学生做项目化实践任务列表输出
    /// </summary>
    public class GetStudentDoProjectTaskListOutput
    {
        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生姓名
        /// </summary>
        public string? StudentName { get; set; }

        /// <summary>
        /// 学生头像
        /// </summary>
        public string? StudentLogo { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 项目化实践Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目化实践名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目化实践Logo
        /// </summary>
        public string? ProjectLogo { get; set; }

        /// <summary>
        /// 项目化实践背景
        /// </summary>
        public string? ProjectIntroduce { get; set; }

        /// <summary>
        /// 项目化实践阶段
        /// </summary>
        public List<GetStudentDoProjectTaskListStageInfoOutput> ProjectStageInfos { get; set; } = new List<GetStudentDoProjectTaskListStageInfoOutput>();
    }

    /// <summary>
    /// 获取学生做项目化实践阶段信息输出
    /// </summary>
    public class GetStudentDoProjectTaskListStageInfoOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 项目化实践阶段任务
        /// </summary>
        public List<GetStudentDoProjectTaskListStageTaskInfoOutput> ProjectStageTaskInfos { get; set; } = new List<GetStudentDoProjectTaskListStageTaskInfoOutput>();
    }

    /// <summary>
    /// 获取学生做项目化实践阶段任务信息输出
    /// </summary>
    public class GetStudentDoProjectTaskListStageTaskInfoOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 项目化实践阶段Id
        /// </summary>
        public string? ProjectStageId { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:思维导图、7:选词填空）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 学生做项目化实践阶段任务次数
        /// </summary>
        public List<GetStudentDoProjectTaskListNumberOutput> Numbers { get; set; } = new List<GetStudentDoProjectTaskListNumberOutput>();

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 最后一次提交时间
        /// </summary>
        public string? SubmitTime { get; set; }
    }

    /// <summary>
    /// 学生做项目化实践阶段任务次数
    /// </summary>
    public class GetStudentDoProjectTaskListNumberOutput
    {
        /// <summary>
        /// 次数
        /// </summary>
        public int Number { get; set; }

        /// <summary>
        /// 是否备份
        /// </summary>
        public bool IsBackups { get; set; }

        /// <summary>
        /// 任务提交记录Id
        /// </summary>
        public string? TaskSubmitId { get; set; }
    }
}
