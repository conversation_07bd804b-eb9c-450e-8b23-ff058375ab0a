﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 智能体项目化实践任务详情输出
    /// </summary>
    public class ProjectTaskDetailsOutput
    {
        /// <summary>
        /// 项目化实践任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 任务图标
        /// </summary>
        public string? TaskLogo { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 发布的班级Id
        /// </summary>
        public List<string> ClassId { get; set; } = new List<string>();

        /// <summary>
        /// 发布时间范围(下标0开始、下标1结束)
        /// </summary>
        public List<DateTime?> TimeRange { get; set; } = new List<DateTime?>();

        /// <summary>
        /// 项目化实践阶段
        /// </summary>
        public List<ProjectStageDetailsOutput> ProjectStageInfos { get; set; } = new List<ProjectStageDetailsOutput>();
    }

    /// <summary>
    /// 项目化实践阶段输出
    /// </summary>
    public class ProjectStageDetailsOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string? Describe { get; set; }

        /// <summary>
        /// 项目化实践阶段任务
        /// </summary>
        public List<ProjectStageTaskDetailsOutput> ProjectStageTaskInfos { get; set; } = new List<ProjectStageTaskDetailsOutput>();
    }

    /// <summary>
    /// 项目化实践阶段任务输出
    /// </summary>
    public class ProjectStageTaskDetailsOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:思维导图、7:选词填空）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 评分标准
        /// </summary>
        public string? ScoreStandard { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string? Demand { get; set; }

        /// <summary>
        /// 问答范围
        /// </summary>
        public string? Scope { get; set; }

        /// <summary>
        /// 角色设定
        /// </summary>
        public string? RoleSetting { get; set; }

        /// <summary>
        /// 开场白
        /// </summary>
        public string? Prologue { get; set; }

        /// <summary>
        /// 音色Id
        /// </summary>
        public string? ToneId { get; set; }

        /// <summary>
        /// 角色名称（用于AI展示名称）
        /// </summary>
        public string? RoleName { get; set; }

        /// <summary>
        /// 是否开启提交条件（组间任务设置）
        /// </summary>
        public bool GroupIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（组间任务设置）
        /// </summary>
        public bool GroupIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（组间任务设置）
        /// </summary>
        public decimal GroupAssessmentScore { get; set; }

        /// <summary>
        /// 是否开启提交条件（任务点设置）
        /// </summary>
        public bool TaskIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（任务点设置）
        /// </summary>
        public bool TaskIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（任务点设置）
        /// </summary>
        public decimal TaskAssessmentScore { get; set; }

        /// <summary>
        /// 任务高频问题
        /// </summary>
        public List<ProjectStageTaskQuestionDetailsOutput> QuestionInfos { get; set; } = new List<ProjectStageTaskQuestionDetailsOutput>();

        /// <summary>
        /// 是否存在答题记录
        /// </summary>
        public bool IsDo { get; set; }

        #region 阅读理解特有字段

        // 视频任务配置字段（TaskType=4）
        // 组间解锁条件
        /// <summary>
        /// 总观看时长要求（分钟）- 组间解锁条件
        /// </summary>
        public int? GroupTotalWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否要求观看所有视频 - 组间解锁条件
        /// </summary>
        public bool? GroupIsWatchAllVideos { get; set; }

        // 任务点解锁条件
        /// <summary>
        /// 总观看时长要求（分钟）- 任务点解锁条件
        /// </summary>
        public int? TaskTotalWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否开启视频观看时长条件（组间任务设置）
        /// </summary>
        public bool GroupIsVideoWatchDuration => GroupTotalWatchDurationLimit > 0;

        /// <summary>
        /// 是否开启视频观看时长条件（任务点设置）
        /// </summary>
        public bool TaskIsVideoWatchDuration => TaskTotalWatchDurationLimit > 0;

        /// <summary>
        /// 是否要求观看所有视频 - 任务点解锁条件
        /// </summary>
        public bool? TaskIsWatchAllVideos { get; set; }

        /// <summary>
        /// 视频资源列表
        /// </summary>
        public List<VideoResourceOutput>? VideoResources { get; set; }

        // 文档任务配置字段（TaskType=5）
        // 组间解锁条件
        /// <summary>
        /// 是否需要阅读全部文档 - 组间解锁条件
        /// </summary>
        public bool? GroupIsReadAllDocuments { get; set; }

        // 任务点解锁条件
        /// <summary>
        /// 是否需要阅读全部文档 - 任务点解锁条件
        /// </summary>
        public bool? TaskIsReadAllDocuments { get; set; }

        /// <summary>
        /// 文档资源列表
        /// </summary>
        public List<DocumentResourceOutput>? DocumentResources { get; set; }

        // 思维导图任务配置字段（TaskType=6）
        /// <summary>
        /// 思维导图制作要求
        /// </summary>
        public string? MindMapRequirement { get; set; }

       // /// <summary>
       // /// 参考模板地址
       // /// </summary>
       //// public string? TemplateUrl { get; set; }

        // 选词填空任务配置字段（TaskType=7）
        /// <summary>
        /// 题目内容
        /// </summary>
        public string? QuestionContent { get; set; }

        /// <summary>
        /// 正确答案（JSON格式存储，按填空顺序排列）
        /// </summary>
        public string? CorrectAnswers { get; set; }

        /// <summary>
        /// 干扰项（JSON格式存储）
        /// </summary>
        public string? DistractorWords { get; set; }

        /// <summary>
        /// 自定义背景图片地址
        /// </summary>
        public string? CustomBackgroundImage { get; set; }

        #endregion
    }

    /// <summary>
    /// 项目化实践阶段任务高频问题输出
    /// </summary>
    public class ProjectStageTaskQuestionDetailsOutput
    {
        /// <summary>
        /// 高频问题Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 问题名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Describe { get; set; }
    }


}
