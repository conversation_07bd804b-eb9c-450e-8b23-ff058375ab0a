﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 创建上下文豆包入参
    /// </summary>
    public class CreateContextDouBaoInput
    {
        /// <summary>
        /// 模型Id
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 消息体
        /// </summary>
        public List<CreateContextDouBaoMessage> messages { get; set; } = new List<CreateContextDouBaoMessage>();

        /// <summary>
        /// 上下文缓存的类型，详细见上下文缓存（Context API）概述。
        /// session ：Session 缓存，支持的模型请参见支持的模型。
        /// common_prefix ：前缀缓存，支持的模型请参见模型列表。
        /// </summary>
        public string? mode { get; set; }

        /// <summary>
        /// 过期时长，单位为秒。信息在创建后即开始计时，
        /// 每次使用则重置为0。计时超过ttl，信息会被从缓存中删除。
        /// 每次调用chat均根据ttl更新过期时间。
        /// 过期时间可以设置的范围在1小时到7天，即[3600, 604800]。
        /// </summary>
        public int ttl { get; set; }

        /// <summary>
        /// 用户截断的策略
        /// </summary>
        public CreateContextDouBaoTruncationStrategy truncation_strategy { get; set; } = new CreateContextDouBaoTruncationStrategy();
    }

    /// <summary>
    /// 消息体
    /// </summary>
    public class CreateContextDouBaoMessage
    {
        /// <summary>
        /// 角色
        /// </summary>
        public string? role { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string? content { get; set; }
    }

    /// <summary>
    /// 用户截断的策略
    /// </summary>
    public class CreateContextDouBaoTruncationStrategy
    {
        /// <summary>
        /// 用户截断的策略，对话会综合最近存储的历史token数、模型大小和chat时的max_tokens进行截断。
        /// last_history_tokens：使用last_history_tokens模式，取决于使用的模型支持哪种Session 缓存。
        /// rolling_tokens：使用rolling_tokens模式，取决于使用的模型支持哪种Session 缓存。
        /// </summary>
        public string? type { get; set; }

        /// <summary>
        /// type设置为rolling_tokens时，进行设置。在context历史消息长度接近模型上下文时，是否自动对历史上下文进行裁剪。
        /// false：在历史消息长度超过上下文长度时模型会停止输出（finish_reason为length时)。
        /// true：在历史消息长度接近上下文长度时模型自动按照先进先出的顺序，删除定量（4k）的内容，为新对话内容腾挪缓存空间；同时对缓存中的信息进行重新计算和读入，保障内容理解一致性。具体的计算逻辑，请参见 rolling_tokens 模式。
        /// </summary>
        public bool rolling_tokens { get; set; }
    }
}
