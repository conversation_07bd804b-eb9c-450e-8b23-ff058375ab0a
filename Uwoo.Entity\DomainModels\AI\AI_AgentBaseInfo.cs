﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// 智能体基础信息
	/// </summary>
	[Table("AI_AgentBaseInfo")]
    public class AI_AgentBaseInfo : BaseEntity
    {
        /// <summary>
		/// Id
		/// </summary>
		[SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 智能体名称
        /// </summary>
        public string AgentName { get; set; }

        /// <summary>
        /// 智能体应用编码
        /// </summary>
        public string AgentBotCode { get; set; }

        /// <summary>
        /// 智能体类型
        /// </summary>
        public string AgentTypeId { get; set; }

        /// <summary>
        /// 学校类型（0代表通用）
        /// </summary>
        public string SchoolType { get; set; }

        /// <summary>
        /// 学科Id（0代表通用）
        /// </summary>
        public string SubjectId { get; set; }

        /// <summary>
        /// 教材版本Id（0代表通用）
        /// </summary>
        public string TextbookId { get; set; }

        /// <summary>
        /// 智能体概述
        /// </summary>
        public string Summarize { get; set; }

        /// <summary>
        /// 采用的模型
        /// </summary>
        public string ModelId { get; set; }

        /// <summary>
        /// 系统提示词
        /// </summary>
        public string SysPrompt { get; set; }

        /// <summary>
        /// 图标Logo
        /// </summary>
        public string Logo { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }
    }
}
