﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Base
{
    /// <summary>
    /// 章节信息表
    /// </summary>
    [Table("Exam_SubjectChapter")]
    public class Exam_SubjectChapter
    {
        /// <summary>
        /// 章节ID
        /// </summary>
        [Key, Column(Order = 1)]
        public string Id { get; set; }

        /// <summary>
        /// ParentId
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 章节名称
        /// </summary>
        public string ChapterName { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? OrderId { get; set; }

        /// <summary>
        /// 学科ID
        /// </summary>
        public string SubjectId { get; set; }

        /// <summary>
        /// 学期
        /// </summary>
        public int? WeekId { get; set; }

        /// <summary>
        /// 小节 章节编码
        /// </summary>
        public string IDNum { get; set; }

        /// <summary>
        /// 必修
        /// </summary>
        public int? IsMajor { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        public int? Versions { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string OriginalId { get; set; }

        /// <summary>
        /// 教材Id
        /// </summary>
        public string TextbookId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
