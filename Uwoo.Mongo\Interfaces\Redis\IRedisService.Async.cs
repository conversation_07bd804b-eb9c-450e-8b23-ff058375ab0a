﻿// -- Function：IRedisService.Async.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/30 15:42

namespace Uwoo.Mongo.Interfaces.Redis;

/// <summary>
/// Redis异步接口
/// </summary>
public partial interface IRedisService
{
    /// <summary>
    /// 前缀
    /// </summary>
    string Prefix { get; }

    /// <summary>
    /// 添加缓存数据
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    /// <param name="span">过期时间</param>
    /// <returns></returns>
    Task SetAsync(string key, object value, TimeSpan? span = null);

    /// <summary>
    /// 获取缓存数据
    /// </summary>
    /// <param name="key">键</param>
    /// <typeparam name="T">类型</typeparam>
    /// <returns></returns>
    Task<T> GetAsync<T>(string key);

    /// <summary>
    /// 获取缓存数据
    /// </summary>
    /// <param name="key">键</param>
    /// <returns></returns>
    Task<string> GetAsync(string key);

    /// <summary>
    /// 删除数据
    /// </summary>
    /// <param name="key">键</param>
    /// <returns></returns>
    Task DeleteAsync(string key);

    /// <summary>
    /// 过期指定键
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="span">过期时间</param>
    /// <returns></returns>
    Task ExpireAsync(string key, TimeSpan span);

    /// <summary>
    /// 是否存在指定键
    /// </summary>
    /// <param name="key">键</param>
    /// <returns></returns>
    Task<bool> IsExistsAsync(string key);

    /// <summary>
    /// 添加哈希数据
    /// </summary>
    /// <param name="key"></param>
    /// <param name="field"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    Task HSetAsync(string key, string field, object value);

    /// <summary>
    /// 添加哈希数据
    /// </summary>
    /// <param name="key"></param>
    /// <param name="field"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    Task HSetAsync(string key, string field, string value);

    /// <summary>
    /// 获取哈希数据
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="field">字段</param>
    /// <typeparam name="T">值</typeparam>
    /// <returns></returns>
    Task<T> HGetAsync<T>(string key, string field);

    /// <summary>
    /// 获取哈希数据
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="field">字段</param>
    /// <returns></returns>
    Task<string> HGetAsync(string key, string field);

    /// <summary>
    /// 删除哈希数据
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="filed">字段</param>
    /// <returns></returns>
    Task HDeleteAsync(string key, string filed);

    /// <summary>
    /// 获取当前哈希列表
    /// </summary>
    /// <param name="key">键</param>
    /// <typeparam name="T">实体</typeparam>
    /// <returns></returns>
    Task<Dictionary<string, T>> HGetAllAsync<T>(string key);

    /// <summary>
    /// 获取当前哈希列表
    /// </summary>
    /// <param name="key">键</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> HGetAllAsync(string key);

    /// <summary>
    /// 添加数据集合
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    /// <returns></returns>
    Task SAddAsync(string key, params string[] value);

    /// <summary>
    /// 添加数据集合
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    /// <typeparam name="T">实体</typeparam>
    /// <returns></returns>
    Task SAddAsync<T>(string key, params T[] value);

    /// <summary>
    /// 获取集合列表
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    Task<List<string>> SMembersAsync(string key);

    /// <summary>
    /// 获取集合列表
    /// </summary>
    /// <param name="key">值</param>
    /// <typeparam name="T">实体</typeparam>
    /// <returns></returns>
    Task<List<T>> SMembersAsync<T>(string key);

    /// <summary>
    /// 删除集合列表
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    /// <returns></returns>
    Task SRemoveAsync(string key, params string[] value);

    /// <summary>
    /// 删除集合列表
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    /// <typeparam name="T">实体</typeparam>
    /// <returns></returns>
    Task SRemoveAsync<T>(string key, T[] value);

    /// <summary>
    /// 判断集合是否包含当前成员
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="member">值</param>
    /// <returns></returns>
    Task<bool> IsSMemberAsync(string key, object member);

    /// <summary>
    /// 查找所有分区节点中符合给定模式的Key
    /// </summary>
    /// <param name="pattern">模糊匹配模式</param>
    /// <returns></returns>
    Task<string[]> KeysAsync(string pattern);
}