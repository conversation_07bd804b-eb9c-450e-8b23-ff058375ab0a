﻿// -- Function：TeacherPenLog.cs
// --- Project：X.PenServer.Models
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/24 15:14

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Uwoo.Mongo.Models;

/// <summary>
/// 教师批改的笔迹数据
/// </summary>
public class TeacherPenLog : PenLog
{
    /// <summary>
    /// 教师用户id
    /// </summary>
    [BsonElement(nameof(TeacherId))]
    [BsonRepresentation(BsonType.String)]
    public string TeacherId { get; set; }
}