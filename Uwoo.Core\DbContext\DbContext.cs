﻿using SqlSugar;
using Uwoo.Core.DBManager;

namespace Uwoo.Core.DbContext
{
	public abstract class DbContext
	{
		public virtual ISqlSugarClient SqlSugarClient { get; set; }

		public bool QueryTracking
		{
			set
			{
			}
		}
		public DbContext() : base() { }

		public virtual ISugarQueryable<TEntity> Set<TEntity>(bool filterDeleted = false) where TEntity : class
		{
			return SqlSugarClient.Set<TEntity>(filterDeleted);
		}

		public int SaveChanges()
		{
			return SqlSugarClient.SaveQueues();
		}
	}
}
