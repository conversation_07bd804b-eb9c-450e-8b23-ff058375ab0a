﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取学生建模相关任务对话内容记录
    /// </summary>
    public class StudentModelingDialogueRecordOutput
    {
        /// <summary>
        /// 会话Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 问
        /// </summary>
        public string? Ask { get; set; }

        /// <summary>
        /// “问”结构化数据
        /// </summary>
        public AIDialogueASKDto AskInfo { get; set; } = new AIDialogueASKDto();

        /// <summary>
        /// 答
        /// </summary>
        public string? Answer { get; set; }

        /// <summary>
        /// 学生头像
        /// </summary>
        public string? StudentLogo { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
}
