using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 阅读理解学生任务记录表
    /// </summary>
    [SugarTable("RC_StudentTaskRecord")]
    public class RC_StudentTaskRecord:BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string StudentId { get; set; }

        /// <summary>
        /// 阅读理解项目Id
        /// </summary>
        public string ReadingProjectId { get; set; }

        /// <summary>
        /// 阅读理解项目阶段任务Id
        /// </summary>
        public string ReadingProjectStageTaskId { get; set; }

        /// <summary>
        /// 任务状态（1:未开始，2:进行中，3:已完成）
        /// </summary>
        public int TaskStatus { get; set; }

        /// <summary>
        /// 项目状态（1:未开始，2:进行中，3:已完成）
        /// </summary>
        public int ProjectStatus { get; set; }

        /// <summary>
        /// 提交内容（JSON格式存储）
        /// </summary>
        public string SubmitContent { get; set; }

        /// <summary>
        /// 评估分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 是否达标
        /// </summary>
        public bool IsStandard { get; set; }

        /// <summary>
        /// AI评估结果
        /// </summary>
        public string AssessmentResult { get; set; }

        /// <summary>
        /// AI评价等第
        /// </summary>
        public string AIGrade { get; set; }

        /// <summary>
        /// 匹配的高频问题（JSON格式存储问题名称列表）
        /// </summary>
        public string? MatchedQuestions { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
