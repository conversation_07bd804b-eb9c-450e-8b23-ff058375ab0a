﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Uwoo.Core.Extensions
{
	public static class HttpRequestExtension
	{
		/// <summary>
		/// 获取Token
		/// </summary>
		/// <param name="req">请求</param>
		/// <returns></returns>
		public static string GetToken(this HttpRequest req)
		{
			string tokenHeader = req.Headers["Authorization"].ToString();
			if (tokenHeader.IsNullOrEmpty())
				throw new Exception("缺少token!");

			string pattern = "^Bearer (.*?)$";
			if (!Regex.IsMatch(tokenHeader, pattern))
				throw new Exception("token格式不对!格式为:Bearer {token}");

			string token = Regex.Match(tokenHeader, pattern).Groups[1]?.ToString();
			if (token.IsNullOrEmpty())
				throw new Exception("token不能为空!");

			return token;
		}
	}
}
