﻿using Azure;
using Coldairarrow.Util;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Quartz.Util;
using SqlSugar;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Model.CustomException;
using Uwoo.System.IServices;
using UwooAgent.Core.CacheManager.IBusinessCacheService;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_学生端项目化实践
    /// </summary>
    public class AgentStudentProjectService : ServiceBase<AI_AgentTask, IAgentStudentProjectRepository>, IAgentStudentProjectService, IDependency
    {
        #region DI

        private IAgentCommonService _agentCommonService;
        private IAgentCommonCacheService _agentCommonCacheService;
        public AgentStudentProjectService(IAgentCommonService agentCommonService,
            IAgentCommonCacheService agentCommonCacheService)
        {
            _agentCommonService = agentCommonService;
            _agentCommonCacheService = agentCommonCacheService;
        }

        #endregion

        /// <summary>
        /// 获取学生端项目化实践信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentProjectInfoOutput> GetStudentProjectInfo(GetStudentProjectInfoInput input)
        {
            try
            {
                //获取项目化实践任务基础信息
                GetStudentProjectInfoOutput projectInfoOutput = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentProjectInfoOutput()
                    {
                        AgentId = p.AgentId,
                        ProjectId = p.Id,
                        ProjectName = p.Name,
                        ProjectIntroduce = p.Introduce,
                        ProjectLogo = p.TaskLogo
                    })
                    .FirstAsync();
                if (projectInfoOutput == null)
                {
                    throw new BusException("项目化实践Id异常!");
                }

                //获取项目化实践发布的记录
                AI_AgentTaskPublish agentTaskPublish = await DBSqlSugar.Queryable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == input.ProjectId && p.PublishBusinessId == input.ClassId && p.IsDeleted == false).FirstAsync();
                if (agentTaskPublish == null || !agentTaskPublish.EndTime.HasValue)
                {
                    throw new BusException("项目化实践发布班级异常!");
                }
                if (agentTaskPublish.EndTime.Value <= DateTime.Now)
                {
                    projectInfoOutput.ProjectTaskState = 2;
                }
                else
                {
                    projectInfoOutput.ProjectTaskState = 1;
                }

                //项目化实践阶段
                List<GetStudentProjectStageInfoOutput> projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentProjectStageInfoOutput()
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Describe = p.Describe,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .ToListAsync();

                //项目化实践阶段任务
                List<GetStudentProjectStageTaskInfoOutput> projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentProjectStageTaskInfoOutput()
                    {
                        Id = p.Id,
                        ProjectStageId = p.ProjectStageId,
                        Name = p.Name,
                        Target = p.Target,
                        TaskType = p.TaskType,
                        GroupAssessmentScore = p.GroupAssessmentScore,
                        GroupIsAssessment = p.GroupIsAssessment,
                        GroupIsSubmit = p.GroupIsSubmit,
                        TaskAssessmentScore = p.TaskAssessmentScore,
                        TaskIsAssessment = p.TaskIsAssessment,
                        TaskIsSubmit = p.TaskIsSubmit,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .ToListAsync();

                //获取学生做项目化实践任务信息
                List<AI_StudentDoProjectTask> studentDoProjectTasks = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.StudentId == input.StudentId && p.IsDeleted == false)
                    .Select(p => new AI_StudentDoProjectTask()
                    {
                        Id = p.Id,
                        ProjectStageId = p.ProjectStageId,
                        ProjectStageTaskId = p.ProjectStageTaskId,
                        Score = p.Score,
                        IsStandard = p.IsStandard,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .ToListAsync();

                //获取学生问答记录
                List<AI_DialogueContentRecord> dialogueContentRecords = new List<AI_DialogueContentRecord>();
                if (projectStageTasks.Count > 0)
                {
                    List<string> taskKey = new List<string>();
                    foreach (var taskId in projectStageTasks.Select(p => p.Id).ToList())
                    {
                        string studentProjectTaskKey = AIAgentKeys.GetStudentProjecKey(projectInfoOutput.AgentId, taskId, input.StudentId);
                        if (!string.IsNullOrEmpty(studentProjectTaskKey))
                        {
                            taskKey.Add(studentProjectTaskKey);
                        }
                    }
                    foreach (var item in studentDoProjectTasks.Where(p => p.IsStandard == false))
                    {
                        string studentProjectNoStandard = AIAgentKeys.GetStudentProjectNoStandardKey(projectInfoOutput.AgentId, item.ProjectStageTaskId, input.StudentId, item.Id);
                        if (!string.IsNullOrEmpty(studentProjectNoStandard))
                        {
                            taskKey.Add(studentProjectNoStandard);
                        }
                    }
                    string dialogueContentRecordsSql = @"SELECT DISTINCT
                                                            	[key],
                                                            	BusinessId 
                                                            FROM
                                                            	AI_DialogueContentRecord WITH ( NOLOCK )
                                                            WHERE
                                                            	[Key] IN (@key1) 
                                                            	AND IsDeleted =0";
                    dialogueContentRecords = await DBSqlSugar.SqlQueryable<AI_DialogueContentRecord>(dialogueContentRecordsSql).AddParameters(new { key1 = taskKey }).ToListAsync();
                }

                //下一个阶段是否锁
                bool isStages = false;
                //处理阶段信息
                foreach (var stage in projectStages)
                {
                    //获取当前阶段的任务
                    List<GetStudentProjectStageTaskInfoOutput> projectStageTask = projectStageTasks.Where(p => p.ProjectStageId == stage.Id).OrderBy(p => p.Order).ToList();
                    if (projectStageTask.Count <= 0)
                    {
                        stage.IsLock = false;
                        continue;
                    }

                    //设置当前阶段锁
                    stage.IsLock = isStages;

                    if (stage.IsLock)
                    {
                        //当前阶段下面的所有任务都锁
                        foreach (var taskLock in projectStageTask)
                        {
                            taskLock.IsLock = true;
                        }
                    }
                    else
                    {
                        //任务是否锁
                        bool isTask = false;
                        foreach (var taskIsLock in projectStageTask)
                        {
                            //知识问答类型不锁
                            if (taskIsLock.TaskType == 3)
                            {
                                //不锁
                                taskIsLock.IsLock = false;
                                continue;
                            }

                            //设置当前任务锁
                            taskIsLock.IsLock = isTask;

                            //获取学生做任务信息
                            List<AI_StudentDoProjectTask> studentDoProjectTask = studentDoProjectTasks.Where(p => p.ProjectStageTaskId == taskIsLock.Id).OrderBy(p => p.Order).ToList();

                            //提交
                            bool submitPass = taskIsLock.TaskIsSubmit ? studentDoProjectTask.Count > 0 : true;
                            //评估分数
                            bool assessmentPass = taskIsLock.TaskIsAssessment ? studentDoProjectTask.Max(p => p.Score) >= taskIsLock.TaskAssessmentScore : true;
                            //设置下一个任务是否锁
                            if (submitPass && assessmentPass)
                            {
                                isTask = false;
                            }
                            else
                            {
                                isTask = true;
                            }
                        }
                    }

                    //验证下一个阶段是否锁
                    if (projectStageTask.Count == projectStageTask.Count(p => p.TaskType == 3))
                    {
                        isStages = false;
                    }
                    else
                    {
                        foreach (var taskStageIsLock in projectStageTask)
                        {
                            //知识问答类型不做条件限制
                            if (taskStageIsLock.TaskType == 3)
                            {
                                continue;
                            }

                            //获取学生做任务信息
                            List<AI_StudentDoProjectTask> studentDoProjectTask = studentDoProjectTasks.Where(p => p.ProjectStageTaskId == taskStageIsLock.Id).OrderBy(p => p.Order).ToList();
                            if (taskStageIsLock.GroupIsSubmit)
                            {
                                if (studentDoProjectTask.Count > 0)
                                {
                                    //已完成当前条件不锁
                                    isStages = false;
                                }
                                else
                                {
                                    //未完成当前条件锁
                                    isStages = true;
                                    break;
                                }
                            }
                            else
                            {
                                //未设置条件不锁
                                isStages = false;
                            }

                            if (taskStageIsLock.GroupIsAssessment)
                            {
                                if (studentDoProjectTask.Max(p => p.Score) >= taskStageIsLock.GroupAssessmentScore)
                                {
                                    //已完成当前条件不锁
                                    isStages = false;
                                }
                                else
                                {
                                    //未完成当前条件锁
                                    isStages = true;
                                    break;
                                }
                            }
                            else
                            {
                                //未设置条件不锁
                                isStages = false;
                            }
                        }
                    }

                    //学生做任务状态
                    foreach (var taskState in projectStageTask)
                    {
                        //获取学生提交记录
                        List<AI_StudentDoProjectTask> doProjectTask = studentDoProjectTasks.Where(p => p.ProjectStageTaskId == taskState.Id).OrderBy(p => p.Order).ToList();

                        //验证学生当前任务是否达标
                        if (doProjectTask.Where(p => p.IsStandard == true).FirstOrDefault() != null)
                        {
                            //任务达标已完成
                            taskState.State = 3;
                            //获取提交记录Id
                            taskState.TaskSubmitId = doProjectTask.Where(p => p.IsStandard == true).OrderByDescending(p => p.Order).FirstOrDefault()?.Id;
                        }
                        else
                        {
                            //是否存在问答记录
                            if (dialogueContentRecords.Count(p => p.BusinessId == taskState.Id) > 0)
                            {
                                //存在问答记录进行中
                                taskState.State = 2;
                            }
                            else
                            {
                                //不存在问答记录未开始
                                taskState.State = 1;
                            }

                            //未达标的做任务记录key
                            List<string> noStandard = new List<string>();
                            foreach (var item in doProjectTask.Where(p => p.IsStandard == false).Select(p => p.Id))
                            {
                                string studentProjectNoStandard = AIAgentKeys.GetStudentProjectNoStandardKey(projectInfoOutput.AgentId, taskState.Id, input.StudentId, item);
                                if (!string.IsNullOrEmpty(studentProjectNoStandard))
                                {
                                    noStandard.Add(studentProjectNoStandard);
                                }
                            }

                            if (noStandard.Count > dialogueContentRecords.Where(p => noStandard.Contains(p.Key)).Count())
                            {
                                //已提交但未达标
                                taskState.IsSubmitNoStandard = true;
                                //获取提交记录Id
                                taskState.TaskSubmitId = doProjectTask.Where(p => p.IsStandard == false).OrderByDescending(p => p.Order).FirstOrDefault()?.Id;
                            }
                            else
                            {
                                //已提交但未达标
                                taskState.IsSubmitNoStandard = false;
                            }
                        }
                    }

                    stage.ProjectStageTaskInfos = projectStageTask;
                }

                projectInfoOutput.ProjectStageInfos = projectStages;
                projectInfoOutput.ProgressBar = BusinessUtil.GetAccuracy(studentDoProjectTasks.Count(p => p.IsStandard.Value), projectStageTasks.Count(p => p.TaskType != 3), 1);
                return projectInfoOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 学生端作品评估
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentAssessOutput> StudentAssess(StudentAssessInput input)
        {
            //缓存锁
            string key = input.StudentId + "|" + input.ProjectStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    //是否存在达标提交记录
                    AI_StudentDoProjectTask isDo = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>().Where(p => p.ProjectStageTaskId == input.ProjectStageTaskId && p.StudentId == input.StudentId && p.IsStandard == true && p.IsDeleted == false).FirstAsync();
                    if (isDo != null)
                    {
                        throw new BusException("已完成，无法重复提交!", 801);
                    }

                    //获取项目化实践阶段任务基础信息
                    string projectStageTaskSql = @"SELECT
                                                	task.Id AS ProjectStageTaskId,
                                                	stage.Id AS ProjectStageId,
                                                	agent.Id AS ProjectId,
                                                	agentBase.Id AS AgentId,
                                                	task.RoleSetting,
                                                	task.ScoreStandard,
                                                    task.TaskIsSubmit,
                                                    task.TaskIsAssessment,
                                                    task.TaskAssessmentScore,
                                                    task.GroupIsSubmit,
                                                    task.GroupIsAssessment,
                                                    task.GroupAssessmentScore
                                                FROM
                                                	AI_ProjectStageTask task WITH ( NOLOCK )
                                                	INNER JOIN AI_ProjectStage stage WITH ( NOLOCK ) ON task.ProjectStageId= stage.id 
                                                	AND stage.IsDeleted= 0
                                                	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON stage.ProjectId= agent.Id 
                                                	AND agent.IsDeleted= 0
                                                	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                	AND agentBase.IsDeleted= 0 
                                                WHERE
                                                	task.IsDeleted= 0 
                                                	AND task.TaskType= 1 
                                                	AND task.Id= @projectStageTaskId";
                    ProjectStageTaskBaseInfoDto projectStageTask = await DBSqlSugar.SqlQueryable<ProjectStageTaskBaseInfoDto>(projectStageTaskSql)
                        .AddParameters(new { projectStageTaskId = input.ProjectStageTaskId }).FirstAsync();
                    if (projectStageTask == null)
                    {
                        throw new BusException("项目化实践阶段任务Id异常!");
                    }

                    //获取项目化实践阶段任务的高频问题
                    List<AI_ProjectStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ProjectStageTaskQuestion>()
                        .Where(p => p.ProjectStageTaskId == projectStageTask.ProjectStageTaskId && p.IsDeleted == false)
                        .Select(p => new AI_ProjectStageTaskQuestion()
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Describe = p.Describe
                        }).ToListAsync();
                    string taskQuestionsText = string.Empty;
                    foreach (var taskQuestion in taskQuestions)
                    {
                        taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                    }
                    if (string.IsNullOrEmpty(taskQuestionsText))
                    {
                        taskQuestionsText = "暂无";
                    }

                    //获取学生端项目化阶段任务key
                    string studentProjectTaskKey = AIAgentKeys.GetStudentProjecKey(projectStageTask.AgentId, projectStageTask.ProjectStageTaskId, input.StudentId);
                    if (string.IsNullOrEmpty(studentProjectTaskKey))
                    {
                        throw new BusException("无法获取项目化实践阶段任务Key!");
                    }

                    //获取配置
                    string apiKey = AppSetting.DouBaoAI.APIKey;
                    string model = await DBSqlSugar.Queryable<AI_ModelConfig>().Where(p => p.SceneType == 6).Select(p => p.Model).FirstAsync();
                    string url = AppSetting.DouBaoAI.MyAppUrl;
                    if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(model) || string.IsNullOrEmpty(url))
                    {
                        throw new Exception("请求配置异常!");
                    }

                    //参数处理
                    string fileUrlStr = string.Empty;
                    foreach (var file in input.Files)
                    {
                        fileUrlStr += $"第{input.Files.IndexOf(file) + 1}个作品地址:{file.FileUrl}。\n";
                    }
                    DouBaoMyAppDto fileAnalysisDouBaoDto = new DouBaoMyAppDto()
                    {
                        model = model,
                        stream = true,
                        messages = new List<DouBaoMyAppMessages>()
                    {
                        new DouBaoMyAppMessages()
                        {
                            role="user",
                            content=$"作品地址:{fileUrlStr}、评分标准:{projectStageTask.ScoreStandard}、用户提示词:{projectStageTask.RoleSetting}、主题:{taskQuestionsText}"
                        }
                    }
                    };
                    string jsonData = JsonConvert.SerializeObject(fileAnalysisDouBaoDto);

                    string resDataJson = string.Empty;
                    //http请求
                    using (var httpClient = new HttpClient())
                    {
                        var request = new HttpRequestMessage(HttpMethod.Post, url);
                        request.Headers.Add("Authorization", apiKey);
                        request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                        using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead))
                        {
                            if (response.IsSuccessStatusCode)
                            {
                                using (var stream = await response.Content.ReadAsStreamAsync())
                                {
                                    using (var reader = new StreamReader(stream, Encoding.UTF8))
                                    {
                                        string line;
                                        while ((line = await reader.ReadLineAsync()) != "data:[DONE]")
                                        {
                                            // 处理SSE数据行
                                            if (line.StartsWith("data:"))
                                            {
                                                //去除(data: )进行解析数据
                                                var data = line.Substring("data:".Length).Trim();
                                                if (!string.IsNullOrEmpty(data))
                                                {
                                                    //解析请求结果
                                                    DouBaoMyAppStreamOutput chatResponse = JsonConvert.DeserializeObject<DouBaoMyAppStreamOutput>(data);
                                                    if (chatResponse != null && chatResponse.choices.Count > 0 && chatResponse.choices[0].delta != null)
                                                    {
                                                        resDataJson += chatResponse.choices[0].delta.content;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                throw new BusException("提交异常!");
                            }
                        }
                    }

                    //豆包评估输出
                    StudentProjectSubmitDouBaoOutput studentAssessDouBaoOutput = StudentProjectSubmitAnalysisJson(resDataJson);
                    if (studentAssessDouBaoOutput != null)
                    {
                        //保存提交记录
                        AI_StudentDoProjectTask studentDoProjectTask = new AI_StudentDoProjectTask()
                        {
                            Id = IdHelper.GetId(),
                            ProjectId = projectStageTask.ProjectId,
                            ProjectStageId = projectStageTask.ProjectStageId,
                            ProjectStageTaskId = projectStageTask.ProjectStageTaskId,
                            Score = studentAssessDouBaoOutput.Score,
                            Level = BusinessUtil.GetAvgLevel(studentAssessDouBaoOutput.Score),
                            AssessmentResult = !string.IsNullOrEmpty(studentAssessDouBaoOutput.AssessmentResult) ? studentAssessDouBaoOutput.AssessmentResult : "暂无评估结果!",
                            StudentId = input.StudentId,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsStandard = true
                        };
                        studentDoProjectTask.Order = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>().Where(p => p.ProjectStageTaskId == projectStageTask.ProjectStageTaskId && p.StudentId == input.StudentId && p.IsDeleted == false).CountAsync() + 1;
                        //验证是否达到准
                        // 1.任务评估
                        bool taskPass = projectStageTask.TaskIsAssessment ? studentDoProjectTask.Score >= projectStageTask.TaskAssessmentScore : true;
                        // 2.组件评估
                        bool groupPass = projectStageTask.GroupIsAssessment ? studentDoProjectTask.Score >= projectStageTask.GroupAssessmentScore : true;
                        // 3. 两者同时满足才算达标
                        studentDoProjectTask.IsStandard = taskPass == true && groupPass == true;
                        await DBSqlSugar.Insertable(studentDoProjectTask).ExecuteCommandAsync();

                        //高频主题保存
                        if (studentAssessDouBaoOutput.QuestionIds != null && studentAssessDouBaoOutput.QuestionIds.Count > 0)
                        {
                            List<AI_StudentDoProjectTaskQuestion> studentDoProjectTaskQuestions = new List<AI_StudentDoProjectTaskQuestion>();
                            foreach (var questionId in studentAssessDouBaoOutput.QuestionIds)
                            {
                                studentDoProjectTaskQuestions.Add(new AI_StudentDoProjectTaskQuestion()
                                {
                                    Id = IdHelper.GetId(),
                                    ProjectId = projectStageTask.ProjectId,
                                    ProjectStageId = projectStageTask.ProjectStageId,
                                    ProjectStageTaskId = projectStageTask.ProjectStageTaskId,
                                    StudentId = input.StudentId,
                                    QuestionId = questionId,
                                    StudentDoProjectTaskId = studentDoProjectTask.Id,
                                    CreateTime = DateTime.Now,
                                    Creator = input.StudentId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.StudentId,
                                    IsDeleted = false
                                });
                            }
                            await DBSqlSugar.Insertable(studentDoProjectTaskQuestions).ExecuteCommandAsync();
                        }

                        //保存会话记录
                        List<AIDialogueASKFileInfo> fileInfos = new List<AIDialogueASKFileInfo>();
                        foreach (var file in input.Files)
                        {
                            fileInfos.Add(new AIDialogueASKFileInfo()
                            {
                                FileName = file.FileName,
                                FileUrl = file.FileUrl
                            });
                        }
                        AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                        {
                            Files = fileInfos
                        };
                        AI_DialogueContentRecord aI_DialogueContentRecord = new AI_DialogueContentRecord()
                        {
                            Id = IdHelper.GetId(),
                            Key = studentProjectTaskKey,
                            Ask = aIDialogueASKDto.ToJsonString(),
                            Answer = "---",
                            CreateTime = DateTime.Now,
                            MessageType = 2,
                            BusinessId = projectStageTask.ProjectStageTaskId,
                            IsDeleted = false
                        };
                        await DBSqlSugar.Insertable(aI_DialogueContentRecord).ExecuteCommandAsync();

                        _agentCommonCacheService.DelRedisLock(key);
                        return new StudentAssessOutput()
                        {
                            IsStandard = studentDoProjectTask.IsStandard,
                            AssessmentResult = studentDoProjectTask.AssessmentResult
                        };
                    }
                    else
                    {
                        throw new Exception("提交异常!");
                    }
                }
                catch (Exception ex)
                {
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
            else
            {
                throw new BusException("数据处理中!", 801);
            }
        }

        /// <summary>
        /// 学生端情景对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task StudentDialogue(StudentDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取项目化实践阶段任务基础信息
                string projectStageTaskSql = @"SELECT
                                                	task.Id AS ProjectStageTaskId,
                                                	stage.Id AS ProjectStageId,
                                                	agent.Id AS ProjectId,
                                                	agentBase.Id AS AgentId,
                                                	task.Demand,
                                                    task.Name,
                                                    task.Target,
                                                	model.Modelkey
                                                FROM
                                                	AI_ProjectStageTask task WITH ( NOLOCK )
                                                	INNER JOIN AI_ProjectStage stage WITH ( NOLOCK ) ON task.ProjectStageId= stage.id 
                                                	AND stage.IsDeleted= 0
                                                	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON stage.ProjectId= agent.Id 
                                                	AND agent.IsDeleted= 0
                                                	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                	AND agentBase.IsDeleted= 0
                                                	INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	AND model.IsDeleted= 0 
                                                WHERE
                                                	task.IsDeleted= 0 
                                                	AND task.TaskType= 2 
                                                	AND task.Id= @projectStageTaskId";
                ProjectStageTaskBaseInfoDto projectStageTask = await DBSqlSugar.SqlQueryable<ProjectStageTaskBaseInfoDto>(projectStageTaskSql)
                    .AddParameters(new { projectStageTaskId = input.ProjectStageTaskId }).FirstAsync();
                if (projectStageTask == null)
                {
                    throw new BusException("项目化实践阶段任务Id异常!");
                }
                if (string.IsNullOrEmpty(projectStageTask.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                //获取学生端项目化阶段任务key
                string studentProjectTaskKey = AIAgentKeys.GetStudentProjecKey(projectStageTask.AgentId, projectStageTask.ProjectStageTaskId, input.StudentId);
                if (string.IsNullOrEmpty(studentProjectTaskKey))
                {
                    throw new BusException("无法获取项目化实践阶段任务Key!");
                }

                //获取豆包上下文缓存Id
                bool isCreate = false;
                bool isTimeOut = false;
                AI_ContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ContextCacheKey>().Where(p => p.CacheKey == studentProjectTaskKey && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                if (contextCacheKey != null)
                {
                    //验证是否过期
                    if (contextCacheKey.ExpirationTime <= DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                        isTimeOut = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                //创建上下文缓存
                if (isCreate)
                {
                    //获取项目化实践阶段任务的高频问题
                    List<AI_ProjectStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ProjectStageTaskQuestion>()
                        .Where(p => p.ProjectStageTaskId == projectStageTask.ProjectStageTaskId && p.IsDeleted == false)
                        .Select(p => new AI_ProjectStageTaskQuestion()
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Describe = p.Describe
                        }).ToListAsync();
                    string taskQuestionsText = string.Empty;
                    foreach (var taskQuestion in taskQuestions)
                    {
                        taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                    }
                    if (string.IsNullOrEmpty(taskQuestionsText))
                    {
                        taskQuestionsText = "暂无";
                    }

                    string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                    {
                        Msg = $"一、任务名称:{projectStageTask.Name}。\n二、任务目标:{projectStageTask.Target}。\n三、对话要求:{projectStageTask.Demand}。\n四、主题:{taskQuestionsText}。",
                        TimeOut = 604800,
                        modelId = projectStageTask.Modelkey
                    });
                    if (string.IsNullOrEmpty(contextId))
                    {
                        throw new Exception("创建上下文缓存异常!");
                    }

                    contextCacheKey = new AI_ContextCacheKey()
                    {
                        Id = IdHelper.GetId(),
                        CacheKey = studentProjectTaskKey,
                        CacheId = contextId,
                        BusinessId = projectStageTask.ProjectStageTaskId,
                        CreateTime = DateTime.Now,
                        TimeOut = 604800,
                        Explain = "学生端项目化实践阶段任务情景对话Key",
                        ExpirationTime = DateTime.Now.AddSeconds(604800),
                        IsDeleted = false
                    };
                    await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                }

                //缓存过期逻辑
                if (isTimeOut)
                {
                    //获取历史对话记录
                    List<AI_DialogueContentRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_DialogueContentRecord>().Where(p => p.Key == studentProjectTaskKey && p.IsDeleted == false).OrderBy(p => p.CreateTime).With(SqlWith.NoLock).ToListAsync();
                    if (dialogueContentRecords.Count > 0)
                    {
                        //历史对话记录
                        string dialogueData = "学生历史问答记录如下:\n";
                        foreach (var dialogueContentRecord in dialogueContentRecords)
                        {
                            AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                            dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                             + $"学生问:{ask.AskText}。\n"
                                             + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                        }
                        dialogueData += "要求：后续学生问答结合历史记录产出你的回答。（注意:当前对话你只需要输出'收到'）";

                        //上下文对话（历史对话记录）
                        Func<string, Task> emptyDataHandler = async (data) =>
                        {
                            await Task.CompletedTask;
                        };
                        await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                        {
                            context_id = contextCacheKey.CacheId,
                            content = dialogueData,
                            role = "user",
                            modelId = projectStageTask.Modelkey
                        }, async (data) =>
                        {
                            await emptyDataHandler(data);
                        });

                        //更新过期时间
                        contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }
                }

                //上下文对话
                string message = string.Empty;
                //获取文件分析报告
                List<AI_FileAnalysis> fileAnalyses = await DBSqlSugar.Queryable<AI_FileAnalysis>().Where(p => p.Key == studentProjectTaskKey).OrderBy("CreateTime,OrderId").With(SqlWith.NoLock).ToListAsync();
                if (fileAnalyses.Count > 0)
                {
                    string fileAnalysesText = "文件分析报告如下：\n";
                    foreach (var item in fileAnalyses)
                    {
                        fileAnalysesText += $"第{fileAnalyses.IndexOf(item) + 1}份文件分析报告:{item.FileAnalysis}\n\n";
                    }
                    message += $"{fileAnalysesText}用户问题:{input.Msg}";
                }
                else
                {
                    message = input.Msg;
                }

                string msg = string.Empty;
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = message,
                    role = "user",
                    modelId = projectStageTask.Modelkey
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);

                    string json = data;
                    int startIndex = json.IndexOf('{');
                    json = json.Substring(startIndex);
                    JObject jsonObject = JObject.Parse(json);
                    string content = jsonObject["Content"]?.Value<string>();
                    if (content != null)
                    {
                        msg += content;
                    }
                }, cancellationToken);

                //更新过期时间
                contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                //保存会话记录
                AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                {
                    AskText = input.Msg,
                    AudioFile = new AIDialogueASKAudioFileInfo()
                    {
                        AudioUrl = input.AudioUrl,
                        Duration = input.Duration,
                    },
                    Files = fileAnalyses.Select(p => new AIDialogueASKFileInfo()
                    {
                        FileName = p.FileName,
                        FileUrl = p.FileUrl,
                        FileSize = p.FileSize,
                        FileType = p.FileType
                    }).ToList()
                };
                AI_DialogueContentRecord aI_DialogueContentRecord = new AI_DialogueContentRecord()
                {
                    Id = IdHelper.GetId(),
                    Key = studentProjectTaskKey,
                    Ask = aIDialogueASKDto.ToJsonString(),
                    Answer = msg,
                    CreateTime = DateTime.Now,
                    MessageType = 2,
                    BusinessId = projectStageTask.ProjectStageTaskId,
                    IsDeleted = false
                };
                await DBSqlSugar.Insertable(aI_DialogueContentRecord).ExecuteCommandAsync();

                //删除文件分析报告
                if (fileAnalyses.Count > 0)
                {
                    await DBSqlSugar.Deleteable<AI_FileAnalysis>().Where(p => p.Key == studentProjectTaskKey).ExecuteCommandAsync();
                }

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
        }

        /// <summary>
        /// 学生端情景对话提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentDialogueSubmitOutput> StudentDialogueSubmit(StudentDialogueSubmitInput input)
        {
            //缓存锁
            string key = input.StudentId + "|" + input.ProjectStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    //是否存在达标提交记录
                    AI_StudentDoProjectTask isDo = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>().Where(p => p.ProjectStageTaskId == input.ProjectStageTaskId && p.StudentId == input.StudentId && p.IsStandard == true && p.IsDeleted == false).FirstAsync();
                    if (isDo != null)
                    {
                        throw new BusException("已完成，无法重复提交!", 801);
                    }

                    //获取项目化实践阶段任务基础信息
                    string projectStageTaskSql = @"SELECT
                                                	task.Id AS ProjectStageTaskId,
                                                	stage.Id AS ProjectStageId,
                                                	agent.Id AS ProjectId,
                                                	agentBase.Id AS AgentId,
                                                	task.RoleSetting,
                                                	task.Demand,
                                                    task.ScoreStandard,
                                                	model.Modelkey,
                                                    task.TaskIsSubmit,
                                                    task.TaskIsAssessment,
                                                    task.TaskAssessmentScore,
                                                    task.GroupIsSubmit,
                                                    task.GroupIsAssessment,
                                                    task.GroupAssessmentScore,
                                                    task.Name,
                                                    task.Target
                                                FROM
                                                	AI_ProjectStageTask task WITH ( NOLOCK )
                                                	INNER JOIN AI_ProjectStage stage WITH ( NOLOCK ) ON task.ProjectStageId= stage.id 
                                                	AND stage.IsDeleted= 0
                                                	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON stage.ProjectId= agent.Id 
                                                	AND agent.IsDeleted= 0
                                                	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                	AND agentBase.IsDeleted= 0
                                                	INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	AND model.IsDeleted= 0 
                                                WHERE
                                                	task.IsDeleted= 0 
                                                	AND task.TaskType= 2 
                                                	AND task.Id= @projectStageTaskId";
                    ProjectStageTaskBaseInfoDto projectStageTask = await DBSqlSugar.SqlQueryable<ProjectStageTaskBaseInfoDto>(projectStageTaskSql)
                        .AddParameters(new { projectStageTaskId = input.ProjectStageTaskId }).FirstAsync();
                    if (projectStageTask == null)
                    {
                        throw new BusException("项目化实践阶段任务Id异常!");
                    }
                    if (string.IsNullOrEmpty(projectStageTask.Modelkey))
                    {
                        throw new BusException("智能体配置的模型异常!");
                    }

                    //获取学生端项目化阶段任务key
                    string studentProjectTaskKey = AIAgentKeys.GetStudentProjecKey(projectStageTask.AgentId, projectStageTask.ProjectStageTaskId, input.StudentId);
                    if (string.IsNullOrEmpty(studentProjectTaskKey))
                    {
                        throw new BusException("无法获取项目化实践阶段任务Key!");
                    }

                    //获取豆包上下文缓存Id
                    bool isCreate = false;
                    bool isTimeOut = false;
                    AI_ContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ContextCacheKey>().Where(p => p.CacheKey == studentProjectTaskKey && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (contextCacheKey != null)
                    {
                        //验证是否过期
                        if (contextCacheKey.ExpirationTime <= DateTime.Now)
                        {
                            contextCacheKey.IsDeleted = true;
                            await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                            isCreate = true;
                            isTimeOut = true;
                        }
                    }
                    else
                    {
                        isCreate = true;
                    }

                    //创建上下文缓存
                    if (isCreate)
                    {
                        //获取项目化实践阶段任务的高频问题
                        List<AI_ProjectStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ProjectStageTaskQuestion>()
                            .Where(p => p.ProjectStageTaskId == projectStageTask.ProjectStageTaskId && p.IsDeleted == false)
                            .Select(p => new AI_ProjectStageTaskQuestion()
                            {
                                Id = p.Id,
                                Name = p.Name,
                                Describe = p.Describe
                            }).ToListAsync();
                        string taskQuestionsText = string.Empty;
                        foreach (var taskQuestion in taskQuestions)
                        {
                            taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                        }
                        if (string.IsNullOrEmpty(taskQuestionsText))
                        {
                            taskQuestionsText = "暂无";
                        }

                        string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                        {
                            Msg = $"一、任务名称:{projectStageTask.Name}。\n二、任务目标:{projectStageTask.Target}。\n三、对话要求:{projectStageTask.Demand}。\n四、主题:{taskQuestionsText}。",
                            TimeOut = 604800,
                            modelId = projectStageTask.Modelkey
                        });
                        if (string.IsNullOrEmpty(contextId))
                        {
                            throw new Exception("创建上下文缓存异常!");
                        }

                        contextCacheKey = new AI_ContextCacheKey()
                        {
                            Id = IdHelper.GetId(),
                            CacheKey = studentProjectTaskKey,
                            CacheId = contextId,
                            BusinessId = projectStageTask.ProjectStageTaskId,
                            CreateTime = DateTime.Now,
                            TimeOut = 604800,
                            Explain = "学生端项目化实践阶段任务情景对话Key",
                            ExpirationTime = DateTime.Now.AddSeconds(604800),
                            IsDeleted = false
                        };
                        await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                    }

                    //获取提交指令
                    AI_Directive submitDirective = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentProjectDialogueSubmit_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (submitDirective == null)
                    {
                        throw new BusException("无法获取提交系统提示词,请联系管理员!");
                    }

                    //提交信息
                    string submitMsg = submitDirective.Directive;
                    submitMsg = submitMsg.Replace("{评分标准}", projectStageTask.ScoreStandard)
                        .Replace("{评估角色设定}", projectStageTask.RoleSetting);

                    //缓存过期逻辑
                    if (isTimeOut)
                    {
                        //获取历史对话记录
                        List<AI_DialogueContentRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_DialogueContentRecord>().Where(p => p.Key == studentProjectTaskKey && p.IsDeleted == false).OrderBy(p => p.CreateTime).With(SqlWith.NoLock).ToListAsync();
                        if (dialogueContentRecords.Count > 0)
                        {
                            //历史对话记录
                            string dialogueData = "\n5.学生历史问答记录如下:\n";
                            foreach (var dialogueContentRecord in dialogueContentRecords)
                            {
                                AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                                dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                                 + $"学生问:{ask.AskText}。\n"
                                                 + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                            }
                            submitMsg = submitMsg + dialogueData;
                        }
                    }

                    //上下文对话
                    string resultData = string.Empty;
                    Func<string, Task> emptyDataHandler = async (data) =>
                    {
                        await Task.CompletedTask;
                    };
                    await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                    {
                        context_id = contextCacheKey.CacheId,
                        content = submitMsg,
                        role = "user",
                        modelId = projectStageTask.Modelkey
                    }, async (data) =>
                    {
                        await emptyDataHandler(data);

                        string json = data;
                        int startIndex = json.IndexOf('{');
                        json = json.Substring(startIndex);
                        JObject jsonObject = JObject.Parse(json);
                        string content = jsonObject["Content"]?.Value<string>();
                        if (content != null)
                        {
                            resultData += content;
                        }
                    });

                    //豆包输出
                    StudentProjectSubmitDouBaoOutput dialogueSubmitDouBaoOutput = StudentProjectSubmitAnalysisJson(resultData);
                    if (dialogueSubmitDouBaoOutput != null)
                    {
                        //保存提交记录
                        AI_StudentDoProjectTask studentDoProjectTask = new AI_StudentDoProjectTask()
                        {
                            Id = IdHelper.GetId(),
                            ProjectId = projectStageTask.ProjectId,
                            ProjectStageId = projectStageTask.ProjectStageId,
                            ProjectStageTaskId = projectStageTask.ProjectStageTaskId,
                            Score = dialogueSubmitDouBaoOutput.Score,
                            Level = BusinessUtil.GetAvgLevel(dialogueSubmitDouBaoOutput.Score),
                            AssessmentResult = !string.IsNullOrEmpty(dialogueSubmitDouBaoOutput.AssessmentResult) ? dialogueSubmitDouBaoOutput.AssessmentResult : "暂无评估结果!",
                            StudentId = input.StudentId,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsStandard = true
                        };
                        studentDoProjectTask.Order = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>().Where(p => p.ProjectStageTaskId == projectStageTask.ProjectStageTaskId && p.StudentId == input.StudentId && p.IsDeleted == false).CountAsync() + 1;
                        //验证是否达到准
                        // 1.任务评估
                        bool taskPass = projectStageTask.TaskIsAssessment ? studentDoProjectTask.Score >= projectStageTask.TaskAssessmentScore : true;
                        // 2.组件评估
                        bool groupPass = projectStageTask.GroupIsAssessment ? studentDoProjectTask.Score >= projectStageTask.GroupAssessmentScore : true;
                        // 3. 两者同时满足才算达标
                        studentDoProjectTask.IsStandard = taskPass == true && groupPass == true;
                        await DBSqlSugar.Insertable(studentDoProjectTask).ExecuteCommandAsync();

                        //高频主题保存
                        if (dialogueSubmitDouBaoOutput.QuestionIds != null && dialogueSubmitDouBaoOutput.QuestionIds.Count > 0)
                        {
                            List<AI_StudentDoProjectTaskQuestion> studentDoProjectTaskQuestions = new List<AI_StudentDoProjectTaskQuestion>();
                            foreach (var questionId in dialogueSubmitDouBaoOutput.QuestionIds)
                            {
                                studentDoProjectTaskQuestions.Add(new AI_StudentDoProjectTaskQuestion()
                                {
                                    Id = IdHelper.GetId(),
                                    ProjectId = projectStageTask.ProjectId,
                                    ProjectStageId = projectStageTask.ProjectStageId,
                                    ProjectStageTaskId = projectStageTask.ProjectStageTaskId,
                                    StudentId = input.StudentId,
                                    QuestionId = questionId,
                                    StudentDoProjectTaskId = studentDoProjectTask.Id,
                                    CreateTime = DateTime.Now,
                                    Creator = input.StudentId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.StudentId,
                                    IsDeleted = false
                                });
                            }
                            await DBSqlSugar.Insertable(studentDoProjectTaskQuestions).ExecuteCommandAsync();
                        }

                        _agentCommonCacheService.DelRedisLock(key);
                        return new StudentDialogueSubmitOutput()
                        {
                            IsStandard = studentDoProjectTask.IsStandard,
                            AssessmentResult = studentDoProjectTask.AssessmentResult
                        };
                    }
                    else
                    {
                        throw new Exception("提交异常!");
                    }
                }
                catch (Exception ex)
                {
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
            else
            {
                throw new BusException("数据处理中!", 801);
            }
        }

        /// <summary>
        /// 学生端知识问答
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task StudentKnowledge(StudentKnowledgeInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取项目化实践阶段任务基础信息
                string projectStageTaskSql = @"SELECT
                                                	task.Id AS ProjectStageTaskId,
                                                	stage.Id AS ProjectStageId,
                                                	agent.Id AS ProjectId,
                                                	agentBase.Id AS AgentId,
                                                	task.RoleSetting,
                                                	task.Scope,
                                                	model.Modelkey
                                                FROM
                                                	AI_ProjectStageTask task WITH ( NOLOCK )
                                                	INNER JOIN AI_ProjectStage stage WITH ( NOLOCK ) ON task.ProjectStageId= stage.id 
                                                	AND stage.IsDeleted= 0
                                                	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON stage.ProjectId= agent.Id 
                                                	AND agent.IsDeleted= 0
                                                	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                	AND agentBase.IsDeleted= 0
                                                	INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	AND model.IsDeleted= 0 
                                                WHERE
                                                	task.IsDeleted= 0 
                                                	AND task.TaskType= 3 
                                                	AND task.Id= @projectStageTaskId";
                ProjectStageTaskBaseInfoDto projectStageTask = await DBSqlSugar.SqlQueryable<ProjectStageTaskBaseInfoDto>(projectStageTaskSql)
                    .AddParameters(new { projectStageTaskId = input.ProjectStageTaskId }).FirstAsync();
                if (projectStageTask == null)
                {
                    throw new BusException("项目化实践阶段任务Id异常!");
                }
                if (string.IsNullOrEmpty(projectStageTask.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                //获取学生端项目化阶段任务key
                string studentProjectTaskKey = AIAgentKeys.GetStudentProjecKey(projectStageTask.AgentId, projectStageTask.ProjectStageTaskId, input.StudentId);
                if (string.IsNullOrEmpty(studentProjectTaskKey))
                {
                    throw new BusException("无法获取项目化实践阶段任务Key!");
                }

                //获取豆包上下文缓存Id
                bool isCreate = false;
                bool isTimeOut = false;
                AI_ContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ContextCacheKey>().Where(p => p.CacheKey == studentProjectTaskKey && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                if (contextCacheKey != null)
                {
                    //验证是否过期
                    if (contextCacheKey.ExpirationTime <= DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                        isTimeOut = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                //创建上下文缓存
                if (isCreate)
                {
                    string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                    {
                        Msg = $"一、角色设定:{projectStageTask.RoleSetting}。\n二、问答范围:{projectStageTask.Scope}。",
                        TimeOut = 604800,
                        modelId = projectStageTask.Modelkey
                    });
                    if (string.IsNullOrEmpty(contextId))
                    {
                        throw new Exception("创建上下文缓存异常!");
                    }

                    contextCacheKey = new AI_ContextCacheKey()
                    {
                        Id = IdHelper.GetId(),
                        CacheKey = studentProjectTaskKey,
                        CacheId = contextId,
                        BusinessId = projectStageTask.ProjectStageTaskId,
                        CreateTime = DateTime.Now,
                        TimeOut = 604800,
                        Explain = "学生端项目化实践阶段任务知识问答Key",
                        ExpirationTime = DateTime.Now.AddSeconds(604800),
                        IsDeleted = false
                    };
                    await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                }

                //缓存过期逻辑
                if (isTimeOut)
                {
                    //获取历史对话记录
                    List<AI_DialogueContentRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_DialogueContentRecord>().Where(p => p.Key == studentProjectTaskKey && p.IsDeleted == false).OrderBy(p => p.CreateTime).With(SqlWith.NoLock).ToListAsync();
                    if (dialogueContentRecords.Count > 0)
                    {
                        //历史对话记录
                        string dialogueData = "学生历史问答记录如下:\n";
                        foreach (var dialogueContentRecord in dialogueContentRecords)
                        {
                            AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                            dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                             + $"学生问:{ask.AskText}。\n"
                                             + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                        }
                        dialogueData += "要求：后续学生问答结合历史记录产出你的回答。（注意:当前对话你只需要输出'收到'）";

                        //上下文对话（历史对话记录）
                        Func<string, Task> emptyDataHandler = async (data) =>
                        {
                            await Task.CompletedTask;
                        };
                        await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                        {
                            context_id = contextCacheKey.CacheId,
                            content = dialogueData,
                            role = "user",
                            modelId = projectStageTask.Modelkey
                        }, async (data) =>
                        {
                            await emptyDataHandler(data);
                        });

                        //更新过期时间
                        contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }
                }

                //上下文对话
                string msg = string.Empty;
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = input.Msg,
                    role = "user",
                    modelId = projectStageTask.Modelkey
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);

                    string json = data;
                    int startIndex = json.IndexOf('{');
                    json = json.Substring(startIndex);
                    JObject jsonObject = JObject.Parse(json);
                    string content = jsonObject["Content"]?.Value<string>();
                    if (content != null)
                    {
                        msg += content;
                    }
                }, cancellationToken);

                //更新过期时间
                contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                //保存会话记录
                AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                {
                    AskText = input.Msg,
                    AudioFile = new AIDialogueASKAudioFileInfo()
                    {
                        AudioUrl = input.AudioUrl,
                        Duration = input.Duration,
                    }
                };
                AI_DialogueContentRecord aI_DialogueContentRecord = new AI_DialogueContentRecord()
                {
                    Id = IdHelper.GetId(),
                    Key = studentProjectTaskKey,
                    Ask = aIDialogueASKDto.ToJsonString(),
                    Answer = msg,
                    CreateTime = DateTime.Now,
                    MessageType = 2,
                    BusinessId = projectStageTask.ProjectStageTaskId,
                    IsDeleted = false
                };
                await DBSqlSugar.Insertable(aI_DialogueContentRecord).ExecuteCommandAsync();

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
        }

        /// <summary>
        /// 解析项目化实践阶段任务提交后返回的Json
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="JsonException"></exception>
        /// <exception cref="Exception"></exception>
        public static StudentProjectSubmitDouBaoOutput StudentProjectSubmitAnalysisJson(string input)
        {
            try
            {
                var result = new StudentProjectSubmitDouBaoOutput();

                if (string.IsNullOrWhiteSpace(input))
                {
                    return result;
                }

                //先尝试直接解析
                try
                {
                    var jsonResult = JsonConvert.DeserializeObject<StudentProjectSubmitDouBaoOutput>(input);
                    if (jsonResult != null)
                    {
                        return jsonResult;
                    }
                }
                catch
                {

                }

                //尝试JSON解析（优先处理标准格式）
                try
                {
                    int startIndex = input.IndexOf('{');
                    int endIndex = input.LastIndexOf('}');
                    if (startIndex != -1 && endIndex != -1 && startIndex < endIndex)
                    {
                        string jsonPart = input.Substring(startIndex, endIndex - startIndex + 1);
                        // 清理JSON中的特殊引号
                        jsonPart = jsonPart.Replace('‘', '\'')
                                           .Replace('’', '\'')
                                           .Replace('“', '"')
                                           .Replace('”', '"');

                        // 尝试 deserialization
                        var jsonResult = JsonConvert.DeserializeObject<StudentProjectSubmitDouBaoOutput>(jsonPart);
                        if (jsonResult != null)
                        {
                            // 验证是否有效解析（至少有一个字段有值）
                            if (jsonResult.Score > 0 || !string.IsNullOrEmpty(jsonResult.Level) ||
                                !string.IsNullOrEmpty(jsonResult.AssessmentResult) ||
                                jsonResult.QuestionIds.Count > 0)
                            {
                                return jsonResult;
                            }
                        }
                    }
                }
                catch (Exception)
                {

                }

                // 正则提取 - 适配带双引号的字段名
                var inputCopy = input;

                // 1. 提取Score（匹配"Score": 0.0格式）
                var scoreMatch = Regex.Match(inputCopy, @"""Score""\s*:\s*(\d+\.?\d*)", RegexOptions.IgnoreCase);
                if (!scoreMatch.Success)
                {
                    // 兼容不带引号的字段名
                    scoreMatch = Regex.Match(inputCopy, @"Score\s*:\s*(\d+\.?\d*)", RegexOptions.IgnoreCase);
                }
                if (scoreMatch.Success && decimal.TryParse(scoreMatch.Groups[1].Value, out decimal score))
                {
                    result.Score = score;
                    inputCopy = inputCopy.Remove(scoreMatch.Index, scoreMatch.Length);
                }

                // 2. 提取Level（匹配"Level":"D"格式）
                var levelMatch = Regex.Match(inputCopy, @"""Level""\s*:\s*""([A-Za-z])""", RegexOptions.IgnoreCase);
                if (!levelMatch.Success)
                {
                    // 兼容不带引号的字段名
                    levelMatch = Regex.Match(inputCopy, @"Level\s*:\s*""?([A-Za-z])""?", RegexOptions.IgnoreCase);
                }
                if (levelMatch.Success)
                {
                    result.Level = levelMatch.Groups[1].Value;
                    inputCopy = inputCopy.Remove(levelMatch.Index, levelMatch.Length);
                }

                // 3. 提取QuestionIds（支持多个ID）
                var questionIdsMatch = Regex.Match(inputCopy, @"""QuestionIds""\s*:\s*\[\s*(.*?)\s*\]", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                if (!questionIdsMatch.Success)
                {
                    // 兼容不带引号的字段名
                    questionIdsMatch = Regex.Match(inputCopy, @"QuestionIds\s*:\s*\[\s*(.*?)\s*\]", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
                if (questionIdsMatch.Success)
                {
                    string idsContent = questionIdsMatch.Groups[1].Value;
                    // 提取所有引号中的ID
                    var idMatches = Regex.Matches(idsContent, @"""([^""]+)""");
                    foreach (Match idMatch in idMatches)
                    {
                        result.QuestionIds.Add(idMatch.Groups[1].Value);
                    }
                    inputCopy = inputCopy.Remove(questionIdsMatch.Index, questionIdsMatch.Length);
                }

                // 4. 提取AssessmentResult（处理长文本）
                var assessmentMatch = Regex.Match(inputCopy, @"""AssessmentResult""\s*:\s*""([^""]+)""", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                if (!assessmentMatch.Success)
                {
                    // 兼容不带引号的字段名
                    assessmentMatch = Regex.Match(inputCopy, @"AssessmentResult\s*:\s*""([^""]+)""", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
                if (assessmentMatch.Success)
                {
                    result.AssessmentResult = assessmentMatch.Groups[1].Value
                        .Replace('‘', '\'')
                        .Replace('’', '\'')
                        .Trim();
                }
                // 处理没有双引号包裹的情况
                else if (Regex.IsMatch(inputCopy, @"AssessmentResult", RegexOptions.IgnoreCase))
                {
                    var fallbackMatch = Regex.Match(inputCopy, @"(AssessmentResult)\s*[:=]\s*(.+?)(?=""Level""|""QuestionIds""|""Score""|Level|QuestionIds|Score|$)",
                        RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    if (fallbackMatch.Success)
                    {
                        result.AssessmentResult = fallbackMatch.Groups[2].Value
                            .Trim('"', '\'', ',', ';', ' ', '}')
                            .Replace('‘', '\'')
                            .Replace('’', '\'');
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new BusException($"解析失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 学生端项目化实践阶段任务提交未达标对话记录备份
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task StudentSubmitNoStandardBackups(StudentSubmitNoStandardBackupsInput input)
        {
            try
            {
                //获取学生端项目化阶段任务key
                string studentProjectTaskKey = AIAgentKeys.GetStudentProjecKey(input.AgentId, input.ProjectStageTaskId, input.StudentId);
                if (string.IsNullOrEmpty(studentProjectTaskKey))
                {
                    throw new BusException("无法获取项目化实践阶段任务Key!");
                }

                //获取学生端项目化阶段任务未达标key
                string studentProjectTaskNoStandardKey = AIAgentKeys.GetStudentProjectNoStandardKey(input.AgentId, input.ProjectStageTaskId, input.StudentId, input.TaskSubmitId);
                if (string.IsNullOrEmpty(studentProjectTaskNoStandardKey))
                {
                    throw new BusException("无法获取项目化实践阶段任务Key!");
                }

                //当前对话记录Key替换为未达标Key
                await DBSqlSugar.Updateable<AI_DialogueContentRecord>()
                    .SetColumns(it => new AI_DialogueContentRecord() { Key = studentProjectTaskNoStandardKey })
                    .Where(p => p.Key == studentProjectTaskKey)
                    .ExecuteCommandAsync();

                //当前缓存Key替换为未达标Key
                await DBSqlSugar.Updateable<AI_ContextCacheKey>()
                    .SetColumns(it => new AI_ContextCacheKey() { CacheKey = studentProjectTaskNoStandardKey })
                    .Where(p => p.CacheKey == studentProjectTaskKey)
                    .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取学生做项目化实践阶段任务结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentDoTaskResultOutput> GetStudentDoTaskResult(GetStudentDoTaskResultInput input)
        {
            try
            {
                GetStudentDoTaskResultOutput resultOutput = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>()
                    .Where(p => p.Id == input.TaskSubmitId && p.StudentId == input.StudentId && p.IsDeleted == false)
                    .Select(p => new GetStudentDoTaskResultOutput()
                    {
                        AssessmentResult = p.AssessmentResult
                    }).FirstAsync();

                return resultOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取学生端未达标列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentNoStandardListOutput> GetStudentNoStandardList(GetStudentNoStandardListInput input)
        {
            try
            {
                //获取项目化实践任务基础信息
                GetStudentNoStandardListOutput projectInfoOutput = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentNoStandardListOutput()
                    {
                        AgentId = p.AgentId,
                        ProjectId = p.Id,
                        ProjectName = p.Name,
                        ProjectIntroduce = p.Introduce,
                        ProjectLogo = p.TaskLogo
                    })
                    .FirstAsync();
                if (projectInfoOutput == null)
                {
                    throw new BusException("项目化实践Id异常!");
                }

                //获取学生做项目化实践任务未达标信息
                List<AI_StudentDoProjectTask> studentDoProjectTasks = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsStandard == false)
                    .Select(p => new AI_StudentDoProjectTask()
                    {
                        Id = p.Id,
                        ProjectStageId = p.ProjectStageId,
                        ProjectStageTaskId = p.ProjectStageTaskId,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .ToListAsync();

                //获取学生问答记录
                List<AI_DialogueContentRecord> dialogueContentRecords = new List<AI_DialogueContentRecord>();
                if (studentDoProjectTasks.Count > 0)
                {
                    List<string> taskKey = new List<string>();
                    foreach (var item in studentDoProjectTasks)
                    {
                        //获取学生端-项目化实践未达标key
                        string studentProjectNoStandard = AIAgentKeys.GetStudentProjectNoStandardKey(projectInfoOutput.AgentId, item.ProjectStageTaskId, input.StudentId, item.Id);
                        if (!string.IsNullOrEmpty(studentProjectNoStandard))
                        {
                            taskKey.Add(studentProjectNoStandard);
                        }
                    }
                    string dialogueContentRecordsSql = @"SELECT DISTINCT
                                                            	[key]
                                                            FROM
                                                            	AI_DialogueContentRecord WITH ( NOLOCK )
                                                            WHERE
                                                            	[Key] IN (@key1) 
                                                            	AND IsDeleted =0";
                    dialogueContentRecords = await DBSqlSugar.SqlQueryable<AI_DialogueContentRecord>(dialogueContentRecordsSql).AddParameters(new { key1 = taskKey }).ToListAsync();

                    //删除未备份的未达标提交记录
                    List<string> delStudentDoProjectTaskIds = new List<string>();
                    foreach (var item in studentDoProjectTasks)
                    {
                        //获取学生端-项目化实践未达标key
                        string studentProjectNoStandard = AIAgentKeys.GetStudentProjectNoStandardKey(projectInfoOutput.AgentId, item.ProjectStageTaskId, input.StudentId, item.Id);

                        //验证是否存在
                        if (dialogueContentRecords.Where(p => p.Key == studentProjectNoStandard).FirstOrDefault() == null)
                        {
                            delStudentDoProjectTaskIds.Add(item.Id);
                        }
                    }
                    if (delStudentDoProjectTaskIds.Count > 0)
                    {
                        studentDoProjectTasks = studentDoProjectTasks.Where(p => !delStudentDoProjectTaskIds.Contains(p.Id)).ToList();
                    }
                }

                if (studentDoProjectTasks.Count > 0)
                {
                    //项目化实践阶段
                    List<string> projectStageIds = studentDoProjectTasks.Select(p => p.ProjectStageId).Distinct().ToList();
                    List<GetStudentNoStandardListStageOutput> projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>()
                        .Where(p => projectStageIds.Contains(p.Id) && p.IsDeleted == false)
                        .Select(p => new GetStudentNoStandardListStageOutput()
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Order = p.Order
                        })
                        .OrderBy(p => p.Order)
                        .ToListAsync();

                    //项目化实践阶段任务
                    List<string> projectStageTaskIds = studentDoProjectTasks.Select(p => p.ProjectStageTaskId).Distinct().ToList();
                    List<GetStudentNoStandardListStageTaskOutput> projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>()
                        .Where(p => projectStageTaskIds.Contains(p.Id) && p.IsDeleted == false)
                        .Select(p => new GetStudentNoStandardListStageTaskOutput()
                        {
                            Id = p.Id,
                            ProjectStageId = p.ProjectStageId,
                            Name = p.Name,
                            Target = p.Target,
                            TaskType = p.TaskType,
                            Order = p.Order
                        })
                        .OrderBy(p => p.Order)
                        .ToListAsync();

                    //处理阶段信息
                    foreach (var stage in projectStages)
                    {
                        //获取当前阶段的任务
                        List<GetStudentNoStandardListStageTaskOutput> projectStageTask = projectStageTasks.Where(p => p.ProjectStageId == stage.Id).OrderBy(p => p.Order).ToList();
                        foreach (var task in projectStageTask)
                        {
                            task.TaskSubmitId = studentDoProjectTasks.Where(p => p.ProjectStageTaskId == task.Id).OrderBy(p => p.Order).Select(p => p.Id).ToList();
                        }
                        stage.ProjectStageTaskInfos = projectStageTask;
                    }

                    projectInfoOutput.ProjectStageInfos = projectStages;
                    return projectInfoOutput;
                }
                else
                {
                    return projectInfoOutput;
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }















    }
}
