﻿using Coldairarrow.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.CustomException;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_教师口语交际
    /// </summary>
    [Route("/AgentTeacherOralCommunication/[controller]/[action]")]
    [ApiController]
    public class AgentTeacherOralCommunicationController : ApiBaseController<IAgentTeacherOralCommunicationService>
    {
        #region DI
        private readonly IAgentTeacherOralCommunicationService _agentTeacherOralCommunicationService;
        public AgentTeacherOralCommunicationController(IAgentTeacherOralCommunicationService agentTeacherOralCommunicationService)
        {
            _agentTeacherOralCommunicationService = agentTeacherOralCommunicationService;
        }
        #endregion

        /// <summary>
        /// 教师保存/编辑口语交际
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<string> SaveTeacherOralCommunication(SaveTeacherOralCommunicationInput input)
        {
            //参数验证
            if (string.IsNullOrEmpty(input.Name))
            {
                throw new BusException("请输入任务名称!", 801);
            }
            else
            {
                if (input.Name.Length > 100)
                {
                    throw new BusException("任务名称文字限制100以内!", 801);
                }
            }

            if (string.IsNullOrEmpty(input.SubjectId))
            {
                throw new BusException("参数异常!", 801);
            }

            if (string.IsNullOrEmpty(input.GradeId))
            {
                throw new BusException("参数异常!", 801);
            }

            if (string.IsNullOrEmpty(input.Introduce))
            {
                throw new BusException("请输入项目背景介绍!", 801);
            }
            else
            {
                if (input.Introduce.Length > 1000)
                {
                    throw new BusException("项目背景介绍文字限制1000以内!", 801);
                }
            }

            if (string.IsNullOrEmpty(input.ChapterId))
            {
                throw new BusException("请选择教材章节!", 801);
            }

            if (string.IsNullOrEmpty(input.Prologue))
            {
                throw new BusException("请输入开场白!", 801);
            }
            else
            {
                if (input.Prologue.Length > 1000)
                {
                    throw new BusException("开场白文字限制1000以内!", 801);
                }
            }

            if (string.IsNullOrEmpty(input.Scene))
            {
                throw new BusException("请输入场景类型!", 801);
            }
            else
            {
                if (input.Scene.Length > 100)
                {
                    throw new BusException("场景类型文字限制100以内!", 801);
                }
            }

            if (!string.IsNullOrEmpty(input.SceneDetail))
            {
                if (input.SceneDetail.Length > 1000)
                {
                    throw new BusException("场景细节描述文字限制1000以内!", 801);
                }
            }

            if (string.IsNullOrEmpty(input.AgentId))
            {
                throw new BusException("智能体Id参数异常!", 801);
            }

            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id参数异常!", 801);
            }

            if (string.IsNullOrEmpty(input.SchoolId))
            {
                throw new BusException("学校Id参数异常!", 801);
            }

            if (input.InteractiveMode < 1 || input.InteractiveMode > 3)
            {
                throw new BusException("交互模式异常!", 801);
            }

            if (input.InteractiveMode == 1)
            {
                if (input.InstructTasks.Count <= 0)
                {
                    throw new BusException("请设置指令式任务!", 801);
                }
                else
                {
                    foreach (var instructTask in input.InstructTasks)
                    {
                        if (string.IsNullOrEmpty(instructTask.Name))
                        {
                            throw new BusException($"第{input.InstructTasks.IndexOf(instructTask) + 1}个指令式任务请输入名称!", 801);
                        }

                        if (string.IsNullOrEmpty(instructTask.InstructContent))
                        {
                            throw new BusException($"第{input.InstructTasks.IndexOf(instructTask) + 1}个指令式任务请输入指令内容!", 801);
                        }
                        else
                        {
                            if (instructTask.InstructContent.Length > 1000)
                            {
                                throw new BusException($"第{input.InstructTasks.IndexOf(instructTask) + 1}个指令式任务指令内容超长，文字长度限制1000以内!", 801);
                            }
                        }

                        if (string.IsNullOrEmpty(instructTask.Result))
                        {
                            throw new BusException($"第{input.InstructTasks.IndexOf(instructTask) + 1}个指令式任务请输入成果要求!", 801);
                        }
                        else
                        {
                            if (instructTask.Result.Length > 1000)
                            {
                                throw new BusException($"第{input.InstructTasks.IndexOf(instructTask) + 1}个指令式任务成果要求超长，文字长度限制1000以内!", 801);
                            }
                        }

                        if (instructTask.VerificationMode < 1 || instructTask.VerificationMode > 3)
                        {
                            throw new BusException($"第{input.InstructTasks.IndexOf(instructTask) + 1}个指令式任务验证方式异常!", 801);
                        }
                        else
                        {
                            if (instructTask.VerificationMode == 1)
                            {
                                if (instructTask.ImgUrls.Count <= 0)
                                {
                                    throw new BusException($"第{input.InstructTasks.IndexOf(instructTask) + 1}个指令式任务图片地址异常!", 801);
                                }
                                if (instructTask.ImgUrls.Count > 5)
                                {
                                    throw new BusException($"第{input.InstructTasks.IndexOf(instructTask) + 1}个指令式任务最多支持5张图片!", 801);
                                }
                                foreach (var imgUrl in instructTask.ImgUrls)
                                {
                                    if (imgUrl.Type != 1 && imgUrl.Type != 2)
                                    {
                                        throw new BusException($"第{input.InstructTasks.IndexOf(instructTask) + 1}个指令式任务图片类型异常!", 801);
                                    }
                                }
                            }

                            if (instructTask.VerificationMode == 2)
                            {
                                if (instructTask.HtmlFileInfo == null)
                                {
                                    throw new BusException($"第{input.InstructTasks.IndexOf(instructTask) + 1}个指令式任务动画地址异常!", 801);
                                }
                                if (string.IsNullOrEmpty(instructTask.HtmlFileInfo.Name) || string.IsNullOrEmpty(instructTask.HtmlFileInfo.Url))
                                {
                                    throw new BusException($"第{input.InstructTasks.IndexOf(instructTask) + 1}个指令式任务动画地址异常!", 801);
                                }
                            }
                        }
                    }
                }
            }

            if (input.InteractiveMode == 2)
            {
                if (input.DialogueTasks.Count <= 0)
                {
                    throw new BusException("请设置对话式任务!", 801);
                }
                else
                {
                    foreach (var dialogueTask in input.DialogueTasks)
                    {
                        if (string.IsNullOrEmpty(dialogueTask.Name))
                        {
                            throw new BusException($"第{input.DialogueTasks.IndexOf(dialogueTask) + 1}个对话式任务请输入任务名称!", 801);
                        }

                        if (string.IsNullOrEmpty(dialogueTask.DialogueTarget))
                        {
                            throw new BusException($"第{input.DialogueTasks.IndexOf(dialogueTask) + 1}个对话式任务请输入对话目标!", 801);
                        }
                        else
                        {
                            if (dialogueTask.DialogueTarget.Length > 1000)
                            {
                                throw new BusException($"第{input.DialogueTasks.IndexOf(dialogueTask) + 1}个指令式任务对话目标超长，文字长度限制1000以内!", 801);
                            }
                        }

                        if (string.IsNullOrEmpty(dialogueTask.ValidRespond))
                        {
                            throw new BusException($"第{input.DialogueTasks.IndexOf(dialogueTask) + 1}个对话式任务请输入有效回复标准!", 801);
                        }
                        else
                        {
                            if (dialogueTask.ValidRespond.Length > 1000)
                            {
                                throw new BusException($"第{input.DialogueTasks.IndexOf(dialogueTask) + 1}个指令式任务有效回复标准超长，文字长度限制1000以内!", 801);
                            }
                        }

                        if (!string.IsNullOrEmpty(dialogueTask.Asked))
                        {
                            if (dialogueTask.Asked.Length > 1000)
                            {
                                throw new BusException($"第{input.DialogueTasks.IndexOf(dialogueTask) + 1}个指令式任务追问话术超长，文字长度限制1000以内!", 801);
                            }
                        }
                    }
                }
            }

            return await _agentTeacherOralCommunicationService.SaveTeacherOralCommunication(input);
        }

        /// <summary>
        /// 获取智能体口语交际详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<AgentOralCommunicationDetailOutput> GetOralCommunicationDetail(AgentOralCommunicationDetailInput input)
        {
            if (string.IsNullOrEmpty(input.Id))
            {
                throw new BusException("参数异常!", 801);
            }
            return await _agentTeacherOralCommunicationService.GetOralCommunicationDetail(input);
        }

        /// <summary>
        /// 删除口语交际任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task DelOralCommunication(DelOralCommunicationInput input)
        {
            if (string.IsNullOrEmpty(input.AgentTaskId) || string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("参数异常!", 801);
            }
            await _agentTeacherOralCommunicationService.DelOralCommunication(input);
        }

        /// <summary>
        /// 智能体_教师端口语交际列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageReturn<AgentTeacherOralCommunicationListOutput>> GetOralCommunicationList(AgentTeacherOralCommunicationListInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId) || string.IsNullOrEmpty(input.SchoolId) || string.IsNullOrEmpty(input.AgentId) || string.IsNullOrEmpty(input.SubjectId) || string.IsNullOrEmpty(input.GradeId))
            {
                throw new BusException("参数异常!", 801);
            }
            if (input.PageIndex <= 0)
            {
                input.PageIndex = 1;
            }
            if (input.PageSize <= 0)
            {
                input.PageSize = 10;
            }
            if (!input.InteractiveMode.HasValue || (input.InteractiveMode < 0 || input.InteractiveMode > 3))
            {
                input.InteractiveMode = 0;
            }
            return await _agentTeacherOralCommunicationService.GetOralCommunicationList(input);
        }

        /// <summary>
        /// 智能体_教师端获取智能体任务分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task<GetOralCommunicationAnalyseOutput> GetOralCommunicationAnalyse(GetOralCommunicationAnalyseInput input)
        {
            if (string.IsNullOrEmpty(input.AgentTaskId) || string.IsNullOrEmpty(input.ClassId))
            {
                throw new BusException("参数异常!", 801);
            }
            return await _agentTeacherOralCommunicationService.GetOralCommunicationAnalyse(input);
        }

        /// <summary>
        /// 教师获取学生口语交际评估结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetOralCommunicationStudentResultOutput> GetOralCommunicationStudentResult(GetOralCommunicationStudentResultInput input)
        {
            if (string.IsNullOrEmpty(input.AgentTaskId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("参数异常!", 801);
            }
            return await _agentTeacherOralCommunicationService.GetOralCommunicationStudentResult(input);
        }

        /// <summary>
        /// 发布口语交际
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task PublishOralCommunication(PublishOralCommunicationInput input)
        {
            if (string.IsNullOrEmpty(input.Id))
            {
                throw new BusException("智能体任务Id参数异常!");
            }
            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id参数异常!");
            }
            if (input.ClassId.Count <= 0)
            {
                throw new BusException("请选择发布的班级Id!", 801);
            }
            if (input.TimeRange.Count != 2)
            {
                throw new BusException("任务周期异常!", 801);
            }
            else
            {
                if (input.TimeRange[0] >= input.TimeRange[1])
                {
                    throw new BusException("任务周期异常!", 801);
                }
            }
            await _agentTeacherOralCommunicationService.PublishOralCommunication(input);
        }

        /// <summary>
        /// 撤销发布口语交际
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task DelPublishOralCommunication(DelPublishOralCommunicationInput input)
        {
            if (string.IsNullOrEmpty(input.Id))
            {
                throw new BusException("智能体任务Id参数异常!");
            }
            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id参数异常!");
            }
            await _agentTeacherOralCommunicationService.DelPublishOralCommunication(input);
        }
    }
}
