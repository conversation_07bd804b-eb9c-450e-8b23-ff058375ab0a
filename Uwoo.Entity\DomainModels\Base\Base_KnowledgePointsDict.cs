﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Base
{
    /// <summary>
    /// 知识点字典表
    /// </summary>
    public class Base_KnowledgePointsDict
    {
        [SugarColumn(IsPrimaryKey =true)]
        public string Id { get; set; }

        public string AreaId { get; set; }

        /// <summary>
        /// 父级Id
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 知识点Id
        /// </summary>
        public string CodeId { get; set; }
        /// <summary>
        /// 知识点内容
        /// </summary>
        public string CodeContent { get; set; }
        /// <summary>
        /// 学科ID
        /// </summary>
        public string SubjectId { get; set; }

        /// <summary>
        /// 1市， 2区 3 校 
        /// </summary>
        public int? Type { get; set; }

        /// <summary>
        ///学段类型（1小学、2初中、3高中）
        /// </summary>
        public int? StageType { get; set; }

        /// <summary>
        /// 版本，和学年关联
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// 层级1-3
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 排序值
        /// </summary>
        public int Sort { get; set; }

        public bool IsDeleted { get; set; }

        public DateTime? CreateTime { get; set; }

        public string Creator { get; set; }

        public DateTime? ModifyTime { get; set; }

        public string Modifier { get; set; }
    }
}
