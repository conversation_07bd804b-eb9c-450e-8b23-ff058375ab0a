using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 阅读理解智能体学生端服务接口
    /// </summary>
    public interface IReadingComprehensionStudentService
    {
        /// <summary>
        /// 获取学生的阅读理解任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentReadingTaskListOutput> GetStudentReadingTaskList(StudentReadingTaskListInput input);

        /// <summary>
        /// 获取阅读理解任务详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentReadingTaskDetailsOutput> GetReadingTaskDetails(StudentReadingTaskDetailsInput input);

        #region 视频任务相关方法

        /// <summary>
        /// 记录视频观看状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<VideoWatchStatusOutput> RecordVideoWatchStatus(VideoWatchStatusInput input);

        /// <summary>
        /// 记录视频观看时长
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<VideoWatchDurationOutput> RecordVideoWatchDuration(VideoWatchDurationInput input);

        #endregion

        #region 文档任务相关方法

        /// <summary>
        /// 记录文档阅读状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<DocumentReadStatusOutput> RecordDocumentReadStatus(DocumentReadStatusInput input);

        #endregion

        #region 思维导图任务相关方法

        /// <summary>
        /// 提交思维导图任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<MindMapSubmitOutput> SubmitMindMap(MindMapSubmitInput input);

        #endregion

        #region 选词填空任务相关方法

        /// <summary>
        /// 提交选词填空任务（流式返回AI评价）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<WordFillSubmitOutput> SubmitWordFillTask(WordFillSubmitInput input);

        #endregion

        #region 情景对话任务相关方法

        /// <summary>
        /// 阅读理解情景对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task ReadingDialogue(ReadingDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 阅读理解情景对话提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<ReadingDialogueSubmitOutput> ReadingDialogueSubmit(ReadingDialogueSubmitInput input);

        #endregion

        #region 成果评估任务相关方法

        /// <summary>
        /// 阅读理解成果评估
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<ReadingAssessmentOutput> ReadingAssessment(ReadingAssessmentInput input);

        #endregion

        #region 知识问答任务相关方法

        /// <summary>
        /// 阅读理解知识问答
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task ReadingKnowledge(ReadingKnowledgeInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        #endregion

        /// <summary>
        /// 获取学生任务完成历史
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentTaskHistoryOutput> GetStudentTaskHistory(StudentTaskHistoryInput input);

        /// <summary>
        /// 获取学生做任务结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentDoTaskResultOutput> GetStudentDoTaskResult(GetStudentDoTaskResultInput input);

        /// <summary>
        /// 查询任务提交记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetTaskSubmitRecordsOutput> GetTaskSubmitRecords(GetTaskSubmitRecordsInput input);

        /// <summary>
        /// 获取学生端未达标列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentNoStandardListOutput> GetStudentNoStandardList(GetStudentNoStandardListInput input);

        /// <summary>
        /// 学生端阅读理解阶段任务提交未达标对话记录备份
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task StudentSubmitNoStandardBackups(StudentSubmitNoStandardBackupsInput input);
    }
}
