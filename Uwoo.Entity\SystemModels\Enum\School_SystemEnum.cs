﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Entity.SystemModels.Enum
{
    /// <summary>
    /// 学制
    /// </summary>
    public enum School_SystemEnum
    {
        /// <summary>
        /// 小学五年制
        /// </summary>
        [Description("小学五年制")]
        FivePrimarySchool = 1,

        /// <summary>
        /// 小学六年制
        /// </summary>
        [Description("小学六年制")]
        SixPrimarySchool = 2,

        /// <summary>
        /// 初中三年制
        /// </summary>
        [Description("初中三年制")]
        ThreeJuniorHigh = 3,

        /// <summary>
        /// 初中四年制
        /// </summary>
        [Description("初中四年制")]
        fourJuniorHigh = 4,

        /// <summary>
        /// 完全初中
        /// </summary>
        [Description("完全初中")]
        CompleteJuniorHigh = 5,

        /// <summary>
        /// 九年一贯制
        /// </summary>
        [Description("九年一贯制")]
        NineIntegratedSystem = 6,

        /// <summary>
        /// 初中三年制
        /// </summary>
        [Description("初中三年制")]
        ThreeJuniorHighSchool = 7,

        /// <summary>
        /// 高中三年制
        /// </summary>
        [Description("高中三年制")]
        ThreeHigh = 8,

        /// <summary>
        /// 十二年一贯制
        /// </summary>
        [Description("十二年一贯制")]
        TwelveConsistent = 9,

        /// <summary>
        /// 其他
        /// </summary>
        [Description("其他")]
        Other = 10,

    }
}
