using Autofac.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using System;
using NLog;
using NLog.Web;

namespace Uwoo.WebApi
{
	public class Program
	{
		public static void Main(string[] args)
		{
			AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
			AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);

			// NLog: setup the logger
			var logger = LogManager.Setup().LoadConfigurationFromAppSettings().GetCurrentClassLogger();
			try
			{
				var host = CreateHostBuilder(args).Build();
				#region kafkaϢ
				//if (AppSetting.Kafka.UseConsumer)
				//{
				//    using var scope = host.Services.CreateScope();
				//    var testConsumer = scope.ServiceProvider.GetService<IKafkaConsumer<string, string>>();
				//    testConsumer.Consume(res =>
				//    {
				//        Console.WriteLine($"recieve:{DateTime.Now.ToLongTimeString()}  value:{res.Message.Value}");
				//        //̬ ݴ Ȳ
				//        bool bl = DataHandle.AlarmData(res.Message.Value);
				//        //ص践رִCommit
				//        return bl;
				//    }, AppSetting.Kafka.Topics.TestTopic);
				//}
				logger.Info(host.ToString());
				logger.Info("service runing");
				#endregion
				host.Run();
			}
			catch (Exception exception)
			{
				// NLog: catch setup errors
				logger.Error(exception, "Stopped program because of exception");
				throw;
			}
			finally
			{
				// Ensure to flush and stop internal timers/threads before application-exit (Avoid segmentation fault on Linux)
				LogManager.Shutdown();
			}
		}

		public static IHostBuilder CreateHostBuilder(string[] args) =>
			   Host.CreateDefaultBuilder(args)
				   .UseServiceProviderFactory(new AutofacServiceProviderFactory())
				   .ConfigureWebHostDefaults(webBuilder =>
				   {
					   webBuilder
						.UseUrls("http://*:9910")
						.UseStartup<Startup>()
						.UseKestrel(option =>
						{
							option.Limits.KeepAliveTimeout = TimeSpan.FromMinutes(60);
							option.Limits.RequestHeadersTimeout = TimeSpan.FromMinutes(60);
						}).UseNLog();
				   });
	}
}
