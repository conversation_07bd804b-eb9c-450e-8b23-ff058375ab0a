﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 教师端阅读理解_下载学生成果Dto
    /// </summary>
    public class ReadingDownloadStudentAchievementDto
    {
        /// <summary>
        /// 任务名称
        /// </summary>
        public string? TaskName { get; set; }

        /// <summary>
        /// 学生名称
        /// </summary>
        public string? RealName { get; set; }

        /// <summary>
        /// 对话记录key（我们自己组成的缓存Key)
        /// </summary>
        public string? Key { get; set; }

        /// <summary>
        /// 问
        /// </summary>
        public string? Ask { get; set; }

        /// <summary>
        /// “问”结构化数据
        /// </summary>
        public AIDialogueASKDto AskInfo { get; set; } = new AIDialogueASKDto();
    }

    /// <summary>
    /// 教师端阅读理解_下载学生成果文件信息Dto
    /// </summary>
    public class ReadingDownloadStudentAchievementFileInfoDto
    {
        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 文件地址
        /// </summary>
        public string? FileUrl { get; set; }
    }
}
