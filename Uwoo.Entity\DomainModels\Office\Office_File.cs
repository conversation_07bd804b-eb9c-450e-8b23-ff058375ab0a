﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Office
{
    /// <summary>
    /// Office_File
    /// </summary>
    [Table("Office_File")]
    public class Office_File
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public long Id { get; set; }

        /// <summary>
        /// Office文件Id
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件下载地址
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// 文件大小(byte)
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// 文件版本
        /// </summary>
        public int Version { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public string CreatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 文件来源类型
        /// </summary>
        public int? SourceType { get; set; }
    }
}
