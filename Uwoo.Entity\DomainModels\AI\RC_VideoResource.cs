using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 阅读理解视频资源表
    /// </summary>
    [SugarTable("RC_VideoResource")]
    public class RC_VideoResource
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 阅读理解项目阶段任务Id
        /// </summary>
        public string ReadingProjectStageTaskId { get; set; }

        /// <summary>
        /// 视频标题
        /// </summary>
        public string VideoTitle { get; set; }

        /// <summary>
        /// 视频描述
        /// </summary>
        public string VideoDescription { get; set; }

        /// <summary>
        /// 视频资源地址
        /// </summary>
        public string VideoUrl { get; set; }

        /// <summary>
        /// 视频时长
        /// </summary>
        public int? Duration { get; set; }

        /// <summary>
        /// 视频排序
        /// </summary>
        public int VideoOrder { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
