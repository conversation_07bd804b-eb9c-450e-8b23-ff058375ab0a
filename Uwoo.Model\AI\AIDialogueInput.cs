﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI对话接口入参
    /// </summary>
    public class AIDialogueInput
    {
        /// <summary>
        /// 对话内容
        /// </summary>
        public List<AIDialogueDouBaoMessageContent> content { get; set; } = new List<AIDialogueDouBaoMessageContent>();
        
        /// <summary>
        /// 模型Key
        /// </summary>
        public string? ModelKey { get; set; }
    }
}
