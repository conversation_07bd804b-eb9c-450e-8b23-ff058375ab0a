﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 发布项目化实践任务
    /// </summary>
    public class PublishProjectTaskToClassInput
    {
        /// <summary>
        /// 项目化实践任务Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 班级Id列表
        /// </summary>
        public List<string>? ClassIds { get; set; }

        /// <summary>
        /// 时间范围(下标0开始时间、下标1结束时间)
        /// </summary>
        public List<DateTime?>? TimeRange { get; set; }
    }
}
