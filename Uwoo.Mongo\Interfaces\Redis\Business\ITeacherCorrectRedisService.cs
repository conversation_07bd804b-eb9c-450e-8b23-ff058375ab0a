﻿// -- Function：ITeacherCorrectRedisService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/31 11:34

namespace Uwoo.Mongo.Interfaces.Redis.Business;

using Uwoo.Contracts.Redis;

/// <summary>
/// 教师批改缓存
/// </summary>
public interface ITeacherCorrectRedisService : IRedisService
{
    /// <summary>
    /// 获取正在批改的试卷
    /// </summary>
    /// <param name="teacherid">教师用户id</param>
    /// <returns></returns>
    Task<TeacherCorrectingViewModel> GetCorrectPaperAsync(string teacherid);

    /// <summary>
    /// 设置正在批改的试卷
    /// </summary>
    /// <param name="teacherid">教师用户id</param>
    /// <param name="paperid">试卷id</param>
    /// <param name="classid">班级id</param>
    /// <returns></returns>
    Task SetCorrectPaperAsync(string teacherid, string paperid, string classid);

    /// <summary>
    /// 获取正在批改的试卷
    /// </summary>
    /// <param name="teacherid">教师用户id</param>
    /// <returns></returns>
    TeacherCorrectingViewModel GetCorrectPaper(string teacherid);

    /// <summary>
    /// 设置正在批改的试卷
    /// </summary>
    /// <param name="teacherid">教师用户id</param>
    /// <param name="paperid">试卷id</param>
    /// <param name="classid">班级id</param>
    /// <returns></returns>
    void SetCorrectPaper(string teacherid, string paperid, string classid);

    /// <summary>
    /// 设置当前正在批改的学生和试卷信息
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <param name="paperid">试卷id</param>
    /// <param name="classid">班级id</param>
    /// <param name="studentid">学生id</param>
    /// <param name="pageno">批改的页码</param>
    /// <returns></returns>
    Task SetCorrectStudentAsync(string teacherid, string paperid, string classid, string studentid, int pageno);

    /// <summary>
    /// 获取当前正在批改的学生和试卷信息
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <returns></returns>
    Task<TeacherCorrectingStudent> GetCorrectStudentAsync(string teacherid);

    /// <summary>
    /// 获取当前正在批改的学生
    /// </summary>
    /// <param name="teacherid">教师用户id</param>
    /// <returns></returns>
    Task<string> HGetCorrectStudentAsync(string teacherid);

    /// <summary>
    /// 设置教师正在批改的学生
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <param name="studentid">学生id</param>
    /// <returns></returns>
    Task HSetCorrectStudentAsync(string teacherid, string studentid);

    /// <summary>
    /// 设置批阅学生状态
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="studentid">学生id</param>
    /// <param name="status">状态: 0.未开始 1.作答中 2.已识别 3.已批阅</param>
    /// <returns></returns>
    Task HSetCorrectStudentStatusAsync(string paperid, string studentid, int status);

    /// <summary>
    /// 获取批阅学生状态
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="studentid">学生id</param>
    /// <returns>状态: 0.未开始 1.作答中 2.已识别 3.已批阅</returns>
    Task<int> HGetCorrectStudentStatusAsync(string paperid, string studentid);

    /// <summary>
    /// 设置批阅学生状态（状态变更为2或3后 会将学生的笔迹归入到订正笔迹，只有在学生提交或教师批改情景时才会变更这个状态）
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="studentid">学生id</param>
    /// <param name="status">状态: 0.未开始 1.作答中 2.已识别(教师批改后) 3.已批阅(学生已提交，作答完毕)</param>
    /// <returns></returns>
    void HSetCorrectStudentStatus(string paperid, string studentid, int status);

    /// <summary>
    /// 获取批阅学生状态
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="studentid">学生id</param>
    /// <returns>状态: 0.未开始 1.作答中 2.已识别 3.已批阅</returns>
    int HGetCorrectStudentStatus(string paperid, string studentid);

    /// <summary>
    /// 设置学生订正试卷记录
    /// </summary>
    /// <param name="studentid">用户id</param>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task HSetStudentCorrectPaperAsync(string studentid, string paperid);

    /// <summary>
    /// 设置教师当前批改的班级
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <param name="classid">班级id</param>
    /// <returns></returns>
    bool SetCorrectClass(string teacherid, string classid);

    /// <summary>
    /// 获取教师当前批改的班级
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <returns></returns>
    string GetCorrectClass(string teacherid);
}