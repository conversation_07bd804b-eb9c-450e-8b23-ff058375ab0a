﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// AI_缓存Key
	/// </summary>
	[Table("AI_ContextCacheKey")]
    public class AI_ContextCacheKey
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 缓存Key（我们自己组成的缓存Key）唯一不可重复
        /// </summary>
        public string CacheKey { get; set; }

        /// <summary>
        /// 缓存Id（豆包的缓存Id）
        /// </summary>
        public string CacheId { get; set; }

        /// <summary>
        /// 备注说明（缓存Key的用途）
        /// </summary>
        public string Explain { get; set; }

        /// <summary>
        /// 过期时间(单位：秒)
        /// </summary>
        public int? TimeOut { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// 业务Id（如：智能体任务Id）
        /// </summary>
        public string BusinessId { get; set; }
    }
}
