﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 教案创建记录优化精调润色入参
    /// </summary>
    public class TeachingPlanCreateRecordOptimizeInput
    {
        /// <summary>
        /// 教案创建记录Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 优化方案（优化类型）
        /// </summary>
        public string? Scheme { get; set; }

        /// <summary>
        /// 将要优化的原文
        /// </summary>
        public string? OptimizeText { get; set; }

        /// <summary>
        /// 教师指令
        /// </summary>
        public string? Prompt { get; set; }
    }
}
