﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Utilities
{
	/// <summary>
	/// Type辅助类
	/// </summary>
	public class TypeHelper
	{
		/// <summary>
		/// 获取模型的属性名称和对应的值
		/// </summary>
		/// <typeparam name="T"></typeparam>
		/// <param name="model"></param>
		/// <returns></returns>
		public static List<(string Name, object Value)> GetPropertyNameValues<T>(T model) where T : class
		{
			var list = new List<(string Name, object Value)>();

			if (model != null)
			{
				var pArray = model.GetType().GetProperties();

				foreach (var p in pArray)
				{
					list.Add((p.Name, p.GetValue(model, null)));
				}
			}

			return list;
		}

		/// <summary>
		/// 获取模型属性和值，按指定分割符拼接
		/// </summary>
		/// <typeparam name="T"></typeparam>
		/// <param name="model"></param>
		/// <param name="separator"></param>
		/// <returns></returns>
		public static string GetPropertyNameValuesStringJoinKey<T>(T model, char separator = '_') where T : class
		{
			var key = string.Empty;
			var list = GetPropertyNameValues<T>(model);

			if (list.Count > 0)
			{
				var items = new List<string>();
				list.ForEach(z =>
				{
					items.Add($"{z.Name}{separator}{z.Value ?? ""}");
				});

				key = string.Join(separator, items);
			}

			return key;
		}
	}
}
