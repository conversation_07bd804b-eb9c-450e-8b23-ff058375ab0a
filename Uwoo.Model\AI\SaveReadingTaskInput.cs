using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 保存阅读理解任务输入
    /// </summary>
    public class SaveReadingTaskInput
    {
        /// <summary>
        /// 项目化实践任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 任务图标
        /// </summary>
        public string? TaskLogo { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string? GradeId { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 是否发布(默认false)
        /// </summary>
        public bool IsPublish { get; set; } = false;

        /// <summary>
        /// 发布的班级Id
        /// </summary>
        public List<string> ClassId { get; set; } = new List<string>();

        /// <summary>
        /// 发布时间范围(下标0开始、下标1结束)
        /// </summary>
        public List<DateTime> TimeRange { get; set; } = new List<DateTime>();

        /// <summary>
        /// 项目化实践阶段
        /// </summary>
        public List<ReadingProjectStageInfoInput> ProjectStageInfos { get; set; } = new List<ReadingProjectStageInfoInput>();
    }

    /// <summary>
    /// 阅读理解项目化实践阶段入参
    /// </summary>
    public class ReadingProjectStageInfoInput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string? Describe { get; set; }

        /// <summary>
        /// 项目化实践阶段任务
        /// </summary>
        public List<ReadingProjectStageTaskInfoInput> ProjectStageTaskInfos { get; set; } = new List<ReadingProjectStageTaskInfoInput>();
    }

    /// <summary>
    /// 阅读理解项目化实践阶段任务入参
    /// </summary>
    public class ReadingProjectStageTaskInfoInput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估(作品评估)、2:情景对话、3:知识问答、4:视频、5:文档、6:思维导图、7:选词填空）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 评分标准
        /// </summary>
        public string? ScoreStandard { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string? Demand { get; set; }

        /// <summary>
        /// 问答范围
        /// </summary>
        public string? Scope { get; set; }

        /// <summary>
        /// 角色设定
        /// </summary>
        public string? RoleSetting { get; set; }

        /// <summary>
        /// 开场白
        /// </summary>
        public string? Prologue { get; set; }

        /// <summary>
        /// 音色Id
        /// </summary>
        public string? ToneId { get; set; }

        /// <summary>
        /// 角色名称（用于AI展示名称）
        /// </summary>
        public string? RoleName { get; set; }

        /// <summary>
        /// 是否开启提交条件（组间任务设置）
        /// </summary>
        public bool GroupIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（组间任务设置）
        /// </summary>
        public bool GroupIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（组间任务设置）
        /// </summary>
        public decimal GroupAssessmentScore { get; set; }

        /// <summary>
        /// 是否开启提交条件（任务点设置）
        /// </summary>
        public bool TaskIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（任务点设置）
        /// </summary>
        public bool TaskIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（任务点设置）
        /// </summary>
        public decimal TaskAssessmentScore { get; set; }

        /// <summary>
        /// 任务高频问题
        /// </summary>
        public List<ReadingProjectStageTaskQuestionInfoInput> QuestionInfos { get; set; } = new List<ReadingProjectStageTaskQuestionInfoInput>();

        #region 新增任务类型特殊字段（4:视频、5:文档、6:思维导图、7:选词填空）

        #region 视频任务相关字段（TaskType=4）

        // 组间解锁条件
        /// <summary>
        /// 总观看时长要求（分钟）- 组间解锁条件
        /// </summary>
        public int? GroupTotalWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否要求观看所有视频 - 组间解锁条件
        /// </summary>
        public bool? GroupIsWatchAllVideos { get; set; }

        // 任务点解锁条件
        /// <summary>
        /// 总观看时长要求（分钟）- 任务点解锁条件
        /// </summary>
        public int? TaskTotalWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否要求观看所有视频 - 任务点解锁条件
        /// </summary>
        public bool? TaskIsWatchAllVideos { get; set; }

        /// <summary>
        /// 是否开启视频观看时长条件（组间任务设置）
        /// </summary>
        public bool GroupIsVideoWatchDuration { get; set; }


        /// <summary>
        /// 是否开启视频观看时长条件（任务点设置）
        /// </summary>
        public bool TaskIsVideoWatchDuration { get; set; }

        /// <summary>
        /// 视频资源列表（最多5个）
        /// </summary>
        public List<VideoResourceInput> VideoResources { get; set; } = new List<VideoResourceInput>();

        #endregion

        #region 文档任务相关字段

        // 组间解锁条件
        /// <summary>
        /// 是否需要阅读全部文档 - 组间解锁条件
        /// </summary>
        public bool? GroupIsReadAllDocuments { get; set; }

        // 任务点解锁条件
        /// <summary>
        /// 是否需要阅读全部文档 - 任务点解锁条件
        /// </summary>
        public bool? TaskIsReadAllDocuments { get; set; }

        /// <summary>
        /// 文档资源列表
        /// </summary>
        public List<DocumentResourceInput> DocumentResources { get; set; } = new List<DocumentResourceInput>();

        #endregion

        #region 思维导图任务相关字段（TaskType=6）

        // 思维导图任务暂无特殊配置字段

        #endregion

        #region 选词填空任务相关字段（TaskType=7）

        /// <summary>
        /// 题目内容
        /// </summary>
        public string? QuestionContent { get; set; }

        /// <summary>
        /// 正确答案（按填空顺序排列）
        /// </summary>
        public List<string> CorrectAnswers { get; set; } = new List<string>();

        /// <summary>
        /// 干扰项（最多5个）
        /// </summary>
        public List<string> DistractorWords { get; set; } = new List<string>();

        /// <summary>
        /// 自定义背景图片地址
        /// </summary>
        public string? CustomBackgroundImage { get; set; }

        #endregion

        #endregion
    }

    /// <summary>
    /// 阅读理解项目化实践阶段任务高频问题入参
    /// </summary>
    public class ReadingProjectStageTaskQuestionInfoInput
    {
        /// <summary>
        /// 高频问题Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 问题名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Describe { get; set; }
    }

    /// <summary>
    /// 视频资源输入
    /// </summary>
    public class VideoResourceInput
    {
        /// <summary>
        /// 视频ID（编辑时使用）
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 视频标题
        /// </summary>
        public string? VideoTitle { get; set; }

        /// <summary>
        /// 视频描述
        /// </summary>
        public string? VideoDescription { get; set; }

        /// <summary>
        /// 视频时长（秒）
        /// </summary>
        public int? Duration { get; set; } 

        /// <summary>
        /// 视频资源地址
        /// </summary>
        public string? VideoUrl { get; set; }

        /// <summary>
        /// 视频排序
        /// </summary>
        public int VideoOrder { get; set; }
    }

    /// <summary>
    /// 文档资源输入
    /// </summary>
    public class DocumentResourceInput
    {
        /// <summary>
        /// 文档ID（编辑时使用）
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 文档标题
        /// </summary>
        public string? DocumentTitle { get; set; }

        /// <summary>
        /// 文档描述
        /// </summary>
        public string? DocumentDescription { get; set; }

        /// <summary>
        /// 文档资源地址
        /// </summary>
        public string? DocumentUrl { get; set; }

        /// <summary>
        /// 文档类型（pdf, word, txt等）
        /// </summary>
        public string? DocumentType { get; set; }

        /// <summary>
        /// 文档排序
        /// </summary>
        public int DocumentOrder { get; set; }
    }
}
