﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI对话“问”格式
    /// </summary>
    public class AIDialogueASKDto
    {
        /// <summary>
        /// “问”文本
        /// </summary>
        public string? AskText { get; set; }

        /// <summary>
        /// 文件消息
        /// </summary>
        public List<AIDialogueASKFileInfo> Files { get; set; } = new List<AIDialogueASKFileInfo>();

        /// <summary>
        /// 语音文件
        /// </summary>
        public AIDialogueASKAudioFileInfo AudioFile { get; set; } = new AIDialogueASKAudioFileInfo();
    }

    /// <summary>
    /// AI对话“问”文件消息
    /// </summary>
    public class AIDialogueASKFileInfo
    {
        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 文件地址
        /// </summary>
        public string? FileUrl { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public string? FileSize { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        public string? FileType { get; set; }
    }

    /// <summary>
    /// AI对话“问”语音文件
    /// </summary>
    public class AIDialogueASKAudioFileInfo
    {
        /// <summary>
        /// 语音地址
        /// </summary>
        public string? AudioUrl { get; set; }

        /// <summary>
        /// 时长（单位:秒）
        /// </summary>
        public int Duration { get; set; }
    }
}
