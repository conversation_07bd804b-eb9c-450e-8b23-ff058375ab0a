﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DBManager;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Entity.DomainModels;
using Uwoo.System.IRepositories.Login;
using Uwoo.System.IServices.Login;
using UwooAgent.Entity.DomainModels.User;
using UwooAgent.System.IRepositories.Login;
using UwooAgent.System.IServices.Login;

namespace UwooAgent.System.Services.Login
{
    public class UniUserInfoService : ServiceBase<UniUserInfo, IUniUserInfoRepository>, IUniUserInfoService, IDependency
    {
        public void Add(UniUserInfo user)
        {
            DBSqlSugar.Insertable(user).ExecuteCommand();
        }

        public UniUserInfo GetUserByUid(string uid)
        {
            return DBSqlSugar.Queryable<UniUserInfo>().Where(x => x.UserId != null && x.UserId.Equals(uid)).First();
        }

        public void Update(UniUserInfo user)
        {
            DBSqlSugar.Updateable(user).ExecuteCommand();
        }

        /// <inheritdoc />
        public bool DeleteUser(string uid)
        {
            var user = DBSqlSugar.Queryable<UniUserInfo>().Where(x => x.UserId != null && x.UserId.Equals(uid)).FirstOrDefault();
            if (user == null)
            {
                return true;
            }

            var result = DBSqlSugar.Deleteable(user).ExecuteCommand();
            return result > 0;
        }

        /// <inheritdoc />
        public UniGroupInfo GetGroupByGid(int? groupid)
        {
            return DBSqlSugar.Queryable<UniGroupInfo>().Where(x => x.GroupId != null && x.GroupId == groupid).FirstOrDefault();
        }

        /// <inheritdoc />
        public bool DeleteGroup(int groupid)
        {
            var group = DBSqlSugar.Queryable<UniGroupInfo>().Where(x => x.GroupId != null && x.GroupId == groupid);
            if (group == null)
            {
                return true;
            }

            var result = DBSqlSugar.Deleteable<UniGroupInfo>(group).ExecuteCommand();
            return result > 0;
        }

        public void UpdateGroup(UniGroupInfo group)
        {
            DBSqlSugar.Updateable(group).ExecuteCommand();
        }

        public void AddGroup(UniGroupInfo groupInfo)
        {
            DBSqlSugar.Insertable(groupInfo).ExecuteCommand();
        }
    }
}
