﻿{
        loadMsg: '数据正在加载。。。。',
        //rownumbers: true,
        pageSize: 30,
        pageList: [10, 20, 30, 50, 80, 100, 200, 500, 1000],
        height: 340,
        key:'#key',
        url: '#Url',
        nameSpace:'',
        queryParams: {TableName:'#TableName',sort:'#SortName',Foots:'#Foots',Wheres:'{}'},
        pagination: true,
        striped:true,
        fileLink:#fileLink,
        richText:#richText,
        hasSection:false,
        cnName:'#cnName',
        calculateHeight:{id:'#lay-search-panel'},
        calculateWidth:{id:'#lay-search-panel'{#Width}},
    frozenColumns: [[
     { field: 'ck', checkbox: true }
    ]],
    //showFooter: true,
        columns: [[{#columns}]]
    }