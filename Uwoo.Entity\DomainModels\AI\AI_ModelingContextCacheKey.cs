﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// 智能体_建模上下文缓存Key
	/// </summary>
	[Table("AI_ModelingContextCacheKey")]
    public class AI_ModelingContextCacheKey
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 建模Id
        /// </summary>
        public string ModelingId { get; set; }

        /// <summary>
        /// 建模阶段Id
        /// </summary>
        public string ModelingStageId { get; set; }

        /// <summary>
        /// 建模阶段任务Id
        /// </summary>
        public string ModelingStageTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string StudentId { get; set; }

        /// <summary>
        /// 学生做建模阶段任务提交记录Id
        /// </summary>
        public string StudentDoModelingTaskId { get; set; }

        /// <summary>
        /// 缓存Id（豆包的缓存Id）
        /// </summary>
        public string CacheId { get; set; }

        /// <summary>
        /// 过期时间(单位: 秒)
        /// </summary>
        public int? TimeOut { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 是否属于备份
        /// </summary>
        public bool IsBackups { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
