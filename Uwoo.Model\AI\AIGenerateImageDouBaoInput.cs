﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI生成图片豆包入参
    /// </summary>
    public class AIGenerateImageDouBaoInput
    {
        /// <summary>
        /// 本次请求使用模型的 Model ID
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 用于生成图像的提示词
        /// </summary>
        public string? prompt { get; set; }

        /// <summary>
        /// 指定生成图像的返回格式。支持以下两种取值：
        /// "url"：以可下载的 JPEG 图片链接形式返回；
        /// "b64_json"：以 Base64 编码字符串的 JSON 格式返回图像数据。
        /// </summary>
        public string? response_format { get; set; }

        /// <summary>
        /// 生成图像的宽高像素，要求介于 [512 x 512, 2048 x 2048] 之间。
        /// 推荐可选的宽高：
        /// 1024x1024 （1:1）
        /// 864x1152 （3:4）
        /// 1152x864 （4:3）
        /// 1280x720 （16:9）
        /// 720x1280 （9:16）
        /// 832x1248 （2:3）
        /// 1248x832 （3:2）
        /// 1512x648 （21:9）
        /// </summary>
        public string? size { get; set; }

        /// <summary>
        /// 模型输出结果与prompt的一致程度，即生成图像的自由度；值越大，模型自由度越小，与用户输入的提示词相关性越强。取值范围：[1, 10] 之间的浮点数
        /// </summary>
        public int guidance_scale { get; set; }

        /// <summary>
        /// 是否在生成的图片中添加水印。
        /// false：不添加水印。
        /// true：在图片右下角添加“AI生成”字样的水印标识。
        /// </summary>
        public bool watermark { get; set; }
    }
}
