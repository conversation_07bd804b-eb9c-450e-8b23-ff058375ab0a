﻿// -- Function：MongoService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/11/07 17:53

namespace Uwoo.Mongo.Services.Mongo;

using System.Linq.Expressions;
using MongoDB.Driver;
using Uwoo.Mongo.Interfaces;
using Uwoo.Mongo.Interfaces.Lifecycle;
using Uwoo.Mongo.Models;

/// <summary>
/// mongo服务
/// </summary>
/// <typeparam name="T">mongo实体模型</typeparam>
public abstract partial class MongoService<T> : IMongoService<T> where T : MongoBaseModel, new()
{
    #region Implementation of IMongoService<T>

    /// <inheritdoc />
    public virtual T Get(long id)
    {
        return _mongo.Find(x => x.Mid.Equals(id)).Limit(1).FirstOrDefault();
    }

    /// <inheritdoc />
    public virtual T Get(Expression<Func<T, bool>> predicate = null)
    {
        Expression<Func<T, bool>> xpredicate = exp => true;
        predicate ??= xpredicate;
        return _mongo.Find(predicate).Limit(1).FirstOrDefault();
    }

    /// <inheritdoc />
    public virtual List<T> GetAll(Expression<Func<T, bool>> predicate = null, FindOptions options = null)
    {
        Expression<Func<T, bool>> xpredicate = exp => true;
        predicate ??= xpredicate;
        var list = _mongo.Find(predicate, options).ToList();
        return list.OrderBy(x => x.Mid).ToList();
    }

    /// <inheritdoc />
    public virtual void Add(T entity)
    {
        _mongo.InsertOne(entity);
    }

    /// <inheritdoc />
    public virtual void AddMany(IEnumerable<T> entities)
    {
        _mongo.InsertMany(entities);
    }

    /// <inheritdoc />
    public virtual bool Update(Expression<Func<T, bool>> predicate, T entity)
    {
        var result = _mongo.ReplaceOne(predicate, entity);
        return result.ModifiedCount > 0;
    }

    /// <inheritdoc />
    public virtual bool Update(long id, T entity)
    {
        var result = _mongo.ReplaceOne(x => x.Mid.Equals(id), entity);
        return result.ModifiedCount > 0;
    }

    /// <inheritdoc />
    public virtual bool Delete(long id)
    {
        var result = _mongo.DeleteOne(x => x.Mid.Equals(id));
        return result.DeletedCount > 0;
    }

    /// <inheritdoc />
    public virtual bool Delete(Expression<Func<T, bool>> predicate)
    {
        var result = _mongo.DeleteOne(predicate);
        return result.DeletedCount > 0;
    }

    /// <inheritdoc />
    public virtual bool DeleteMany(Expression<Func<T, bool>> predicate)
    {
        var result = _mongo.DeleteMany(predicate);
        return result.DeletedCount > 0;
    }

    #endregion
}