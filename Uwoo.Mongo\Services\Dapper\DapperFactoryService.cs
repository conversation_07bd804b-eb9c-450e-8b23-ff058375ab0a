﻿// -- Function：DapperFactoryService.cs
// --- Project：Uwoo.Mongo
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/11/16 16:55
namespace Uwoo.Mongo.Services.Dapper;

using System.Data;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Uwoo.Mongo.Interfaces.Dapper;

/// <inheritdoc />
public class DapperFactoryService : IDapperFactoryService
{
	private readonly IConfiguration _config;

	/// <inheritdoc />
	public DapperFactoryService(IConfiguration config)
	{
		_config = config;
	}

	/// <inheritdoc />
	public IDbConnection CreateConnection()
	{
		var sqlcfg = _config.GetSection("Database").Value;
		if (string.IsNullOrEmpty(sqlcfg))
		{
			throw new Exception("sql connection strings is empty");
		}

		return new SqlConnection(sqlcfg);
	}
}