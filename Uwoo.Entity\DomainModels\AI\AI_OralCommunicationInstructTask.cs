﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 智能体_口语交际任务信息（指令式）
    /// </summary>
    [Table("AI_OralCommunicationInstructTask")]
    public class AI_OralCommunicationInstructTask
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 口语交际Id
        /// </summary>
        public string OralCommunicationId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 指令内容
        /// </summary>
        public string InstructContent { get; set; }

        /// <summary>
        /// 验证方式（1图片、2动画(Html文件)、3文本)
        /// </summary>
        public int? VerificationMode { get; set; }

        /// <summary>
        /// 图片地址
        /// </summary>
        public string ImgUrl { get; set; }

        /// <summary>
        /// html文件地址
        /// </summary>
        public string HtmlFileUrl { get; set; }

        /// <summary>
        /// 成果要求
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int OrderId { get; set; }
    }
}
