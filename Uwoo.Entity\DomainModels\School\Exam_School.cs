﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.School
{
    /// <summary>
    /// 学校实体
    /// </summary>
    [Entity(TableCnName = "学校表", TableName = "Exam_School")]
    public partial class Exam_School : BaseEntity
    {
        /// <summary>
		/// ID
		/// </summary>
		[SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        ///学校名称
        /// </summary>
        public string SchoolName { get; set; }

        /// <summary>
        /// 学校类型 2 小學，3初中，5 高中
        /// </summary>
        public string SchoolType { get; set; }

        /// <summary>
        /// 学校电话
        /// </summary>
        public string SchoolTel { get; set; }

        /// <summary>
        ///地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 学校IP
        /// </summary>
        public string SchoolIp { get; set; }

        /// <summary>
        ///是否显示
        /// </summary>
        public Int32? IsShow { get; set; }

        /// <summary>
        /// 区
        /// </summary>
        public Int32? AreaId { get; set; }

        /// <summary>
        /// 是否Vip
        /// </summary>
        public bool? IsVip { get; set; }

        /// <summary>
        /// Vip开始时间
        /// </summary>
        public DateTime? VipStartTime { get; set; }

        /// <summary>
        /// Vip截止时间
        /// </summary>
        public DateTime? VipEndTime { get; set; }

        /// <summary>
        /// vip审核状态 1--审核中 2--审核通过 3--审核不通过
        /// </summary>
        public int? AuditStatus { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? Sort { get; set; }

        /// <summary>
        /// 是否是课题校
        /// </summary>
        public int? IsTopic { get; set; }

        /// <summary>
        ///是否是作业助手实验校
        /// </summary>
        public string TYId { get; set; }
        /// <summary>
        /// 微校网校认证 Id
        /// </summary>
        public string WxId { get; set; }

        /// <summary>
        /// 徐汇区认证Id
        /// </summary>
        public string XhId { get; set; }

        /// <summary>
        /// 黄埔区认证Id
        /// </summary>
        public string HpId { get; set; }

        /// <summary>
        ///统一用户认证名字
        /// </summary>
        public string TYSchoolName { get; set; }

        /// <summary>
        /// 是否市统一用户
        /// </summary>
        public int IsTY { get; set; }

        /// <summary>
        /// 是否测试 学校
        /// </summary>
        public Boolean? IsTest { get; set; }

        /// <summary>
        /// 是否开放新版教师分析
        /// </summary>
        public int? IsTanalyse { get; set; }
        /// <summary>
        /// 是否OCR识别
        /// </summary>
        public Boolean? IsOcr { get; set; }

        /// <summary>
        /// 学段
        /// </summary>
        public string Stage { get; set; }

        /// <summary>
        /// 是否有点阵笔权限(1是、0否)
        /// </summary>
        public int IsDZBQX { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string XJXXID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string XJXXDM { get; set; }

        /// <summary>
        /// 学校代码
        /// </summary>
        public string SchoolCode { get; set; }

        /// <summary>
        /// 作业助手实验校类型（区：1、市：2）
        /// </summary>
        public int? TYSchoolType { get; set; }

        /// <summary>
        /// 是否是专课专练学校(1是、0否)
        /// </summary>
        public int? IsZKZL { get; set; }

        /// <summary>
        /// 是否是好学效学校(1是、0否)
        /// </summary>
        public int? IsHXX { get; set; }

        /// <summary>
        /// 学校唯一ID(区分各个学校分校的情况)
        /// </summary>
        public string UniqueId { get; set; }

        /// <summary>
        /// 是否使用教材练习册(1是、0否)
        /// </summary>
        public int? IsJCLXC { get; set; }
    }
}
