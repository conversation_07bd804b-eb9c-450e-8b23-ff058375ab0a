using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI生成题目流式输出
    /// </summary>
    public class AIGenerateQuestionsStreamOutput
    {
        /// <summary>
        /// 本次请求的模型输出内容
        /// </summary>
        public List<AIGenerateQuestionsStreamChoicesOutput> choices { get; set; } = new List<AIGenerateQuestionsStreamChoicesOutput>();

        /// <summary>
        /// 本次请求创建时间的 Unix 时间戳（秒）
        /// </summary>
        public long created { get; set; }

        /// <summary>
        /// 本次请求的唯一标识
        /// </summary>
        public string? id { get; set; }

        /// <summary>
        /// 本次请求实际使用的模型名称和版本
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 本次请求的token用量（流式模式下可能为空）
        /// </summary>
        public AIGenerateQuestionsDouBaoUsageOutput? usage { get; set; }
    }

    /// <summary>
    /// 流式模式下的选择输出
    /// </summary>
    public class AIGenerateQuestionsStreamChoicesOutput
    {
        /// <summary>
        /// 模型停止生成 token 的原因
        /// </summary>
        public string? finish_reason { get; set; }

        /// <summary>
        /// 当前元素在 choices 列表的索引
        /// </summary>
        public int index { get; set; }

        /// <summary>
        /// 流式模式下的增量内容
        /// </summary>
        public AIGenerateQuestionsStreamDeltaOutput? delta { get; set; }
    }

    /// <summary>
    /// 流式模式下的增量内容
    /// </summary>
    public class AIGenerateQuestionsStreamDeltaOutput
    {
        /// <summary>
        /// 增量的消息内容
        /// </summary>
        public string? content { get; set; }

        /// <summary>
        /// 内容输出的角色
        /// </summary>
        public string? role { get; set; }
    }

    /// <summary>
    /// 流式题目解析器的状态
    /// </summary>
    public class QuestionStreamParser
    {
        /// <summary>
        /// 当前缓冲区内容
        /// </summary>
        public StringBuilder Buffer { get; set; } = new StringBuilder();

        /// <summary>
        /// 是否在题目块内
        /// </summary>
        public bool IsInQuestionBlock { get; set; } = false;

        /// <summary>
        /// 大括号计数
        /// </summary>
        public int BraceCount { get; set; } = 0;

        /// <summary>
        /// 当前题目索引
        /// </summary>
        public int CurrentQuestionIndex { get; set; } = 0;

        /// <summary>
        /// 是否在questions数组内
        /// </summary>
        public bool IsInQuestionsArray { get; set; } = false;

        /// <summary>
        /// 方括号计数
        /// </summary>
        public int BracketCount { get; set; } = 0;

        /// <summary>
        /// 重置解析器状态
        /// </summary>
        public void Reset()
        {
            Buffer.Clear();
            IsInQuestionBlock = false;
            BraceCount = 0;
            IsInQuestionsArray = false;
            BracketCount = 0;
        }

        /// <summary>
        /// 重置题目状态（保持数组状态）
        /// </summary>
        public void ResetQuestion()
        {
            Buffer.Clear();
            IsInQuestionBlock = false;
            BraceCount = 0;
            CurrentQuestionIndex++;
        }
    }
}
