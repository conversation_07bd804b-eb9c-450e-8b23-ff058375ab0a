1、现有后台与SqlSugar区别：SqlSugar版本只有SqlSugar一种操作数据库并且是独立同步维护的；现有版本(.net6后台文件夹)数据库访问是EF+Dapper
2、两个后台版本都会同步维护，按需使用其中一个版本即可；

3、现有代码迁移SqlSugar版本：
      （1）为了更好的开发体验，SqlSugar版本不对框架做兼容，而是重写了整个框架数据库访问，所以现有项目不能直接升级；
      （2）升级方式：
                           a、 现有后台项目不要了，数据库与前端不变；
                           b、SqlSugar版本项目，修改配置appsettings.json数据库链接
                           c、后台双击builder_run.bat启动项目，将现有数据表在代码生成器全部重新点一次【生成model】与【生成业务类】
                           d、 业务逻辑迁移：将【表Service】类中的代码直接复制过来即可，里面调用数据库操作大部分方法是一样的。如果有差异自行修改或者截图发群里


其他问题：截图发群里
                                