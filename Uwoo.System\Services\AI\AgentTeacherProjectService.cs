﻿using Coldairarrow.Util;
using MongoDB.Bson.Serialization.Serializers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.DBManager;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Model.CustomException;
using Uwoo.System.IServices;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.Base;
using UwooAgent.Entity.DomainModels.School;
using UwooAgent.Entity.DomainModels.Student;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.Model.SemesterTime;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;
using static MongoDB.Bson.Serialization.Serializers.SerializerHelper;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_教师端项目化实践
    /// </summary>
    public class AgentTeacherProjectService : ServiceBase<AI_AgentTask, IAgentTeacherProjectRepository>, IAgentTeacherProjectService, IDependency
    {
        #region DI

        private IBase_SemesterTimeService _semesterTimeService;
        private IAgentCommonService _agentCommonService;
        public AgentTeacherProjectService(IBase_SemesterTimeService semesterTimeService,
            IAgentCommonService agentCommonService)
        {
            _semesterTimeService = semesterTimeService;
            _agentCommonService = agentCommonService;
        }

        #endregion

        /// <summary>
        /// 保存/编辑项目化实践任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<string> SaveProjectTaskInfo(SaveProjectTaskInfoInput input)
        {
            try
            {
                if (string.IsNullOrEmpty(input.Id))
                {
                    //获取当前学年学期
                    NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                    if (nowSemesterTime == null)
                    {
                        throw new BusException("无法获取当前学年!", 801);
                    }

                    //智能体任务
                    AI_AgentTask agentTask = new AI_AgentTask()
                    {
                        Id = IdHelper.GetId(),
                        AgentId = input.AgentId,
                        Name = input.Name,
                        Introduce = input.Introduce,
                        Term = nowSemesterTime.NowTerm,
                        Year = nowSemesterTime.Year,
                        TaskLogo = input.TaskLogo,
                        CreateTime = DateTime.Now,
                        Creator = input.TeacherId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId,
                        IsDeleted = false,
                        SubjectId = input.SubjectId,
                        AgentTaskType = 2,
                        GradeId = input.GradeId
                    };
                    await DBSqlSugar.Insertable(agentTask).ExecuteCommandAsync();

                    //项目化实践阶段
                    List<AI_ProjectStage> addProjectStages = new List<AI_ProjectStage>();
                    //项目化实践阶段任务
                    List<AI_ProjectStageTask> addProjectStageTasks = new List<AI_ProjectStageTask>();
                    //项目化实践阶段任务高频问题
                    List<AI_ProjectStageTaskQuestion> taskQuestions = new List<AI_ProjectStageTaskQuestion>();
                    foreach (var projectStageInfo in input.ProjectStageInfos)
                    {
                        //项目化实践阶段
                        AI_ProjectStage projectStage = new AI_ProjectStage()
                        {
                            Id = IdHelper.GetId(),
                            ProjectId = agentTask.Id,
                            Name = projectStageInfo.Name,
                            Describe = projectStageInfo.Describe,
                            Order = input.ProjectStageInfos.IndexOf(projectStageInfo) + 1,
                            CreateTime = DateTime.Now,
                            Creator = input.TeacherId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.TeacherId,
                            IsDeleted = false
                        };
                        addProjectStages.Add(projectStage);

                        //项目化实践阶段任务处理
                        foreach (var projectStageTaskInfo in projectStageInfo.ProjectStageTaskInfos)
                        {
                            //项目化实践阶段任务
                            AI_ProjectStageTask projectStageTask = new AI_ProjectStageTask()
                            {
                                Id = IdHelper.GetId(),
                                ProjectStageId = projectStage.Id,
                                ProjectId = agentTask.Id,
                                TaskType = projectStageTaskInfo.TaskType,
                                Name = projectStageTaskInfo.Name,
                                Target = projectStageTaskInfo.Target,
                                ScoreStandard = projectStageTaskInfo.ScoreStandard,
                                Scope = projectStageTaskInfo.Scope,
                                RoleSetting = projectStageTaskInfo.RoleSetting,
                                Demand = projectStageTaskInfo.Demand,
                                GroupIsSubmit = projectStageTaskInfo.GroupIsSubmit,
                                GroupIsAssessment = projectStageTaskInfo.GroupIsAssessment,
                                GroupAssessmentScore = projectStageTaskInfo.GroupAssessmentScore,
                                TaskIsSubmit = projectStageTaskInfo.TaskIsSubmit,
                                TaskIsAssessment = projectStageTaskInfo.TaskIsAssessment,
                                TaskAssessmentScore = projectStageTaskInfo.TaskAssessmentScore,
                                Order = projectStageInfo.ProjectStageTaskInfos.IndexOf(projectStageTaskInfo) + 1,
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            };
                            addProjectStageTasks.Add(projectStageTask);

                            //项目化实践阶段任务高频问题
                            foreach (var questionInfo in projectStageTaskInfo.QuestionInfos)
                            {
                                taskQuestions.Add(new AI_ProjectStageTaskQuestion()
                                {
                                    Id = IdHelper.GetId(),
                                    ProjectStageTaskId = projectStageTask.Id,
                                    Name = questionInfo.Name,
                                    Describe = questionInfo.Describe,
                                    Order = projectStageTaskInfo.QuestionInfos.IndexOf(questionInfo) + 1,
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false
                                });
                            }
                        }
                    }

                    await DBSqlSugar.Insertable(addProjectStages).ExecuteCommandAsync();
                    await DBSqlSugar.Insertable(addProjectStageTasks).ExecuteCommandAsync();
                    await DBSqlSugar.Insertable(taskQuestions).ExecuteCommandAsync();
                    if (input.IsPublish)
                    {
                        //发布班级
                        List<AI_AgentTaskPublish> agentTaskPublishes = new List<AI_AgentTaskPublish>();
                        foreach (var item in input.ClassId)
                        {
                            agentTaskPublishes.Add(new AI_AgentTaskPublish()
                            {
                                Id = IdHelper.GetId(),
                                AgentTaskId = agentTask.Id,
                                PublishType = 1,
                                PublishBusinessId = item,
                                BeginTime = input.TimeRange[0],
                                EndTime = input.TimeRange[1],
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            });
                        }
                        await DBSqlSugar.Insertable(agentTaskPublishes).ExecuteCommandAsync();
                    }

                    return agentTask.Id;
                }
                else
                {
                    //获取项目化实践任务
                    AI_AgentTask agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.Id && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                    if (agentTask == null)
                    {
                        throw new BusException("项目化实践Id异常!");
                    }

                    //智能体任务更新
                    agentTask.Name = input.Name;
                    agentTask.Introduce = input.Introduce;
                    agentTask.TaskLogo = input.TaskLogo;
                    agentTask.ModifyTime = DateTime.Now;
                    agentTask.Modifier = input.TeacherId;
                    await DBSqlSugar.Updateable(agentTask).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                    //项目化实践阶段
                    List<AI_ProjectStage> projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>().Where(p => p.ProjectId == agentTask.Id && p.IsDeleted == false && p.Creator == input.TeacherId).ToListAsync();
                    //项目化实践阶段任务
                    List<AI_ProjectStageTask> projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>().Where(p => p.ProjectId == agentTask.Id && p.IsDeleted == false && p.Creator == input.TeacherId).ToListAsync();
                    //项目化实践阶段任务问题
                    List<AI_ProjectStageTaskQuestion> projectStageTaskQuestions = new List<AI_ProjectStageTaskQuestion>();
                    if (projectStageTasks.Count > 0)
                    {
                        List<string> taskIds = projectStageTasks.Select(p => p.Id).ToList();
                        projectStageTaskQuestions = await DBSqlSugar.Queryable<AI_ProjectStageTaskQuestion>().Where(p => taskIds.Contains(p.ProjectStageTaskId) && p.IsDeleted == false).ToListAsync();
                    }

                    //项目化实践阶段(新增)
                    List<AI_ProjectStage> addProjectStages = new List<AI_ProjectStage>();
                    //项目化实践阶段(修改)
                    List<AI_ProjectStage> updateProjectStages = new List<AI_ProjectStage>();
                    //项目化实践阶段任务(新增)
                    List<AI_ProjectStageTask> addProjectStageTasks = new List<AI_ProjectStageTask>();
                    //项目化实践阶段任务高频问题(新增)
                    List<AI_ProjectStageTaskQuestion> addTaskQuestions = new List<AI_ProjectStageTaskQuestion>();

                    //获取存在提交记录的阶段任务Id
                    List<string> submitTaskIds = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>()
                        .Where(p => p.ProjectId == agentTask.Id && p.IsDeleted == false)
                        .Select(p => p.ProjectStageTaskId)
                        .Distinct()
                        .With(SqlWith.NoLock)
                        .ToListAsync();

                    //删除历史任务信息的Id
                    List<string> delStageTaskIds = new List<string>();

                    foreach (var projectStageInfo in input.ProjectStageInfos)
                    {
                        if (string.IsNullOrEmpty(projectStageInfo.Id))
                        {
                            //项目化实践阶段
                            AI_ProjectStage projectStage = new AI_ProjectStage()
                            {
                                Id = IdHelper.GetId(),
                                ProjectId = agentTask.Id,
                                Name = projectStageInfo.Name,
                                Describe = projectStageInfo.Describe,
                                Order = input.ProjectStageInfos.IndexOf(projectStageInfo) + 1,
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            };
                            addProjectStages.Add(projectStage);

                            //项目化实践阶段任务
                            foreach (var projectStageTaskInfo in projectStageInfo.ProjectStageTaskInfos)
                            {
                                //项目化实践阶段任务
                                AI_ProjectStageTask projectStageTask = new AI_ProjectStageTask()
                                {
                                    Id = IdHelper.GetId(),
                                    ProjectStageId = projectStage.Id,
                                    ProjectId = agentTask.Id,
                                    TaskType = projectStageTaskInfo.TaskType,
                                    Name = projectStageTaskInfo.Name,
                                    Target = projectStageTaskInfo.Target,
                                    ScoreStandard = projectStageTaskInfo.ScoreStandard,
                                    Scope = projectStageTaskInfo.Scope,
                                    RoleSetting = projectStageTaskInfo.RoleSetting,
                                    Demand = projectStageTaskInfo.Demand,
                                    GroupIsSubmit = projectStageTaskInfo.GroupIsSubmit,
                                    GroupIsAssessment = projectStageTaskInfo.GroupIsAssessment,
                                    GroupAssessmentScore = projectStageTaskInfo.GroupAssessmentScore,
                                    TaskIsSubmit = projectStageTaskInfo.TaskIsSubmit,
                                    TaskIsAssessment = projectStageTaskInfo.TaskIsAssessment,
                                    TaskAssessmentScore = projectStageTaskInfo.TaskAssessmentScore,
                                    Order = projectStageInfo.ProjectStageTaskInfos.IndexOf(projectStageTaskInfo) + 1,
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false
                                };
                                addProjectStageTasks.Add(projectStageTask);

                                //项目化实践阶段任务高频问题
                                foreach (var questionInfo in projectStageTaskInfo.QuestionInfos)
                                {
                                    addTaskQuestions.Add(new AI_ProjectStageTaskQuestion()
                                    {
                                        Id = IdHelper.GetId(),
                                        ProjectStageTaskId = projectStageTask.Id,
                                        Name = questionInfo.Name,
                                        Describe = questionInfo.Describe,
                                        Order = projectStageTaskInfo.QuestionInfos.IndexOf(questionInfo) + 1,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false
                                    });
                                }
                            }
                        }
                        else
                        {
                            //修改项目化实践阶段
                            AI_ProjectStage projectStage = projectStages.Where(p => p.Id == projectStageInfo.Id).FirstOrDefault();
                            if (projectStage != null)
                            {
                                projectStage.Describe = projectStageInfo.Describe;
                                projectStage.Name = projectStageInfo.Name;
                                projectStage.Order = input.ProjectStageInfos.IndexOf(projectStageInfo) + 1;
                                updateProjectStages.Add(projectStage);

                                //项目化实践阶段任务
                                foreach (var projectStageTaskInfo in projectStageInfo.ProjectStageTaskInfos)
                                {
                                    if (!string.IsNullOrEmpty(projectStageTaskInfo.Id))
                                    {
                                        //获取任务信息
                                        AI_ProjectStageTask stageTask = projectStageTasks.Where(p => p.Id == projectStageTaskInfo.Id).FirstOrDefault();
                                        if (stageTask != null)
                                        {
                                            //验证是否存在答题记录
                                            bool isDo = submitTaskIds.Contains(projectStageTaskInfo.Id);
                                            if (isDo)
                                            {
                                                continue;
                                            }

                                            //验证是否修改
                                            bool isUpdate = false;
                                            //任务基础信息验证
                                            if (stageTask.TaskType != projectStageTaskInfo.TaskType ||
                                                stageTask.Name != projectStageTaskInfo.Name ||
                                                stageTask.Target != projectStageTaskInfo.Target ||
                                                stageTask.ScoreStandard != projectStageTaskInfo.ScoreStandard ||
                                                stageTask.Demand != projectStageTaskInfo.Demand ||
                                                stageTask.Scope != projectStageTaskInfo.Scope ||
                                                stageTask.RoleSetting != projectStageTaskInfo.RoleSetting ||
                                                stageTask.GroupIsSubmit != projectStageTaskInfo.GroupIsSubmit ||
                                                stageTask.GroupIsAssessment != projectStageTaskInfo.GroupIsAssessment ||
                                                stageTask.GroupAssessmentScore != projectStageTaskInfo.GroupAssessmentScore ||
                                                stageTask.TaskIsSubmit != projectStageTaskInfo.TaskIsSubmit ||
                                                stageTask.TaskIsAssessment != projectStageTaskInfo.TaskIsAssessment ||
                                                stageTask.TaskAssessmentScore != projectStageTaskInfo.TaskAssessmentScore ||
                                                stageTask.Order != projectStageInfo.ProjectStageTaskInfos.IndexOf(projectStageTaskInfo) + 1)
                                            {
                                                isUpdate = true;
                                            }

                                            //任务问题验证
                                            if (!isUpdate)
                                            {
                                                List<AI_ProjectStageTaskQuestion> stageTaskQuestions = projectStageTaskQuestions.Where(p => p.ProjectStageTaskId == stageTask.Id).ToList();
                                                if (stageTaskQuestions.Count != projectStageTaskInfo.QuestionInfos.Count)
                                                {
                                                    isUpdate = true;
                                                }
                                                else
                                                {
                                                    foreach (var taskQuestion in projectStageTaskInfo.QuestionInfos)
                                                    {
                                                        if (string.IsNullOrEmpty(taskQuestion.Id))
                                                        {
                                                            isUpdate = true;
                                                            break;
                                                        }
                                                        else
                                                        {
                                                            //获取问题信息
                                                            AI_ProjectStageTaskQuestion stageTaskQuestion = stageTaskQuestions.Where(p => p.Id == taskQuestion.Id).FirstOrDefault();
                                                            if (stageTaskQuestion != null)
                                                            {
                                                                if (stageTaskQuestion.Name != taskQuestion.Name
                                                                    || stageTaskQuestion.Describe != taskQuestion.Describe
                                                                    || stageTaskQuestion.Order != projectStageTaskInfo.QuestionInfos.IndexOf(taskQuestion) + 1)
                                                                {
                                                                    isUpdate = true;
                                                                    break;
                                                                }
                                                            }
                                                            else
                                                            {
                                                                isUpdate = true;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            if (!isUpdate)
                                            {
                                                continue;
                                            }

                                            delStageTaskIds.Add(projectStageTaskInfo.Id);
                                        }
                                    }

                                    //项目化实践阶段任务
                                    AI_ProjectStageTask projectStageTask = new AI_ProjectStageTask()
                                    {
                                        Id = IdHelper.GetId(),
                                        ProjectStageId = projectStage.Id,
                                        ProjectId = agentTask.Id,
                                        TaskType = projectStageTaskInfo.TaskType,
                                        Name = projectStageTaskInfo.Name,
                                        Target = projectStageTaskInfo.Target,
                                        ScoreStandard = projectStageTaskInfo.ScoreStandard,
                                        Scope = projectStageTaskInfo.Scope,
                                        RoleSetting = projectStageTaskInfo.RoleSetting,
                                        Demand = projectStageTaskInfo.Demand,
                                        GroupIsSubmit = projectStageTaskInfo.GroupIsSubmit,
                                        GroupIsAssessment = projectStageTaskInfo.GroupIsAssessment,
                                        GroupAssessmentScore = projectStageTaskInfo.GroupAssessmentScore,
                                        TaskIsSubmit = projectStageTaskInfo.TaskIsSubmit,
                                        TaskIsAssessment = projectStageTaskInfo.TaskIsAssessment,
                                        TaskAssessmentScore = projectStageTaskInfo.TaskAssessmentScore,
                                        Order = projectStageInfo.ProjectStageTaskInfos.IndexOf(projectStageTaskInfo) + 1,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false
                                    };
                                    addProjectStageTasks.Add(projectStageTask);

                                    //项目化实践阶段任务高频问题
                                    foreach (var questionInfo in projectStageTaskInfo.QuestionInfos)
                                    {
                                        addTaskQuestions.Add(new AI_ProjectStageTaskQuestion()
                                        {
                                            Id = IdHelper.GetId(),
                                            ProjectStageTaskId = projectStageTask.Id,
                                            Name = questionInfo.Name,
                                            Describe = questionInfo.Describe,
                                            Order = projectStageTaskInfo.QuestionInfos.IndexOf(questionInfo) + 1,
                                            CreateTime = DateTime.Now,
                                            Creator = input.TeacherId,
                                            ModifyTime = DateTime.Now,
                                            Modifier = input.TeacherId,
                                            IsDeleted = false
                                        });
                                    }
                                }
                            }
                        }
                    }

                    //获取所有阶段任务Id和阶段Id
                    List<string> stageTaskIds = new List<string>();
                    List<string> stageIds = new List<string>();
                    foreach (var projectStage in input.ProjectStageInfos)
                    {
                        if (!string.IsNullOrEmpty(projectStage.Id))
                        {
                            stageIds.Add(projectStage.Id);
                        }
                        foreach (var stageTaskInfo in projectStage.ProjectStageTaskInfos)
                        {
                            if (!string.IsNullOrEmpty(stageTaskInfo.Id))
                            {
                                stageTaskIds.Add(stageTaskInfo.Id);
                            }
                        }
                    }

                    //删除的任务Id
                    foreach (var stageTask in projectStageTasks)
                    {
                        if (!stageTaskIds.Contains(stageTask.Id))
                        {
                            delStageTaskIds.Add(stageTask.Id);
                        }
                    }
                    if (delStageTaskIds.Count > 0)
                    {
                        //删除任务
                        await DBSqlSugar.Deleteable<AI_ProjectStageTask>().Where(p => delStageTaskIds.Contains(p.Id)).ExecuteCommandAsync();
                        //删除任务问题
                        await DBSqlSugar.Deleteable<AI_ProjectStageTaskQuestion>().Where(p => delStageTaskIds.Contains(p.ProjectStageTaskId)).ExecuteCommandAsync();
                        //删除提交记录
                        await DBSqlSugar.Deleteable<AI_StudentDoProjectTask>().Where(p => delStageTaskIds.Contains(p.ProjectStageTaskId)).ExecuteCommandAsync();
                        //删除提交记录问题
                        await DBSqlSugar.Deleteable<AI_StudentDoProjectTaskQuestion>().Where(p => delStageTaskIds.Contains(p.ProjectStageTaskId)).ExecuteCommandAsync();
                        //清空缓存池和问答记录
                        await DBSqlSugar.Deleteable<AI_ContextCacheKey>().Where(p => delStageTaskIds.Contains(p.BusinessId)).ExecuteCommandAsync();
                        await DBSqlSugar.Deleteable<AI_DialogueContentRecord>().Where(p => delStageTaskIds.Contains(p.BusinessId)).ExecuteCommandAsync();
                    }

                    //删除的阶段Id
                    List<string> delStageIds = new List<string>();
                    foreach (var stage in projectStages)
                    {
                        if (!stageIds.Contains(stage.Id))
                        {
                            delStageIds.Add(stage.Id);
                        }
                    }
                    if (delStageIds.Count > 0)
                    {
                        //删除阶段
                        await DBSqlSugar.Deleteable<AI_ProjectStage>().Where(p => delStageIds.Contains(p.Id)).ExecuteCommandAsync();
                    }

                    //更新/新增任务相关信息
                    if (addProjectStages.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addProjectStages).ExecuteCommandAsync();
                    }
                    if (addProjectStageTasks.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addProjectStageTasks).ExecuteCommandAsync();
                    }
                    if (addTaskQuestions.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addTaskQuestions).ExecuteCommandAsync();
                    }
                    if (updateProjectStages.Count > 0)
                    {
                        await DBSqlSugar.Updateable(updateProjectStages).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }

                    //发布设置
                    if (input.IsPublish)
                    {
                        //发布班级
                        List<AI_AgentTaskPublish> agentTaskPublishes = new List<AI_AgentTaskPublish>();
                        foreach (var item in input.ClassId)
                        {
                            agentTaskPublishes.Add(new AI_AgentTaskPublish()
                            {
                                Id = IdHelper.GetId(),
                                AgentTaskId = agentTask.Id,
                                PublishType = 1,
                                PublishBusinessId = item,
                                BeginTime = input.TimeRange[0],
                                EndTime = input.TimeRange[1],
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            });
                        }
                        //清空历史数据
                        await DBSqlSugar.Deleteable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == agentTask.Id).ExecuteCommandAsync();
                        //保存最新数据
                        await DBSqlSugar.Insertable(agentTaskPublishes).ExecuteCommandAsync();
                    }
                    else
                    {
                        //清空历史数据
                        await DBSqlSugar.Deleteable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == agentTask.Id).ExecuteCommandAsync();
                    }

                    return agentTask.Id;
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取项目化实践详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ProjectTaskDetailsOutput> GetProjectTaskDetails(ProjectTaskDetailsInput input)
        {
            try
            {
                // 查询项目化实践任务基本信息
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("项目化实践任务不存在或无权限访问!");
                }

                // 初始化输出对象
                var output = new ProjectTaskDetailsOutput
                {
                    Id = agentTask.Id,
                    Name = agentTask.Name,
                    Introduce = agentTask.Introduce,
                    TaskLogo = agentTask.TaskLogo,
                    TeacherId = agentTask.Creator
                };

                // 查询项目化实践阶段
                var projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>().Where(p => p.ProjectId == agentTask.Id && p.IsDeleted == false).OrderBy(p => p.Order).ToListAsync();
                if (projectStages.Any())
                {
                    output.ProjectStageInfos = new List<ProjectStageDetailsOutput>();

                    // 查询项目化实践阶段任务
                    var projectStageIds = projectStages.Select(p => p.Id).ToList();
                    var projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>().Where(p => projectStageIds.Contains(p.ProjectStageId) && p.IsDeleted == false).OrderBy(p => p.Order).ToListAsync();

                    if (projectStageTasks.Any())
                    {
                        // 查询项目化实践阶段任务高频问题
                        var projectStageTaskIds = projectStageTasks.Select(p => p.Id).ToList();
                        var taskQuestions = await DBSqlSugar.Queryable<AI_ProjectStageTaskQuestion>().Where(p => projectStageTaskIds.Contains(p.ProjectStageTaskId) && p.IsDeleted == false).OrderBy(p => p.Order).ToListAsync();

                        // 查询任务是否有答题记录
                        var taskHasAnswers = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>()
                            .Where(p => projectStageTaskIds.Contains(p.ProjectStageTaskId) && p.IsDeleted == false)
                            .GroupBy(p => p.ProjectStageTaskId)
                            .Select(p => p.ProjectStageTaskId)
                            .ToListAsync();

                        // 构建阶段输出
                        foreach (var stage in projectStages)
                        {
                            var stageOutput = new ProjectStageDetailsOutput
                            {
                                Id = stage.Id,
                                Name = stage.Name,
                                Describe = stage.Describe,
                                ProjectStageTaskInfos = new List<ProjectStageTaskDetailsOutput>()
                            };

                            // 构建阶段任务输出
                            var stageTasks = projectStageTasks.Where(p => p.ProjectStageId == stage.Id).ToList();
                            foreach (var task in stageTasks)
                            {
                                var taskOutput = new ProjectStageTaskDetailsOutput
                                {
                                    Id = task.Id,
                                    TaskType = task.TaskType,
                                    Name = task.Name,
                                    Target = task.Target,
                                    ScoreStandard = task.ScoreStandard,
                                    Demand = task.Demand,
                                    Scope = task.Scope,
                                    RoleSetting = task.RoleSetting,
                                    GroupIsSubmit = task.GroupIsSubmit,
                                    GroupIsAssessment = task.GroupIsAssessment,
                                    GroupAssessmentScore = task.GroupAssessmentScore,
                                    TaskIsSubmit = task.TaskIsSubmit,
                                    TaskIsAssessment = task.TaskIsAssessment,
                                    TaskAssessmentScore = task.TaskAssessmentScore,
                                    QuestionInfos = taskQuestions
                                        .Where(q => q.ProjectStageTaskId == task.Id)
                                        .Select(q => new ProjectStageTaskQuestionDetailsOutput
                                        {
                                            Id = q.Id,
                                            Name = q.Name,
                                            Describe = q.Describe
                                        })
                                        .ToList(),
                                    IsDo = taskHasAnswers.Contains(task.Id)
                                };

                                stageOutput.ProjectStageTaskInfos.Add(taskOutput);
                            }

                            output.ProjectStageInfos.Add(stageOutput);
                        }
                    }
                }

                //获取项目化实践发布的班级
                List<AI_AgentTaskPublish> taskPublishes = await DBSqlSugar.Queryable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == output.Id && p.IsDeleted == false && p.Creator == input.TeacherId).ToListAsync();
                if (taskPublishes.Count > 0)
                {
                    //发布时间范围(下标0开始、下标1结束)
                    List<DateTime?> timeRange = new List<DateTime?>() { taskPublishes[0].BeginTime, taskPublishes[0].EndTime };
                    output.TimeRange = timeRange;

                    //发布的班级
                    output.ClassId = taskPublishes.Select(p => p.PublishBusinessId).ToList();
                }

                return output;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 删除项目化实践任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task DelProjectTask(DelProjectTaskInput input)
        {
            try
            {
                // 查询项目化实践任务
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("项目化实践任务不存在或无权限访问!");
                }

                // 查询项目化实践阶段
                var projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>().Where(p => p.ProjectId == agentTask.Id && p.IsDeleted == false).ToListAsync();
                if (projectStages.Any())
                {
                    // 查询项目化实践阶段任务
                    var projectStageIds = projectStages.Select(p => p.Id).ToList();
                    var projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>().Where(p => projectStageIds.Contains(p.ProjectStageId) && p.IsDeleted == false).ToListAsync();
                    if (projectStageTasks.Any())
                    {
                        // 查询项目化实践阶段任务高频问题
                        var projectStageTaskIds = projectStageTasks.Select(p => p.Id).ToList();
                        var taskQuestions = await DBSqlSugar.Queryable<AI_ProjectStageTaskQuestion>().Where(p => projectStageTaskIds.Contains(p.ProjectStageTaskId) && p.IsDeleted == false).ToListAsync();
                        // 逻辑删除高频问题
                        if (taskQuestions.Any())
                        {
                            var updateQuestions = taskQuestions.Select(q => new AI_ProjectStageTaskQuestion
                            {
                                Id = q.Id,
                                IsDeleted = true,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId
                            }).ToList();
                            await DBSqlSugar.Updateable(updateQuestions).UpdateColumns(it => new { it.IsDeleted, it.ModifyTime, it.Modifier }).ExecuteCommandAsync();
                        }

                        // 逻辑删除阶段任务
                        var updateTasks = projectStageTasks.Select(t => new AI_ProjectStageTask
                        {
                            Id = t.Id,
                            IsDeleted = true,
                            ModifyTime = DateTime.Now,
                            Modifier = input.TeacherId
                        }).ToList();

                        await DBSqlSugar.Updateable(updateTasks).UpdateColumns(it => new { it.IsDeleted, it.ModifyTime, it.Modifier }).ExecuteCommandAsync();

                        if (projectStageTaskIds.Count > 0)
                        {
                            //删除提交记录
                            await DBSqlSugar.Deleteable<AI_StudentDoProjectTask>().Where(p => projectStageTaskIds.Contains(p.ProjectStageTaskId)).ExecuteCommandAsync();
                            //清空缓存池和问答记录
                            await DBSqlSugar.Deleteable<AI_ContextCacheKey>().Where(p => projectStageTaskIds.Contains(p.BusinessId)).ExecuteCommandAsync();
                            await DBSqlSugar.Deleteable<AI_DialogueContentRecord>().Where(p => projectStageTaskIds.Contains(p.BusinessId)).ExecuteCommandAsync();
                        }
                    }

                    // 逻辑删除阶段
                    var updateStages = projectStages.Select(s => new AI_ProjectStage
                    {
                        Id = s.Id,
                        IsDeleted = true,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId
                    }).ToList();
                    await DBSqlSugar.Updateable(updateStages).UpdateColumns(it => new { it.IsDeleted, it.ModifyTime, it.Modifier }).ExecuteCommandAsync();
                }

                // 逻辑删除发布记录
                var taskPublishes = await DBSqlSugar.Queryable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == agentTask.Id && p.IsDeleted == false).ToListAsync();
                if (taskPublishes.Any())
                {
                    var updatePublishes = taskPublishes.Select(p => new AI_AgentTaskPublish
                    {
                        Id = p.Id,
                        IsDeleted = true,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId
                    }).ToList();
                    await DBSqlSugar.Updateable(updatePublishes).UpdateColumns(it => new { it.IsDeleted, it.ModifyTime, it.Modifier }).ExecuteCommandAsync();
                }

                // 逻辑删除智能体任务
                agentTask.IsDeleted = true;
                agentTask.ModifyTime = DateTime.Now;
                agentTask.Modifier = input.TeacherId;
                await DBSqlSugar.Updateable(agentTask).UpdateColumns(it => new { it.IsDeleted, it.ModifyTime, it.Modifier }).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 发布项目化实践任务到班级
        /// </summary>
        /// <param name="input">发布参数</param>
        /// <returns></returns>
        public async Task PublishProjectTaskToClasses(PublishProjectTaskToClassInput input)
        {
            try
            {
                // 检查任务是否存在且属于当前教师
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("项目化实践任务不存在或无权限访问!");
                }

                // 检查项目是否有有效的阶段和任务
                var projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>().Where(p => p.ProjectId == input.ProjectId && p.IsDeleted == false).ToListAsync();
                if (!projectStages.Any())
                {
                    throw new BusException("项目至少需要一个有效阶段才能发布!");
                }

                var projectStageIds = projectStages.Select(p => p.Id).ToList();
                var projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>().Where(p => projectStageIds.Contains(p.ProjectStageId) && p.IsDeleted == false).ToListAsync();
                if (!projectStageTasks.Any())
                {
                    throw new BusException("项目至少需要一个有效任务才能发布!");
                }

                // 逻辑删除该任务之前的所有发布记录
                await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                    .SetColumns(p => new AI_AgentTaskPublish { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.AgentTaskId == input.ProjectId && p.Creator == input.TeacherId).ExecuteCommandAsync();

                // 创建新的发布记录
                var publishRecords = input.ClassIds.Select(classId => new AI_AgentTaskPublish
                {
                    Id = IdHelper.GetId(),
                    AgentTaskId = input.ProjectId,
                    PublishType = 1, // 班级发布
                    PublishBusinessId = classId,
                    BeginTime = input.TimeRange[0].Value,
                    EndTime = input.TimeRange[1].Value,
                    CreateTime = DateTime.Now,
                    Creator = input.TeacherId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.TeacherId,
                    IsDeleted = false
                }).ToList();

                // 批量插入发布记录
                await DBSqlSugar.Insertable(publishRecords).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 撤销项目化实践任务发布
        /// </summary>
        /// <param name="input">撤销发布参数</param>
        /// <returns></returns>
        public async Task UnpublishProjectTask(UnpublishProjectTaskInput input)
        {
            try
            {
                // 检查任务是否存在且属于当前教师
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("项目化实践任务不存在或无权限访问!");
                }

                // 逻辑删除该任务的所有发布记录
                await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                    .SetColumns(p => new AI_AgentTaskPublish { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.AgentTaskId == input.ProjectId && p.Creator == input.TeacherId).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取项目化实践列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageReturn<GetProjectTaskListOutput>> GetProjectTaskList(GetProjectTaskListInput input)
        {
            try
            {
                //获取学校信息
                Exam_School schoolInfo = await DBSqlSugar.Queryable<Exam_School>().Where(p => p.Id == input.SchoolId).FirstAsync();
                if (schoolInfo == null)
                {
                    throw new BusException("学校Id异常!");
                }

                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!", 801);
                }

                //参数
                List<SugarParameter> parameters = new List<SugarParameter>();
                parameters.Add(new SugarParameter("@year", nowSemesterTime.Year));
                parameters.Add(new SugarParameter("@term", nowSemesterTime.NowTerm));
                parameters.Add(new SugarParameter("@teacherId", input.TeacherId));
                parameters.Add(new SugarParameter("@agentId", input.AgentId));
                parameters.Add(new SugarParameter("@subjectId", input.SubjectId));
                parameters.Add(new SugarParameter("@gradeId", input.GradeId));
                parameters.Add(new SugarParameter("@agentTaskType", input.AgentTaskType));
                string where = string.Empty;
                string whereTwo = string.Empty;
                if (!string.IsNullOrEmpty(input.Name))
                {
                    where += $" AND t.Name LIKE '%{input.Name}%' ";
                }

                if (!string.IsNullOrEmpty(input.ClassId))
                {
                    where += $" AND atp.PublishBusinessId=@classId  ";
                    parameters.Add(new SugarParameter("@classId", input.ClassId));
                }

                if (input.PublishStatus != 0)
                {
                    whereTwo += $" AND PublishStatus=@publishStatus ";
                    parameters.Add(new SugarParameter("@publishStatus", input.PublishStatus));
                }

                //获取项目化实践列表
                string sql = $@"SELECT
                                	AgentId,
                                	ProjectId,
                                	Name,
                                	Introduce,
                                	CreateTime,
                                	TaskLogo,
                                	PublishStatus 
                                FROM
                                	(
                                	SELECT
                                		t.AgentId,
                                		t.Id AS ProjectId,
                                		t.Name,
                                		t.Introduce,
                                		t.CreateTime,
                                		COALESCE ( t.TaskLogo, a.Logo ) AS TaskLogo,
                                	    CASE WHEN atp.AgentTaskId IS NOT NULL THEN 1 ELSE 2 END AS PublishStatus,
                                		ROW_NUMBER ( ) OVER ( PARTITION BY t.Id ORDER BY atp.CreateTime DESC ) AS rn 
                                	FROM
                                		AI_AgentTask t WITH ( NOLOCK )
                                		INNER JOIN AI_AgentBaseInfo a WITH ( NOLOCK ) ON t.AgentId = a.Id 
                                		AND a.IsDeleted = 0
                                		LEFT JOIN AI_AgentTaskPublish atp WITH ( NOLOCK ) ON t.Id = atp.AgentTaskId 
                                		AND atp.IsDeleted = 0 
                                	WHERE
                                		t.IsDeleted = 0 
                                		AND t.[Year] = @year 
                                		AND t.Term = @term 
                                		AND t.SubjectId = @subjectId 
                                		AND t.AgentId =@agentId
                                		AND t.Creator = @teacherId
                                		AND t.GradeId = @gradeId
                                		AND t.AgentTaskType = @agentTaskType {where}
                                	) AS sub 
                                WHERE
                                	rn = 1 {whereTwo}";
                RefAsync<int> totalNumber = 0;
                List<GetProjectTaskListOutput> taskListOutputs = await DBSqlSugar.SqlQueryable<GetProjectTaskListOutput>(sql)
                    .AddParameters(parameters)
                    .OrderBy("CreateTime DESC,PublishStatus DESC")
                    .ToPageListAsync(input.PageIndex, input.PageSize, totalNumber);

                if (taskListOutputs.Count > 0)
                {
                    List<string> ids = taskListOutputs.Select(p => p.ProjectId).ToList();

                    //获取智能体任务发布的班级
                    string classSql = $@"SELECT
                                         	atp.AgentTaskId,
                                            class.Id AS ClassId,
                                         	class.ClassName 
                                         FROM
                                         	AI_AgentTaskPublish atp WITH ( NOLOCK )
                                         	INNER JOIN Exam_Class class WITH ( NOLOCK ) ON atp.PublishBusinessId= class.Id 
                                         WHERE
                                         	atp.IsDeleted= 0 
                                         	AND atp.Creator=@teacherId 
                                            AND atp.AgentTaskId in (@agentTaskIds) ";
                    List<AgentTeacherOralCommunicationListClass> classInfos = await DBSqlSugar.SqlQueryable<AgentTeacherOralCommunicationListClass>(classSql)
                        .AddParameters(new { teacherId = input.TeacherId, agentTaskIds = ids }).ToListAsync();
                    foreach (var taskListOutput in taskListOutputs)
                    {
                        taskListOutput.ClassInfos = classInfos.Where(p => p.AgentTaskId == taskListOutput.ProjectId)
                            .Select(p => new GetProjectTaskListClassInfoOutput()
                            {
                                ClassId = p.ClassId,
                                ClassName = p.ClassName,
                            }).ToList();
                    }
                }

                return new PageReturn<GetProjectTaskListOutput>()
                {
                    Datas = taskListOutputs,
                    TotalCount = totalNumber
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// AI创建项目化实践阶段任务相关属性
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task AICreateTaskProperty(AICreateTaskPropertyInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string model = await DBSqlSugar.Queryable<AI_ModelConfig>().Where(p => p.SceneType == 5).Select(p => p.Model).FirstAsync();
                string url = AppSetting.DouBaoAI.DialogueUrl;
                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(model) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //年级名称
                string gradeName = BusinessUtil.GradeName(input.GradeId);

                //获取学科
                Exam_Subject subjectInfo = await DBSqlSugar.Queryable<Exam_Subject>().Where(p => p.Id == input.SubjectId).With(SqlWith.NoLock).FirstAsync();

                //指令处理
                string directiveStr = string.Empty;
                if (input.TaskType == 1)
                {
                    //获取成果评估类型指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherProjectAICreateOutcomeProperty_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取项目化实践阶段成果评估系统提示词,请联系管理员!");
                    }
                    directiveStr = directive?.Directive;
                }
                if (input.TaskType == 2)
                {
                    //获取情景对话类型指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherProjectAICreateDialogueProperty_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取项目化实践阶段情景对话系统提示词,请联系管理员!");
                    }
                    directiveStr = directive?.Directive;
                }
                if (input.TaskType == 3)
                {
                    //获取知识问答类型指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherProjectAICreateKeyknowledgeProperty_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取知识问答系统提示词,请联系管理员!");
                    }
                    directiveStr = directive?.Directive;
                }
                if (input.TaskType == 6)
                {
                    //获取知识问答类型指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherProjectAICreateKeyMindMapProperty_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取思维导图系统提示词,请联系管理员!");
                    }
                    directiveStr = directive?.Directive;
                }
                if (input.TaskType == 7)
                {
                    //获取知识问答类型指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherProjectAICreateKeyWordFileProperty_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取选词填空系统提示词,请联系管理员!");
                    }
                    directiveStr = directive?.Directive;
                }
                if (string.IsNullOrEmpty(directiveStr))
                {
                    throw new BusException("无法获取系统提示词,请联系管理员!");
                }
                directiveStr = directiveStr.Replace("{任务名称}", input.Name)
                .Replace("{任务目标}", input.Target)
                .Replace("{学段}", gradeName)
                .Replace("{学科}", subjectInfo?.Name);

                //入参处理
                List<AICreateTaskPropertyDouBaoMessageInput> messages = new List<AICreateTaskPropertyDouBaoMessageInput>()
                {
                    new AICreateTaskPropertyDouBaoMessageInput(){  role="system", content=directiveStr},
                    new AICreateTaskPropertyDouBaoMessageInput(){ role="user", content=$"请帮我生成相关数据" }
                };
                AICreateTaskPropertyDouBaoInput douBaoInput = new AICreateTaskPropertyDouBaoInput()
                {
                    model = model,
                    messages = messages,
                    stream = true,
                    stream_options = new AICreateTaskPropertyDouBaoStreamOptionsInput()
                    {
                        include_usage = true
                    },
                    response_format = new AICreateTaskPropertyDouBaoResponseFormatInput()
                    {
                        type = "json_schema",
                        json_schema = new AICreateTaskPropertyDouBaoJsonSchemaInput()
                        {
                            name = "PropertyGenerator",
                            strict = true,
                            schema = new AICreateTaskPropertyOutput()
                            {
                                Demand = "对话要求（注意:300字以内）",
                                RoleSetting = "评估角色设定（注意:300字以内）",
                                ScoreStandard = "评分标准（注意:100字以内）",
                                Scope = "问答范围（注意:300字以内）",
                                QuestionInfos = new List<AICreateTaskPropertyQuestionOutput>()
                                {
                                    new AICreateTaskPropertyQuestionOutput()
                                    {
                                        Name="主题（注意:10字以内,最多输出5个）",
                                        Describe="描述（注意:50字以内）"
                                    }
                                }
                            }
                        }
                    },
                    thinking = new AICreateTaskPropertyDouBaoThinkingInput()
                    {
                        type = "disabled"
                    }
                };
                string jsonData = JsonConvert.SerializeObject(douBaoInput);

                //创建一个用于累积JSON片段的缓冲区
                string jsonBuffer = string.Empty;
                //http请求
                using (var httpClient = new HttpClient())
                {
                    var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Authorization", apiKey);
                    request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken))
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            using (var stream = await response.Content.ReadAsStreamAsync())
                            {
                                using (var reader = new StreamReader(stream, Encoding.UTF8))
                                {
                                    string line;
                                    while ((line = await reader.ReadLineAsync()) != null && !cancellationToken.IsCancellationRequested)
                                    {
                                        if (line.StartsWith("data: [DONE]"))
                                        {
                                            break;
                                        }
                                        // 处理SSE数据行
                                        if (line.StartsWith("data:"))
                                        {
                                            // 去除(data: )进行解析数据
                                            var data = line.Substring("data:".Length).Trim();
                                            if (!string.IsNullOrEmpty(data))
                                            {
                                                AICreateTaskPropertyDouBaoOutput douBaoOutput = JsonConvert.DeserializeObject<AICreateTaskPropertyDouBaoOutput>(data);
                                                if (douBaoOutput != null && douBaoOutput.choices.Count > 0 && douBaoOutput.choices[0].delta != null)
                                                {
                                                    // 将新数据添加到缓冲区
                                                    jsonBuffer += douBaoOutput.choices[0].delta.content;
                                                    // 尝试解析完整的属性
                                                    bool propertyFound = true;

                                                    while (propertyFound && !cancellationToken.IsCancellationRequested)
                                                    {
                                                        propertyFound = TryParseNextProperty(jsonBuffer, out string? propertyName, out JToken? propertyValue, out int parsedLength);

                                                        if (propertyFound && propertyName != null && propertyValue != null)
                                                        {
                                                            // 创建一个只包含当前属性的输出对象
                                                            var propertyOutput = new AICreateTaskPropertyOutput();

                                                            switch (propertyName)
                                                            {
                                                                case "ScoreStandard":
                                                                    propertyOutput.ScoreStandard = propertyValue.ToString();
                                                                    break;
                                                                case "Demand":
                                                                    propertyOutput.Demand = propertyValue.ToString();
                                                                    break;
                                                                case "Scope":
                                                                    propertyOutput.Scope = propertyValue.ToString();
                                                                    break;
                                                                case "RoleSetting":
                                                                    propertyOutput.RoleSetting = propertyValue.ToString();
                                                                    break;
                                                                case "QuestionInfos":
                                                                    // 特殊处理数组属性
                                                                    propertyOutput.QuestionInfos = propertyValue.ToObject<List<AICreateTaskPropertyQuestionOutput>>();
                                                                    Console.WriteLine($"解析到QuestionInfos，包含 {propertyOutput.QuestionInfos.Count} 个问题");
                                                                    break;
                                                            }

                                                            // 立即推送给前端
                                                            AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                                                            {
                                                                Success = true,
                                                                Content = JsonConvert.SerializeObject(propertyOutput),
                                                                BusinessId = propertyName
                                                            };
                                                            string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                                                            await dataHandler(ssePushData);

                                                            // 从缓冲区中移除已处理的数据
                                                            jsonBuffer = jsonBuffer.Substring(parsedLength);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            string errorMsg = await response.Content.ReadAsStringAsync();
                            AgentSSEOutput errorSSEOutput = new AgentSSEOutput()
                            {
                                Success = false,
                                Content = $"第三方接口异常：{response.StatusCode}，详情：{errorMsg}"
                            };
                            //SSE推送客户端
                            string errorPushData = "data: " + JsonConvert.SerializeObject(errorSSEOutput) + "\n\n";
                            await dataHandler(errorPushData);
                        }
                    }
                }

                //执行结束
                AgentSSEOutput endSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string endPushData = "data: " + JsonConvert.SerializeObject(endSSEOutput) + "\n\n";
                await dataHandler(endPushData);
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 尝试解析下一个完整的属性
        /// </summary>
        /// <param name="jsonBuffer"></param>
        /// <param name="propertyName"></param>
        /// <param name="propertyValue"></param>
        /// <param name="parsedLength"></param>
        /// <returns></returns>
        private bool TryParseNextProperty(string jsonBuffer, out string? propertyName, out JToken? propertyValue, out int parsedLength)
        {
            propertyName = null;
            propertyValue = null;
            parsedLength = 0;

            // 寻找属性名称的开始（格式："属性名":）
            int nameStartIndex = jsonBuffer.IndexOf('"');
            if (nameStartIndex < 0)
                return false;

            int nameEndIndex = jsonBuffer.IndexOf('"', nameStartIndex + 1);
            if (nameEndIndex < 0)
                return false;

            // 提取属性名称
            propertyName = jsonBuffer.Substring(nameStartIndex + 1, nameEndIndex - nameStartIndex - 1);

            // 寻找属性值的开始（冒号后面的字符）
            int valueStartIndex = jsonBuffer.IndexOf(':', nameEndIndex);
            if (valueStartIndex < 0)
                return false;

            valueStartIndex++; // 跳到冒号后面的位置

            // 跳过空白字符
            while (valueStartIndex < jsonBuffer.Length && char.IsWhiteSpace(jsonBuffer[valueStartIndex]))
                valueStartIndex++;

            if (valueStartIndex >= jsonBuffer.Length)
                return false;

            // 根据值的类型找到结束位置
            int valueEndIndex = FindValueEnd(jsonBuffer, valueStartIndex);
            if (valueEndIndex < 0)
                return false;

            try
            {
                // 提取属性值的JSON字符串
                string valueJson = jsonBuffer.Substring(valueStartIndex, valueEndIndex - valueStartIndex + 1);

                // 解析属性值
                propertyValue = JToken.Parse(valueJson);

                // 计算已解析的长度（包括属性名、冒号和值）
                parsedLength = valueEndIndex + 1;

                // 如果后面有逗号，也包含逗号
                if (parsedLength < jsonBuffer.Length && jsonBuffer[parsedLength] == ',')
                    parsedLength++;

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 找到JSON值的结束位置（考虑不同类型的值和嵌套情况）
        /// </summary>
        /// <param name="jsonBuffer"></param>
        /// <param name="startIndex"></param>
        /// <returns></returns>
        private int FindValueEnd(string jsonBuffer, int startIndex)
        {
            char firstChar = jsonBuffer[startIndex];

            switch (firstChar)
            {
                case '{': // 对象
                    return FindJsonEnd(jsonBuffer, startIndex);

                case '[': // 数组
                    int depth = 0;
                    bool inString = false;
                    char stringDelimiter = '\0';

                    for (int i = startIndex; i < jsonBuffer.Length; i++)
                    {
                        char c = jsonBuffer[i];

                        // 处理字符串
                        if (c == '"' || c == '\'')
                        {
                            // 检查是否是转义字符
                            if (i > 0 && jsonBuffer[i - 1] == '\\')
                                continue;

                            if (!inString)
                            {
                                inString = true;
                                stringDelimiter = c;
                            }
                            else if (c == stringDelimiter)
                            {
                                inString = false;
                            }
                        }

                        if (inString)
                            continue;

                        // 处理数组结构字符
                        if (c == '[')
                            depth++;
                        else if (c == ']')
                            depth--;

                        // 如果找到匹配的结束括号
                        if (depth == 0 && c == ']')
                            return i;
                    }
                    return -1;

                case '"': // 字符串
                    for (int i = startIndex + 1; i < jsonBuffer.Length; i++)
                    {
                        char c = jsonBuffer[i];

                        // 检查是否是转义字符
                        if (c == '"' && i > 0 && jsonBuffer[i - 1] != '\\')
                            return i;
                    }
                    return -1;

                default: // 数字、布尔值、null
                         // 找到值的结束（逗号、右括号等）
                    for (int i = startIndex; i < jsonBuffer.Length; i++)
                    {
                        char c = jsonBuffer[i];
                        if (c == ',' || c == '}' || c == ']')
                            return i - 1;
                    }
                    return jsonBuffer.Length - 1;
            }
        }

        /// <summary>
        /// 找到JSON对象的结束位置（考虑嵌套情况）
        /// </summary>
        /// <param name="jsonBuffer"></param>
        /// <param name="startIndex"></param>
        /// <returns></returns>
        private int FindJsonEnd(string jsonBuffer, int startIndex)
        {
            int depth = 0;
            bool inString = false;
            char stringDelimiter = '\0';

            for (int i = startIndex; i < jsonBuffer.Length; i++)
            {
                char c = jsonBuffer[i];

                // 处理字符串
                if (c == '"' || c == '\'')
                {
                    // 检查是否是转义字符
                    if (i > 0 && jsonBuffer[i - 1] == '\\')
                        continue;

                    if (!inString)
                    {
                        inString = true;
                        stringDelimiter = c;
                    }
                    else if (c == stringDelimiter)
                    {
                        inString = false;
                    }
                }

                if (inString)
                    continue;

                // 处理JSON结构字符
                if (c == '{')
                    depth++;
                else if (c == '}')
                    depth--;

                // 如果找到匹配的结束括号
                if (depth == 0 && c == '}')
                    return i;
            }

            return -1; // 没有找到完整的JSON对象
        }

        /// <summary>
        /// 获取学生做任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentDoProjectTaskListOutput> GetStudentDoProjectTaskList(GetStudentDoProjectTaskListInput input)
        {
            try
            {
                //获取学生信息
                Exam_Student studentInfo = await DBSqlSugar.Queryable<Exam_Student>().Where(p => p.Id == input.StudentId && p.Deleted == false).FirstAsync();
                if (studentInfo == null)
                {
                    throw new BusException("学生信息不存在!");
                }

                //获取项目化实践任务基础信息
                GetStudentDoProjectTaskListOutput projectInfoOutput = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentDoProjectTaskListOutput()
                    {
                        AgentId = p.AgentId,
                        ProjectId = p.Id,
                        ProjectName = p.Name,
                        ProjectIntroduce = p.Introduce,
                        ProjectLogo = p.TaskLogo
                    })
                    .FirstAsync();
                if (projectInfoOutput == null)
                {
                    throw new BusException("项目化实践Id异常!");
                }
                projectInfoOutput.StudentId = studentInfo.Id;
                projectInfoOutput.StudentName = studentInfo.RealName;
                projectInfoOutput.StudentLogo = string.IsNullOrEmpty(studentInfo.Photo) ? "https://userphoto.obs.cn-east-2.myhuaweicloud.com/uploadFile131e561d405a4f6a834a6a79d27ded7e.20210305132909.png" : studentInfo.Photo;

                //项目化实践阶段
                List<GetStudentDoProjectTaskListStageInfoOutput> projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentDoProjectTaskListStageInfoOutput()
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .ToListAsync();

                //项目化实践阶段任务
                List<GetStudentDoProjectTaskListStageTaskInfoOutput> projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentDoProjectTaskListStageTaskInfoOutput()
                    {
                        Id = p.Id,
                        ProjectStageId = p.ProjectStageId,
                        Name = p.Name,
                        Target = p.Target,
                        TaskType = p.TaskType,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .ToListAsync();

                //获取学生做项目化实践任务信息
                List<AI_StudentDoProjectTask> studentDoProjectTasks = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.StudentId == input.StudentId && p.IsDeleted == false)
                    .Select(p => new AI_StudentDoProjectTask()
                    {
                        Id = p.Id,
                        ProjectStageId = p.ProjectStageId,
                        ProjectStageTaskId = p.ProjectStageTaskId,
                        IsStandard = p.IsStandard,
                        Order = p.Order,
                        CreateTime = p.CreateTime
                    })
                    .OrderBy(p => p.Order)
                    .ToListAsync();

                //获取学生未达标问答记录
                List<AI_DialogueContentRecord> dialogueContentRecords = new List<AI_DialogueContentRecord>();
                if (studentDoProjectTasks.Count(p => p.IsStandard == false) > 0)
                {
                    List<string> taskKey = new List<string>();
                    foreach (var item in studentDoProjectTasks.Where(p => p.IsStandard == false))
                    {
                        //获取学生端-项目化实践未达标key
                        string studentProjectNoStandard = AIAgentKeys.GetStudentProjectNoStandardKey(projectInfoOutput.AgentId, item.ProjectStageTaskId, input.StudentId, item.Id);
                        if (!string.IsNullOrEmpty(studentProjectNoStandard))
                        {
                            taskKey.Add(studentProjectNoStandard);
                        }
                    }
                    string dialogueContentRecordsSql = @"SELECT DISTINCT
                                                            	[key]
                                                            FROM
                                                            	AI_DialogueContentRecord WITH ( NOLOCK )
                                                            WHERE
                                                            	[Key] IN (@key1) 
                                                            	AND IsDeleted =0";
                    dialogueContentRecords = await DBSqlSugar.SqlQueryable<AI_DialogueContentRecord>(dialogueContentRecordsSql).AddParameters(new { key1 = taskKey }).ToListAsync();
                }

                //处理阶段信息
                foreach (var stage in projectStages)
                {
                    //获取当前阶段的任务
                    List<GetStudentDoProjectTaskListStageTaskInfoOutput> projectStageTask = projectStageTasks.Where(p => p.ProjectStageId == stage.Id).OrderBy(p => p.Order).ToList();
                    foreach (var task in projectStageTask)
                    {
                        //获取学生作答记录
                        List<AI_StudentDoProjectTask> doStageTasks = studentDoProjectTasks.Where(p => p.ProjectStageTaskId == task.Id).OrderBy(p => p.Order).ToList();
                        foreach (var doTask in doStageTasks)
                        {
                            GetStudentDoProjectTaskListNumberOutput doTaskNumberOutput = new GetStudentDoProjectTaskListNumberOutput();
                            doTaskNumberOutput.Number = doStageTasks.IndexOf(doTask) + 1;
                            doTaskNumberOutput.TaskSubmitId = doTask.Id;
                            if (doTask.IsStandard.Value)
                            {
                                doTaskNumberOutput.IsBackups = false;
                            }
                            else
                            {
                                //获取学生端-项目化实践未达标key
                                string studentProjectNoStandard = AIAgentKeys.GetStudentProjectNoStandardKey(projectInfoOutput.AgentId, doTask.ProjectStageTaskId, input.StudentId, doTask.Id);
                                //验证是否存在
                                if (dialogueContentRecords.Where(p => p.Key == studentProjectNoStandard).FirstOrDefault() == null)
                                {
                                    doTaskNumberOutput.IsBackups = false;
                                }
                                else
                                {
                                    doTaskNumberOutput.IsBackups = true;
                                }
                            }
                            task.Numbers.Add(doTaskNumberOutput);
                        }

                        if (doStageTasks.Count > 0)
                        {
                            task.SubmitTime = doStageTasks.OrderByDescending(p => p.Order).FirstOrDefault()?.CreateTime.ToString("yyyy/MM/dd HH:mm:ss");
                        }
                    }
                    stage.ProjectStageTaskInfos = projectStageTask;
                }

                projectInfoOutput.ProjectStageInfos = projectStages;
                return projectInfoOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取项目化实践综合分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetProjectSynthesizeAnalyseOutput> GetProjectSynthesizeAnalyse(GetProjectSynthesizeAnalyseInput input)
        {
            try
            {
                //获取项目化实践任务基础信息
                GetProjectSynthesizeAnalyseOutput projectInfoOutput = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetProjectSynthesizeAnalyseOutput()
                    {
                        AgentId = p.AgentId,
                        ProjectId = p.Id,
                        ProjectName = p.Name,
                        ProjectIntroduce = p.Introduce,
                        ProjectLogo = p.TaskLogo
                    })
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (projectInfoOutput == null)
                {
                    throw new BusException("项目化实践Id异常!");
                }

                //项目化实践阶段
                List<GetProjectSynthesizeAnalyseStageOutput> projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.IsDeleted == false)
                    .OrderBy(p => p.Order)
                    .Select(p => new GetProjectSynthesizeAnalyseStageOutput()
                    {
                        Id = p.Id,
                        Name = p.Name
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //项目化实践阶段任务
                List<AI_ProjectStageTask> projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.IsDeleted == false)
                    .OrderBy(p => p.ProjectStageId)
                    .OrderBy(p => p.Order)
                    .Select(p => new AI_ProjectStageTask()
                    {
                        Id = p.Id,
                        ProjectStageId = p.ProjectStageId,
                        Name = p.Name,
                        Order = p.Order,
                        TaskType = p.TaskType
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //获取班级学生数量
                List<string> studentIds = await DBSqlSugar.Queryable<Exam_Student>().Where(p => p.ClassId == input.ClassId && p.Deleted == false).Select(p => p.Id).With(SqlWith.NoLock).ToListAsync();
                projectInfoOutput.StudentCount = studentIds.Count;

                //获取班级学生提交数据
                string stuDoTaskSql = @"SELECT
                                        	dop.Id,
                                        	dop.ProjectStageId,
                                        	dop.ProjectStageTaskId,
                                        	dop.StudentId,
                                        	dop.Score,
                                        	dop.IsStandard,
                                        	dop.[Order] 
                                        FROM
                                        	AI_StudentDoProjectTask dop WITH ( NOLOCK )
                                        	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON dop.StudentId= stu.Id 
                                        	AND stu.Deleted= 0 
                                        WHERE
                                        	dop.IsDeleted= 0 
                                        	AND stu.ClassId= @classId 
                                        	AND dop.ProjectId=@projectId";
                List<AI_StudentDoProjectTask> stuDoTaskInfos = await DBSqlSugar.SqlQueryable<AI_StudentDoProjectTask>(stuDoTaskSql).AddParameters(new { classId = input.ClassId, projectId = input.ProjectId }).ToListAsync();

                //平均等第
                decimal? avgScore = stuDoTaskInfos.Where(p => p.Order == 1).Average(p => p.Score);
                projectInfoOutput.AvgLevel = BusinessUtil.GetAvgLevel(avgScore.HasValue ? avgScore.Value : 0);
                projectInfoOutput.AvgLevelCount = stuDoTaskInfos.Count(p => p.Order == 1);

                //完成率
                projectInfoOutput.Finish = BusinessUtil.GetAccuracy(stuDoTaskInfos.Count(p => p.IsStandard == true), projectStageTasks.Count(p => p.TaskType != 3) * projectInfoOutput.StudentCount, 1);

                //提交率
                projectInfoOutput.SubmitCount = projectStageTasks.Count(p => p.TaskType != 3) * projectInfoOutput.StudentCount;
                int submitDistinct = stuDoTaskInfos.GroupBy(task => new { task.ProjectStageTaskId, task.StudentId }).Select(group => group.First()).Count();
                projectInfoOutput.Submit = BusinessUtil.GetAccuracy(submitDistinct, projectInfoOutput.SubmitCount, 1);

                //参与率
                if (projectStageTasks.Count > 0)
                {
                    //验证是否存在对话
                    List<string> dialogueContentRecord = await DBSqlSugar.Queryable<AI_DialogueContentRecord>()
                        .Where(p => projectStageTasks.Select(p => p.Id).Contains(p.BusinessId) && p.IsDeleted == false)
                        .Select(p => p.Key).Distinct().With(SqlWith.NoLock).ToListAsync();
                    foreach (var studentId in studentIds)
                    {
                        if (dialogueContentRecord.Where(s => s.Contains(studentId)).Count() > 0)
                        {
                            projectInfoOutput.ParticipationCount += 1;
                        }
                    }

                    projectInfoOutput.Participation = BusinessUtil.GetAccuracy(projectInfoOutput.ParticipationCount, projectInfoOutput.StudentCount, 1);
                }

                //阶段完成情况
                foreach (var stage in projectStages)
                {
                    //获取当前阶段的任务
                    List<AI_ProjectStageTask> projectStageTask = projectStageTasks.Where(p => p.ProjectStageId == stage.Id && p.TaskType != 3).ToList();
                    foreach (var task in projectStageTask)
                    {
                        //是否完成
                        if (stuDoTaskInfos.Where(p => p.ProjectStageTaskId == task.Id && p.IsStandard == true).Count() >= projectInfoOutput.StudentCount)
                        {
                            stage.FinishCount += 1;
                        }
                    }
                    stage.TaskCount = projectStageTask.Count;
                    stage.Finish = BusinessUtil.GetAccuracy(stage.FinishCount, stage.TaskCount, 1);
                }
                projectInfoOutput.StageInfo = projectStages;

                //阶段任务平均分
                foreach (var stageTask in projectStageTasks.Where(p => p.TaskType != 3))
                {
                    projectInfoOutput.StageTaskAvgScore.Add(new GetProjectSynthesizeAnalyseScoreOutput()
                    {
                        StageTaskName = stageTask.Name,
                        AvgScore = stuDoTaskInfos.Where(p => p.ProjectStageTaskId == stageTask.Id && p.Order == 1).Average(p => p.Score)
                    });
                }

                return projectInfoOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取项目化实践阶段任务统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<GetProjectStageTaskCountOutput>> GetProjectStageTaskCount(GetProjectStageTaskCountInput input)
        {
            try
            {
                //项目化实践阶段
                List<GetProjectStageTaskCountOutput> projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>()
                    .Where(p => p.ProjectId == input.ProjectId && p.IsDeleted == false)
                    .OrderBy(p => p.Order)
                    .Select(p => new GetProjectStageTaskCountOutput()
                    {
                        Id = p.Id,
                        Name = p.Name
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //项目化实践阶段任务
                List<AI_ProjectStageTask> projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>()
                    .Where(p => p.ProjectId == input.ProjectId && p.IsDeleted == false)
                    .OrderBy(p => p.Order)
                    .Select(p => new AI_ProjectStageTask()
                    {
                        Id = p.Id,
                        ProjectStageId = p.ProjectStageId,
                        Target = p.Target,
                        Name = p.Name,
                        Order = p.Order,
                        TaskType = p.TaskType
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //获取班级学生Id
                List<string> studentIds = await DBSqlSugar.Queryable<Exam_Student>().Where(p => p.ClassId == input.ClassId && p.Deleted == false).Select(p => p.Id).With(SqlWith.NoLock).ToListAsync();

                //获取班级学生提交数据
                string stuDoTaskSql = @"SELECT
                                        	dop.Id,
                                        	dop.ProjectStageTaskId,
                                        	dop.StudentId,
                                        	dop.Score,
                                            dop.Level,
                                        	dop.[Order] 
                                        FROM
                                        	AI_StudentDoProjectTask dop WITH ( NOLOCK )
                                        	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON dop.StudentId= stu.Id 
                                        	AND stu.Deleted= 0 
                                        WHERE
                                        	dop.IsDeleted= 0 
                                        	AND stu.ClassId= @classId 
                                        	AND dop.ProjectId=@projectId";
                List<AI_StudentDoProjectTask> stuDoTaskInfos = await DBSqlSugar.SqlQueryable<AI_StudentDoProjectTask>(stuDoTaskSql).AddParameters(new { classId = input.ClassId, projectId = input.ProjectId }).ToListAsync();

                //对话主题
                string themeSql = @"SELECT
                                    	doq.ProjectStageTaskId,
                                    	pstq.Name
                                    FROM
                                    	AI_StudentDoProjectTaskQuestion doq WITH ( NOLOCK )
                                    	INNER JOIN AI_ProjectStageTaskQuestion pstq WITH ( NOLOCK ) ON doq.QuestionId= pstq.Id 
                                    	AND pstq.IsDeleted= 0
                                    	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON doq.StudentId= stu.Id 
                                    	AND stu.Deleted= 0 
                                    WHERE
                                    	doq.IsDeleted= 0 
                                    	AND doq.ProjectId= @projectId
                                    	AND stu.ClassId= @classId
                                    GROUP BY
                                    	doq.ProjectStageTaskId,
                                    	pstq.Name";
                List<GetDoProjectStageTaskThemes> taskThemes = await DBSqlSugar.SqlQueryable<GetDoProjectStageTaskThemes>(themeSql).AddParameters(new { classId = input.ClassId, projectId = input.ProjectId }).ToListAsync();

                //对话记录Key
                List<AI_DialogueContentRecord> dialogueContentRecord = new List<AI_DialogueContentRecord>();
                if (projectStageTasks.Count > 0)
                {
                    List<AI_DialogueContentRecord> dialogues = await DBSqlSugar.Queryable<AI_DialogueContentRecord>()
                    .Where(p => projectStageTasks.Select(p => p.Id).Contains(p.BusinessId) && p.IsDeleted == false)
                    .Select(p => new AI_DialogueContentRecord()
                    {
                        Key = p.Key,
                        BusinessId = p.BusinessId,
                        CreateTime = p.CreateTime
                    }).With(SqlWith.NoLock).ToListAsync();

                    foreach (var studentId in studentIds)
                    {
                        List<AI_DialogueContentRecord> dialogue = dialogues.Where(p => p.Key.Contains(studentId)).ToList();
                        if (dialogue.Count > 0)
                        {
                            dialogueContentRecord.AddRange(dialogue);
                        }
                    }
                }

                //处理阶段
                foreach (var stage in projectStages)
                {
                    //获取当前阶段的任务
                    List<AI_ProjectStageTask> projectStageTask = projectStageTasks.Where(p => p.ProjectStageId == stage.Id).OrderBy(p => p.Id).ToList();
                    foreach (var task in projectStageTask)
                    {
                        GetProjectStageTaskInfoOutput stageTaskInfoOutput = new GetProjectStageTaskInfoOutput()
                        {
                            TaskType = task.TaskType,
                            Name = task.Name,
                            Target = task.Target
                        };

                        //当前任务对话key
                        List<string> taskDialogueKeys = dialogueContentRecord.Where(p => p.BusinessId == task.Id).Select(p => p.Key).ToList();

                        //参与率、查询率
                        int participationCount = 0;
                        foreach (var studentId in studentIds)
                        {
                            int count = taskDialogueKeys.Where(s => s.Contains(studentId)).Count();
                            if (count > 0)
                            {
                                participationCount += 1;
                            }
                        }
                        stageTaskInfoOutput.Participation = BusinessUtil.GetAccuracy(participationCount, studentIds.Count, 1);

                        //平均对话次数、平均查询次数
                        stageTaskInfoOutput.AvgDialogue = Convert.ToDecimal(taskDialogueKeys.Count) / studentIds.Count;

                        //上传率
                        int submitDistinct = stuDoTaskInfos.Where(p => p.ProjectStageTaskId == task.Id).Select(p => p.StudentId).Distinct().Count();
                        stageTaskInfoOutput.Submit = BusinessUtil.GetAccuracy(submitDistinct, studentIds.Count, 1);

                        //等第分布
                        stageTaskInfoOutput.TaskLevels = stuDoTaskInfos.Where(p => p.ProjectStageTaskId == task.Id && p.Order == 1)
                            .Select(p => new GetProjectStageTaskLevelOutput()
                            {
                                LevelName = p.Level
                            }).DistinctBy(p => p.LevelName).OrderBy(p => p.LevelName).ToList();
                        foreach (var level in stageTaskInfoOutput.TaskLevels)
                        {
                            level.LevelCount = stuDoTaskInfos.Where(p => p.ProjectStageTaskId == task.Id && p.Level == level.LevelName && p.Order == 1).Count();
                        }

                        //主题
                        stageTaskInfoOutput.Themes = taskThemes.Where(p => p.ProjectStageTaskId == task.Id).Select(p => p.Name).Distinct().ToList();

                        //平均得分
                        stageTaskInfoOutput.AvgScore = stuDoTaskInfos.Where(p => p.ProjectStageTaskId == task.Id && p.Order == 1).Average(p => p.Score);

                        //查询趋势
                        stageTaskInfoOutput.QueryCount = dialogueContentRecord.Where(p => p.BusinessId == task.Id).Select(p => new GetProjectStageTaskQueryCountOutput()
                        {
                            Title = p.CreateTime.Value.ToString("yyyy/MM/dd")
                        }).DistinctBy(p => p.Title).ToList();
                        foreach (var query in stageTaskInfoOutput.QueryCount)
                        {
                            query.Count = dialogueContentRecord.Where(p => p.BusinessId == task.Id && p.CreateTime.Value.ToString("yyyy/MM/dd") == query.Title).Count();
                        }
                        stageTaskInfoOutput.QueryCount = stageTaskInfoOutput.QueryCount.OrderBy(p => p.Title).ToList();

                        stage.Tasks.Add(stageTaskInfoOutput);
                    }
                }

                return projectStages;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取项目化实践学生统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetProjectStudentCountOutput> GetProjectStudentCount(GetProjectStudentCountInput input)
        {
            try
            {
                //获取班级学生
                List<Exam_Student> studentInfos = await DBSqlSugar.Queryable<Exam_Student>()
                    .Where(p => p.ClassId == input.ClassId && p.Deleted == false)
                    .Select(p => new Exam_Student()
                    {
                        Id = p.Id,
                        StudentNo = p.StudentNo,
                        RealName = p.RealName
                    })
                    .OrderBy(p => p.StudentNo)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //获取班级学生提交数据
                string stuDoTaskSql = @"SELECT
                                        	dop.Id,
                                        	dop.ProjectStageTaskId,
                                        	dop.StudentId,
                                        	dop.IsStandard,
                                            dop.CreateTime
                                        FROM
                                        	AI_StudentDoProjectTask dop WITH ( NOLOCK )
                                        	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON dop.StudentId= stu.Id 
                                        	AND stu.Deleted= 0 
                                        WHERE
                                        	dop.IsDeleted= 0 
                                        	AND stu.ClassId= @classId 
                                        	AND dop.ProjectId=@projectId";
                List<AI_StudentDoProjectTask> stuDoTaskInfos = await DBSqlSugar.SqlQueryable<AI_StudentDoProjectTask>(stuDoTaskSql).AddParameters(new { classId = input.ClassId, projectId = input.ProjectId }).ToListAsync();

                //项目化实践阶段任务数量
                int projectStageTaskCount = await DBSqlSugar.Queryable<AI_ProjectStageTask>().Where(p => p.ProjectId == input.ProjectId && p.IsDeleted == false && p.TaskType != 3).With(SqlWith.NoLock).CountAsync();

                //学生信息
                List<GetProjectStudentInfoOutput> studentInfoOutputs = new List<GetProjectStudentInfoOutput>();
                foreach (var student in studentInfos)
                {
                    GetProjectStudentInfoOutput studentInfoOutput = new GetProjectStudentInfoOutput()
                    {
                        StudentId = student.Id,
                        StudentName = student.RealName,
                        StudentNum = student.StudentNo,
                        ProgressBar = BusinessUtil.GetAccuracy(stuDoTaskInfos.Where(p => p.StudentId == student.Id && p.IsStandard == true).Count(), projectStageTaskCount, 1),
                        NoStandard = stuDoTaskInfos.Where(p => p.StudentId == student.Id && p.IsStandard == false).Count(),
                        SubmitTime = stuDoTaskInfos.Where(p => p.StudentId == student.Id).OrderByDescending(p => p.CreateTime).FirstOrDefault()?.CreateTime.ToString("yyyy/MM/dd HH:mm:ss")
                    };
                    studentInfoOutputs.Add(studentInfoOutput);
                }

                return new GetProjectStudentCountOutput()
                {
                    ProjectId = input.ProjectId,
                    StudentInfos = studentInfoOutputs
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }
    }
}
