﻿/*
 *所有关于{TableName}类的业务代码应在此处编写
*可使用repository.调用常用方法，获取EF/Dapper等信息
*如果需要事务请使用repository.DbContextBeginTransaction
*也可使用DBServerProvider.手动获取数据库相关信息
*用户信息、权限、角色等使用UserContext.Current操作
*{TableName}Service对增、删、改查、导入、导出、审核业务代码扩展参照ServiceFunFilter
*/
using {StartName}.Core.BaseProvider;
using {StartName}.Core.Extensions.AutofacManager;
using {StartName}.Entity.DomainModels;
using System.Linq;
using Uwoo.Core.Utilities;
using System.Linq.Expressions;
using {StartName}.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using {Namespace}.IRepositories;

namespace {Namespace}.Services
{
    public partial class {TableName}Service
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly I{TableName}Repository _repository;//访问数据库

        [ActivatorUtilitiesConstructor]
        public {TableName}Service(
            I{TableName}Repository dbRepository,
            IHttpContextAccessor httpContextAccessor
            )
        : base(dbRepository)
        {
            _httpContextAccessor = httpContextAccessor;
            _repository = dbRepository;
            //多租户会用到这init代码，其他情况可以不用
            //base.Init(dbRepository);
        }
  }
}
