﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Entity.DomainModels;
using Uwoo.Model;
using Uwoo.Model.Login;
using UwooAgent.Entity.DomainModels.Base;
using UwooAgent.Entity.DomainModels.User;
using UwooAgent.Model.UniLogin;

namespace Uwoo.System.IServices.Login
{
    public interface ILoginService : IService<Base_User>
    {
        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        SubmitLoginOutput SubmitLogin(SubmitLoginInput input);


        /// <summary>
        /// 用户是否登录
        /// </summary>
        /// <param name="sessid">sessionid</param>
        /// <returns></returns>
        Task<string> IsUserLogin(string sessid);

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        Task<UniUserInfo> GetUserByUidAsync(string uid);

        /// <summary>
        /// 获取绑定用户信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        Task<Base_User> GetTokenInfoByUniUid(string uid);

        Task AddorUpdateXHUser(Base_User userInfo, Base_UserRole base_UserRole, int groupId);

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="tempid">临时授权码id</param>
        /// <returns></returns>
        Task<AjaxResult<LoginVo>> UniLogin(string tempid);
    }
}
