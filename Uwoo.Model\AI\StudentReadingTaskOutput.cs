using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 学生阅读理解项目列表输出
    /// </summary>
    public class StudentReadingTaskListOutput
    {
        /// <summary>
        /// 项目列表
        /// </summary>
        public List<StudentReadingTaskListItemOutput> TaskList { get; set; } = new List<StudentReadingTaskListItemOutput>();

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }
    }

    /// <summary>
    /// 学生阅读理解项目列表项输出
    /// </summary>
    public class StudentReadingTaskListItemOutput
    {
        /// <summary>
        /// 项目Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 包含的任务类型名称
        /// </summary>
        public string? TaskTypeName { get; set; }

        /// <summary>
        /// 项目状态（1:未开始，2:进行中，3:已完成）
        /// </summary>
        public int TaskStatus { get; set; }

        /// <summary>
        /// 项目状态名称
        /// </summary>
        public string? TaskStatusName { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 项目阶段数量
        /// </summary>
        public int StageCount { get; set; }

        /// <summary>
        /// 项目任务总数量
        /// </summary>
        public int TotalTaskCount { get; set; }

        /// <summary>
        /// 已完成任务数量
        /// </summary>
        public int CompletedTaskCount { get; set; }

        /// <summary>
        /// 项目完成进度（百分比）
        /// </summary>
        public decimal Progress { get; set; }

        /// <summary>
        /// 项目平均分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 是否达标
        /// </summary>
        public bool IsStandard { get; set; }

        /// <summary>
        /// AI综合评价等第
        /// </summary>
        public string? AIGrade { get; set; }
    }

    /// <summary>
    /// 学生阅读理解项目详情输出
    /// </summary>
    public class StudentReadingTaskDetailsOutput
    {
        /// <summary>
        /// 项目Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 项目化实践Logo
        /// </summary>
        public string? ProjectLogo { get; set; }

        /// <summary>
        /// 包含的任务类型名称
        /// </summary>
        public string? TaskTypeName { get; set; }

        /// <summary>
        /// 项目状态（1:未开始，2:进行中，3:已完成）
        /// </summary>
        public int TaskStatus { get; set; }

        /// <summary>
        /// 项目阶段数量
        /// </summary>
        public int StageCount { get; set; }

        /// <summary>
        /// 项目任务总数量
        /// </summary>
        public int TotalTaskCount { get; set; }

        /// <summary>
        /// 已完成任务数量
        /// </summary>
        public int CompletedTaskCount { get; set; }

        /// <summary>
        /// 项目完成进度（百分比）
        /// </summary>
        public decimal Progress { get; set; }

        /// <summary>
        /// 项目平均分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 是否达标
        /// </summary>
        public bool IsStandard { get; set; }

        /// <summary>
        /// AI综合评价等第
        /// </summary>
        public string? AIGrade { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 项目阶段列表
        /// </summary>
        public List<StudentProjectStageOutput> Stages { get; set; } = new List<StudentProjectStageOutput>();
    }

    /// <summary>
    /// 学生项目阶段输出
    /// </summary>
    public class StudentProjectStageOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? StageId { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string? Describe { get; set; }

        /// <summary>
        /// 阶段排序
        /// </summary>
        public int StageOrder { get; set; }

        /// <summary>
        /// 是否锁定
        /// </summary>
        public bool IsLock { get; set; }

        /// <summary>
        /// 阶段任务列表
        /// </summary>
        public List<StudentProjectStageTaskOutput> Tasks { get; set; } = new List<StudentProjectStageTaskOutput>();
    }

    /// <summary>
    /// 学生项目阶段任务输出
    /// </summary>
    public class StudentProjectStageTaskOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:思维导图、7:选词填空）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 评分标准
        /// </summary>
        public string? ScoreStandard { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string? Demand { get; set; }

        /// <summary>
        /// 问答范围
        /// </summary>
        public string? Scope { get; set; }

        /// <summary>
        /// 角色设定
        /// </summary>
        public string? RoleSetting { get; set; }

        /// <summary>
        /// 开场白
        /// </summary>
        public string? Prologue { get; set; }

        /// <summary>
        /// 音色Id
        /// </summary>
        public string? ToneId { get; set; }

        /// <summary>
        /// 角色名称（用于AI展示名称）
        /// </summary>
        public string? RoleName { get; set; }

        /// <summary>
        /// 任务排序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 任务状态（1:未开始，2:进行中，3:已完成）
        /// </summary>
        public int State { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompleteTime { get; set; }

        /// <summary>
        /// 评估分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 是否达标
        /// </summary>
        public bool IsStandard { get; set; }

        /// <summary>
        /// AI评价等第
        /// </summary>
        public string? AIGrade { get; set; }

        /// <summary>
        /// 是否开启提交条件（组间任务设置）
        /// </summary>
        public bool GroupIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（组间任务设置）
        /// </summary>
        public bool GroupIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（组间任务设置）
        /// </summary>
        public decimal GroupAssessmentScore { get; set; }

        /// <summary>
        /// 是否开启提交条件（任务点设置）
        /// </summary>
        public bool TaskIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（任务点设置）
        /// </summary>
        public bool TaskIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（任务点设置）
        /// </summary>
        public decimal TaskAssessmentScore { get; set; }

        /// <summary>
        /// 是否开启视频观看条件（任务点设置） 
        /// </summary>
        public bool? TaskIsWatchVideo { get; set; }

        /// <summary>
        /// 是否开启文档阅读条件（任务点设置）
        /// </summary>
        public bool? TaskIsReadAllDocuments { get; set; }

        /// <summary>
        /// 是否锁定
        /// </summary>
        public bool IsLock { get; set; }

        /// <summary>
        /// 是否已提交但未达标
        /// </summary>
        public bool IsSubmitNoStandard { get; set; }

        /// <summary>
        /// 任务提交记录Id
        /// </summary>
        public string? TaskSubmitId { get; set; }

        /// <summary>
        /// 任务特殊配置（根据任务类型）
        /// </summary>
        public StudentTaskConfigOutput? TaskConfig { get; set; }
    }

    /// <summary>
    /// 学生任务特殊配置输出
    /// </summary>
    public class StudentTaskConfigOutput
    {
        #region 视频任务配置（TaskType=4）

        // 组间解锁条件
        /// <summary>
        /// 总观看时长要求（分钟）- 组间解锁条件
        /// </summary>
        public int? GroupTotalWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否要求观看所有视频 - 组间解锁条件
        /// </summary>
        public bool? GroupIsWatchAllVideos { get; set; }

        // 任务点解锁条件
        /// <summary>
        /// 总观看时长要求（分钟）- 任务点解锁条件
        /// </summary>
        public int? TaskTotalWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否要求观看所有视频 - 任务点解锁条件
        /// </summary>
        public bool? TaskIsWatchAllVideos { get; set; }

        /// <summary>
        /// 视频资源列表
        /// </summary>
        public List<StudentVideoResourceOutput> VideoResources { get; set; } = new List<StudentVideoResourceOutput>();

        #endregion

        #region 文档任务配置（TaskType=5）

        // 组间解锁条件
        /// <summary>
        /// 是否需要阅读全部文档 - 组间解锁条件
        /// </summary>
        public bool? GroupIsReadAllDocuments { get; set; }

        // 任务点解锁条件
        /// <summary>
        /// 是否需要阅读全部文档 - 任务点解锁条件
        /// </summary>
        public bool? TaskIsReadAllDocuments { get; set; }

        /// <summary>
        /// 文档资源列表
        /// </summary>
        public List<StudentDocumentResourceOutput> DocumentResources { get; set; } = new List<StudentDocumentResourceOutput>();

        #endregion

        #region 选词填空任务配置（TaskType=7）

        /// <summary>
        /// 题目内容
        /// </summary>
        public string? QuestionContent { get; set; }

        /// <summary>
        /// 正确答案列表
        /// </summary>
        public List<string> CorrectAnswers { get; set; } = new List<string>();

        /// <summary>
        /// 干扰项列表
        /// </summary>
        public List<string> DistractorWords { get; set; } = new List<string>();

        /// <summary>
        /// 自定义背景图片地址
        /// </summary>
        public string? CustomBackgroundImage { get; set; }

        #endregion
    }

    /// <summary>
    /// 学生视频资源输出
    /// </summary>
    public class StudentVideoResourceOutput
    {
        /// <summary>
        /// 视频Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 视频标题
        /// </summary>
        public string? VideoTitle { get; set; }

        /// <summary>
        /// 视频描述
        /// </summary>
        public string? VideoDescription { get; set; }

        /// <summary>
        /// 视频资源地址
        /// </summary>
        public string? VideoUrl { get; set; }

        /// <summary>
        /// 视频时长（秒）
        /// </summary>
        public int? Duration { get; set; }

        /// <summary>
        /// 视频排序
        /// </summary>
        public int VideoOrder { get; set; }

        /// <summary>
        /// 是否已观看
        /// </summary>
        public bool HasWatched { get; set; }

        /// <summary>
        /// 累计观看时长（秒）
        /// </summary>
        public int TotalWatchDuration { get; set; }
    }

    /// <summary>
    /// 学生文档资源输出
    /// </summary>
    public class StudentDocumentResourceOutput
    {
        /// <summary>
        /// 文档Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 文档标题
        /// </summary>
        public string? DocumentTitle { get; set; }

        /// <summary>
        /// 文档描述
        /// </summary>
        public string? DocumentDescription { get; set; }

        /// <summary>
        /// 文档资源地址
        /// </summary>
        public string? DocumentUrl { get; set; }

        /// <summary>
        /// 文档类型
        /// </summary>
        public string? DocumentType { get; set; }

        /// <summary>
        /// 文档排序
        /// </summary>
        public int DocumentOrder { get; set; }

        /// <summary>
        /// 是否已阅读
        /// </summary>
        public bool HasRead { get; set; }

        /// <summary>
        /// 阅读时间
        /// </summary>
        public DateTime? ReadTime { get; set; }
    }

    /// <summary>
    /// 阅读理解情景对话提交输出
    /// </summary>
    public class ReadingDialogueSubmitOutput
    {
        /// <summary>
        /// 是否达到任务设定标准
        /// </summary>
        public bool IsStandard { get; set; }

        /// <summary>
        /// 评估结果
        /// </summary>
        public string? AssessmentResult { get; set; }
    }

    /// <summary>
    /// 阅读理解成果评估输出
    /// </summary>
    public class ReadingAssessmentOutput
    {
        /// <summary>
        /// 是否达到任务设定标准
        /// </summary>
        public bool? IsStandard { get; set; }

        /// <summary>
        /// 评估结果
        /// </summary>
        public string? AssessmentResult { get; set; }
    }

    /// <summary>
    /// 查询任务提交记录输出
    /// </summary>
    public class GetTaskSubmitRecordsOutput
    {
        /// <summary>
        /// 提交记录列表
        /// </summary>
        public List<TaskSubmitRecordItem> Records { get; set; } = new List<TaskSubmitRecordItem>();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }
    }

    /// <summary>
    /// 任务提交记录项
    /// </summary>
    public class TaskSubmitRecordItem
    {
        /// <summary>
        /// 提交记录Id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string StudentId { get; set; }

        /// <summary>
        /// 阅读理解项目阶段任务Id
        /// </summary>
        public string ReadingProjectStageTaskId { get; set; }

        /// <summary>
        /// 阅读理解项目Id
        /// </summary>
        public string ReadingProjectId { get; set; }

        /// <summary>
        /// 任务状态（1:未开始，2:进行中，3:已完成）
        /// </summary>
        public int TaskStatus { get; set; }

        /// <summary>
        /// 提交内容（JSON格式存储）
        /// </summary>
        public string? SubmitContent { get; set; }

        /// <summary>
        /// 评估分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 是否达标
        /// </summary>
        public bool IsStandard { get; set; }

        /// <summary>
        /// AI评估结果
        /// </summary>
        public string? AssessmentResult { get; set; }

        /// <summary>
        /// AI评价等第
        /// </summary>
        public string? AIGrade { get; set; }

        /// <summary>
        /// 匹配的高频问题（JSON格式存储问题名称列表）
        /// </summary>
        public string? MatchedQuestions { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        // 关联信息字段
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? StageName { get; set; }

        /// <summary>
        /// 阶段排序
        /// </summary>
        public int StageOrder { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? TaskName { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:思维导图、7:选词填空）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务排序
        /// </summary>
        public int TaskOrder { get; set; }
    }
}
