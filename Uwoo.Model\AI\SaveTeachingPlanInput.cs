﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 保存教案入参
    /// </summary>
    public class SaveTeachingPlanInput
    {
        /// <summary>
        /// 教案类型（1文本、2word、3PPT）
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 教案名称（文本类型教案：自定义\教案名称。word或PPT类型教案:word或PPT文件名称）
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 教案文本（文本类型教案必填）
        /// </summary>
        public string? Text { get; set; }

        /// <summary>
        /// 文件地址（word/ppt类型教案必填）
        /// </summary>
        public string? FileUrl { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public string? SchoolId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string? GradeId { get; set; }

        /// <summary>
        /// 学科ID
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 教案创建记录Id（用于下载文多多PPT）
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 文多多PPTId（用于下载文多多PPT）
        /// </summary>
        public string? PPTId { get; set; }

        /// <summary>
        /// 是否属于下载文多多PPT（来源文多多PPT）
        /// </summary>
        public bool IsDownloadPPT { get; set; } = false;
    }
}
