﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.CacheManager.IService;

namespace UwooAgent.Core.CacheManager.IBusinessCacheService
{
    /// <summary>
    /// OfficeFile缓存
    /// </summary>
    public interface IOfficeFileCacheService : IRedisService
    {
        /// <summary>
        /// 获取Office文件版本
        /// </summary>
        /// <param name="id"></param>
        long GetOfficeVersion(string id);
    }
}
