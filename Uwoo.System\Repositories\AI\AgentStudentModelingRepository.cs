﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.System.IRepositories.AI;

namespace UwooAgent.System.Repositories.AI
{
    /// <summary>
    /// 智能体_学生端建模
    /// </summary>
    public class AgentStudentModelingRepository : RepositoryBase<AI_AgentTask>, IAgentStudentModelingRepository
    {
        public AgentStudentModelingRepository(VOLContext dbContext) : base(dbContext)
        {

        }

        public static IAgentStudentModelingRepository Instance
        {
            get
            {
                return AutofacContainerModule.GetService<IAgentStudentModelingRepository>();
            }
        }
    }
}