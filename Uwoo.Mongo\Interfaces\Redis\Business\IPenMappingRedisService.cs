﻿// -- Function：IPenMappingRedisService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/30 17:27

namespace Uwoo.Mongo.Interfaces.Redis.Business;

/// <summary>
/// 点阵笔和用户映射服务
/// </summary>
public interface IPenMappingRedisService : IRedisService
{
    /// <summary>
    /// 获取点阵笔绑定的用户
    /// </summary>
    /// <param name="mac">mac</param>
    /// <returns></returns>
    Task<string> HGetMappingUserAsync(string mac);

    /// <summary>
    /// 添加点阵笔和用户绑定
    /// </summary>
    /// <param name="mac"></param>
    /// <param name="userid"></param>
    /// <returns></returns>
    Task HSetMappingUserAsync(string mac, string userid);

    /// <summary>
    /// 获取点阵笔绑定的用户
    /// </summary>
    /// <param name="mac">mac</param>
    /// <returns></returns>
    string HGetMappingUser(string mac);

    /// <summary>
    /// 添加点阵笔和用户绑定
    /// </summary>
    /// <param name="mac"></param>
    /// <param name="userid"></param>
    /// <returns></returns>
    void HSetMappingUser(string mac, string userid);

    /// <summary>
    /// 设置点阵笔用户类型
    /// </summary>
    /// <param name="mac">编号</param>
    /// <param name="usertype">0.学生 1.教师</param>
    void HSetPenRole(string mac, int usertype);

    /// <summary>
    /// 删除点阵笔绑定的用户
    /// </summary>
    /// <param name="mac"></param>
    void HDeleteMappingUser(string mac);

    /// <summary>
    /// 删除相关角色
    /// </summary>
    /// <param name="mac"></param>
    void HDeletePenRole(string mac);
}