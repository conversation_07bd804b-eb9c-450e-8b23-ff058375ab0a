﻿//  -- Function：CorrectPenLogService.cs
//  --- Project：X.PenServer.Services
//  ---- Remark：
//  ---- Author：Lucifer
//  ------ Date：2023/10/25 01:30:50

namespace Uwoo.Mongo.Services.Mongo;

using MongoDB.Driver;
using Uwoo.Contracts.Config;
using Uwoo.Mongo.Infrastructure;
using Uwoo.Mongo.Interfaces.Business;
using Uwoo.Mongo.Models;

/// <summary>
/// 订正笔迹
/// </summary>
public class CorrectPenLogService : MongoAutoService<CorrectPenLog>, ICorrectPenLogService
{
    /// <inheritdoc />
    public CorrectPenLogService(IMongoConfig config) : base(config)
    {
    }

    /// <inheritdoc />
    protected override void CreateIndex(IMongoCollection<CorrectPenLog> collection)
    {
        var userid_builder = Builders<CorrectPenLog>.IndexKeys
            .Ascending(x => x.UserId)
            .Ascending(x => x.PageId)
            .Ascending(x => x.Page)
            .Ascending(x => x.AddTime)
            .Ascending(x => x.Mac);
        collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");

        var mac_builder = Builders<CorrectPenLog>.IndexKeys
            .Ascending(x => x.Mac)
            .Ascending(x => x.UserId)
            .Ascending(x => x.PageId)
            .Ascending(x => x.Page)
            .Ascending(x => x.AddTime);

        collection.Indexes.CreateIndex(mac_builder, collection.CollectionNamespace.CollectionName + "_Mac_Key");
    }

    #region Implementation of ICorrectPenLogService

    /// <inheritdoc />
    public List<CorrectPenLog> GetAll(string colname, List<int> page, string userid)
    {
        var mongo = GetConnection(colname);
        var list = mongo.Find(x => page.Contains(x.PageId) && x.UserId == userid).ToList();
        return list.OrderBy(x => x.Mid).ToList();
    }

    /// <inheritdoc />
    public List<CorrectPenLog> GetAll(string colname, string userid, int page,int? year=null)
    {
        var mongo = GetConnection(colname, year);
        var list = mongo.Find(x => x.PageId == page && x.UserId == userid).ToList();
        return list.OrderBy(x => x.Mid).ToList();
    }

    /// <inheritdoc />
    public void DeletePenLog(string colname, List<int> page, List<string> userid)
    {
        var mongo = GetConnection(colname);
        mongo.DeleteMany(x => page.Contains(x.PageId) && userid.Contains(x.UserId));
    }

    #endregion
}