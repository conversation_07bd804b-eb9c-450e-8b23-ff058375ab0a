﻿// -- Function：IMongoAutoService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/11/07 17:49

using System.Linq.Expressions;
using MongoDB.Driver;
using Uwoo.Mongo.Models;

namespace Uwoo.Mongo.Interfaces;

using Uwoo.Mongo.Interfaces.Lifecycle;

/// <summary>
/// Mongo服务接口
/// </summary>
/// <remarks>自动根据年份分表</remarks>
public partial interface IMongoAutoService<T> : ISingletonService where T : MongoBaseModel, new()
{
	/// <summary>
	/// 通过主键获取数据
	/// </summary>
	/// <param name="id">主键id</param>
	/// <param name="colname">集合名称</param>
	/// <returns></returns>
	T Get(long id, string colname = "");

	/// <summary>
	/// 通过条件查询单条数据
	/// </summary>
	/// <param name="predicate">条件</param>
	/// <param name="colname">集合名称</param>
	/// <returns></returns>
	T Get(Expression<Func<T, bool>> predicate = null, string colname = "");

	/// <summary>
	/// 通过条件查询数据列表
	/// </summary>
	/// <param name="predicate">条件</param>
	/// <param name="colname">集合名称</param>
	/// <param name="options">查询选项</param>
	/// <param name="year"></param>
	/// <returns></returns>
	List<T> GetAll(Expression<Func<T, bool>> predicate = null, string colname = "", FindOptions options = null, int? year = null);

	/// <summary>
	/// 添加单条数据
	/// </summary>
	/// <param name="entity">数据实体</param>
	/// <param name="colname">集合名称</param>
	/// <returns></returns>
	void Add(T entity, string colname = "");

	/// <summary>
	/// 批量添加数据
	/// </summary>
	/// <param name="entities">数据实体列表</param>
	/// <param name="colname">集合名称</param>
	/// <returns></returns>
	void AddMany(IEnumerable<T> entities, string colname = "");

	/// <summary>
	/// 根据条件更新单条数据
	/// </summary>
	/// <param name="predicate">条件</param>
	/// <param name="entity">数据实体</param>
	/// <param name="colname">集合名称</param>
	/// <returns></returns>
	bool Update(Expression<Func<T, bool>> predicate, T entity, string colname = "");

	/// <summary>
	/// 根据主键更新单条数据
	/// </summary>
	/// <param name="id">主键id</param>
	/// <param name="entity">数据实体</param>
	/// <param name="colname">集合名称</param>
	/// <returns></returns>
	bool Update(long id, T entity, string colname = "");

	/// <summary>
	/// 根据id删除单条数据
	/// </summary>
	/// <param name="id">主键id</param>
	/// <param name="colname">集合名称</param>
	/// <returns></returns>
	bool Delete(long id, string colname = "");

	/// <summary>
	/// 根据条件删除单条数据
	/// </summary>
	/// <param name="predicate">条件</param>
	/// <param name="colname">集合名称</param>
	/// <returns></returns>
	bool Delete(Expression<Func<T, bool>> predicate, string colname = "");

	/// <summary>
	/// 根据条件批量删除数据
	/// </summary>
	/// <param name="predicate">条件</param>
	/// <param name="colname">集合名称</param>
	/// <returns></returns>
	bool DeleteMany(Expression<Func<T, bool>> predicate, string colname = "");
}