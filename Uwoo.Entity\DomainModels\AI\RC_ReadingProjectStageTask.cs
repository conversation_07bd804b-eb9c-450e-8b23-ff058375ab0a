using SqlSugar;
using System;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 阅读理解项目化实践阶段任务表
    /// </summary>
    [SugarTable("RC_ReadingProjectStageTask")]
    public class RC_ReadingProjectStageTask : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 阅读理解项目阶段Id
        /// </summary>
        public string ReadingProjectStageId { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估(作品评估)、2:情景对话、3:知识问答、4:视频、5:文档、6:思维导图、7:选词填空）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string Target { get; set; }

        /// <summary>
        /// 评分标准
        /// </summary>
        public string ScoreStandard { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string Demand { get; set; }

        /// <summary>
        /// 问答范围
        /// </summary>
        public string Scope { get; set; }

        /// <summary>
        /// 角色设定
        /// </summary>
        public string RoleSetting { get; set; }

        /// <summary>
        /// 开场白
        /// </summary>
        public string Prologue { get; set; }

        /// <summary>
        /// 音色Id
        /// </summary>
        public string ToneId { get; set; }

        /// <summary>
        /// 角色名称（用于AI展示名称）
        /// </summary>
        public string RoleName { get; set; }

        /// <summary>
        /// 是否开启提交条件（组间任务设置）
        /// </summary>
        public bool GroupIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（组间任务设置）
        /// </summary>
        public bool GroupIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（组间任务设置）
        /// </summary>
        public decimal GroupAssessmentScore { get; set; }

        /// <summary>
        /// 是否开启提交条件（任务点设置）
        /// </summary>
        public bool TaskIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（任务点设置）
        /// </summary>
        public bool TaskIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（任务点设置）
        /// </summary>
        public decimal TaskAssessmentScore { get; set; }

        /// <summary>
        /// 任务排序
        /// </summary>
        public int TaskOrder { get; set; }

        #region 新增任务类型特殊字段（4:视频、5:文档、6:思维导图、7:选词填空）

        // 视频任务解锁条件字段
        /// <summary>
        /// 总观看时长要求（分钟）- 组间解锁条件
        /// </summary>
        public int? GroupTotalWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否要求观看所有视频 - 组间解锁条件
        /// </summary>
        public bool? GroupIsWatchAllVideos { get; set; }

        /// <summary>
        /// 总观看时长要求（分钟）- 任务点解锁条件
        /// </summary>
        public int? TaskTotalWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否要求观看所有视频 - 任务点解锁条件
        /// </summary>
        public bool? TaskIsWatchAllVideos { get; set; }

        // 文档任务解锁条件字段
        /// <summary>
        /// 是否需要阅读全部文档 - 组间解锁条件
        /// </summary>
        public bool? GroupIsReadAllDocuments { get; set; }

        /// <summary>
        /// 是否需要阅读全部文档 - 任务点解锁条件
        /// </summary>
        public bool? TaskIsReadAllDocuments { get; set; }

        /// <summary>
        /// 题目内容 - 选词填空任务使用
        /// </summary>
        public string QuestionContent { get; set; }

        /// <summary>
        /// 正确答案（JSON格式存储，按填空顺序排列）- 选词填空任务使用
        /// </summary>
        public string CorrectAnswers { get; set; }

        /// <summary>
        /// 干扰项（JSON格式存储）- 选词填空任务使用
        /// </summary>
        public string DistractorWords { get; set; }

        /// <summary>
        /// 自定义背景图片地址 - 选词填空任务使用
        /// </summary>
        public string CustomBackgroundImage { get; set; }

        #endregion

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }
    }
}
