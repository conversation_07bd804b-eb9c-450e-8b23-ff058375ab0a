﻿// -- Function：IDapperService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/24 16:14

namespace Uwoo.Mongo.Interfaces.Dapper;

using System.Data;
using Uwoo.Mongo.Interfaces.Lifecycle;

/// <summary>
/// Dapper Service
/// </summary>
public interface IDapperService : ITransientService
{
    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    int Execute(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<int> ExecuteAsync(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    DataTable ExecuteTable(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<DataTable> ExecuteTableAsync(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    DataSet ExecuteDataSet(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<DataSet> ExecuteDataSetAsync(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    dynamic ExecuteScalar(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<dynamic> ExecuteScalarAsync(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    T ExecuteScalar<T>(string sql, object parameters = null, int? timeout = null) where T : class, new();

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<T> ExecuteScalarAsync<T>(string sql, object parameters = null, int? timeout = null) where T : class, new();


    /// <summary>
    /// 批量插入数据
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sql">插入语句</param>
    /// <param name="newObjects"></param>
    /// <returns></returns>
    bool BulkInsert<T>(string sql, List<T> newObjects);

	/// <summary>
	/// 查询首条数据
	/// </summary>
	/// <param name="sql">sql</param>
	/// <param name="parameters">参数</param>
	/// <param name="timeout">超时时间,以秒为单位</param>
	/// <returns></returns>
	dynamic QueryFirst(string sql, object parameters = null, int? timeout = null);


    /// <summary>
    /// 查询首条数据
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<dynamic> QueryFirstAsync(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 查询首条数据
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    T QueryFirst<T>(string sql, object parameters = null, int? timeout = null) where T : class, new();

    /// <summary>
    /// 查询首条数据
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<T> QueryFirstAsync<T>(string sql, object parameters = null, int? timeout = null) where T : class, new();

    /// <summary>
    /// 查询数据列表
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    IQueryable<dynamic> QueryList(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 查询数据列表
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<IQueryable<dynamic>> QueryListAsync(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 查询数据列表
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    IQueryable<T> QueryList<T>(string sql, object parameters = null, int? timeout = null) where T : class, new();

    /// <summary>
    /// 查询数据列表
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<IQueryable<T>> QueryListAsync<T>(string sql, object parameters = null, int? timeout = null) where T : class, new();

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    IDataReader ExecuteReader(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<IDataReader> ExecuteReaderAsync(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    T ExecuteReader<T>(string sql, object parameters = null, int? timeout = null) where T : class, new();

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<T> ExecuteReaderAsync<T>(string sql, object parameters = null, int? timeout = null) where T : class, new();

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    List<T> ExecuteReaderList<T>(string sql, object parameters = null, int? timeout = null) where T : class, new();

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    Task<List<T>> ExecuteReaderListAsync<T>(string sql, object parameters = null, int? timeout = null) where T : class, new();

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    /// <returns></returns>
    bool Transaction(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    /// <returns></returns>
    Task<bool> TransactionAsync(string sql, object parameters = null, int? timeout = null);

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sqls">sql列表</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    bool Transaction(Dictionary<string, object> sqls, int? timeout = null);

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sqls">sql列表</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    Task<bool> TransactionAsync(Dictionary<string, object> sqls, int? timeout = null);

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sqls">sql列表</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    /// <returns></returns>
    bool Transaction(List<string> sqls, int? timeout = null);

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sqls">sql列表</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    Task<bool> TransactionAsync(List<string> sqls, int? timeout = null);

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="ta"></param>
    /// <param name="tb"></param>
    void ExecuteMultip<TA, TB>(out List<TA> ta, out List<TB> tb, string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new();

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="ta"></param>
    /// <param name="tb"></param>
    /// <param name="tc"></param>
    void ExecuteMultip<TA, TB, TC>(out List<TA> ta, out List<TB> tb, out List<TC> tc, string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new();

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <typeparam name="TD"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="ta"></param>
    /// <param name="tb"></param>
    /// <param name="tc"></param>
    /// <param name="td"></param>
    void ExecuteMultip<TA, TB, TC, TD>(out List<TA> ta, out List<TB> tb, out List<TC> tc, out List<TD> td, string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new()
        where TD : class, new();

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    Tuple<List<TA>, List<TB>> ExecuteMultip<TA, TB>(string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new();

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    Task<Tuple<List<TA>, List<TB>>> ExecuteMultipAsync<TA, TB>(string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new();

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    Tuple<List<TA>, List<TB>, List<TC>> ExecuteMultip<TA, TB, TC>(string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new();

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    Task<Tuple<List<TA>, List<TB>, List<TC>>> ExecuteMultipAsync<TA, TB, TC>(string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new();

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <typeparam name="TD"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    Tuple<List<TA>, List<TB>, List<TC>, List<TD>> ExecuteMultip<TA, TB, TC, TD>(string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new()
        where TD : class, new();

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <typeparam name="TD"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    Task<Tuple<List<TA>, List<TB>, List<TC>, List<TD>>> ExecuteMultipAsync<TA, TB, TC, TD>(string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new()
        where TD : class, new();
}