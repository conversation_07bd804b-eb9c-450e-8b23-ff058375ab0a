﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_教师端建模
    /// </summary>
    public interface IAgentTeacherModelingService : IService<AI_AgentTask>
    {
        /// <summary>
        /// 保存/编辑建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<string> SaveModelingInfo(SaveModelingInfoInput input);

        /// <summary>
        /// 获取建模阶段任务默认信息
        /// </summary>
        /// <returns></returns>
        List<GetModelingStageDefaultInfoOutput> GetModelingStageTaskDefaultInfo();

        /// <summary>
        /// 获取建模信息
        /// </summary>
        /// <returns></returns>
        Task<GetModelingInfoOutput> GetModelingInfo(GetModelingInfoInput input);

        /// <summary>
        /// 删除建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<bool> DeleteModelingInfo(DeleteModelingInfoInput input);

        /// <summary>
        /// 发布建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task PublishModelingInfo(PublishModelingInfoInput input);

        /// <summary>
        /// 撤销建模发布
        /// </summary>
        /// <param name="input">撤销发布参数</param>
        /// <returns></returns>
        Task UnpublishModelingInfo(UnpublishModelingInfoInput input);

        /// <summary>
        /// 获取建模列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PageReturn<GetModelingListOutput>> GetModelingList(GetModelingListInput input);

        /// <summary>
        /// 获取建模综合分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetModelingSynthesizeAnalyseOutput> GetModelingSynthesizeAnalyse(GetModelingSynthesizeAnalyseInput input);

        /// <summary>
        /// 获取建模阶段任务统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<GetModelingStageTaskCountOutput>> GetModelingStageTaskCount(GetModelingStageTaskCountInput input);

        /// <summary>
        /// 获取建模学生统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetModelingStudentCountOutput> GetModelingStudentCount(GetModelingStudentCountInput input);

        /// <summary>
        /// 获取学生做建模任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentDoModelingTaskListOutput> GetStudentDoModelingTaskList(GetStudentDoModelingTaskListInput input);

        /// <summary>
        /// 教师端建模下载学生成果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<byte[]> ModelingDownloadStudentAchievement(ModelingDownloadStudentAchievementInput input);

        /// <summary>
        /// 教师端建模下载学生评估结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<byte[]> ModelingDownloadStudentResult(ModelingDownloadStudentResultInput input);
    }
}
