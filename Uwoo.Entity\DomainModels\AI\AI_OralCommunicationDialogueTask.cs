﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 智能体_口语交际任务信息（对话式）
    /// </summary>
    [Table("AI_OralCommunicationDialogueTask")]
    public class AI_OralCommunicationDialogueTask
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 口语交际Id
        /// </summary>
        public string OralCommunicationId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 对话目标
        /// </summary>
        public string DialogueTarget { get; set; }

        /// <summary>
        /// 有效回应标准
        /// </summary>
        public string ValidRespond { get; set; }

        /// <summary>
        /// 追问话术
        /// </summary>
        public string Asked { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int OrderId { get; set; }
    }
}
