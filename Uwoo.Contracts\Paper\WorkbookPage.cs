﻿// -- Function：WorkbookPage.cs
// --- Project：X.PenServer.Models
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/31 15:56

namespace Uwoo.Contracts.Paper
{
    using System.ComponentModel.DataAnnotations;

    /// <summary>
    /// 
    /// </summary>
    public class WorkbookPage
    {
		/// <inheritdoc />
		[Key]
        public string Id { get; set; }

        /// <summary>
        /// 图片路径
        /// </summary>
        public string ImgUrl { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 年级
        /// </summary>
        public int Grade { get; set; }
        /// <summary>
        /// 学期
        /// </summary>
        public int Term { get; set; }
        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; }
        /// <summary>
        /// 类型 1练习册 2试卷
        /// </summary>
        public int PaperType { get; set; }
        /// <summary>
        /// 试卷Id
        /// </summary>
        public string PaperId { get; set; }

        /// <summary>
        /// 明鼎对应页码Id
        /// </summary>
        public int PageId { get; set; }

        /// <summary>
        /// 高
        /// </summary>
        public int height { get; set; }

        /// <summary>
        /// 宽
        /// </summary>
        public int width { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public int SubjectId { get; set; }

        /// <summary>
        /// 是否单页包含多套试卷组合
        /// </summary>
        public int IsCombination { get; set; } = 0;
    }
}