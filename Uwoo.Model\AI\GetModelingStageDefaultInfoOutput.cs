﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取建模阶段默认信息输出
    /// </summary>
    public class GetModelingStageDefaultInfoOutput
    {
        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string? Describe { get; set; }

        /// <summary>
        /// 默认阶段类型(1问题理解、2假设分析、3模型评价)
        /// </summary>
        public int DefaultStageType { get; set; }

        /// <summary>
        /// 是否默认(默认禁止删除)
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 建模阶段任务
        /// </summary>
        public List<GetModelingStageTaskDefaultOutput> ModelingStageTaskInfos { get; set; } = new List<GetModelingStageTaskDefaultOutput>();
    }

    /// <summary>
    /// 获取建模阶段任务默认信息输出
    /// </summary>
    public class GetModelingStageTaskDefaultOutput
    {
        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:问题理解、7:模型构建、8:模型假设、9:模型评价）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 评分标准
        /// </summary>
        public string? ScoreStandard { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string? Demand { get; set; }

        /// <summary>
        /// 问答范围
        /// </summary>
        public string? Scope { get; set; }

        /// <summary>
        /// 评估角色设定
        /// </summary>
        public string? RoleSetting { get; set; }

        /// <summary>
        /// 引导角色设定
        /// </summary>
        public string? GuideRoleSetting { get; set; }

        /// <summary>
        /// 开场白
        /// </summary>
        public string? Prologue { get; set; }

        /// <summary>
        /// 是否开启提交条件（任务点设置）
        /// </summary>
        public bool TaskIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（任务点设置）
        /// </summary>
        public bool TaskIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（任务点设置）
        /// </summary>
        public decimal TaskAssessmentScore { get; set; }

        /// <summary>
        /// 是否观看视频（任务点设置）
        /// </summary>
        public bool TaskIsWatchVideo { get; set; }

        /// <summary>
        /// 视频观看时长限制（分钟）（任务点设置）
        /// </summary>
        public int TaskVideoWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否需要阅读全部文档（任务点设置）
        /// </summary>
        public bool TaskIsReadAllDocuments { get; set; }

        /// <summary>
        /// 是否开启提交条件（组间任务设置）
        /// </summary>
        public bool GroupIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（组间任务设置）
        /// </summary>
        public bool GroupIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（组间任务设置）
        /// </summary>
        public decimal GroupAssessmentScore { get; set; }

        /// <summary>
        /// 是否观看视频（组间任务设置）
        /// </summary>
        public bool GroupIsWatchVideo { get; set; }

        /// <summary>
        /// 视频观看时长限制（分钟）（组间任务设置）
        /// </summary>
        public int GroupVideoWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否需要阅读全部文档（组间任务设置）
        /// </summary>
        public bool GroupIsReadAllDocuments { get; set; }

        /// <summary>
        /// 是否默认（默认禁止删除）
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 建模阶段任务问题
        /// </summary>
        public List<GetModelingStageTaskQuestionDefaultOutput> ModelingStageTaskQuestionInfos { get; set; } = new List<GetModelingStageTaskQuestionDefaultOutput>();
    }

    /// <summary>
    /// 获取建模阶段任务问题默认信息输出
    /// </summary>
    public class GetModelingStageTaskQuestionDefaultOutput
    {
        /// <summary>
        /// 问题名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Describe { get; set; }
    }
}
