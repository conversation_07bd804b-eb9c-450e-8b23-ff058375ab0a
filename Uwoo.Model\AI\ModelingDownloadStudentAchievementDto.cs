﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 教师端建模_下载学生成果Dto
    /// </summary>
    public class ModelingDownloadStudentAchievementDto
    {
        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 学生名称
        /// </summary>
        public string? RealName { get; set; }

        /// <summary>
        /// 问
        /// </summary>
        public string? Ask { get; set; }

        /// <summary>
        /// “问”结构化数据
        /// </summary>
        public AIDialogueASKDto AskInfo { get; set; } = new AIDialogueASKDto();
    }

    /// <summary>
    /// 教师端建模_下载学生成果文件信息Dto
    /// </summary>
    public class ModelingDownloadStudentAchievementFileInfoDto
    {
        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 文件地址
        /// </summary>
        public string? FileUrl { get; set; }
    }
}
