using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 难度等级数据传输对象
    /// </summary>
    public class DifficultyLevelDto
    {
        /// <summary>
        /// 难度等级ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 难度等级名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 难度等级描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
}
