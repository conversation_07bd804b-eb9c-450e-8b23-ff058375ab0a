﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.User
{
    /// <summary>
    /// 统一用户组信息
    /// </summary>
    [SugarTable("UniGroupInfos")]
    public class UniGroupInfo 
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Oid { get; set; }

        /// <summary>
        /// 组ID号
        /// </summary>
        public int? GroupId { get; set; }

        /// <summary>
        /// 组名称，0 < 字符长度 <= 100
        /// </summary>
        public string GroupName { get; set; }

        /// <summary>
        /// 上层组节点的组ID号
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 组树的线索 ID 号，即UAC中第一级组的ID号，可帮助快速定位组树的位置
        /// </summary>
        public int? ThreadId { get; set; }

        /// <summary>
        ///  组标记，0: 普通组，1: 学校，2: 年级，3: 班级
        /// </summary>
        public int? GroupFlag { get; set; }
    }
}
