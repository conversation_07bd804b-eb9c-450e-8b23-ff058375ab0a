using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.CustomException;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;

namespace Uwoo.WebApi.Controllers.AI
{
    /// <summary>
    /// 阅读理解智能体学生端控制器
    /// </summary>
    [Route("api/AgentRCStudent/[action]")]
    [ApiController]
    public class ReadingComprehensionStudentController : ApiBaseController<IReadingComprehensionStudentService>
    {
        private readonly IReadingComprehensionStudentService _readingComprehensionStudentService;
        private readonly IAgentCommonService _agentCommonService;

        public ReadingComprehensionStudentController(IReadingComprehensionStudentService readingComprehensionStudentService, IAgentCommonService agentCommonService)
        {
            _readingComprehensionStudentService = readingComprehensionStudentService;
            _agentCommonService = agentCommonService;
        }

        /// <summary>
        /// 获取学生的阅读理解任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentReadingTaskListOutput> GetStudentReadingTaskList(StudentReadingTaskListInput input)
        {
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            return await _readingComprehensionStudentService.GetStudentReadingTaskList(input);
        }

        /// <summary>
        /// 获取阅读理解任务详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentReadingTaskDetailsOutput> GetReadingTaskDetails(StudentReadingTaskDetailsInput input)
        {
            if (string.IsNullOrEmpty(input.TaskId))
            {
                throw new BusException("任务Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            return await _readingComprehensionStudentService.GetReadingTaskDetails(input);
        }

        #region 视频任务相关接口

        /// <summary>
        /// 记录视频观看状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<VideoWatchStatusOutput> RecordVideoWatchStatus(VideoWatchStatusInput input)
        {
            if (string.IsNullOrEmpty(input.TaskId))
            {
                throw new BusException("任务Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.VideoId))
            {
                throw new BusException("视频Id不能为空!");
            }
            return await _readingComprehensionStudentService.RecordVideoWatchStatus(input);
        }

        /// <summary>
        /// 记录视频观看时长
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<VideoWatchDurationOutput> RecordVideoWatchDuration(VideoWatchDurationInput input)
        {
            if (string.IsNullOrEmpty(input.TaskId))
            {
                throw new BusException("任务Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.VideoId))
            {
                throw new BusException("视频Id不能为空!");
            }
            if (input.WatchDuration <= 0)
            {
                throw new BusException("观看时长必须大于0!");
            }
            return await _readingComprehensionStudentService.RecordVideoWatchDuration(input);
        }

        #endregion

        #region 文档任务相关接口

        /// <summary>
        /// 记录文档阅读状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<DocumentReadStatusOutput> RecordDocumentReadStatus(DocumentReadStatusInput input)
        {
            if (string.IsNullOrEmpty(input.TaskId))
            {
                throw new BusException("任务Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            if (input.ReadDocumentIds == null || input.ReadDocumentIds.Count == 0)
            {
                throw new BusException("已读文档Id不能为空!");
            }
            return await _readingComprehensionStudentService.RecordDocumentReadStatus(input);
        }

        #endregion

        #region 思维导图任务相关接口

        /// <summary>
        /// 提交思维导图任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MindMapSubmitOutput> SubmitMindMap(MindMapSubmitInput input)
        {
            if (string.IsNullOrEmpty(input.TaskId))
            {
                throw new BusException("任务Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.MindMapContent))
            {
                throw new BusException("思维导图内容不能为空!");
            }
            return await _readingComprehensionStudentService.SubmitMindMap(input);
        }

        #endregion

        #region 选词填空任务相关接口

        /// <summary>
        /// 提交选词填空任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<WordFillSubmitOutput> SubmitWordFillTask(WordFillSubmitInput input)
        {
            if (string.IsNullOrEmpty(input.TaskId))
            {
                throw new BusException("任务Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            if (input.Answers == null || input.Answers.Count == 0)
            {
                throw new BusException("答案不能为空!");
            }
            return await _readingComprehensionStudentService.SubmitWordFillTask(input);
        }

        #endregion

        /// <summary>
        /// 获取学生任务完成历史
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentTaskHistoryOutput> GetStudentTaskHistory(StudentTaskHistoryInput input)
        {
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            return await _readingComprehensionStudentService.GetStudentTaskHistory(input);
        }

        /// <summary>
        /// 阅读理解情景对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task ReadingDialogue(ReadingDialogueInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.ProjectStageTaskId)
                    || string.IsNullOrEmpty(input.StudentId)
                    || (string.IsNullOrEmpty(input.Msg) && string.IsNullOrEmpty(input.AudioUrl)))
                {
                    throw new Exception("参数异常!");
                }

                // 音频文件处理
                if (!string.IsNullOrEmpty(input.AudioUrl))
                {
                    string fileExtension = Path.GetExtension(input.AudioUrl);
                    if (fileExtension.ToLower() != ".wav" && fileExtension.ToLower() != ".mp3")
                    {
                        throw new Exception("非常抱歉，目前我们仅支持 wav、mp3 音频格式，还请您谅解!");
                    }
                    input.Msg = await _agentCommonService.AudioFileChangeText(new AudioFileChangeTextInput()
                    {
                        FileUrl = input.AudioUrl,
                    });
                }

                // 设置SSE响应头
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _readingComprehensionStudentService.ReadingDialogue(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                // SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 阅读理解情景对话提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ReadingDialogueSubmitOutput> ReadingDialogueSubmit(ReadingDialogueSubmitInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectStageTaskId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("参数异常!");
            }
            return await _readingComprehensionStudentService.ReadingDialogueSubmit(input);
        }

        /// <summary>
        /// 阅读理解成果评估
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ReadingAssessmentOutput> ReadingAssessment(ReadingAssessmentInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectStageTaskId))
            {
                throw new BusException("阅读理解项目阶段任务Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            if (input.Files == null || input.Files.Count <= 0)
            {
                throw new BusException("文件信息不能为空!");
            }
            else
            {
                foreach (var file in input.Files)
                {
                    if (string.IsNullOrEmpty(file.FileUrl))
                    {
                        throw new BusException($"第{input.Files.IndexOf(file) + 1}个文件地址不能为空!");
                    }
                    if (string.IsNullOrEmpty(file.FileName))
                    {
                        throw new BusException($"第{input.Files.IndexOf(file) + 1}个文件名称不能为空!");
                    }
                }
            }

            return await _readingComprehensionStudentService.ReadingAssessment(input);
        }

        /// <summary>
        /// 阅读理解知识问答
        /// </summary>
        /// <param name="input"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task ReadingKnowledge(ReadingKnowledgeInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.ProjectStageTaskId)
                    || string.IsNullOrEmpty(input.StudentId)
                    || (string.IsNullOrEmpty(input.Msg) && string.IsNullOrEmpty(input.AudioUrl)))
                {
                    throw new Exception("参数异常!");
                }

                // 音频文件处理（参考项目化实践）
                if (!string.IsNullOrEmpty(input.AudioUrl))
                {
                    string fileExtension = Path.GetExtension(input.AudioUrl);
                    if (fileExtension.ToLower() != ".wav" && fileExtension.ToLower() != ".mp3")
                    {
                        throw new Exception("非常抱歉，目前我们仅支持 wav、mp3 音频格式，还请您谅解!");
                    }
                    input.Msg = await _agentCommonService.AudioFileChangeText(new AudioFileChangeTextInput()
                    {
                        FileUrl = input.AudioUrl,
                    });
                }

                // 设置SSE响应头
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _readingComprehensionStudentService.ReadingKnowledge(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                // SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 获取学生做任务结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentDoTaskResultOutput> GetStudentDoTaskResult(GetStudentDoTaskResultInput input)
        {
            if (string.IsNullOrEmpty(input.StudentId) || string.IsNullOrEmpty(input.TaskSubmitId))
            {
                throw new BusException("参数异常!");
            }
            return await _readingComprehensionStudentService.GetStudentDoTaskResult(input);
        }

        /// <summary>
        /// 查询任务提交记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetTaskSubmitRecordsOutput> GetTaskSubmitRecords(GetTaskSubmitRecordsInput input)
        {
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }

            if (input.PageIndex < 1)
            {
                throw new BusException("页码必须大于0!");
            }

            if (input.PageSize < 1 || input.PageSize > 100)
            {
                throw new BusException("每页数量必须在1-100之间!");
            }

            if (input.StartTime.HasValue && input.EndTime.HasValue && input.StartTime > input.EndTime)
            {
                throw new BusException("开始时间不能大于结束时间!");
            }

            return await _readingComprehensionStudentService.GetTaskSubmitRecords(input);
        }

        /// <summary>
        /// 获取学生端未达标列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentNoStandardListOutput> GetStudentNoStandardList(GetStudentNoStandardListInput input)
        {
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("项目Id不能为空!");
            }
            return await _readingComprehensionStudentService.GetStudentNoStandardList(input);
        }

        /// <summary>
        /// 学生端阅读理解阶段任务提交未达标对话记录备份
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task StudentSubmitNoStandardBackups(StudentSubmitNoStandardBackupsInput input)
        {
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.ProjectStageTaskId))
            {
                throw new BusException("项目阶段任务Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.TaskSubmitId))
            {
                throw new BusException("任务提交记录Id不能为空!");
            }
            await _readingComprehensionStudentService.StudentSubmitNoStandardBackups(input);
        }
    }
}
