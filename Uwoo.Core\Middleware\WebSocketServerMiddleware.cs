﻿using log4net;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using NLog;
using Spire.Pdf.General.Paper.Uof;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Uwoo.Core.Middleware
{
	public class WebSocketServerMiddleware
	{
		private readonly RequestDelegate _next;
		private readonly WebSocketManager _webSocketManager;

		private static readonly Logger logger = NLog.LogManager.GetCurrentClassLogger();

		public WebSocketServerMiddleware(RequestDelegate next, WebSocketManager webSocketManager)
		{
			_next = next;
			_webSocketManager = webSocketManager;
		}

		public async Task InvokeAsync(HttpContext context)
		{
			if (context.Request.Path == "/ws/qrscanlogin") // 假设你的 WebSocket 端点是 /ws
			{
				if (context.WebSockets.IsWebSocketRequest)
				{
					try
					{
						WebSocket webSocket = await _webSocketManager.AddSocket(context);
						await Echo(context, webSocket);
					}
					catch (Exception)
					{

						throw;
					}
				}
				else
				{
					context.Response.StatusCode = 400;
				}
			}
			else
			{
				await _next(context);
			}
		}

		private async Task Echo(HttpContext context, WebSocket webSocket)
		{
			var buffer = new byte[1024 * 4];
			WebSocketReceiveResult result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
			while (!result.CloseStatus.HasValue)
			{

				await webSocket.SendAsync(new ArraySegment<byte>(buffer, 0, result.Count), result.MessageType, result.EndOfMessage, CancellationToken.None);

				result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
			}
			await webSocket.CloseAsync(result.CloseStatus.Value, result.CloseStatusDescription, CancellationToken.None);
			if ("/ws/qrscanlogin" == context.Request.Path)
			{
				WebSocketManager._ClientQRScanWebSocket.TryRemove(context.Connection.RemoteIpAddress.ToString(), out _);
			}
		}

		private async Task ReceiveMessage(WebSocket socket, Action<WebSocketReceiveResult, byte[]> handleMessage)
		{
			var buffer = new byte[1024 * 4];
			while (socket.State == WebSocketState.Open)
			{
				WebSocketReceiveResult result;
				try
				{
					result = await socket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
				}
				catch (WebSocketException ex)
				{
					// 处理 WebSocket 异常
					Console.WriteLine($"WebSocket error: {ex.Message}");
					break;
				}

				handleMessage(result, buffer);
			}
		}
	}
}
