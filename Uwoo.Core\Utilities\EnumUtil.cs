﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Utilities
{
    public static class EnumUtil
    {
        /// <summary>
        /// 通过反射获取枚举成员
        /// </summary>
        /// <param name = "value" ></ param >
        /// < returns ></ returns >
        /// < exception cref="ArgumentNullException"></exception>

        public static string GetDescription(this Enum value)
        {
            if (value == null)
                throw new ArgumentNullException(nameof(value));

            // 获取枚举成员的FieldInfo
            var fieldInfo = value.GetType().GetField(value.ToString());
            if (fieldInfo == null)
                return value.ToString();

            // 获取Description特性
            var descriptionAttributes = fieldInfo.GetCustomAttributes(typeof(DescriptionAttribute), false) as DescriptionAttribute[];
            return descriptionAttributes?.FirstOrDefault()?.Description ?? value.ToString();
        }
    }
}
