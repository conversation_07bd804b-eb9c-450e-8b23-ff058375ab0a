﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Entity.DomainModels
{
    /// <summary>
    /// 登录日志表
    /// </summary>
    [Entity(TableCnName = "登录日志表", TableName = "Sys_UserLoginLogs")]
    public class Sys_UserLoginLogs 
    {
        /// <summary>
        ///
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        [Key]
        [Display(Name = "Id")]
        [Column(TypeName = "bigint")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public long Id { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "UserId")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string UserId { get; set; }

        /// <summary>
        ///0学生；1教师；2管理
        /// </summary>
        [Display(Name = "0学生；1教师；2管理")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? UserType { get; set; }

        /// <summary>
        ///登陆日期，例：2025-03-25
        /// </summary>
        [Display(Name = "登陆日期，例：2025-03-25")]
        [MaxLength(10)]
        [Column(TypeName = "varchar(10)")]
        [Editable(true)]
        public string LoginDate { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "CreateTime")]
        [Column(TypeName = "datetime")]
        [Editable(true)]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        ///来自哪个平台
        /// </summary>
        [Display(Name = "来自哪个平台")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string Platform { get; set; }

        /// <summary>
        ///1 PC网页端；2 移动APP端
        /// </summary>
        [Display(Name = "1 PC网页端；2 移动APP端")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? RequestAgent { get; set; }


    }
}
