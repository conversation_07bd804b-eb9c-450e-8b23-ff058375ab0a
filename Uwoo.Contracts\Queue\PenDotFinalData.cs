﻿// -- Function：PenDotFinalData.cs
// --- Project：X.PenServer.Contracts
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/11/01 17:23

// ReSharper disable once CheckNamespace
namespace X.PenServer.Contracts.Queue
{
    using System.Text.Json.Serialization;

	/// <inheritdoc />
	public class PenDotFinalData
    {
        /// <summary>
        /// 点位坐标
        /// </summary>
        [JsonPropertyName(nameof(Dots))]
        [JsonInclude]
        public List<PenDot> Dots { get; set; }

        /// <summary>
        /// 页码id
        /// </summary>
        [JsonPropertyName(nameof(PageId))]
        [JsonInclude]
        public int PageId { get; set; }

        /// <summary>
        /// 试卷id
        /// </summary>
        [JsonPropertyName(nameof(PaperId))]
        [JsonInclude]
        public string PaperId { get; set; }

        /// <summary>
        /// 用户id
        /// </summary>
        [JsonPropertyName(nameof(UserId))]
        [JsonInclude]
        public string UserId { get; set; }

        /// <summary>
        /// 状态: 0.未开始 1.作答中 2.已识别 3.已批阅
        /// </summary>
        [JsonPropertyName(nameof(DoStatus))]
        [JsonInclude]
        public int DoStatus { get; set; }
    }
}