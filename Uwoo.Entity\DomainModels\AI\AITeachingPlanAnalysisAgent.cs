﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// AI 教学计划分析 Agent 配置表
    /// </summary>
    [Table("AI_TeachingPlanAnalysisAgent")]
    public class AI_TeachingPlanAnalysisAgent
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        /// <summary>
        /// 生成分析的类型 1数据分析  2 维度分析 3 综合评价与建议
        /// </summary>
        public string GenerateAnalysisType { get; set; }
        /// <summary>
        /// Agent 提示词模板
        /// </summary>

        public string Prompt { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;

        public DateTime UpdateTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 排序字段，数值越小越靠前显示
        /// </summary>
        public int Sort { get; set; } 
    }
}
