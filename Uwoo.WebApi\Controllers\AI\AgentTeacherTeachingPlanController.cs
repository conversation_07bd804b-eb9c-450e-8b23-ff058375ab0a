﻿using Coldairarrow.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.CustomException;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;
using UwooAgent.System.Services.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_教师端教案
    /// </summary>
    [Route("/AgentTeacherTeachingPlan/[controller]/[action]")]
    [ApiController]
    public class AgentTeacherTeachingPlanController : ApiBaseController<IAgentTeacherTeachingPlanService>
    {
        #region DI
        private readonly IAgentTeacherTeachingPlanService _agentTeacherTeachingPlanService;
        public AgentTeacherTeachingPlanController(IAgentTeacherTeachingPlanService agentTeacherTeachingPlanService)
        {
            _agentTeacherTeachingPlanService = agentTeacherTeachingPlanService;
        }
        #endregion

        /// <summary>
        /// 保存教案内容要求
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task SaveTeachingPlanContentDemand(SaveTeachingPlanContentDemandInput input)
        {
            if (string.IsNullOrEmpty(input.Title) || string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("参数异常!", 801);
            }
            await _agentTeacherTeachingPlanService.SaveTeachingPlanContentDemand(input);
        }

        /// <summary>
        /// 删除教案内容要求
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task DelTeachingPlanContentDemand(DelTeachingPlanContentDemandInput input)
        {
            if (string.IsNullOrEmpty(input.Id) || string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("参数异常!", 801);
            }

            await _agentTeacherTeachingPlanService.DelTeachingPlanContentDemand(input);
        }

        /// <summary>
        /// 获取教案内容要求
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<GetTeachingPlanContentDemandOutput>> GetTeachingPlanContentDemand(GetTeachingPlanContentDemandInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("参数异常!", 801);
            }
            return await _agentTeacherTeachingPlanService.GetTeachingPlanContentDemand(input);
        }

        /// <summary>
        /// 创建教案
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task CreateTeachingPlan(CreateTeachingPlanInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.SubjectId)
                    || string.IsNullOrEmpty(input.ModelId)
                    || string.IsNullOrEmpty(input.SchoolId)
                    || string.IsNullOrEmpty(input.TeacherId)
                    || input.Grade == 0)
                {
                    throw new BusException("参数异常!");
                }

                if (input.ContentDemandId.Count <= 0)
                {
                    throw new BusException("请选择内容要求!", 801);
                }
                if (input.Type < 1 || input.Type > 4)
                {
                    throw new BusException("参数异常!");
                }
                else
                {
                    if (input.Type == 1)
                    {
                        if (string.IsNullOrEmpty(input.Title))
                        {
                            throw new BusException("请输入教案标题!", 801);
                        }
                    }
                    if (input.Type == 2)
                    {
                        if (string.IsNullOrEmpty(input.Content))
                        {
                            throw new BusException("请输入教案内容!", 801);
                        }
                    }
                    if (input.Type == 3)
                    {
                        if (string.IsNullOrEmpty(input.ChapterId))
                        {
                            throw new BusException("请选择章节!", 801);
                        }
                    }
                    if (input.Type == 4)
                    {
                        if (string.IsNullOrEmpty(input.FileUrl))
                        {
                            throw new BusException("请上传文档!", 801);
                        }
                    }
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentTeacherTeachingPlanService.CreateTeachingPlan(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 修改教案创建记录中的教案文本
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task UpdateTeachingPlanCreateRecord(UpdateTeachingPlanCreateRecordInput input)
        {
            if (string.IsNullOrEmpty(input.Id) || string.IsNullOrEmpty(input.TeacherId) || string.IsNullOrEmpty(input.TeachingPlanText))
            {
                throw new BusException("参数异常!");
            }
            await _agentTeacherTeachingPlanService.UpdateTeachingPlanCreateRecord(input);
        }

        /// <summary>
        /// 获取教案创建记录列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task<PageReturn<GetTeachingPlanCreateRecordOutput>> GetTeachingPlanCreateRecord(GetTeachingPlanCreateRecordInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId)
                || string.IsNullOrEmpty(input.SchoolId)
                || string.IsNullOrEmpty(input.SubjectId)
                || input.Grade <= 0)
            {
                throw new BusException("参数异常!");
            }
            if (input.PageIndex <= 0)
            {
                input.PageIndex = 1;
            }
            if (input.PageSize <= 0)
            {
                input.PageSize = 10;
            }
            return await _agentTeacherTeachingPlanService.GetTeachingPlanCreateRecord(input);
        }

        /// <summary>
        /// 教案创建记录文本润色
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task TeachingPlanCreateRecordOptimize(TeachingPlanCreateRecordOptimizeInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.Id)
                    || string.IsNullOrEmpty(input.TeacherId)
                    || string.IsNullOrEmpty(input.Scheme)
                    || string.IsNullOrEmpty(input.OptimizeText))
                {
                    throw new BusException("参数异常!");
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentTeacherTeachingPlanService.TeachingPlanCreateRecordOptimize(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 教案创建记录详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<TeachingPlanCreateRecordDetailsOutput> TeachingPlanCreateRecordDetails(TeachingPlanCreateRecordDetailsInput input)
        {
            if (string.IsNullOrEmpty(input.Id))
            {
                throw new BusException("参数异常!");
            }
            return await _agentTeacherTeachingPlanService.TeachingPlanCreateRecordDetails(input);
        }

        /// <summary>
        /// 教案生成记录文本转word
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<TeachingPlanCreateRecordTextToWordOutput> TeachingPlanCreateRecordTextToWord(TeachingPlanCreateRecordTextToWordInput input)
        {
            if (string.IsNullOrEmpty(input.Id) || string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("参数异常!");
            }
            return await _agentTeacherTeachingPlanService.TeachingPlanCreateRecordTextToWord(input);
        }

        /// <summary>
        /// 教案生成记录修改文件名称
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task TeachingPlanCreateRecordFileUpdateName(TeachingPlanCreateRecordFileUpdateNameInput input)
        {
            if (string.IsNullOrEmpty(input.Id) || string.IsNullOrEmpty(input.TeacherId) || string.IsNullOrEmpty(input.FileName))
            {
                throw new BusException("参数异常!");
            }
            await _agentTeacherTeachingPlanService.TeachingPlanCreateRecordFileUpdateName(input);
        }

        /// <summary>
        /// 获取教案生成记录文件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<TeachingPlanCreateRecordFileOutput>> TeachingPlanCreateRecordFile(TeachingPlanCreateRecordFileInput input)
        {
            if (string.IsNullOrEmpty(input.Id) || string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("参数异常!");
            }
            return await _agentTeacherTeachingPlanService.TeachingPlanCreateRecordFile(input);
        }

        /// <summary>
        /// 保存教案
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task SaveTeachingPlan(SaveTeachingPlanInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId) ||
                string.IsNullOrEmpty(input.SchoolId) ||
                string.IsNullOrEmpty(input.SubjectId) ||
                string.IsNullOrEmpty(input.GradeId))
            {
                throw new BusException("参数异常!");
            }

            if (input.Type != 1 && input.Type != 2 && input.Type != 3)
            {
                throw new BusException("参数异常!");
            }
            else
            {
                if (input.Type == 1 || input.Type == 2 || (input.Type == 3 && !input.IsDownloadPPT))
                {
                    if (string.IsNullOrEmpty(input.Name))
                    {
                        throw new BusException("参数异常!");
                    }
                }
                if (input.Type == 1)
                {
                    if (string.IsNullOrEmpty(input.Text))
                    {
                        throw new BusException("参数异常!");
                    }
                }
                if (input.Type == 2 || (input.Type == 3 && !input.IsDownloadPPT))
                {
                    if (string.IsNullOrEmpty(input.FileUrl))
                    {
                        throw new BusException("参数异常!");
                    }
                }
                if (input.Type == 3 && input.IsDownloadPPT)
                {
                    if (string.IsNullOrEmpty(input.PPTId))
                    {
                        throw new BusException("参数异常!");
                    }
                }
            }
            await _agentTeacherTeachingPlanService.SaveTeachingPlan(input);
        }

        /// <summary>
        /// 获取教案列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageReturn<GetTeachingPlanOutput>> GetTeachingPlan(GetTeachingPlanInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId)
               || string.IsNullOrEmpty(input.SchoolId)
               || string.IsNullOrEmpty(input.SubjectId)
               || string.IsNullOrEmpty(input.GradeId))
            {
                throw new BusException("参数异常!");
            }
            if (input.PageIndex <= 0)
            {
                input.PageIndex = 1;
            }
            if (input.PageSize <= 0)
            {
                input.PageSize = 10;
            }
            return await _agentTeacherTeachingPlanService.GetTeachingPlan(input);
        }

        /// <summary>
        /// 获取教案详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetTeachingPlanDetailsOutput> GetTeachingPlanDetails(GetTeachingPlanDetailsInput input)
        {
            if (string.IsNullOrEmpty(input.Id))
            {
                throw new BusException("参数异常!");
            }
            return await _agentTeacherTeachingPlanService.GetTeachingPlanDetails(input);
        }

        /// <summary>
        /// 教案文本类型编辑
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task TeachingPlanTextUpdate(TeachingPlanTextUpdateInput input)
        {
            if (string.IsNullOrEmpty(input.Id) ||
                string.IsNullOrEmpty(input.TeacherId) ||
                string.IsNullOrEmpty(input.Name) ||
                string.IsNullOrEmpty(input.Text))
            {
                throw new BusException("参数异常!");
            }
            await _agentTeacherTeachingPlanService.TeachingPlanTextUpdate(input);
        }

        /// <summary>
        /// 教案重命名
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task TeachingPlanUpdateName(TeachingPlanUpdateNameInput input)
        {
            if (string.IsNullOrEmpty(input.Id) ||
                string.IsNullOrEmpty(input.TeacherId) ||
                string.IsNullOrEmpty(input.Name))
            {
                throw new BusException("参数异常!");
            }
            await _agentTeacherTeachingPlanService.TeachingPlanUpdateName(input);
        }

        /// <summary>
        /// 删除教案
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task DelTeachingPlan(DelTeachingPlanInput input)
        {
            if (string.IsNullOrEmpty(input.Id) || string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("参数异常!");
            }
            await _agentTeacherTeachingPlanService.DelTeachingPlan(input);
        }
    }
}
