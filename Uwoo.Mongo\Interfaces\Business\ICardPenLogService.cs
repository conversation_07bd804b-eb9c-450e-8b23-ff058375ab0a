﻿// -- Function：ICardPenLogService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2024/1/31 16:20
namespace Uwoo.Mongo.Interfaces.Business;

using Uwoo.Mongo.Interfaces.Lifecycle;
using Uwoo.Mongo.Models;

/// <summary>
/// 答题卡点位笔迹服务
/// </summary>
public interface ICardPenLogService : IMongoAutoService<CardPenLog>, ISingletonService
{
	/// <inheritdoc />
	List<CardPenLog> GetAll(string colname, string userId, string paperId, int? year = null);

	/// <summary>
	/// 根据题目编号获取笔迹
	/// </summary>
	/// <param name="colname"></param>
	/// <param name="userId"></param>
	/// <param name="paperId"></param>
	/// <param name="itemNo"></param>
	/// <returns></returns>
	List<CardPenLog> GetPenLogsByItemNo(string colname, string userId, string paperId, int itemNo);

	/// <summary>
	/// 删除答题卡笔迹
	/// </summary>
	/// <param name="colname"></param>
	/// <param name="paperId"></param>
	/// <param name="userid"></param>
	void DeleteCardPenLog(string colname, string paperId, List<string> userid);
}