﻿/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model

    *using System;
    *using System.Collections.Generic;
    *using System.ComponentModel.DataAnnotations;
    *using System.ComponentModel.DataAnnotations.Schema;
    *using System.Linq;
    *using System.Text;
    *using System.Threading.Tasks;
    *using SqlSugar;
    *using {Namespace}.SystemModels;
    *using {Namespace}.AttributeManager;
*/

namespace {Namespace}.DomainModels
{
    {AttributeManager}
    public partial class {TableName}:BaseEntity
    {
        {AttributeList}
    }
}
