<?xml version="1.0"?>
<doc>
    <assembly>
        <name>UwooAgent.Mongo</name>
    </assembly>
    <members>
        <member name="T:Uwoo.Mongo.Infrastructure.DateTimeJsonConverter">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Infrastructure.DateTimeJsonConverter.Read(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Infrastructure.DateTimeJsonConverter.Write(System.Text.Json.Utf8JsonWriter,System.DateTime,System.Text.Json.JsonSerializerOptions)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Infrastructure.ServiceExtensions">
            <summary>
            服务扩展
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Infrastructure.ServiceExtensions.CreateIndex``1(MongoDB.Driver.IMongoIndexManager{``0},MongoDB.Driver.IndexKeysDefinition{``0},System.String)">
            <summary>
            创建索引
            </summary>
            <param name="index">索引</param>
            <param name="keys">键</param>
            <param name="name">名称</param>
            <typeparam name="T">实体</typeparam>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Business.ICardPenLogService">
            <summary>
            答题卡点位笔迹服务
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ICardPenLogService.GetAll(System.String,System.String,System.String,System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ICardPenLogService.GetPenLogsByItemNo(System.String,System.String,System.String,System.Int32)">
            <summary>
            根据题目编号获取笔迹
            </summary>
            <param name="colname"></param>
            <param name="userId"></param>
            <param name="paperId"></param>
            <param name="itemNo"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ICardPenLogService.DeleteCardPenLog(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            删除答题卡笔迹
            </summary>
            <param name="colname"></param>
            <param name="paperId"></param>
            <param name="userid"></param>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Business.ICorrectPenLogService">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ICorrectPenLogService.GetAll(System.String,System.Collections.Generic.List{System.Int32},System.String)">
            <summary>
            获取学生笔迹列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ICorrectPenLogService.GetAll(System.String,System.String,System.Int32,System.Nullable{System.Int32})">
            <summary>
            获取笔迹列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码</param>
            <param name="userid">用户id</param>
            <param name="year"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ICorrectPenLogService.DeletePenLog(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <summary>
            删除学生订正笔迹
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id集合</param>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Business.IPenLogService">
            <summary>
            点阵笔点位数据服务
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IPenLogService.GetAll(System.String,System.Collections.Generic.List{System.Int32},System.String,System.Nullable{System.Int32})">
            <summary>
            获取学生笔迹列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id</param>
            <param name="year"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IPenLogService.GetAll(System.String,System.String,System.Int32,System.Nullable{System.Int32})">
            <summary>
            获取笔迹列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码</param>
            <param name="userid">用户id</param>
            <param name="year">学年</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IPenLogService.GetAggregateTime(System.String,System.Collections.Generic.List{System.Int32},System.String)">
            <summary>
            获取时间
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IPenLogService.GetUserList(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <summary>
            获取已作答用户列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id集合</param>
            <param name="year">学年</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IPenLogService.GetUserPageList(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <summary>
            获取已作答页面列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id集合</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IPenLogService.DeletePenLog(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <summary>
            删除学生作答笔迹
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id集合</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IPenLogService.CorrectMDLog(System.String,System.Int32,System.String,System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Business.IPenMappingService">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Business.ISingleItemPenLogService">
            <summary>
            单题模式
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ISingleItemPenLogService.GetUserList(System.String,System.Int32,System.String,System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ISingleItemPenLogService.GetUserList(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <summary>
            获取已作答用户列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id集合</param>
            <param name="year">学年</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ISingleItemPenLogService.GetAll(System.String,System.String,System.Int32,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ISingleItemPenLogService.GetAll(System.String,System.Collections.Generic.List{System.Int32},System.String,System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ISingleItemPenLogService.DeleteSinglePenLog(System.String,System.String,System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ISingleItemPenLogService.CorrectMDLog(System.String,System.Int32,System.String,System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Business.IStudentInfoService">
            <summary>
            学生信息
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Business.ITeacherInfoService">
            <summary>
            教师信息
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ITeacherInfoService.GetTeacherInfoAsync(System.String)">
            <summary>
            获取教师信息
            </summary>
            <param name="teacherid">教师id</param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Business.ITeacherPenLogService">
            <summary>
            教师笔迹
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ITeacherPenLogService.GetAll(System.String,System.String,System.Int32,System.Nullable{System.Int32})">
            <summary>
            获取笔迹列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码</param>
            <param name="userid">学生id</param>
            <param name="year"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ITeacherPenLogService.GetAll(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <summary>
            获取已作答笔迹数据
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id集合</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ITeacherPenLogService.GetUserPageList(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <summary>
            获取已作答页面列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id集合</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ITeacherPenLogService.GetUserPageList(System.String,System.Collections.Generic.List{System.Int32},System.String)">
            <summary>
            获取已作答页面列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">学生id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.ITeacherPenLogService.DeletePenLog(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <summary>
            删除批改学生笔迹
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id集合</param>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Business.IWorkbookPenLogService">
            <summary>
            练习薄
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IWorkbookPenLogService.GetWorkbookPenLogListByItemId(System.String,System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IWorkbookPenLogService.GetWorkbookPenLogsByUserId(System.String,System.String,System.String,System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IWorkbookPenLogService.DeleteWorkbookPenLog(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            删除练习簿笔迹
            </summary>
            <param name="colname"></param>
            <param name="paperId"></param>
            <param name="userid"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IWorkbookPenLogService.GetUserList(System.String,System.String,System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <summary>
            获取已作答用户列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="paperId"></param>
            <param name="userid">用户id集合</param>
            <param name="year">学年</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IWorkbookPenLogService.GetUserListByItemId(System.String,System.String,System.String,System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <summary>
            根据试题id 获取已作答的学生id
            </summary>
            <param name="colname"></param>
            <param name="paperId"></param>
            <param name="itemId"></param>
            <param name="userid"></param>
            <param name="year">学年</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Business.IWorkbookPenLogService.DeletePenLog(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            删除学生笔迹
            </summary>
            <param name="colname">集合名称</param>
            <param name="paperId">paperId</param>
            <param name="userid">用户id集合</param>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Dapper.IDapperFactoryService">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperFactoryService.CreateConnection">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Dapper.IDapperService">
            <summary>
            Dapper Service
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.Execute(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteTable(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteTableAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteDataSet(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteDataSetAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteScalar(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteScalarAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteScalar``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteScalarAsync``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.BulkInsert``1(System.String,System.Collections.Generic.List{``0})">
            <summary>
            批量插入数据
            </summary>
            <typeparam name="T"></typeparam>
            <param name="sql">插入语句</param>
            <param name="newObjects"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.QueryFirst(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询首条数据
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.QueryFirstAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询首条数据
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.QueryFirst``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询首条数据
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.QueryFirstAsync``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询首条数据
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.QueryList(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询数据列表
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.QueryListAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询数据列表
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.QueryList``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询数据列表
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.QueryListAsync``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询数据列表
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteReader(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteReaderAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteReader``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <typeparam name="T"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteReaderAsync``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <typeparam name="T"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteReaderList``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <typeparam name="T"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteReaderListAsync``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <typeparam name="T"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.Transaction(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间, 以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.TransactionAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间, 以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.Transaction(System.Collections.Generic.Dictionary{System.String,System.Object},System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sqls">sql列表</param>
            <param name="timeout">超时时间, 以秒为单位</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.TransactionAsync(System.Collections.Generic.Dictionary{System.String,System.Object},System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sqls">sql列表</param>
            <param name="timeout">超时时间, 以秒为单位</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.Transaction(System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sqls">sql列表</param>
            <param name="timeout">超时时间, 以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.TransactionAsync(System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sqls">sql列表</param>
            <param name="timeout">超时时间, 以秒为单位</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteMultip``2(System.Collections.Generic.List{``0}@,System.Collections.Generic.List{``1}@,System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="ta"></param>
            <param name="tb"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteMultip``3(System.Collections.Generic.List{``0}@,System.Collections.Generic.List{``1}@,System.Collections.Generic.List{``2}@,System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="ta"></param>
            <param name="tb"></param>
            <param name="tc"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteMultip``4(System.Collections.Generic.List{``0}@,System.Collections.Generic.List{``1}@,System.Collections.Generic.List{``2}@,System.Collections.Generic.List{``3}@,System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <typeparam name="TD"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="ta"></param>
            <param name="tb"></param>
            <param name="tc"></param>
            <param name="td"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteMultip``2(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteMultipAsync``2(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteMultip``3(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteMultipAsync``3(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteMultip``4(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <typeparam name="TD"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Dapper.IDapperService.ExecuteMultipAsync``4(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <typeparam name="TD"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.IMongoAutoService`1">
            <summary>
            Mongo服务接口
            </summary>
            <remarks>自动根据年份分表</remarks>
            <typeparam name="T">实体</typeparam>
            <summary>
            Mongo服务接口
            </summary>
            <remarks>自动根据年份分表</remarks>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.GetAsync(System.Int64,System.String)">
            <summary>
            通过主键获取数据
            </summary>
            <param name="id">主键id</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.GetAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <summary>
            通过条件查询单条数据
            </summary>
            <param name="predicate">条件</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.GetAllAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String,MongoDB.Driver.FindOptions)">
            <summary>
            通过条件查询数据列表
            </summary>
            <param name="predicate">条件</param>
            <param name="colname">集合名称</param>
            <param name="options">查询选项</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.AddAsync(`0,System.String)">
            <summary>
            添加单条数据
            </summary>
            <param name="entity">数据实体</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.AddManyAsync(System.Collections.Generic.IEnumerable{`0},System.String)">
            <summary>
            批量添加数据
            </summary>
            <param name="entities">数据实体列表</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.UpdateAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},`0,System.String)">
            <summary>
            根据条件更新单条数据
            </summary>
            <param name="predicate">条件</param>
            <param name="entity">数据实体</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.UpdateAsync(System.Int64,`0,System.String)">
            <summary>
            根据主键更新单条数据
            </summary>
            <param name="id">主键id</param>
            <param name="entity">数据实体</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.DeleteAsync(System.Int64,System.String)">
            <summary>
            根据id删除单条数据
            </summary>
            <param name="id">主键id</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.DeleteAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <summary>
            根据条件删除单条数据
            </summary>
            <param name="predicate">条件</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.DeleteManyAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <summary>
            根据条件批量删除数据
            </summary>
            <param name="predicate">条件</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.Get(System.Int64,System.String)">
            <summary>
            通过主键获取数据
            </summary>
            <param name="id">主键id</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.Get(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <summary>
            通过条件查询单条数据
            </summary>
            <param name="predicate">条件</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.GetAll(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String,MongoDB.Driver.FindOptions,System.Nullable{System.Int32})">
            <summary>
            通过条件查询数据列表
            </summary>
            <param name="predicate">条件</param>
            <param name="colname">集合名称</param>
            <param name="options">查询选项</param>
            <param name="year"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.Add(`0,System.String)">
            <summary>
            添加单条数据
            </summary>
            <param name="entity">数据实体</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.AddMany(System.Collections.Generic.IEnumerable{`0},System.String)">
            <summary>
            批量添加数据
            </summary>
            <param name="entities">数据实体列表</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.Update(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},`0,System.String)">
            <summary>
            根据条件更新单条数据
            </summary>
            <param name="predicate">条件</param>
            <param name="entity">数据实体</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.Update(System.Int64,`0,System.String)">
            <summary>
            根据主键更新单条数据
            </summary>
            <param name="id">主键id</param>
            <param name="entity">数据实体</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.Delete(System.Int64,System.String)">
            <summary>
            根据id删除单条数据
            </summary>
            <param name="id">主键id</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.Delete(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <summary>
            根据条件删除单条数据
            </summary>
            <param name="predicate">条件</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoAutoService`1.DeleteMany(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <summary>
            根据条件批量删除数据
            </summary>
            <param name="predicate">条件</param>
            <param name="colname">集合名称</param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.IMongoService`1">
            <summary>
            mongodb服务
            </summary>
            <typeparam name="T">实体</typeparam>
            <summary>
            mongodb服务
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.GetAsync(System.Int64)">
            <summary>
            通过主键获取数据
            </summary>
            <param name="id">主键id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.GetAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            通过条件查询单条数据
            </summary>
            <param name="predicate">条件</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.GetAllAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},MongoDB.Driver.FindOptions)">
            <summary>
            通过条件查询数据列表
            </summary>
            <param name="predicate">条件</param>
            <param name="options">查询选项</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.AddAsync(`0)">
            <summary>
            添加单条数据
            </summary>
            <param name="entity">数据实体</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.AddManyAsync(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量添加数据
            </summary>
            <param name="entities">数据实体列表</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.UpdateAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},`0)">
            <summary>
            根据条件更新单条数据
            </summary>
            <param name="predicate">条件</param>
            <param name="entity">数据实体</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.UpdateAsync(System.Int64,`0)">
            <summary>
            根据主键更新单条数据
            </summary>
            <param name="id">主键id</param>
            <param name="entity">数据实体</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.DeleteAsync(System.Int64)">
            <summary>
            根据id删除单条数据
            </summary>
            <param name="id">主键id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.DeleteAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件删除单条数据
            </summary>
            <param name="predicate">条件</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.DeleteManyAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件批量删除数据
            </summary>
            <param name="predicate">条件</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.Get(System.Int64)">
            <summary>
            通过主键获取数据
            </summary>
            <param name="id">主键id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.Get(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            通过条件查询单条数据
            </summary>
            <param name="predicate">条件</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.GetAll(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},MongoDB.Driver.FindOptions)">
            <summary>
            通过条件查询数据列表
            </summary>
            <param name="predicate">条件</param>
            <param name="options">查询选项</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.Add(`0)">
            <summary>
            添加单条数据
            </summary>
            <param name="entity">数据实体</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.AddMany(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量添加数据
            </summary>
            <param name="entities">数据实体列表</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.Update(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},`0)">
            <summary>
            根据条件更新单条数据
            </summary>
            <param name="predicate">条件</param>
            <param name="entity">数据实体</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.Update(System.Int64,`0)">
            <summary>
            根据主键更新单条数据
            </summary>
            <param name="id">主键id</param>
            <param name="entity">数据实体</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.Delete(System.Int64)">
            <summary>
            根据id删除单条数据
            </summary>
            <param name="id">主键id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.Delete(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件删除单条数据
            </summary>
            <param name="predicate">条件</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.IMongoService`1.DeleteMany(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据条件批量删除数据
            </summary>
            <param name="predicate">条件</param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Lifecycle.IScopedService">
            <summary>
            Scoped周期服务注入标记
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Lifecycle.ISingletonService">
            <summary>
            Singleton周期服务注入标记
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Lifecycle.ITransientService">
            <summary>
            Transient周期服务注入标记
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService">
            <summary>
            试卷信息
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HGetPaperPageInfoAsync(System.String,System.Int32)">
            <summary>
            获取试卷子页面数据
            </summary>
            <param name="paperid">试卷id</param>
            <param name="pageno">页码</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HSetPaperPageInfoAsync(System.String,System.Int32,Uwoo.Contracts.Paper.WorkbookPage)">
            <summary>
            缓存试卷子页面数据
            </summary>
            <param name="paperid">试卷id</param>
            <param name="page">页码</param>
            <param name="workpage">试卷子页面</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HGetPaperIdAsync(System.Int32)">
            <summary>
            根据页码id获取试卷信息
            </summary>
            <param name="pageid">页码id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HSetPaperIdAsync(System.Int32,System.String)">
            <summary>
            设置页码对应试卷信息
            </summary>
            <param name="pageid">页码id</param>
            <param name="paperid">试卷id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HGetPaperRangeAsync(System.String)">
            <summary>
            获取完成提交页面数据
            </summary>
            <param name="paperid">试卷id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HSetPaperRangeAsync(System.String,Uwoo.Contracts.Paper.PaperFinishRange)">
            <summary>
            设置完成提交页面区域数据
            </summary>
            <param name="paperid">试卷id</param>
            <param name="data">区域数据</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HGetPaperRangeCountAsync(System.String)">
            <summary>
            获取完成提交区域是否为首次查询
            </summary>
            <param name="paperid">试卷id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HSetPaperRangeCountAsync(System.String,System.Int32)">
            <summary>
            设置完成提交区域是否为首次查询
            </summary>
            <param name="paperid">试卷id</param>
            <param name="count">数量</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HDeletePaperPage(System.String)">
            <summary>
            移除试卷页面缓存
            </summary>
            <param name="paperid"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HDeletePaperMarkInfo(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HSetPaperIdIsMedia(System.String)">
            <summary>
            当前试卷是否包含语音题
            </summary>
            <param name="paperid">试卷id</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperInfoRedisService.HSetPaperIdIsMediaAsync(System.String)">
            <summary>
            当前试卷是否包含语音题
            </summary>
            <param name="paperid">试卷id</param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService">
            <summary>
            做卷相关笔迹缓存
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.GetCurrentPaperIdAsync(System.String)">
            <summary>
            获取当前一键赋码的试卷
            </summary>
            <param name="classid">班级id</param>
            <remarks>卷码纸专用</remarks>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.GetStudentCurrentDoPaperPageInfoAsync(System.String)">
            <summary>
            获取学生正在书写的页面
            </summary>
            <param name="userid">学生用户id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.DeleteStudentCurrentDoPaperPageInfoAsync(System.String)">
            <summary>
            删除学生正在书写的页面
            </summary>
            <param name="userid">学生用户id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.SetStudentCurrentDoPaperPageInfoAsync(System.String,Uwoo.Contracts.Paper.WorkbookPage)">
            <summary>
            设置学生正在书写的页面
            </summary>
            <param name="userid">用户id</param>
            <param name="workpage">当前作答子页面</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HSetUserPaperNumAsync(System.String,System.Collections.Generic.List{X.PenServer.Contracts.Queue.PenDot})">
            <summary>
            缓存卷码区域书写笔迹
            </summary>
            <param name="userid">用户id</param>
            <param name="dots">点位数据</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HGetUserPaperNumAsync(System.String)">
            <summary>
            获取卷码区域书写点位笔迹
            </summary>
            <param name="userid">用户id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.SaveRecognitionNumberAsync(System.String,System.String)">
            <summary>
            缓存识别的卷码
            </summary>
            <param name="userid">用户id</param>
            <param name="paperno">卷码</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HDelUserPaperNumAsync(System.String)">
            <summary>
            清理临时卷码区域书写笔迹
            </summary>
            <param name="userid">用户id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HGetPaperNumAsync(System.String)">
            <summary>
            获取卷码对应的试卷信息
            </summary>
            <param name="paperno">卷码</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HSetPaperNumAsync(System.String,System.String)">
            <summary>
            设置卷码对应的试卷信息
            </summary>
            <param name="paperno">卷码</param>
            <param name="paperid">试卷id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HGetCorrectAsync(System.String,System.String)">
            <summary>
            获取该试卷是否开启了互批
            </summary>
            <param name="classid">班级id</param>
            <param name="paperid">试卷id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.IsPageMemberAsync(System.String)">
            <summary>
            是否包含当前页码
            </summary>
            <param name="pageid">页码id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HGetEachCorrectStudentAsync(System.String,System.String)">
            <summary>
            获取被当前用户互批的学生id
            </summary>
            <param name="paperid">试卷id</param>
            <param name="userid">用户id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HSetEachCorrectStudentAsync(System.String,System.String,System.String)">
            <summary>
            设置当前学生和被批改的学生对应关系
            </summary>
            <param name="paperid">试卷id</param>
            <param name="current_studentid">当前学生id</param>
            <param name="studentid">被批改的学生id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.SAddStudentCurrentPaperInfoAsync(System.String,System.String,System.String,Uwoo.Mongo.Models.TeacherPenLog)">
            <summary>
            保存当前学生正在书写的试卷子页面笔迹数据
            </summary>
            <param name="studentid">学生id</param>
            <param name="paperid">试卷id</param>
            <param name="pageid">页码id</param>
            <param name="teacher_log">笔迹</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.DeleteRecognitionNumberAsync(System.String)">
            <summary>
            删除卷码识别数据
            </summary>
            <param name="userid">用户id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.SetCurrentPaperId(System.String,System.String,System.String)">
            <summary>
            设置当前卷码
            </summary>
            <param name="teacherid">教师id</param>
            <param name="classid">班级id</param>
            <param name="paperid">试卷id</param>
            <remarks>两小时失效</remarks>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.SetCurrentItemId(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            设置当前作答的题目id
            </summary>
            <param name="teacherid"></param>
            <param name="classid"></param>
            <param name="paperid"></param>
            <param name="itemid"></param>
            <param name="isFullLine"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.SetPaperAnswerMode(System.String,System.String,System.Int32)">
            <summary>
            获取试卷的作答方式 
            </summary>
            <param name="classId"></param>
            <param name="paperId"></param>
            <param name="modeNum">1 整卷作答；2单题作答</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.GetPaperAnswerMode(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.RemovePaperAnswerMode(System.String,System.String)">
            <summary>
            清除试卷作答方式
            </summary>
            <param name="classid"></param>
            <param name="paperid"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.RemoveSingleItemAnswerState(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.GetCurrentItem(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.RemoveCurrentItemId(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.RemoveCurrentPaperId(System.String)">
            <summary>
            清除当前卷码
            </summary>
            <param name="classid"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.GetCurrentPaperId(System.String)">
            <summary>
            获取当前一键赋码的试卷
            </summary>
            <param name="classid">班级id</param>
            <remarks>卷码纸专用</remarks>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.SetCurrentPaperIdPermanent(System.String,System.String,System.String)">
            <summary>
            设置当前卷码
            </summary>
            <param name="teacherid">教师id</param>
            <param name="classid">班级id</param>
            <param name="paperid">试卷id</param>
            <remarks>10天过期</remarks>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.RemoveCurrentPaperIdPermanent(System.String)">
            <summary>
            清除当前持久化券码
            </summary>
            <param name="classid"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.GetCurrentPaperIdPermanent(System.String)">
            <summary>
            获取当前一键赋码的试卷
            </summary>
            <param name="classid">班级id</param>
            <remarks>卷码纸专用</remarks>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.SetCurrentPaperIdOffline(System.String,System.String,System.String)">
            <summary>
            设置当前离线卷码
            </summary>
            <param name="teacherid">教师id</param>
            <param name="classid">班级id</param>
            <param name="paperid">试卷id</param>
            <remarks>卷码纸专用</remarks>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.SAddPageId(System.String)">
            <summary>
            添加页码缓存
            </summary>
            <param name="pageid"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HSetCorrect(System.String,System.String)">
            <summary>
            开启学生互批状态
            </summary>
            <param name="classid"></param>
            <param name="paperid"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HDelCorrect(System.String,System.String)">
            <summary>
            删除学生互批状态
            </summary>
            <param name="classid"></param>
            <param name="paperid"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HSetAndDelComment(System.String,System.String,System.Int32)">
            <summary>
            开启或删除学生互评
            </summary>
            <param name="classid"></param>
            <param name="paperid"></param>
            <param name="type"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.HGetCommentState(System.String,System.String)">
            <summary>
            获取学生互评的状态
            </summary>
            <param name="classid"></param>
            <param name="paperid"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPaperLogRedisService.SMemberStudentCurrentPaperInfo(System.String,System.String,System.String)">
            <summary>
            获取学生互批数据
            </summary>
            <param name="studentid">学生id</param>
            <param name="paperid">试卷id</param>
            <param name="pageid">页码id</param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Redis.Business.IPenMappingRedisService">
            <summary>
            点阵笔和用户映射服务
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPenMappingRedisService.HGetMappingUserAsync(System.String)">
            <summary>
            获取点阵笔绑定的用户
            </summary>
            <param name="mac">mac</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPenMappingRedisService.HSetMappingUserAsync(System.String,System.String)">
            <summary>
            添加点阵笔和用户绑定
            </summary>
            <param name="mac"></param>
            <param name="userid"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPenMappingRedisService.HGetMappingUser(System.String)">
            <summary>
            获取点阵笔绑定的用户
            </summary>
            <param name="mac">mac</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPenMappingRedisService.HSetMappingUser(System.String,System.String)">
            <summary>
            添加点阵笔和用户绑定
            </summary>
            <param name="mac"></param>
            <param name="userid"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPenMappingRedisService.HSetPenRole(System.String,System.Int32)">
            <summary>
            设置点阵笔用户类型
            </summary>
            <param name="mac">编号</param>
            <param name="usertype">0.学生 1.教师</param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPenMappingRedisService.HDeleteMappingUser(System.String)">
            <summary>
            删除点阵笔绑定的用户
            </summary>
            <param name="mac"></param>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.IPenMappingRedisService.HDeletePenRole(System.String)">
            <summary>
            删除相关角色
            </summary>
            <param name="mac"></param>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService">
            <summary>
            教师批改缓存
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.GetCorrectPaperAsync(System.String)">
            <summary>
            获取正在批改的试卷
            </summary>
            <param name="teacherid">教师用户id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.SetCorrectPaperAsync(System.String,System.String,System.String)">
            <summary>
            设置正在批改的试卷
            </summary>
            <param name="teacherid">教师用户id</param>
            <param name="paperid">试卷id</param>
            <param name="classid">班级id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.GetCorrectPaper(System.String)">
            <summary>
            获取正在批改的试卷
            </summary>
            <param name="teacherid">教师用户id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.SetCorrectPaper(System.String,System.String,System.String)">
            <summary>
            设置正在批改的试卷
            </summary>
            <param name="teacherid">教师用户id</param>
            <param name="paperid">试卷id</param>
            <param name="classid">班级id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.SetCorrectStudentAsync(System.String,System.String,System.String,System.String,System.Int32)">
            <summary>
            设置当前正在批改的学生和试卷信息
            </summary>
            <param name="teacherid">教师id</param>
            <param name="paperid">试卷id</param>
            <param name="classid">班级id</param>
            <param name="studentid">学生id</param>
            <param name="pageno">批改的页码</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.GetCorrectStudentAsync(System.String)">
            <summary>
            获取当前正在批改的学生和试卷信息
            </summary>
            <param name="teacherid">教师id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.HGetCorrectStudentAsync(System.String)">
            <summary>
            获取当前正在批改的学生
            </summary>
            <param name="teacherid">教师用户id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.HSetCorrectStudentAsync(System.String,System.String)">
            <summary>
            设置教师正在批改的学生
            </summary>
            <param name="teacherid">教师id</param>
            <param name="studentid">学生id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.HSetCorrectStudentStatusAsync(System.String,System.String,System.Int32)">
            <summary>
            设置批阅学生状态
            </summary>
            <param name="paperid">试卷id</param>
            <param name="studentid">学生id</param>
            <param name="status">状态: 0.未开始 1.作答中 2.已识别 3.已批阅</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.HGetCorrectStudentStatusAsync(System.String,System.String)">
            <summary>
            获取批阅学生状态
            </summary>
            <param name="paperid">试卷id</param>
            <param name="studentid">学生id</param>
            <returns>状态: 0.未开始 1.作答中 2.已识别 3.已批阅</returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.HSetCorrectStudentStatus(System.String,System.String,System.Int32)">
            <summary>
            设置批阅学生状态（状态变更为2或3后 会将学生的笔迹归入到订正笔迹，只有在学生提交或教师批改情景时才会变更这个状态）
            </summary>
            <param name="paperid">试卷id</param>
            <param name="studentid">学生id</param>
            <param name="status">状态: 0.未开始 1.作答中 2.已识别(教师批改后) 3.已批阅(学生已提交，作答完毕)</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.HGetCorrectStudentStatus(System.String,System.String)">
            <summary>
            获取批阅学生状态
            </summary>
            <param name="paperid">试卷id</param>
            <param name="studentid">学生id</param>
            <returns>状态: 0.未开始 1.作答中 2.已识别 3.已批阅</returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.HSetStudentCorrectPaperAsync(System.String,System.String)">
            <summary>
            设置学生订正试卷记录
            </summary>
            <param name="studentid">用户id</param>
            <param name="paperid">试卷id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.SetCorrectClass(System.String,System.String)">
            <summary>
            设置教师当前批改的班级
            </summary>
            <param name="teacherid">教师id</param>
            <param name="classid">班级id</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.Business.ITeacherCorrectRedisService.GetCorrectClass(System.String)">
            <summary>
            获取教师当前批改的班级
            </summary>
            <param name="teacherid">教师id</param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.Mongo.Interfaces.Redis.IRedisService">
            <summary>
            Redis异步接口
            </summary>
            <summary>
            Redis同步接口
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Interfaces.Redis.IRedisService.Prefix">
            <summary>
            前缀
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SetAsync(System.String,System.Object,System.Nullable{System.TimeSpan})">
            <summary>
            添加缓存数据
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <param name="span">过期时间</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.GetAsync``1(System.String)">
            <summary>
            获取缓存数据
            </summary>
            <param name="key">键</param>
            <typeparam name="T">类型</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.GetAsync(System.String)">
            <summary>
            获取缓存数据
            </summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.DeleteAsync(System.String)">
            <summary>
            删除数据
            </summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.ExpireAsync(System.String,System.TimeSpan)">
            <summary>
            过期指定键
            </summary>
            <param name="key">键</param>
            <param name="span">过期时间</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.IsExistsAsync(System.String)">
            <summary>
            是否存在指定键
            </summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HSetAsync(System.String,System.String,System.Object)">
            <summary>
            添加哈希数据
            </summary>
            <param name="key"></param>
            <param name="field"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HSetAsync(System.String,System.String,System.String)">
            <summary>
            添加哈希数据
            </summary>
            <param name="key"></param>
            <param name="field"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HGetAsync``1(System.String,System.String)">
            <summary>
            获取哈希数据
            </summary>
            <param name="key">键</param>
            <param name="field">字段</param>
            <typeparam name="T">值</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HGetAsync(System.String,System.String)">
            <summary>
            获取哈希数据
            </summary>
            <param name="key">键</param>
            <param name="field">字段</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HDeleteAsync(System.String,System.String)">
            <summary>
            删除哈希数据
            </summary>
            <param name="key">键</param>
            <param name="filed">字段</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HGetAllAsync``1(System.String)">
            <summary>
            获取当前哈希列表
            </summary>
            <param name="key">键</param>
            <typeparam name="T">实体</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HGetAllAsync(System.String)">
            <summary>
            获取当前哈希列表
            </summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SAddAsync(System.String,System.String[])">
            <summary>
            添加数据集合
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SAddAsync``1(System.String,``0[])">
            <summary>
            添加数据集合
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <typeparam name="T">实体</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SMembersAsync(System.String)">
            <summary>
            获取集合列表
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SMembersAsync``1(System.String)">
            <summary>
            获取集合列表
            </summary>
            <param name="key">值</param>
            <typeparam name="T">实体</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SRemoveAsync(System.String,System.String[])">
            <summary>
            删除集合列表
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SRemoveAsync``1(System.String,``0[])">
            <summary>
            删除集合列表
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <typeparam name="T">实体</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.IsSMemberAsync(System.String,System.Object)">
            <summary>
            判断集合是否包含当前成员
            </summary>
            <param name="key">键</param>
            <param name="member">值</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.KeysAsync(System.String)">
            <summary>
            查找所有分区节点中符合给定模式的Key
            </summary>
            <param name="pattern">模糊匹配模式</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.Set(System.String,System.Object,System.Nullable{System.TimeSpan})">
            <summary>
            添加缓存数据
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <param name="span">过期时间</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.Get``1(System.String)">
            <summary>
            获取缓存数据
            </summary>
            <param name="key">键</param>
            <typeparam name="T">类型</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.Get(System.String)">
            <summary>
            获取缓存数据
            </summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.Delete(System.String)">
            <summary>
            移除数据
            </summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.Expire(System.String,System.TimeSpan)">
            <summary>
            过期指定键
            </summary>
            <param name="key">键</param>
            <param name="span">过期时间</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.IsExists(System.String)">
            <summary>
            是否存在指定键
            </summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HSet(System.String,System.String,System.Object)">
            <summary>
            添加哈希数据
            </summary>
            <param name="key"></param>
            <param name="field"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HSet(System.String,System.String,System.String)">
            <summary>
            添加哈希数据
            </summary>
            <param name="key"></param>
            <param name="field"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HGet``1(System.String,System.String)">
            <summary>
            获取哈希数据
            </summary>
            <param name="key">键</param>
            <param name="field">字段</param>
            <typeparam name="T">值</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HGet(System.String,System.String)">
            <summary>
            获取哈希数据
            </summary>
            <param name="key">键</param>
            <param name="field">字段</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HDelete(System.String,System.String)">
            <summary>
            删除哈希数据
            </summary>
            <param name="key">键</param>
            <param name="filed">字段</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HGetAll``1(System.String)">
            <summary>
            获取当前哈希列表
            </summary>
            <param name="key">键</param>
            <typeparam name="T">实体</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.HGetAll(System.String)">
            <summary>
            获取当前哈希列表
            </summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SAdd(System.String,System.String[])">
            <summary>
            添加数据集合
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SAdd(System.String,System.String[],System.Nullable{System.TimeSpan})">
            <summary>
            添加数据集合
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <param name="ts">值</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SAdd``1(System.String,``0[])">
            <summary>
            添加数据集合
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <typeparam name="T">实体</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SMembers(System.String)">
            <summary>
            获取集合列表
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SMembers``1(System.String)">
            <summary>
            获取集合列表
            </summary>
            <param name="key">值</param>
            <typeparam name="T">实体</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SRemove(System.String,System.String[])">
            <summary>
            删除集合列表
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.SRemove``1(System.String,``0[])">
            <summary>
            删除集合列表
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <typeparam name="T">实体</typeparam>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.IsSMember(System.String,System.Object)">
            <summary>
            判断集合是否包含当前成员
            </summary>
            <param name="key">键</param>
            <param name="member">值</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Interfaces.Redis.IRedisService.Keys(System.String)">
            <summary>
            查找所有分区节点中符合给定模式的Key
            </summary>
            <param name="pattern">模糊匹配模式</param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.Mongo.Models.CardPenLog">
            <summary>
            答题卡笔迹
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.CardPenLog.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.CardPenLog.ItemNo">
            <summary>
            题目编号
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.CardPenLog.SubItemNo">
            <summary>
            子题目编号
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Models.CorrectPenLog">
            <summary>
            订正的笔迹数据
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Models.DotBase">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Mongo.Models.DotBase.X">
            <summary>
            X坐标
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.DotBase.Y">
            <summary>
            Y坐标
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.DotBase.Type">
            <summary>
            点位类型: 1.开始 2.结束
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.DotBase.BookNo">
            <summary>
            BookNo
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.DotBase.Pressure">
            <summary>
            Pressure
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Models.PenLog">
            <summary>
            点阵笔点位数据
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenLog.Page">
            <summary>
            点阵笔采集到的页码
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenLog.PageId">
            <summary>
            对应业务页码id, 卷码纸为实际的卷码, 其余情况和Page字段值一致
            </summary>
            <seealso cref="P:Uwoo.Mongo.Models.PenLog.Page"/>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenLog.Mac">
            <summary>
            点阵笔Mac地址
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenLog.UserId">
            <summary>
            用户id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenLog.Dots">
            <summary>
            点位列表
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Models.PenMapping">
            <summary>
            点阵笔绑定用户数据
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenMapping.UserId">
            <summary>
            用户id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenMapping.Mac">
            <summary>
            点阵笔Mac地址
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenMapping.State">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenMapping.UserType">
            <summary>
            用户类型: 0.学生 1.教师
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenMapping.Battery">
            <summary>
            电量: 0 ~ 10
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenMapping.Ssid">
            <summary>
            配网热点名称
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.PenMapping.Version">
            <summary>
            固件版本号
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Models.SingleItemPenLog">
            <summary>
            单题模式笔迹
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.SingleItemPenLog.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.SingleItemPenLog.ItemId">
            <summary>
            题目id
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Models.StudentInfo">
            <summary>
            学生信息
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.StudentInfo.UserId">
            <summary>
            学生id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.StudentInfo.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.StudentInfo.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.StudentInfo.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.StudentInfo.SchoolId">
            <summary>
            学校id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.StudentInfo.Mobile">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.StudentInfo.ClassId">
            <summary>
            班级id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.StudentInfo.StudentNo">
            <summary>
            学号
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Models.TeacherInfo">
            <summary>
            教师信息
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.TeacherInfo.UserId">
            <summary>
            教师id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.TeacherInfo.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.TeacherInfo.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.TeacherInfo.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.TeacherInfo.SchoolId">
            <summary>
            学校id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.TeacherInfo.Mobile">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.TeacherInfo.Area">
            <summary>
            区域
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Models.TeacherPenLog">
            <summary>
            教师批改的笔迹数据
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.TeacherPenLog.TeacherId">
            <summary>
            教师用户id
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Models.WorkbookPenLog">
            <summary>
            练习薄笔迹
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.WorkbookPenLog.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.WorkbookPenLog.ItemId">
            <summary>
            题目id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.WorkbookPenLog.IsFullLine">
            <summary>
            是否为整行
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.WorkbookPenLog.LineNo">
            <summary>
            行号
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Models.WorkbookPenLog.Blank">
            <summary>
            当前空索引
            </summary>
            <remarks>非整行生效</remarks>
        </member>
        <member name="T:Uwoo.Mongo.MongoBaseModel">
            <summary>
            数据基类
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.MongoBaseModel.Mid">
            <summary>
            mongodb主键id
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.MongoBaseModel.AddTime">
            <summary>
            入库时间
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.RedisKeys">
            <summary>
            Redis键
            </summary>
        </member>
        <member name="F:Uwoo.Mongo.RedisKeys.PEN_MAPPING">
            <summary>
            点阵笔用户映射关系
            </summary>
        </member>
        <member name="F:Uwoo.Mongo.RedisKeys.STUDENT">
            <summary>
            学生信息
            </summary>
        </member>
        <member name="F:Uwoo.Mongo.RedisKeys.BASE_USER">
            <summary>
            教师信息
            </summary>
        </member>
        <member name="F:Uwoo.Mongo.RedisKeys.TEACHER_CORRECT">
            <summary>
            教师批改
            </summary>
        </member>
        <member name="F:Uwoo.Mongo.RedisKeys.PAPER_LOG">
            <summary>
            做卷相关缓存
            </summary>
        </member>
        <member name="F:Uwoo.Mongo.RedisKeys.PAPER_INFO">
            <summary>
            试卷相关
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.Services.Dapper.DapperFactoryService">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperFactoryService.#ctor(Microsoft.Extensions.Configuration.IConfiguration)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperFactoryService.CreateConnection">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Dapper.DapperService">
            <summary>
            Dapper
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.#ctor(Uwoo.Mongo.Interfaces.Dapper.IDapperFactoryService)">
            <summary>
            Init
            </summary>
            <param name="factory"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.Execute(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteTable(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteTableAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ConvertReaderToSet(System.Data.IDataReader)">
            <summary>
            reader转daset
            </summary>
            <param name="reader"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ConvertParams(System.Object)">
            <summary>
            参数转换
            </summary>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteDataSet(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteDataSetAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteScalar(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteScalarAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteScalar``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteScalarAsync``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行SQL
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.BulkInsert``1(System.String,System.Collections.Generic.List{``0})">
            <summary>
            批量插入
            </summary>
            <typeparam name="T"></typeparam>
            <param name="sql"></param>
            <param name="newObjects"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.QueryFirst(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询首条数据
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.QueryFirstAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询首条数据
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.QueryFirst``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询首条数据
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.QueryFirstAsync``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询首条数据
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.QueryList(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询数据列表
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.QueryListAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询数据列表
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.QueryList``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询数据列表
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.QueryListAsync``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            查询数据列表
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteReader(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteReaderAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteReader``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <typeparam name="T"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteReaderAsync``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <typeparam name="T"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteReaderList``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <typeparam name="T"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteReaderListAsync``1(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行查询
            </summary>
            <typeparam name="T"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间,以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.Transaction(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间, 以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.TransactionAsync(System.String,System.Object,System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="timeout">超时时间, 以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.Transaction(System.Collections.Generic.Dictionary{System.String,System.Object},System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sqls">sql列表</param>
            <param name="timeout">超时时间, 以秒为单位</param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.TransactionAsync(System.Collections.Generic.Dictionary{System.String,System.Object},System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sqls">sql列表</param>
            <param name="timeout">超时时间, 以秒为单位</param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.Transaction(System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sqls">sql列表</param>
            <param name="timeout">超时时间, 以秒为单位</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.TransactionAsync(System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <summary>
            执行事务
            </summary>
            <param name="sqls">sql列表</param>
            <param name="timeout">超时时间, 以秒为单位</param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteMultip``2(System.Collections.Generic.List{``0}@,System.Collections.Generic.List{``1}@,System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <param name="sql"></param>
            <param name="parameters"></param>
            <param name="ta"></param>
            <param name="tb"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteMultip``3(System.Collections.Generic.List{``0}@,System.Collections.Generic.List{``1}@,System.Collections.Generic.List{``2}@,System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <param name="sql"></param>
            <param name="parameters"></param>
            <param name="ta"></param>
            <param name="tb"></param>
            <param name="tc"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteMultip``4(System.Collections.Generic.List{``0}@,System.Collections.Generic.List{``1}@,System.Collections.Generic.List{``2}@,System.Collections.Generic.List{``3}@,System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <typeparam name="TD"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
            <param name="ta"></param>
            <param name="tb"></param>
            <param name="tc"></param>
            <param name="td"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteMultip``2(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <param name="sql"></param>
            <param name="parameters"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteMultipAsync``2(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <param name="sql"></param>
            <param name="parameters"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteMultip``3(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <param name="sql"></param>
            <param name="parameters"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteMultipAsync``3(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <param name="sql"></param>
            <param name="parameters"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteMultip``4(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <typeparam name="TD"></typeparam>
            <param name="sql">sql</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Dapper.DapperService.ExecuteMultipAsync``4(System.String,System.Object)">
            <summary>
            查询多结果集
            </summary>
            <typeparam name="TA"></typeparam>
            <typeparam name="TB"></typeparam>
            <typeparam name="TC"></typeparam>
            <typeparam name="TD"></typeparam>
            <param name="sql"></param>
            <param name="parameters"></param>
        </member>
        <member name="T:Uwoo.Mongo.Services.Mongo.CardPenLogService">
            <inheritdoc cref="T:Uwoo.Mongo.Interfaces.Business.ICardPenLogService" />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.CardPenLogService.#ctor(Uwoo.Contracts.Config.IMongoConfig)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.CardPenLogService.CreateIndex(MongoDB.Driver.IMongoCollection{Uwoo.Mongo.Models.CardPenLog})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.CardPenLogService.GetAll(System.String,System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            获取主观题的答题笔迹
            </summary>
            <param name="colname"></param>
            <param name="userId"></param>
            <param name="paperId"></param>
            <param name="year">学年</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.CardPenLogService.GetPenLogsByItemNo(System.String,System.String,System.String,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.CardPenLogService.DeleteCardPenLog(System.String,System.String,System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Mongo.CorrectPenLogService">
            <summary>
            订正笔迹
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.CorrectPenLogService.#ctor(Uwoo.Contracts.Config.IMongoConfig)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.CorrectPenLogService.CreateIndex(MongoDB.Driver.IMongoCollection{Uwoo.Mongo.Models.CorrectPenLog})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.CorrectPenLogService.GetAll(System.String,System.Collections.Generic.List{System.Int32},System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.CorrectPenLogService.GetAll(System.String,System.String,System.Int32,System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.CorrectPenLogService.DeletePenLog(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Mongo.MongoAutoService`1">
            <summary>
            mongo服务
            </summary>
            <remarks>自动根据年份分表</remarks>
            <typeparam name="T">mongo模型</typeparam>
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.#ctor(Uwoo.Contracts.Config.IMongoConfig)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.CreateIndex(MongoDB.Driver.IMongoCollection{`0})">
            <summary>
            创建索引
            </summary>
            <param name="collection"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.GetConnection(System.String,System.Nullable{System.Int32})">
            <summary>
            获取数据集合
            </summary>
            <param name="colname"></param>
            <param name="lYear">学年</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.GetCurrentYear">
            <summary>
            获取当前学期年份
            </summary>
            <remarks>升学年自动切换学期年份,自动拆分学期数据</remarks>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.GetAsync(System.Int64,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.GetAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.GetAllAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String,MongoDB.Driver.FindOptions)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.AddAsync(`0,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.AddManyAsync(System.Collections.Generic.IEnumerable{`0},System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.UpdateAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},`0,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.UpdateAsync(System.Int64,`0,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.DeleteAsync(System.Int64,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.DeleteAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.DeleteManyAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.Get(System.Int64,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.Get(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.GetAll(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String,MongoDB.Driver.FindOptions,System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.Add(`0,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.AddMany(System.Collections.Generic.IEnumerable{`0},System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.Update(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},`0,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.Update(System.Int64,`0,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.Delete(System.Int64,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.Delete(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoAutoService`1.DeleteMany(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.String)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Mongo.MongoService`1">
            <summary>
            mongo服务
            </summary>
            <typeparam name="T">mongo实体模型</typeparam>
        </member>
        <member name="F:Uwoo.Mongo.Services.Mongo.MongoService`1._mongo">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.#ctor(Uwoo.Contracts.Config.IMongoConfig)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.CreateIndex(MongoDB.Driver.IMongoCollection{`0})">
            <summary>
            创建索引
            </summary>
            <param name="collection"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.GetAsync(System.Int64)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.GetAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.GetAllAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},MongoDB.Driver.FindOptions)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.AddAsync(`0)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.AddManyAsync(System.Collections.Generic.IEnumerable{`0})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.UpdateAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},`0)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.UpdateAsync(System.Int64,`0)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.DeleteAsync(System.Int64)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.DeleteAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.DeleteManyAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.Get(System.Int64)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.Get(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.GetAll(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},MongoDB.Driver.FindOptions)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.Add(`0)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.AddMany(System.Collections.Generic.IEnumerable{`0})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.Update(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},`0)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.Update(System.Int64,`0)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.Delete(System.Int64)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.Delete(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.MongoService`1.DeleteMany(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Mongo.PenLogService">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.PenLogService.#ctor(Uwoo.Contracts.Config.IMongoConfig)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.PenLogService.CreateIndex(MongoDB.Driver.IMongoCollection{Uwoo.Mongo.Models.PenLog})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.PenLogService.GetAll(System.String,System.Collections.Generic.List{System.Int32},System.String,System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.PenLogService.GetAll(System.String,System.String,System.Int32,System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.PenLogService.GetAggregateTime(System.String,System.Collections.Generic.List{System.Int32},System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.PenLogService.GetUserList(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.PenLogService.GetUserPageList(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.PenLogService.DeletePenLog(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.PenLogService.CorrectMDLog(System.String,System.Int32,System.String,System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Mongo.PenMappingService">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.PenMappingService.#ctor(Uwoo.Contracts.Config.IMongoConfig)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.PenMappingService.CreateIndex(MongoDB.Driver.IMongoCollection{Uwoo.Mongo.Models.PenMapping})">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Mongo.SingleItemPenLogService">
            <inheritdoc cref="T:Uwoo.Mongo.Interfaces.Business.ISingleItemPenLogService" />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.SingleItemPenLogService.#ctor(Uwoo.Contracts.Config.IMongoConfig)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.SingleItemPenLogService.CreateIndex(MongoDB.Driver.IMongoCollection{Uwoo.Mongo.Models.SingleItemPenLog})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.SingleItemPenLogService.GetUserList(System.String,System.Int32,System.String,System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.SingleItemPenLogService.GetUserList(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.SingleItemPenLogService.GetAll(System.String,System.String,System.Int32,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.SingleItemPenLogService.GetAll(System.String,System.Collections.Generic.List{System.Int32},System.String,System.Nullable{System.Int32})">
            <summary>
            获取学生笔迹列表
            </summary>
            <param name="colname">集合名称</param>
            <param name="page">页码集合</param>
            <param name="userid">用户id</param>
            <param name="year">学年</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.SingleItemPenLogService.DeleteSinglePenLog(System.String,System.String,System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.SingleItemPenLogService.CorrectMDLog(System.String,System.Int32,System.String,System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Mongo.StudentInfoService">
            <summary>
            学生信息
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.StudentInfoService.#ctor(Uwoo.Contracts.Config.IMongoConfig)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.StudentInfoService.CreateIndex(MongoDB.Driver.IMongoCollection{Uwoo.Mongo.Models.StudentInfo})">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Mongo.TeacherInfoService">
            <summary>
            教师信息
            </summary>
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.TeacherInfoService.#ctor(Uwoo.Contracts.Config.IMongoConfig)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.TeacherInfoService.CreateIndex(MongoDB.Driver.IMongoCollection{Uwoo.Mongo.Models.TeacherInfo})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.TeacherInfoService.GetTeacherInfoAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Mongo.TeacherPenLogService">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.TeacherPenLogService.#ctor(Uwoo.Contracts.Config.IMongoConfig)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.TeacherPenLogService.CreateIndex(MongoDB.Driver.IMongoCollection{Uwoo.Mongo.Models.TeacherPenLog})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.TeacherPenLogService.GetAll(System.String,System.String,System.Int32,System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.TeacherPenLogService.GetAll(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.TeacherPenLogService.GetUserPageList(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.TeacherPenLogService.GetUserPageList(System.String,System.Collections.Generic.List{System.Int32},System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.TeacherPenLogService.DeletePenLog(System.String,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Mongo.WorkbookPenLogService">
            <inheritdoc cref="T:Uwoo.Mongo.Interfaces.Business.IWorkbookPenLogService" />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.WorkbookPenLogService.#ctor(Uwoo.Contracts.Config.IMongoConfig)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.WorkbookPenLogService.CreateIndex(MongoDB.Driver.IMongoCollection{Uwoo.Mongo.Models.WorkbookPenLog})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.WorkbookPenLogService.GetWorkbookPenLogListByItemId(System.String,System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.WorkbookPenLogService.GetWorkbookPenLogsByUserId(System.String,System.String,System.String,System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.WorkbookPenLogService.DeleteWorkbookPenLog(System.String,System.String,System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.WorkbookPenLogService.GetUserList(System.String,System.String,System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.WorkbookPenLogService.GetUserListByItemId(System.String,System.String,System.String,System.Collections.Generic.List{System.String},System.Nullable{System.Int32})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Mongo.WorkbookPenLogService.DeletePenLog(System.String,System.String,System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService">
            <summary>
            试卷信息
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.Prefix">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HSetPaperPageInfoAsync(System.String,System.Int32,Uwoo.Contracts.Paper.WorkbookPage)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HGetPaperPageInfoAsync(System.String,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HGetPaperIdAsync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HSetPaperIdAsync(System.Int32,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HGetPaperRangeAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HSetPaperRangeAsync(System.String,Uwoo.Contracts.Paper.PaperFinishRange)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HGetPaperRangeCountAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HSetPaperRangeCountAsync(System.String,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HDeletePaperPage(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HDeletePaperMarkInfo(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HSetPaperIdIsMedia(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperInfoRedisService.HSetPaperIdIsMediaAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.Prefix">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.GetCurrentPaperIdAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.GetStudentCurrentDoPaperPageInfoAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.DeleteStudentCurrentDoPaperPageInfoAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.SetStudentCurrentDoPaperPageInfoAsync(System.String,Uwoo.Contracts.Paper.WorkbookPage)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HSetUserPaperNumAsync(System.String,System.Collections.Generic.List{X.PenServer.Contracts.Queue.PenDot})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HGetUserPaperNumAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.SaveRecognitionNumberAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HDelUserPaperNumAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HGetPaperNumAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HSetPaperNumAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HGetCorrectAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.IsPageMemberAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HGetEachCorrectStudentAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HSetEachCorrectStudentAsync(System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.SAddStudentCurrentPaperInfoAsync(System.String,System.String,System.String,Uwoo.Mongo.Models.TeacherPenLog)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.DeleteRecognitionNumberAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.SetCurrentPaperId(System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.SetCurrentItemId(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            设置当前正在作答的试题
            </summary>
            <param name="teacherid"></param>
            <param name="classid"></param>
            <param name="paperid"></param>
            <param name="itemid"></param>
            <param name="is_fullline"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.GetCurrentItem(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.RemoveCurrentItemId(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.RemoveCurrentPaperId(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.GetCurrentPaperId(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.SetCurrentPaperIdPermanent(System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.SetPaperAnswerMode(System.String,System.String,System.Int32)">
            <summary>
            设置试卷的作答方式 
            </summary>
            <param name="classId"></param>
            <param name="paperId"></param>
            <param name="modeNum">1 整卷作答；2单题作答</param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.GetPaperAnswerMode(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.RemovePaperAnswerMode(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.RemoveSingleItemAnswerState(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.RemoveCurrentPaperIdPermanent(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.GetCurrentPaperIdPermanent(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.SetCurrentPaperIdOffline(System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.SAddPageId(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HSetCorrect(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HDelCorrect(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HSetAndDelComment(System.String,System.String,System.Int32)">
            <summary>
            开启学生互评
            </summary>
            <param name="classid"></param>
            <param name="paperid"></param>
            <param name="type"></param>
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.HGetCommentState(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PaperLogRedisService.SMemberStudentCurrentPaperInfo(System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Redis.Implement.PenMappingRedisService">
            <summary>
            点阵笔用户映射服务
            </summary>
        </member>
        <member name="P:Uwoo.Mongo.Services.Redis.Implement.PenMappingRedisService.Prefix">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PenMappingRedisService.HGetMappingUserAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PenMappingRedisService.HSetMappingUserAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PenMappingRedisService.HGetMappingUser(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PenMappingRedisService.HSetMappingUser(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PenMappingRedisService.HSetPenRole(System.String,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PenMappingRedisService.HDeleteMappingUser(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.PenMappingRedisService.HDeletePenRole(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.Prefix">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.GetCorrectPaperAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.SetCorrectPaperAsync(System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.GetCorrectPaper(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.SetCorrectPaper(System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.SetCorrectStudentAsync(System.String,System.String,System.String,System.String,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.GetCorrectStudentAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.HGetCorrectStudentAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.HSetCorrectStudentAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.HSetCorrectStudentStatusAsync(System.String,System.String,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.HGetCorrectStudentStatusAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.HSetCorrectStudentStatus(System.String,System.String,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.HGetCorrectStudentStatus(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.HSetStudentCorrectPaperAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.SetCorrectClass(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.Implement.TeacherCorrectRedisService.GetCorrectClass(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Redis.RedisService">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Mongo.Services.Redis.RedisService.Prefix">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SetAsync(System.String,System.Object,System.Nullable{System.TimeSpan})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.GetAsync``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.GetAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.DeleteAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.ExpireAsync(System.String,System.TimeSpan)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.IsExistsAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HSetAsync(System.String,System.String,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HSetAsync(System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HGetAsync``1(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HGetAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HDeleteAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HGetAllAsync``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HGetAllAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SAddAsync(System.String,System.String[])">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SAddAsync``1(System.String,``0[])">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SMembersAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SMembersAsync``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SRemoveAsync(System.String,System.String[])">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SRemoveAsync``1(System.String,``0[])">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.IsSMemberAsync(System.String,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.KeysAsync(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.Set(System.String,System.Object,System.Nullable{System.TimeSpan})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.Get``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.Get(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.Delete(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.Expire(System.String,System.TimeSpan)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.IsExists(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HSet(System.String,System.String,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HSet(System.String,System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HGet``1(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HGet(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HDelete(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HGetAll``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.HGetAll(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SAdd(System.String,System.String[])">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SAdd(System.String,System.String[],System.Nullable{System.TimeSpan})">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SAdd``1(System.String,``0[])">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SMembers(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SMembers``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SRemove(System.String,System.String[])">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.SRemove``1(System.String,``0[])">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.IsSMember(System.String,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Uwoo.Mongo.Services.Redis.RedisService.Keys(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Mongo.Services.Redis.XRedisHelper">
            <summary>
            Redis抽象
            </summary>
        </member>
        <member name="T:Uwoo.Mongo.ViewModels.PenLogUserInfo">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Mongo.ViewModels.PenLogUserInfo.UserId">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Mongo.ViewModels.PenLogUserInfo.Page">
            <inheritdoc />
        </member>
    </members>
</doc>
