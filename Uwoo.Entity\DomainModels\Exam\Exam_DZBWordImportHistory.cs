﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Exam
{
    /// <summary>
    /// 点阵笔试卷Word草稿本记录
    /// </summary>
    [Table("Exam_DZBWordImportHistory")]
    public class Exam_DZBWordImportHistory
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Key]
        public string Id { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }


        /// <summary>
        /// 地址
        /// </summary>
        public string Url { get; set; }


        /// <summary>
        /// 试卷Id
        /// </summary>
        public string PaperId { get; set; }

        /// <summary>
        /// 有几页
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 大小
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人Id
        /// </summary>
        public string CreatorId { get; set; }

        /// <summary>
        /// 类型  0:word导卷;1:在线命题;2:试卷生成word;
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 试卷纸张类型  0:默认(自行铺码打印)   1:铺码纸打印, 2 拍照分割  5 自主答题卡
        /// </summary>
        public int PaperWordType { get; set; }

        /// <summary>
        /// 是否协同
        /// </summary>
        public bool? IsSynergy { get; set; } = false;

        /// <summary>
        /// 协同允许复制
        /// </summary>
        public bool? SynergyCopy { get; set; }

        /// <summary>
        /// 协同允许引用
        /// </summary>
        public bool? SynergyQuote { get; set; }

        /// <summary>
        /// 协同是否仅自己可以编辑
        /// </summary>
        public bool? SynergyEdit { get; set; }
        /// <summary>
        /// 是否锁定重置
        /// </summary>
        public bool IsLockReset { get; set; } = false;
    }
}
