using Coldairarrow.Util;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using OfficeOpenXml;
using Spire.Pdf;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Model.CustomException;
using Uwoo.System.IServices;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.Student;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.Model.SemesterTime;
using UwooAgent.Model.Word;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 阅读理解智能体教师端服务实现
    /// </summary>
    public class ReadingComprehensionTeacherService : ServiceBase<RC_ReadingTask, IAgentTeacherReadingComprehensionRepository>, IReadingComprehensionTeacherService, IDependency
    {
        private IBase_SemesterTimeService _semesterTimeService;
        private readonly IHttpClientFactory _httpClientFactory;

        public ReadingComprehensionTeacherService(IBase_SemesterTimeService semesterTimeService, IHttpClientFactory httpClientFactory)
        {
            _semesterTimeService = semesterTimeService;
            _httpClientFactory = httpClientFactory;
        }
        //private readonly IConfiguration _configuration;

        //public ReadingComprehensionTeacherService(ISqlSugarClient db, IConfiguration configuration)
        //{
        //    _configuration = configuration;
        //}

        /// <summary>
        /// 保存阅读理解项目化实践任务信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<SaveReadingTaskOutput> SaveReadingTaskNew(SaveReadingTaskInput input)
        {
            try
            {
                if (string.IsNullOrEmpty(input.Id))
                {
                    //获取当前学年学期
                    NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                    if (nowSemesterTime == null)
                    {
                        throw new BusException("无法获取当前学年!", 801);
                    }

                    //阅读理解智能体任务
                    var agentTask = new AI_AgentTask
                    {
                        Id = IdHelper.GetId(),
                        AgentId = input.AgentId,
                        Creator = input.TeacherId,
                        Name = input.Name,
                        AgentTaskType = 3,
                        Introduce = input.Introduce,
                        TaskLogo = input.TaskLogo,
                        SubjectId = input.SubjectId,
                        Term = nowSemesterTime.NowTerm,
                        Year = nowSemesterTime.Year,
                        GradeId = input.GradeId,
                        CreateTime = DateTime.Now,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId,
                        IsDeleted = false
                    };
                    await DBSqlSugar.Insertable(agentTask).ExecuteCommandAsync();

                    //保存阅读理解阶段
                    List<RC_ReadingProjectStage> readingProjectStages = new List<RC_ReadingProjectStage>();
                    //保存阅读理解阶段任务
                    List<RC_ReadingProjectStageTask> readingProjectStageTask = new List<RC_ReadingProjectStageTask>();
                    //保存阅读理解阶段任务高频问题
                    List<RC_ReadingProjectStageTaskQuestion> readingProjectStageTaskQuestion = new List<RC_ReadingProjectStageTaskQuestion>();
                    //保存阅读理解阶段任务视频资源
                    List<RC_VideoResource> videoResource = new List<RC_VideoResource>();
                    //保存阅读理解阶段任务文档资源
                    List<RC_DocumentResource> documentResource = new List<RC_DocumentResource>();

                    foreach (var stageInput in input.ProjectStageInfos)
                    {
                        //阶段
                        var stage = new RC_ReadingProjectStage
                        {
                            Id = IdHelper.GetId(),
                            ReadingProjectId = agentTask.Id,
                            StageName = stageInput.Name,
                            StageDescribe = stageInput.Describe,
                            StageOrder = input.ProjectStageInfos.IndexOf(stageInput) + 1,
                            CreateTime = DateTime.Now,
                            Creator = input.TeacherId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.TeacherId,
                            IsDeleted = false
                        };
                        readingProjectStages.Add(stage);

                        foreach (var taskInput in stageInput.ProjectStageTaskInfos)
                        {
                            //阶段任务
                            var task = new RC_ReadingProjectStageTask
                            {
                                Id = IdHelper.GetId(),
                                ReadingProjectStageId = stage.Id,
                                TaskType = taskInput.TaskType,
                                TaskName = taskInput.Name,
                                Target = taskInput.Target,
                                ScoreStandard = taskInput.ScoreStandard,
                                Demand = taskInput.Demand,
                                Scope = taskInput.Scope,
                                RoleSetting = taskInput.RoleSetting,
                                Prologue = taskInput.Prologue,
                                ToneId = taskInput.ToneId,
                                RoleName = taskInput.RoleName,
                                GroupIsSubmit = taskInput.GroupIsSubmit,
                                GroupIsAssessment = taskInput.GroupIsAssessment,
                                GroupAssessmentScore = taskInput.GroupAssessmentScore,
                                TaskIsSubmit = taskInput.TaskIsSubmit,
                                TaskIsAssessment = taskInput.TaskIsAssessment,
                                TaskAssessmentScore = taskInput.TaskAssessmentScore,
                                TaskOrder = stageInput.ProjectStageTaskInfos.IndexOf(taskInput) + 1,
                                GroupTotalWatchDurationLimit = taskInput.GroupTotalWatchDurationLimit,
                                GroupIsWatchAllVideos = taskInput.GroupIsWatchAllVideos,
                                TaskTotalWatchDurationLimit = taskInput.TaskTotalWatchDurationLimit,
                                TaskIsWatchAllVideos = taskInput.TaskIsWatchAllVideos,
                                GroupIsReadAllDocuments = taskInput.GroupIsReadAllDocuments,
                                TaskIsReadAllDocuments = taskInput.TaskIsReadAllDocuments,
                                QuestionContent = taskInput.QuestionContent,
                                CorrectAnswers = taskInput.CorrectAnswers != null ? JsonConvert.SerializeObject(taskInput.CorrectAnswers) : null,
                                DistractorWords = taskInput.DistractorWords != null ? JsonConvert.SerializeObject(taskInput.DistractorWords) : null,
                                CustomBackgroundImage = taskInput.CustomBackgroundImage,
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            };
                            readingProjectStageTask.Add(task);

                            //高频问题
                            if (taskInput.QuestionInfos != null && taskInput.QuestionInfos.Count > 0)
                            {
                                foreach (var questionInput in taskInput.QuestionInfos)
                                {
                                    readingProjectStageTaskQuestion.Add(new RC_ReadingProjectStageTaskQuestion
                                    {
                                        Id = IdHelper.GetId(),
                                        ReadingProjectStageTaskId = task.Id,
                                        Name = questionInput.Name,
                                        Describe = questionInput.Describe,
                                        Order = taskInput.QuestionInfos.IndexOf(questionInput) + 1,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false
                                    });
                                }
                            }

                            //视频资源（任务类型4）
                            if (taskInput.TaskType == 4 && taskInput.VideoResources != null && taskInput.VideoResources.Count > 0)
                            {
                                foreach (var videoInput in taskInput.VideoResources)
                                {
                                    videoResource.Add(new RC_VideoResource
                                    {
                                        Id = IdHelper.GetId(),
                                        ReadingProjectStageTaskId = task.Id,
                                        VideoTitle = videoInput.VideoTitle,
                                        VideoUrl = videoInput.VideoUrl,
                                        Duration = videoInput.Duration,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false,
                                        VideoOrder = taskInput.VideoResources.IndexOf(videoInput) + 1
                                    });
                                }
                            }

                            //文档资源（任务类型5）
                            if (taskInput.TaskType == 5 && taskInput.DocumentResources != null && taskInput.DocumentResources.Count > 0)
                            {
                                foreach (var docInput in taskInput.DocumentResources)
                                {
                                    documentResource.Add(new RC_DocumentResource
                                    {
                                        Id = IdHelper.GetId(),
                                        ReadingProjectStageTaskId = task.Id,
                                        DocumentTitle = docInput.DocumentTitle,
                                        DocumentUrl = docInput.DocumentUrl,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false,
                                        DocumentOrder = taskInput.DocumentResources.IndexOf(docInput) + 1
                                    });
                                }
                            }
                        }
                    }

                    //入库
                    if (readingProjectStages.Count > 0)
                    {
                        await DBSqlSugar.Insertable(readingProjectStages).ExecuteCommandAsync();
                    }
                    if (readingProjectStageTask.Count > 0)
                    {
                        await DBSqlSugar.Insertable(readingProjectStageTask).ExecuteCommandAsync();
                    }
                    if (readingProjectStageTaskQuestion.Count > 0)
                    {
                        await DBSqlSugar.Insertable(readingProjectStageTaskQuestion).ExecuteCommandAsync();
                    }
                    if (videoResource.Count > 0)
                    {
                        await DBSqlSugar.Insertable(videoResource).ExecuteCommandAsync();
                    }
                    if (documentResource.Count > 0)
                    {
                        await DBSqlSugar.Insertable(documentResource).ExecuteCommandAsync();
                    }

                    if (input.IsPublish)
                    {
                        //发布班级
                        List<AI_AgentTaskPublish> agentTaskPublishes = new List<AI_AgentTaskPublish>();
                        foreach (var item in input.ClassId)
                        {
                            agentTaskPublishes.Add(new AI_AgentTaskPublish()
                            {
                                Id = IdHelper.GetId(),
                                AgentTaskId = agentTask.Id,
                                PublishType = 1,
                                PublishBusinessId = item,
                                BeginTime = input.TimeRange[0],
                                EndTime = input.TimeRange[1],
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            });
                        }
                        await DBSqlSugar.Insertable(agentTaskPublishes).ExecuteCommandAsync();
                    }

                    return new SaveReadingTaskOutput()
                    {
                        TaskId = agentTask.Id,
                        Success = true,
                        Message = "保存成功"
                    };
                }
                else
                {
                    //获取阅读理解任务
                    AI_AgentTask agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.Id && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                    if (agentTask == null)
                    {
                        throw new BusException("阅读理解Id异常!");
                    }

                    //智能体任务更新
                    agentTask.Name = input.Name;
                    agentTask.Introduce = input.Introduce;
                    agentTask.TaskLogo = input.TaskLogo;
                    agentTask.ModifyTime = DateTime.Now;
                    agentTask.Modifier = input.TeacherId;
                    await DBSqlSugar.Updateable(agentTask).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                    //获取阅读理解阶段
                    List<RC_ReadingProjectStage> readingProjectStages = await DBSqlSugar.Queryable<RC_ReadingProjectStage>().Where(p => p.ReadingProjectId == agentTask.Id && p.IsDeleted == false).With(SqlWith.NoLock).ToListAsync();
                    //获取阅读理解阶段任务
                    List<RC_ReadingProjectStageTask> readingProjectStageTask = new List<RC_ReadingProjectStageTask>();
                    //获取阅读理解阶段任务高频问题
                    List<RC_ReadingProjectStageTaskQuestion> readingProjectStageTaskQuestion = new List<RC_ReadingProjectStageTaskQuestion>();
                    //获取阅读理解阶段任务视频资源
                    List<RC_VideoResource> videoResource = new List<RC_VideoResource>();
                    //获取阅读理解阶段任务文档资源
                    List<RC_DocumentResource> documentResource = new List<RC_DocumentResource>();
                    if (readingProjectStages.Count > 0)
                    {
                        List<string> readingProjectStageIds = readingProjectStages.Select(p => p.Id).ToList();
                        readingProjectStageTask = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>().Where(p => readingProjectStageIds.Contains(p.ReadingProjectStageId) && p.IsDeleted == false).With(SqlWith.NoLock).ToListAsync();
                    }
                    if (readingProjectStageTask.Count > 0)
                    {
                        List<string> readingProjectStageTaskIds = readingProjectStageTask.Select(p => p.Id).ToList();
                        readingProjectStageTaskQuestion = await DBSqlSugar.Queryable<RC_ReadingProjectStageTaskQuestion>().Where(p => readingProjectStageTaskIds.Contains(p.ReadingProjectStageTaskId) && p.IsDeleted == false).With(SqlWith.NoLock).ToListAsync();
                        videoResource = await DBSqlSugar.Queryable<RC_VideoResource>().Where(p => readingProjectStageTaskIds.Contains(p.ReadingProjectStageTaskId) && p.IsDeleted == false).With(SqlWith.NoLock).ToListAsync();
                        documentResource = await DBSqlSugar.Queryable<RC_DocumentResource>().Where(p => readingProjectStageTaskIds.Contains(p.ReadingProjectStageTaskId) && p.IsDeleted == false).With(SqlWith.NoLock).ToListAsync();
                    }

                    //保存阅读理解阶段
                    List<RC_ReadingProjectStage> addReadingProjectStages = new List<RC_ReadingProjectStage>();
                    //编辑阅读理解阶段
                    List<RC_ReadingProjectStage> updateReadingProjectStages = new List<RC_ReadingProjectStage>();
                    //保存阅读理解阶段任务
                    List<RC_ReadingProjectStageTask> addReadingProjectStageTask = new List<RC_ReadingProjectStageTask>();
                    //保存阅读理解阶段任务高频问题
                    List<RC_ReadingProjectStageTaskQuestion> addReadingProjectStageTaskQuestion = new List<RC_ReadingProjectStageTaskQuestion>();
                    //保存阅读理解阶段任务视频资源
                    List<RC_VideoResource> addVideoResource = new List<RC_VideoResource>();
                    //保存阅读理解阶段任务文档资源
                    List<RC_DocumentResource> addDocumentResource = new List<RC_DocumentResource>();

                    //获取存在提交记录的阶段任务Id
                    List<string> submitTaskIds = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                        .Where(p => p.ReadingProjectId == agentTask.Id && p.IsDeleted == false)
                        .With(SqlWith.NoLock).Select(p => p.ReadingProjectStageTaskId).Distinct().ToListAsync();

                    //删除历史任务信息的Id
                    List<string> delStageTaskIds = new List<string>();

                    foreach (var stageInput in input.ProjectStageInfos)
                    {
                        if (string.IsNullOrEmpty(stageInput.Id))
                        {
                            //阶段
                            var stage = new RC_ReadingProjectStage
                            {
                                Id = IdHelper.GetId(),
                                ReadingProjectId = agentTask.Id,
                                StageName = stageInput.Name,
                                StageDescribe = stageInput.Describe,
                                StageOrder = input.ProjectStageInfos.IndexOf(stageInput) + 1,
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            };
                            addReadingProjectStages.Add(stage);

                            foreach (var taskInput in stageInput.ProjectStageTaskInfos)
                            {
                                //阶段任务
                                var task = new RC_ReadingProjectStageTask
                                {
                                    Id = IdHelper.GetId(),
                                    ReadingProjectStageId = stage.Id,
                                    TaskType = taskInput.TaskType,
                                    TaskName = taskInput.Name,
                                    Target = taskInput.Target,
                                    ScoreStandard = taskInput.ScoreStandard,
                                    Demand = taskInput.Demand,
                                    Scope = taskInput.Scope,
                                    RoleSetting = taskInput.RoleSetting,
                                    Prologue = taskInput.Prologue,
                                    ToneId = taskInput.ToneId,
                                    RoleName = taskInput.RoleName,
                                    GroupIsSubmit = taskInput.GroupIsSubmit,
                                    GroupIsAssessment = taskInput.GroupIsAssessment,
                                    GroupAssessmentScore = taskInput.GroupAssessmentScore,
                                    TaskIsSubmit = taskInput.TaskIsSubmit,
                                    TaskIsAssessment = taskInput.TaskIsAssessment,
                                    TaskAssessmentScore = taskInput.TaskAssessmentScore,
                                    TaskOrder = stageInput.ProjectStageTaskInfos.IndexOf(taskInput) + 1,
                                    GroupTotalWatchDurationLimit = taskInput.GroupTotalWatchDurationLimit,
                                    GroupIsWatchAllVideos = taskInput.GroupIsWatchAllVideos,
                                    TaskTotalWatchDurationLimit = taskInput.TaskTotalWatchDurationLimit,
                                    TaskIsWatchAllVideos = taskInput.TaskIsWatchAllVideos,
                                    GroupIsReadAllDocuments = taskInput.GroupIsReadAllDocuments,
                                    TaskIsReadAllDocuments = taskInput.TaskIsReadAllDocuments,
                                    QuestionContent = taskInput.QuestionContent,
                                    CorrectAnswers = taskInput.CorrectAnswers != null ? JsonConvert.SerializeObject(taskInput.CorrectAnswers) : null,
                                    DistractorWords = taskInput.DistractorWords != null ? JsonConvert.SerializeObject(taskInput.DistractorWords) : null,
                                    CustomBackgroundImage = taskInput.CustomBackgroundImage,
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false
                                };
                                addReadingProjectStageTask.Add(task);

                                //高频问题
                                if (taskInput.QuestionInfos != null && taskInput.QuestionInfos.Count > 0)
                                {
                                    foreach (var questionInput in taskInput.QuestionInfos)
                                    {
                                        addReadingProjectStageTaskQuestion.Add(new RC_ReadingProjectStageTaskQuestion
                                        {
                                            Id = IdHelper.GetId(),
                                            ReadingProjectStageTaskId = task.Id,
                                            Name = questionInput.Name,
                                            Describe = questionInput.Describe,
                                            Order = taskInput.QuestionInfos.IndexOf(questionInput) + 1,
                                            CreateTime = DateTime.Now,
                                            Creator = input.TeacherId,
                                            ModifyTime = DateTime.Now,
                                            Modifier = input.TeacherId,
                                            IsDeleted = false
                                        });
                                    }
                                }

                                //视频资源（任务类型4）
                                if (taskInput.TaskType == 4 && taskInput.VideoResources != null && taskInput.VideoResources.Count > 0)
                                {
                                    foreach (var videoInput in taskInput.VideoResources)
                                    {
                                        addVideoResource.Add(new RC_VideoResource
                                        {
                                            Id = IdHelper.GetId(),
                                            ReadingProjectStageTaskId = task.Id,
                                            VideoTitle = videoInput.VideoTitle,
                                            VideoUrl = videoInput.VideoUrl,
                                            Duration = videoInput.Duration,
                                            CreateTime = DateTime.Now,
                                            Creator = input.TeacherId,
                                            ModifyTime = DateTime.Now,
                                            Modifier = input.TeacherId,
                                            IsDeleted = false,
                                            VideoOrder = taskInput.VideoResources.IndexOf(videoInput) + 1
                                        });
                                    }
                                }

                                //文档资源（任务类型5）
                                if (taskInput.TaskType == 5 && taskInput.DocumentResources != null && taskInput.DocumentResources.Count > 0)
                                {
                                    foreach (var docInput in taskInput.DocumentResources)
                                    {
                                        addDocumentResource.Add(new RC_DocumentResource
                                        {
                                            Id = IdHelper.GetId(),
                                            ReadingProjectStageTaskId = task.Id,
                                            DocumentTitle = docInput.DocumentTitle,
                                            DocumentUrl = docInput.DocumentUrl,
                                            CreateTime = DateTime.Now,
                                            Creator = input.TeacherId,
                                            ModifyTime = DateTime.Now,
                                            Modifier = input.TeacherId,
                                            IsDeleted = false,
                                            DocumentOrder = taskInput.DocumentResources.IndexOf(docInput) + 1
                                        });
                                    }
                                }
                            }
                        }
                        else
                        {
                            //获取阅读理解阶段
                            RC_ReadingProjectStage readingProjectStage = readingProjectStages.Where(p => p.Id == stageInput.Id).FirstOrDefault();
                            if (readingProjectStage != null)
                            {
                                readingProjectStage.StageName = stageInput.Name;
                                readingProjectStage.StageDescribe = stageInput.Describe;
                                readingProjectStage.StageOrder = input.ProjectStageInfos.IndexOf(stageInput) + 1;
                                updateReadingProjectStages.Add(readingProjectStage);

                                foreach (var taskInput in stageInput.ProjectStageTaskInfos)
                                {
                                    if (!string.IsNullOrEmpty(taskInput.Id))
                                    {
                                        //获取阅读理解阶段任务信息
                                        RC_ReadingProjectStageTask stageTask = readingProjectStageTask.Where(p => p.Id == taskInput.Id).FirstOrDefault();
                                        if (stageTask != null)
                                        {
                                            //验证是否存在答题记录
                                            bool isDo = submitTaskIds.Contains(stageTask.Id);
                                            if (isDo)
                                            {
                                                continue;
                                            }

                                            //验证是否修改
                                            bool isUpdate = false;
                                            //任务基础信息验证
                                            if (stageTask.TaskType != taskInput.TaskType ||
                                                stageTask.TaskName != taskInput.Name ||
                                                stageTask.Target != taskInput.Target ||
                                                stageTask.ScoreStandard != taskInput.ScoreStandard ||
                                                stageTask.Demand != taskInput.Demand ||
                                                stageTask.Scope != taskInput.Scope ||
                                                stageTask.RoleSetting != taskInput.RoleSetting ||
                                                stageTask.Prologue != taskInput.Prologue ||
                                                stageTask.ToneId != taskInput.ToneId ||
                                                stageTask.RoleName != taskInput.RoleName ||
                                                stageTask.GroupIsSubmit != taskInput.GroupIsSubmit ||
                                                stageTask.GroupIsAssessment != taskInput.GroupIsAssessment ||
                                                stageTask.GroupAssessmentScore != taskInput.GroupAssessmentScore ||
                                                stageTask.TaskIsSubmit != taskInput.TaskIsSubmit ||
                                                stageTask.TaskIsAssessment != taskInput.TaskIsAssessment ||
                                                stageTask.TaskAssessmentScore != taskInput.TaskAssessmentScore ||
                                                stageTask.GroupTotalWatchDurationLimit != taskInput.GroupTotalWatchDurationLimit ||
                                                stageTask.GroupIsWatchAllVideos != taskInput.GroupIsWatchAllVideos ||
                                                stageTask.TaskTotalWatchDurationLimit != taskInput.TaskTotalWatchDurationLimit ||
                                                stageTask.TaskIsWatchAllVideos != taskInput.TaskIsWatchAllVideos ||
                                                stageTask.GroupIsReadAllDocuments != taskInput.GroupIsReadAllDocuments ||
                                                stageTask.TaskIsReadAllDocuments != taskInput.TaskIsReadAllDocuments ||
                                                stageTask.QuestionContent != taskInput.QuestionContent ||
                                                stageTask.CorrectAnswers != JsonConvert.SerializeObject(taskInput.CorrectAnswers) ||
                                                stageTask.DistractorWords != JsonConvert.SerializeObject(taskInput.DistractorWords) ||
                                                stageTask.CustomBackgroundImage != taskInput.CustomBackgroundImage ||
                                                stageTask.TaskOrder != stageInput.ProjectStageTaskInfos.IndexOf(taskInput) + 1)
                                            {
                                                isUpdate = true;
                                            }

                                            //任务问题验证
                                            if (!isUpdate)
                                            {
                                                List<RC_ReadingProjectStageTaskQuestion> taskQuestions = readingProjectStageTaskQuestion.Where(p => p.ReadingProjectStageTaskId == stageTask.Id).ToList();
                                                if (taskQuestions.Count != taskInput.QuestionInfos.Count)
                                                {
                                                    isUpdate = true;
                                                }
                                                else
                                                {
                                                    foreach (var question in taskInput.QuestionInfos)
                                                    {
                                                        if (string.IsNullOrEmpty(question.Id))
                                                        {
                                                            isUpdate = true;
                                                            break;
                                                        }
                                                        else
                                                        {
                                                            //获取问题信息
                                                            RC_ReadingProjectStageTaskQuestion stageTaskQuestion = taskQuestions.Where(p => p.Id == question.Id).FirstOrDefault();
                                                            if (stageTaskQuestion != null)
                                                            {
                                                                if (stageTaskQuestion.Name != question.Name
                                                                    || stageTaskQuestion.Describe != question.Describe
                                                                    || stageTaskQuestion.Order != taskInput.QuestionInfos.IndexOf(question) + 1)
                                                                {
                                                                    isUpdate = true;
                                                                    break;
                                                                }
                                                            }
                                                            else
                                                            {
                                                                isUpdate = true;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            //任务文档验证
                                            if (!isUpdate)
                                            {
                                                List<RC_DocumentResource> documents = documentResource.Where(p => p.ReadingProjectStageTaskId == stageTask.Id).ToList();
                                                if (documents.Count != taskInput.DocumentResources.Count)
                                                {
                                                    isUpdate = true;
                                                }
                                                else
                                                {
                                                    foreach (var taskDocument in taskInput.DocumentResources)
                                                    {
                                                        if (string.IsNullOrEmpty(taskDocument.Id))
                                                        {
                                                            isUpdate = true;
                                                            break;
                                                        }
                                                        else
                                                        {
                                                            //获取文档信息
                                                            RC_DocumentResource stageTaskDocument = documents.Where(p => p.Id == taskDocument.Id).FirstOrDefault();
                                                            if (stageTaskDocument != null)
                                                            {
                                                                if (stageTaskDocument.DocumentTitle != taskDocument.DocumentTitle
                                                                    || stageTaskDocument.DocumentDescription != taskDocument.DocumentDescription
                                                                    || stageTaskDocument.DocumentUrl != taskDocument.DocumentUrl
                                                                    || stageTaskDocument.DocumentOrder != taskInput.DocumentResources.IndexOf(taskDocument) + 1)
                                                                {
                                                                    isUpdate = true;
                                                                    break;
                                                                }
                                                            }
                                                            else
                                                            {
                                                                isUpdate = true;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            //任务视频验证
                                            if (!isUpdate)
                                            {
                                                List<RC_VideoResource> videos = videoResource.Where(p => p.ReadingProjectStageTaskId == stageTask.Id).ToList();
                                                if (videos.Count != taskInput.VideoResources.Count)
                                                {
                                                    isUpdate = true;
                                                }
                                                else
                                                {
                                                    foreach (var taskVideo in taskInput.VideoResources)
                                                    {
                                                        if (string.IsNullOrEmpty(taskVideo.Id))
                                                        {
                                                            isUpdate = true;
                                                            break;
                                                        }
                                                        else
                                                        {
                                                            //获取视频信息
                                                            RC_VideoResource stageTaskVideo = videos.Where(p => p.Id == taskVideo.Id).FirstOrDefault();
                                                            if (stageTaskVideo != null)
                                                            {
                                                                if (stageTaskVideo.VideoTitle != taskVideo.VideoTitle
                                                                    || stageTaskVideo.VideoUrl != taskVideo.VideoUrl
                                                                    || stageTaskVideo.VideoDescription != taskVideo.VideoDescription
                                                                    || stageTaskVideo.Duration != taskVideo.Duration
                                                                    || stageTaskVideo.VideoOrder != taskInput.VideoResources.IndexOf(taskVideo) + 1)
                                                                {
                                                                    isUpdate = true;
                                                                    break;
                                                                }
                                                            }
                                                            else
                                                            {
                                                                isUpdate = true;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            if (!isUpdate)
                                            {
                                                continue;
                                            }

                                            delStageTaskIds.Add(taskInput.Id);
                                        }
                                    }

                                    //阶段任务
                                    var task = new RC_ReadingProjectStageTask
                                    {
                                        Id = IdHelper.GetId(),
                                        ReadingProjectStageId = readingProjectStage.Id,
                                        TaskType = taskInput.TaskType,
                                        TaskName = taskInput.Name,
                                        Target = taskInput.Target,
                                        ScoreStandard = taskInput.ScoreStandard,
                                        Demand = taskInput.Demand,
                                        Scope = taskInput.Scope,
                                        RoleSetting = taskInput.RoleSetting,
                                        Prologue = taskInput.Prologue,
                                        ToneId = taskInput.ToneId,
                                        RoleName = taskInput.RoleName,
                                        GroupIsSubmit = taskInput.GroupIsSubmit,
                                        GroupIsAssessment = taskInput.GroupIsAssessment,
                                        GroupAssessmentScore = taskInput.GroupAssessmentScore,
                                        TaskIsSubmit = taskInput.TaskIsSubmit,
                                        TaskIsAssessment = taskInput.TaskIsAssessment,
                                        TaskAssessmentScore = taskInput.TaskAssessmentScore,
                                        TaskOrder = stageInput.ProjectStageTaskInfos.IndexOf(taskInput) + 1,
                                        GroupTotalWatchDurationLimit = taskInput.GroupTotalWatchDurationLimit,
                                        GroupIsWatchAllVideos = taskInput.GroupIsWatchAllVideos,
                                        TaskTotalWatchDurationLimit = taskInput.TaskTotalWatchDurationLimit,
                                        TaskIsWatchAllVideos = taskInput.TaskIsWatchAllVideos,
                                        GroupIsReadAllDocuments = taskInput.GroupIsReadAllDocuments,
                                        TaskIsReadAllDocuments = taskInput.TaskIsReadAllDocuments,
                                        QuestionContent = taskInput.QuestionContent,
                                        CorrectAnswers = taskInput.CorrectAnswers != null ? JsonConvert.SerializeObject(taskInput.CorrectAnswers) : null,
                                        DistractorWords = taskInput.DistractorWords != null ? JsonConvert.SerializeObject(taskInput.DistractorWords) : null,
                                        CustomBackgroundImage = taskInput.CustomBackgroundImage,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false
                                    };
                                    addReadingProjectStageTask.Add(task);

                                    //高频问题
                                    if (taskInput.QuestionInfos != null && taskInput.QuestionInfos.Count > 0)
                                    {
                                        foreach (var questionInput in taskInput.QuestionInfos)
                                        {
                                            addReadingProjectStageTaskQuestion.Add(new RC_ReadingProjectStageTaskQuestion
                                            {
                                                Id = IdHelper.GetId(),
                                                ReadingProjectStageTaskId = task.Id,
                                                Name = questionInput.Name,
                                                Describe = questionInput.Describe,
                                                Order = taskInput.QuestionInfos.IndexOf(questionInput) + 1,
                                                CreateTime = DateTime.Now,
                                                Creator = input.TeacherId,
                                                ModifyTime = DateTime.Now,
                                                Modifier = input.TeacherId,
                                                IsDeleted = false
                                            });
                                        }
                                    }

                                    //视频资源（任务类型4）
                                    if (taskInput.TaskType == 4 && taskInput.VideoResources != null && taskInput.VideoResources.Count > 0)
                                    {
                                        foreach (var videoInput in taskInput.VideoResources)
                                        {
                                            addVideoResource.Add(new RC_VideoResource
                                            {
                                                Id = IdHelper.GetId(),
                                                ReadingProjectStageTaskId = task.Id,
                                                VideoTitle = videoInput.VideoTitle,
                                                VideoUrl = videoInput.VideoUrl,
                                                Duration = videoInput.Duration,
                                                CreateTime = DateTime.Now,
                                                Creator = input.TeacherId,
                                                ModifyTime = DateTime.Now,
                                                Modifier = input.TeacherId,
                                                IsDeleted = false,
                                                VideoOrder = taskInput.VideoResources.IndexOf(videoInput) + 1
                                            });
                                        }
                                    }

                                    //文档资源（任务类型5）
                                    if (taskInput.TaskType == 5 && taskInput.DocumentResources != null && taskInput.DocumentResources.Count > 0)
                                    {
                                        foreach (var docInput in taskInput.DocumentResources)
                                        {
                                            addDocumentResource.Add(new RC_DocumentResource
                                            {
                                                Id = IdHelper.GetId(),
                                                ReadingProjectStageTaskId = task.Id,
                                                DocumentTitle = docInput.DocumentTitle,
                                                DocumentUrl = docInput.DocumentUrl,
                                                CreateTime = DateTime.Now,
                                                Creator = input.TeacherId,
                                                ModifyTime = DateTime.Now,
                                                Modifier = input.TeacherId,
                                                IsDeleted = false,
                                                DocumentOrder = taskInput.DocumentResources.IndexOf(docInput) + 1
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }

                    //获取所有阶段任务Id和阶段Id
                    List<string> stageTaskIds = new List<string>();
                    List<string> stageIds = new List<string>();
                    foreach (var stage in input.ProjectStageInfos)
                    {
                        if (!string.IsNullOrEmpty(stage.Id))
                        {
                            stageIds.Add(stage.Id);
                        }
                        foreach (var stageTaskInfo in stage.ProjectStageTaskInfos)
                        {
                            if (!string.IsNullOrEmpty(stageTaskInfo.Id))
                            {
                                stageTaskIds.Add(stageTaskInfo.Id);
                            }
                        }
                    }

                    //删除的任务Id
                    foreach (var stageTask in readingProjectStageTask)
                    {
                        if (!stageTaskIds.Contains(stageTask.Id))
                        {
                            delStageTaskIds.Add(stageTask.Id);
                        }
                    }
                    if (delStageTaskIds.Count > 0)
                    {
                        //删除任务
                        await DBSqlSugar.Deleteable<RC_ReadingProjectStageTask>().Where(p => delStageTaskIds.Contains(p.Id)).ExecuteCommandAsync();
                        //删除提交记录
                        await DBSqlSugar.Deleteable<RC_StudentTaskRecord>().Where(p => delStageTaskIds.Contains(p.ReadingProjectStageTaskId)).ExecuteCommandAsync();
                        //清空高频问题
                        await DBSqlSugar.Deleteable<RC_ReadingProjectStageTaskQuestion>().Where(p => delStageTaskIds.Contains(p.ReadingProjectStageTaskId)).ExecuteCommandAsync();
                        //删除任务视频
                        await DBSqlSugar.Deleteable<RC_VideoResource>().Where(p => delStageTaskIds.Contains(p.ReadingProjectStageTaskId)).ExecuteCommandAsync();
                        //删除任务文档信息
                        await DBSqlSugar.Deleteable<RC_DocumentResource>().Where(p => delStageTaskIds.Contains(p.ReadingProjectStageTaskId)).ExecuteCommandAsync();
                        //删除任务视频观看记录
                        await DBSqlSugar.Deleteable<RC_StudentVideoWatchRecord>().Where(p => delStageTaskIds.Contains(p.ReadingProjectStageTaskId)).ExecuteCommandAsync();
                        //删除任务文档信息阅读记录
                        await DBSqlSugar.Deleteable<RC_StudentDocumentReadRecord>().Where(p => delStageTaskIds.Contains(p.ReadingProjectStageTaskId)).ExecuteCommandAsync();
                        //清空缓冲池和问答记录
                        await DBSqlSugar.Deleteable<AI_ContextCacheKey>().Where(p => delStageTaskIds.Contains(p.BusinessId)).ExecuteCommandAsync();
                        await DBSqlSugar.Deleteable<AI_DialogueContentRecord>().Where(p => delStageTaskIds.Contains(p.BusinessId)).ExecuteCommandAsync();
                    }

                    //删除的阶段Id
                    List<string> delStageIds = new List<string>();
                    foreach (var stage in readingProjectStages)
                    {
                        if (!stageIds.Contains(stage.Id))
                        {
                            delStageIds.Add(stage.Id);
                        }
                    }
                    if (delStageIds.Count > 0)
                    {
                        //删除阶段
                        await DBSqlSugar.Deleteable<RC_ReadingProjectStage>().Where(p => delStageIds.Contains(p.Id)).ExecuteCommandAsync();
                    }

                    //更新/新增任务相关信息
                    if (updateReadingProjectStages.Count > 0)
                    {
                        await DBSqlSugar.Updateable(updateReadingProjectStages).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }
                    if (addReadingProjectStages.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addReadingProjectStages).ExecuteCommandAsync();
                    }
                    if (addReadingProjectStageTask.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addReadingProjectStageTask).ExecuteCommandAsync();
                    }
                    if (addReadingProjectStageTaskQuestion.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addReadingProjectStageTaskQuestion).ExecuteCommandAsync();
                    }
                    if (addVideoResource.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addVideoResource).ExecuteCommandAsync();
                    }
                    if (addDocumentResource.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addDocumentResource).ExecuteCommandAsync();
                    }

                    //发布设置
                    if (input.IsPublish)
                    {
                        //发布班级
                        List<AI_AgentTaskPublish> agentTaskPublishes = new List<AI_AgentTaskPublish>();
                        foreach (var item in input.ClassId)
                        {
                            agentTaskPublishes.Add(new AI_AgentTaskPublish()
                            {
                                Id = IdHelper.GetId(),
                                AgentTaskId = agentTask.Id,
                                PublishType = 1,
                                PublishBusinessId = item,
                                BeginTime = input.TimeRange[0],
                                EndTime = input.TimeRange[1],
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            });
                        }
                        //清空历史数据
                        await DBSqlSugar.Deleteable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == agentTask.Id).ExecuteCommandAsync();
                        //保存最新数据
                        await DBSqlSugar.Insertable(agentTaskPublishes).ExecuteCommandAsync();
                    }
                    else
                    {
                        //清空历史数据
                        await DBSqlSugar.Deleteable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == agentTask.Id).ExecuteCommandAsync();
                    }

                    return new SaveReadingTaskOutput()
                    {
                        TaskId = agentTask.Id,
                        Success = true,
                        Message = "保存成功"
                    };
                }
            }
            catch (Exception ex)
            {
                throw new BusException($"保存失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 保存阅读理解项目化实践任务信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<SaveReadingTaskOutput> SaveReadingTask(SaveReadingTaskInput input)
        {
            try
            {
                DBSqlSugar.BeginTran();

                // 保存项目基本信息
                string projectId = await SaveProjectInfo(input);

                // 保存项目阶段和任务
                await SaveProjectStages(projectId, input);

                // 如果需要发布，则发布到班级
                if (input.IsPublish && input.ClassId != null && input.ClassId.Count > 0)
                {
                    await PublishProjectToClasses(projectId, input.ClassId, input.TimeRange, input.TeacherId);
                }

                DBSqlSugar.CommitTran();

                return new SaveReadingTaskOutput
                {
                    TaskId = projectId,
                    Success = true,
                    Message = "保存成功"
                };
            }
            catch (Exception ex)
            {
                DBSqlSugar.RollbackTran();
                throw new BusException($"保存失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 保存项目基本信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<string> SaveProjectInfo(SaveReadingTaskInput input)
        {
            bool isEdit = !string.IsNullOrEmpty(input.Id);
            string projectId;

            if (isEdit)
            {
                // 编辑模式 - 更新项目基本信息
                var existingProject = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.Id && p.IsDeleted == false)
                    .FirstAsync();

                if (existingProject == null)
                {
                    throw new BusException("项目不存在！");
                }

                existingProject.Name = input.Name;
                existingProject.Introduce = input.Introduce;
                existingProject.TaskLogo = input.TaskLogo;
                existingProject.ModifyTime = DateTime.Now;
                existingProject.Modifier = input.TeacherId;
                await DBSqlSugar.Updateable(existingProject).ExecuteCommandAsync();
                projectId = existingProject.Id;
            }
            else
            {
                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!", 801);
                }

                // 新增模式 - 创建新项目
                projectId = IdHelper.GetId();
                var newProject = new AI_AgentTask
                {
                    Id = projectId,
                    AgentId = input.AgentId,
                    Creator = input.TeacherId,
                    Name = input.Name,
                    AgentTaskType = 3, // 3表示阅读理解
                    Introduce = input.Introduce,
                    TaskLogo = input.TaskLogo,
                    SubjectId = input.SubjectId,
                    Term = nowSemesterTime.NowTerm,
                    Year = nowSemesterTime.Year,
                    GradeId = input.GradeId,
                    // IsPublished = false,
                    CreateTime = DateTime.Now,
                    ModifyTime = DateTime.Now,
                    Modifier = input.TeacherId,
                    IsDeleted = false
                };

                await DBSqlSugar.Insertable(newProject).ExecuteCommandAsync();
            }

            return projectId;
        }

        /// <summary>
        /// 保存项目阶段和任务
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task SaveProjectStages(string projectId, SaveReadingTaskInput input)
        {
            // 如果是编辑模式，先删除旧的阶段和任务
            if (!string.IsNullOrEmpty(input.Id))
            {
                await DeleteOldStagesAndTasks(projectId);
            }

            // 保存新的阶段和任务
            int stageOrder = 1;
            foreach (var stageInput in input.ProjectStageInfos)
            {
                string stageId = await SaveProjectStage(projectId, stageInput, stageOrder, input.TeacherId);
                await SaveStageTasks(stageId, stageInput.ProjectStageTaskInfos, input.TeacherId);
                stageOrder++;
            }
        }

        /// <summary>
        /// 删除旧的阶段和任务
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        private async Task DeleteOldStagesAndTasks(string projectId)
        {
            // 获取所有阶段
            var stages = await DBSqlSugar.Queryable<RC_ReadingProjectStage>()
                .Where(p => p.ReadingProjectId == projectId && p.IsDeleted == false)
                .ToListAsync();

            foreach (var stage in stages)
            {
                // 删除阶段下的所有任务
                await DBSqlSugar.Updateable<RC_ReadingProjectStageTask>()
                    .SetColumns(p => new RC_ReadingProjectStageTask { IsDeleted = true })
                    .Where(p => p.ReadingProjectStageId == stage.Id)
                    .ExecuteCommandAsync();

                // 获取阶段下的所有任务ID
                var taskIds = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                    .Where(t => t.ReadingProjectStageId == stage.Id)
                    .Select(t => t.Id)
                    .ToListAsync();

                if (taskIds.Count > 0)
                {
                    // 删除任务的高频问题
                    await DBSqlSugar.Updateable<RC_ReadingProjectStageTaskQuestion>()
                        .SetColumns(p => new RC_ReadingProjectStageTaskQuestion { IsDeleted = true })
                        .Where(p => taskIds.Contains(p.ReadingProjectStageTaskId))
                        .ExecuteCommandAsync();

                    // 删除视频资源
                    await DBSqlSugar.Updateable<RC_VideoResource>()
                        .SetColumns(p => new RC_VideoResource { IsDeleted = true })
                        .Where(p => taskIds.Contains(p.ReadingProjectStageTaskId))
                        .ExecuteCommandAsync();

                    // 删除文档资源
                    await DBSqlSugar.Updateable<RC_DocumentResource>()
                        .SetColumns(p => new RC_DocumentResource { IsDeleted = true })
                        .Where(p => taskIds.Contains(p.ReadingProjectStageTaskId))
                        .ExecuteCommandAsync();
                }
            }

            // 删除阶段
            await DBSqlSugar.Updateable<RC_ReadingProjectStage>()
                .SetColumns(p => new RC_ReadingProjectStage { IsDeleted = true })
                .Where(p => p.ReadingProjectId == projectId)
                .ExecuteCommandAsync();
        }

        /// <summary>
        /// 保存项目阶段
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="stageInput"></param>
        /// <param name="stageOrder"></param>
        /// <returns></returns>
        private async Task<string> SaveProjectStage(string projectId, ReadingProjectStageInfoInput stageInput, int stageOrder, string creator)
        {
            string stageId = IdHelper.GetId();
            var stage = new RC_ReadingProjectStage
            {
                Id = stageId,
                ReadingProjectId = projectId,
                StageName = stageInput.Name,
                StageDescribe = stageInput.Describe,
                StageOrder = stageOrder,
                CreateTime = DateTime.Now,
                Creator = creator, // 这里可以传入创建者ID
                ModifyTime = DateTime.Now,
                Modifier = creator,
                IsDeleted = false
            };

            await DBSqlSugar.Insertable(stage).ExecuteCommandAsync();
            return stageId;
        }

        /// <summary>
        /// 保存阶段任务
        /// </summary>
        /// <param name="stageId"></param>
        /// <param name="taskInputs"></param>
        /// <returns></returns>
        private async Task SaveStageTasks(string stageId, List<ReadingProjectStageTaskInfoInput> taskInputs, string creator)
        {
            int taskOrder = 1;
            foreach (var taskInput in taskInputs)
            {
                string taskId = await SaveStageTask(stageId, taskInput, taskOrder, creator);

                // 保存高频问题
                await SaveTaskQuestions(taskId, taskInput.QuestionInfos, creator);

                // 根据任务类型保存特殊资源
                await SaveTaskResources(taskId, taskInput, creator);

                taskOrder++;
            }
        }

        /// <summary>
        /// 保存阶段任务
        /// </summary>
        /// <param name="stageId"></param>
        /// <param name="taskInput"></param>
        /// <param name="taskOrder"></param>
        /// <returns></returns>
        private async Task<string> SaveStageTask(string stageId, ReadingProjectStageTaskInfoInput taskInput, int taskOrder, string creator)
        {
            string taskId = IdHelper.GetId();
            var task = new RC_ReadingProjectStageTask
            {
                Id = taskId,
                ReadingProjectStageId = stageId,
                TaskType = taskInput.TaskType,
                TaskName = taskInput.Name,
                Target = taskInput.Target,
                ScoreStandard = taskInput.ScoreStandard,
                Demand = taskInput.Demand,
                Scope = taskInput.Scope,
                RoleSetting = taskInput.RoleSetting,
                Prologue = taskInput.Prologue,
                ToneId = taskInput.ToneId,
                RoleName = taskInput.RoleName,
                GroupIsSubmit = taskInput.GroupIsSubmit,
                GroupIsAssessment = taskInput.GroupIsAssessment,
                GroupAssessmentScore = taskInput.GroupAssessmentScore,
                TaskIsSubmit = taskInput.TaskIsSubmit,
                TaskIsAssessment = taskInput.TaskIsAssessment,
                TaskAssessmentScore = taskInput.TaskAssessmentScore,
                TaskOrder = taskOrder,
                // 新增任务类型特殊字段 - 分离的解锁条件
                GroupTotalWatchDurationLimit = taskInput.GroupTotalWatchDurationLimit,
                GroupIsWatchAllVideos = taskInput.GroupIsWatchAllVideos,
                TaskTotalWatchDurationLimit = taskInput.TaskTotalWatchDurationLimit,
                TaskIsWatchAllVideos = taskInput.TaskIsWatchAllVideos,
                GroupIsReadAllDocuments = taskInput.GroupIsReadAllDocuments,
                TaskIsReadAllDocuments = taskInput.TaskIsReadAllDocuments,
                QuestionContent = taskInput.QuestionContent,
                CorrectAnswers = taskInput.CorrectAnswers != null ? JsonConvert.SerializeObject(taskInput.CorrectAnswers) : null,
                DistractorWords = taskInput.DistractorWords != null ? JsonConvert.SerializeObject(taskInput.DistractorWords) : null,
                CustomBackgroundImage = taskInput.CustomBackgroundImage,
                CreateTime = DateTime.Now,
                Creator = creator,
                ModifyTime = DateTime.Now,
                Modifier = creator,
                IsDeleted = false
            };

            await DBSqlSugar.Insertable(task).ExecuteCommandAsync();
            return taskId;
        }

        /// <summary>
        /// 保存任务高频问题
        /// </summary>
        /// <param name="taskId"></param>
        /// <param name="questionInputs"></param>
        /// <returns></returns>
        private async Task SaveTaskQuestions(string taskId, List<ReadingProjectStageTaskQuestionInfoInput> questionInputs, string creator)
        {
            if (questionInputs == null || questionInputs.Count == 0) return;

            int questionOrder = 1;
            foreach (var questionInput in questionInputs)
            {
                var question = new RC_ReadingProjectStageTaskQuestion
                {
                    Id = IdHelper.GetId(),
                    ReadingProjectStageTaskId = taskId,
                    Name = questionInput.Name,
                    Describe = questionInput.Describe,
                    Order = questionOrder,
                    CreateTime = DateTime.Now,
                    Creator = creator,
                    ModifyTime = DateTime.Now,
                    Modifier = creator,
                    IsDeleted = false
                };

                await DBSqlSugar.Insertable(question).ExecuteCommandAsync();
                questionOrder++;
            }
        }

        /// <summary>
        /// 保存任务资源
        /// </summary>
        /// <param name="taskId"></param>
        /// <param name="taskInput"></param>
        /// <returns></returns>
        private async Task SaveTaskResources(string taskId, ReadingProjectStageTaskInfoInput taskInput, string creator)
        {
            // 保存视频资源（任务类型4）
            if (taskInput.TaskType == 4 && taskInput.VideoResources != null && taskInput.VideoResources.Count > 0)
            {
                foreach (var videoInput in taskInput.VideoResources)
                {
                    var video = new RC_VideoResource
                    {
                        Id = IdHelper.GetId(),
                        ReadingProjectStageTaskId = taskId,
                        VideoTitle = videoInput.VideoTitle,
                        VideoUrl = videoInput.VideoUrl,
                        Duration = videoInput.Duration,
                        // VideoDuration = videoInput.VideoDuration,
                        // VideoSize = videoInput.VideoSize,
                        CreateTime = DateTime.Now,
                        Creator = creator,
                        ModifyTime = DateTime.Now,
                        Modifier = creator,
                        IsDeleted = false
                    };

                    await DBSqlSugar.Insertable(video).ExecuteCommandAsync();
                }
            }

            // 保存文档资源（任务类型5）
            if (taskInput.TaskType == 5 && taskInput.DocumentResources != null && taskInput.DocumentResources.Count > 0)
            {
                foreach (var docInput in taskInput.DocumentResources)
                {
                    var document = new RC_DocumentResource
                    {
                        Id = IdHelper.GetId(),
                        ReadingProjectStageTaskId = taskId,
                        DocumentTitle = docInput.DocumentTitle,
                        DocumentUrl = docInput.DocumentUrl,
                        CreateTime = DateTime.Now,
                        Creator = creator,
                        ModifyTime = DateTime.Now,
                        Modifier = creator,
                        IsDeleted = false
                    };

                    await DBSqlSugar.Insertable(document).ExecuteCommandAsync();
                }
            }
        }

        /// <summary>
        /// 发布项目到班级
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="classIds"></param>
        /// <param name="timeRange"></param>
        /// <param name="teacherId"></param>
        /// <returns></returns>
        private async Task PublishProjectToClasses(string projectId, List<string> classIds, List<DateTime> timeRange, string teacherId = "System")
        {
            // 先删除旧的发布记录
            await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                .SetColumns(p => new AI_AgentTaskPublish { IsDeleted = true, ModifyTime = DateTime.Now })
                .Where(p => p.AgentTaskId == projectId)
                .ExecuteCommandAsync();

            // 创建新的发布记录
            List<AI_AgentTaskPublish> publishRecords = new List<AI_AgentTaskPublish>();
            foreach (var classId in classIds)
            {
                publishRecords.Add(new AI_AgentTaskPublish
                {
                    Id = IdHelper.GetId(),
                    AgentTaskId = projectId,
                    PublishType = 1, // 班级发布
                    PublishBusinessId = classId,
                    BeginTime = timeRange.Count > 0 ? timeRange[0] : DateTime.Now,
                    EndTime = timeRange.Count > 1 ? timeRange[1] : null,
                    CreateTime = DateTime.Now,
                    Creator = teacherId,
                    ModifyTime = DateTime.Now,
                    Modifier = teacherId,
                    IsDeleted = false
                });
            }

            // 批量插入发布记录
            await DBSqlSugar.Insertable(publishRecords).ExecuteCommandAsync();

            // 更新项目发布状态
            //await DBSqlSugar.Updateable<AI_AgentTask>()
            //    .SetColumns(p => new AI_AgentTask
            //    {
            //        IsPublished = true,
            //        PublishTime = DateTime.Now
            //    })
            //    .Where(p => p.Id == projectId)
            //    .ExecuteCommandAsync();
        }

        /// <summary>
        /// 获取阅读理解任务详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ReadingTaskDetailsOutput> GetReadingTaskDetails(ReadingTaskDetailsInput input)
        {
            // 获取项目基本信息
            AI_AgentTask project = await DBSqlSugar.Queryable<AI_AgentTask>()
                .Where(p => p.Id == input.ProjectId && p.IsDeleted == false)
                .FirstAsync();

            if (project == null)
            {
                throw new BusException("阅读理解项目不存在！");
            }

            ReadingTaskDetailsOutput output = new ReadingTaskDetailsOutput
            {
                TaskId = project.Id,
                TeacherId = project.Creator,
                Name = project.Name,
                Introduce = project.Introduce,
                TaskType = 0, // 项目化实践不使用单一任务类型
                RoleSetting = "",
                ScoreStandard = "",
                TaskTarget = "",
                TaskRequirement = "",
                TaskScope = "",
                //IsPublished = project.IsPublished,
                //PublishTime = project.PublishTime,
                CreateTime = project.CreateTime
            };

            return output;
        }

        /// <summary>
        /// 获取阅读理解项目详情（与项目化实践接口保持一致）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ProjectTaskDetailsOutput> GetProjectTaskDetails(ProjectTaskDetailsInput input)
        {
            try
            {
                // 查询阅读理解项目基本信息
                var readingProject = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId)
                    .FirstAsync();
                if (readingProject == null)
                {
                    throw new BusException("阅读理解项目不存在或无权限访问!");
                }

                // 初始化输出对象
                var output = new ProjectTaskDetailsOutput
                {
                    Id = readingProject.Id,
                    Name = readingProject.Name,
                    Introduce = readingProject.Introduce,
                    TaskLogo = readingProject.TaskLogo,
                    TeacherId = readingProject.Creator
                };

                // 获取发布信息
                var publishInfo = await DBSqlSugar.Queryable<AI_AgentTaskPublish>()
                    .Where(p => p.AgentTaskId == readingProject.Id && p.IsDeleted == false)
                    .ToListAsync();
                if (publishInfo.Any())
                {
                    output.ClassId = publishInfo.Select(p => p.PublishBusinessId).ToList();
                    output.TimeRange = new List<DateTime?>
                    {
                        publishInfo.Min(p => p.BeginTime),
                        publishInfo.Max(p => p.EndTime)
                    };
                }

                // 获取项目阶段信息
                var stages = await DBSqlSugar.Queryable<RC_ReadingProjectStage>()
                    .Where(s => s.ReadingProjectId == readingProject.Id && s.IsDeleted == false)
                    .OrderBy(s => s.StageOrder)
                    .ToListAsync();

                foreach (var stage in stages)
                {
                    var stageOutput = new ProjectStageDetailsOutput
                    {
                        Id = stage.Id,
                        Name = stage.StageName,
                        Describe = stage.StageDescribe, // 阅读理解阶段表没有描述字段，使用名称
                        ProjectStageTaskInfos = new List<ProjectStageTaskDetailsOutput>()
                    };

                    // 获取阶段任务信息
                    var tasks = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                        .Where(t => t.ReadingProjectStageId == stage.Id && t.IsDeleted == false)
                        .OrderBy(t => t.TaskOrder)
                        .ToListAsync();

                    foreach (var task in tasks)
                    {
                        var taskOutput = new ProjectStageTaskDetailsOutput
                        {
                            Id = task.Id,
                            TaskType = task.TaskType,
                            Name = task.TaskName,
                            Target = task.Target,
                            ScoreStandard = task.ScoreStandard,
                            Demand = task.Demand,
                            Scope = task.Scope,
                            RoleSetting = task.RoleSetting,
                            Prologue = task.Prologue,
                            ToneId = task.ToneId,
                            RoleName = task.RoleName,
                            GroupIsSubmit = task.GroupIsSubmit,
                            GroupIsAssessment = task.GroupIsAssessment,
                            GroupAssessmentScore = task.GroupAssessmentScore,
                            TaskIsSubmit = task.TaskIsSubmit,
                            TaskIsAssessment = task.TaskIsAssessment,
                            TaskAssessmentScore = task.TaskAssessmentScore,
                            QuestionInfos = new List<ProjectStageTaskQuestionDetailsOutput>()
                        };

                        // 获取任务高频问题
                        var questions = await DBSqlSugar.Queryable<RC_ReadingProjectStageTaskQuestion>()
                            .Where(q => q.ReadingProjectStageTaskId == task.Id && q.IsDeleted == false)
                            .OrderBy(q => q.Order)
                            .ToListAsync();

                        foreach (var question in questions)
                        {
                            taskOutput.QuestionInfos.Add(new ProjectStageTaskQuestionDetailsOutput
                            {
                                Id = question.Id,
                                Name = question.Name,
                                Describe = question.Describe
                            });
                        }

                        // 检查是否存在学生提交记录
                        taskOutput.IsDo = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                            .Where(r => r.ReadingProjectStageTaskId == task.Id && r.IsDeleted == false)
                            .AnyAsync();

                        // 根据任务类型获取特有配置
                        await LoadTaskSpecificConfig(taskOutput, task);

                        stageOutput.ProjectStageTaskInfos.Add(taskOutput);
                    }

                    output.ProjectStageInfos.Add(stageOutput);
                }

                return output;
            }
            catch (Exception ex)
            {
                throw new BusException($"获取阅读理解项目详情失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取阅读理解任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ReadingTaskListOutput> GetReadingTaskList(ReadingTaskListInput input)
        {
            var query = DBSqlSugar.Queryable<AI_AgentTask>()
                .Where(p => p.Creator == input.TeacherId && p.IsDeleted == false);

            // 班级筛选：如果指定了班级ID，则只查询发布到该班级的任务
            if (!string.IsNullOrEmpty(input.ClassId))
            {
                query = query.Where(p => DBSqlSugar.Queryable<AI_AgentTaskPublish>()
                    .Where(pub => pub.AgentTaskId == p.Id
                        && pub.PublishBusinessId == input.ClassId
                        && pub.PublishType == 1
                        && pub.IsDeleted == false)
                    .Any());
            }

            // 发布状态筛选（PublishStatus优先级高于IsPublished）
            if (input.PublishStatus == 1) // 已发布
            {
                // query = query.Where(p => p.IsPublished == true);
            }
            else if (input.PublishStatus == 2) // 未发布
            {
                // query = query.Where(p => p.IsPublished == false);
            }
            // PublishStatus == 0 表示全部，不添加筛选条件

            // 兼容旧版本的IsPublished字段（如果PublishStatus为0且IsPublished有值）
            if (input.PublishStatus == 0 && input.IsPublished.HasValue)
            {
                //query = query.Where(p => p.IsPublished == input.IsPublished.Value);
            }

            // 搜索关键词
            if (!string.IsNullOrEmpty(input.Name))
            {
                query = query.Where(p => p.Name.Contains(input.Name) || p.Introduce.Contains(input.Name));
            }

            // 总数量
            int totalCount = await query.CountAsync();

            // 分页查询
            List<AI_AgentTask> projects = await query
                .OrderByDescending(p => p.CreateTime)
                .Skip((input.PageIndex - 1) * input.PageSize)
                .Take(input.PageSize)
                .ToListAsync();

            List<ReadingTaskListItemOutput> taskList = new List<ReadingTaskListItemOutput>();

            foreach (var project in projects)
            {
                // 获取发布班级数量
                int publishedClassCount = await DBSqlSugar.Queryable<AI_AgentTaskPublish>()
                    .Where(p => p.AgentTaskId == project.Id && p.IsDeleted == false)
                    .CountAsync();

                // 获取学生完成情况
                var studentStats = await GetProjectStudentStats(project.Id);

                // 获取项目包含的任务类型（用于显示）
                var taskTypes = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                    .LeftJoin<RC_ReadingProjectStage>((t, s) => t.ReadingProjectStageId == s.Id)
                    .Where((t, s) => s.ReadingProjectId == project.Id && t.IsDeleted == false && s.IsDeleted == false)
                    .Select(t => t.TaskType)
                    .Distinct()
                    .ToListAsync();

                // 获取发布班级信息
                var publishedClasses = await GetPublishedClassInfo(project.Id);

                ReadingTaskListItemOutput item = new ReadingTaskListItemOutput
                {
                    TaskId = project.Id,
                    Name = project.Name,
                    Introduce = project.Introduce,
                    TaskLogo = project.TaskLogo,
                    TaskType = taskTypes.FirstOrDefault(), // 显示第一个任务类型
                    TaskTypeName = GetProjectTaskTypesName(taskTypes), // 显示所有任务类型
                                                                       // IsPublished = project.IsPublished,
                                                                       // PublishTime = project.PublishTime,
                    CreateTime = project.CreateTime,
                    PublishedClassCount = publishedClassCount,
                    CompletedStudentCount = studentStats.CompletedCount,
                    TotalStudentCount = studentStats.TotalCount,
                    PublishedClasses = publishedClasses
                };

                taskList.Add(item);
            }

            return new ReadingTaskListOutput
            {
                TaskList = taskList,
                TotalCount = totalCount,
                PageIndex = input.PageIndex,
                PageSize = input.PageSize
            };
        }

        /// <summary>
        /// 获取阅读理解项目列表（与项目化实践接口保持一致）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageReturn<GetProjectTaskListOutput>> GetProjectTaskList(GetProjectTaskListInput input)
        {
            try
            {
                input.AgentTaskType = 3;
                // 构建基础查询 - 添加智能体类型筛选
                var query = DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Creator == input.TeacherId && p.IsDeleted == false && p.AgentTaskType == input.AgentTaskType);

                // 智能体ID筛选
                if (!string.IsNullOrEmpty(input.AgentId))
                {
                    query = query.Where(p => p.AgentId == input.AgentId);
                }

                // 项目名称筛选
                if (!string.IsNullOrEmpty(input.Name))
                {
                    query = query.Where(p => p.Name.Contains(input.Name));
                }

                // 班级筛选：如果指定了班级ID，则只查询发布到该班级的任务
                if (!string.IsNullOrEmpty(input.ClassId))
                {
                    // 先查询发布到该班级的项目ID列表
                    var publishedProjectIds = await DBSqlSugar.Queryable<AI_AgentTaskPublish>()
                        .Where(pub => pub.PublishBusinessId == input.ClassId
                            && pub.PublishType == 1
                            && pub.IsDeleted == false)
                        .Select(pub => pub.AgentTaskId)
                        .ToListAsync();

                    if (publishedProjectIds.Any())
                    {
                        query = query.Where(p => publishedProjectIds.Contains(p.Id));
                    }
                    else
                    {
                        // 如果没有发布到该班级的项目，返回空结果
                        query = query.Where(p => false);
                    }
                }

                // 发布状态筛选 - 参考项目化实践的逻辑
                if (input.PublishStatus == 1) // 已发布
                {
                    // 查询在AI_AgentTaskPublish表中存在记录的项目
                    var publishedTaskIds = await DBSqlSugar.Queryable<AI_AgentTaskPublish>()
                        .Where(pub => pub.PublishType == 1 && pub.IsDeleted == false)
                        .Select(pub => pub.AgentTaskId)
                        .Distinct()
                        .ToListAsync();

                    if (publishedTaskIds.Any())
                    {
                        query = query.Where(p => publishedTaskIds.Contains(p.Id));
                    }
                    else
                    {
                        // 如果没有任何发布记录，返回空结果
                        query = query.Where(p => false);
                    }
                }
                else if (input.PublishStatus == 2) // 未发布
                {
                    // 查询在AI_AgentTaskPublish表中不存在记录的项目
                    var publishedTaskIds = await DBSqlSugar.Queryable<AI_AgentTaskPublish>()
                        .Where(pub => pub.PublishType == 1 && pub.IsDeleted == false)
                        .Select(pub => pub.AgentTaskId)
                        .Distinct()
                        .ToListAsync();

                    if (publishedTaskIds.Any())
                    {
                        query = query.Where(p => !publishedTaskIds.Contains(p.Id));
                    }
                    // 如果没有任何发布记录，所有项目都是未发布状态，不需要额外筛选
                }
                // PublishStatus == 0 表示全部，不添加筛选条件

                // 获取总数
                var totalCount = await query.CountAsync();

                // 分页查询
                var projects = await query
                    .OrderByDescending(p => p.CreateTime)
                    .Skip((input.PageIndex - 1) * input.PageSize)
                    .Take(input.PageSize)
                    .ToListAsync();

                var result = new List<GetProjectTaskListOutput>();

                foreach (var project in projects)
                {
                    // 获取发布班级信息
                    var publishedClasses = await GetProjectPublishedClasses(project.Id);

                    // 判断发布状态：基于是否存在发布记录
                    int publishStatus = publishedClasses.Any() ? 1 : 2; // 1=已发布，2=未发布

                    var item = new GetProjectTaskListOutput
                    {
                        AgentId = input.AgentId, // 使用输入的智能体ID
                        ProjectId = project.Id,
                        Name = project.Name,
                        Introduce = project.Introduce,
                        TaskLogo = project.TaskLogo,
                        PublishStatus = publishStatus, // 基于实际发布记录判断
                        ClassInfos = publishedClasses
                    };

                    result.Add(item);
                }

                return new PageReturn<GetProjectTaskListOutput>
                {
                    TotalCount = totalCount,
                    Datas = result
                };
            }
            catch (Exception ex)
            {
                throw new BusException($"获取阅读理解项目列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取项目发布的班级信息
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        private async Task<List<GetProjectTaskListClassInfoOutput>> GetProjectPublishedClasses(string projectId)
        {
            // 直接关联查询 AI_AgentTaskPublish 和 Exam_Class 获取班级信息
            var classInfos = await DBSqlSugar.Queryable<AI_AgentTaskPublish, Exam_Class>((p, c) => new JoinQueryInfos(
                JoinType.Inner, p.PublishBusinessId == c.Id
            ))
            .Where((p, c) => p.AgentTaskId == projectId && p.IsDeleted == false && p.PublishType == 1 && c.Deleted == false)
            .Select((p, c) => new GetProjectTaskListClassInfoOutput
            {
                ClassId = c.Id,
                ClassName = c.ClassName
            })
            .ToListAsync();

            return classInfos;
        }

        /// <summary>
        /// 删除阅读理解任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<bool> DeleteReadingTask(DeleteReadingTaskInput input)
        {
            try
            {
                DBSqlSugar.BeginTran();

                // 验证项目是否存在且属于当前教师
                var project = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId)
                    .FirstAsync();

                if (project == null)
                {
                    throw new BusException("阅读理解项目不存在或无权限访问！");
                }

                // 删除项目发布记录
                await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                    .SetColumns(p => new AI_AgentTaskPublish { IsDeleted = true })
                    .Where(p => p.AgentTaskId == input.ProjectId)
                    .ExecuteCommandAsync();

                // 获取所有阶段
                var stages = await DBSqlSugar.Queryable<RC_ReadingProjectStage>()
                    .Where(p => p.ReadingProjectId == input.ProjectId && p.IsDeleted == false)
                    .ToListAsync();

                foreach (var stage in stages)
                {
                    // 获取阶段下的所有任务
                    var tasks = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                        .Where(p => p.ReadingProjectStageId == stage.Id && p.IsDeleted == false)
                        .ToListAsync();

                    foreach (var task in tasks)
                    {
                        // 删除任务的高频问题
                        await DBSqlSugar.Updateable<RC_ReadingProjectStageTaskQuestion>()
                            .SetColumns(p => new RC_ReadingProjectStageTaskQuestion { IsDeleted = true })
                            .Where(p => p.ReadingProjectStageTaskId == task.Id)
                            .ExecuteCommandAsync();

                        // 删除任务的视频资源
                        await DBSqlSugar.Updateable<RC_VideoResource>()
                            .SetColumns(p => new RC_VideoResource { IsDeleted = true })
                            .Where(p => p.ReadingProjectStageTaskId == task.Id)
                            .ExecuteCommandAsync();

                        // 删除任务的文档资源
                        await DBSqlSugar.Updateable<RC_DocumentResource>()
                            .SetColumns(p => new RC_DocumentResource { IsDeleted = true })
                            .Where(p => p.ReadingProjectStageTaskId == task.Id)
                            .ExecuteCommandAsync();
                    }

                    // 删除阶段下的所有任务
                    await DBSqlSugar.Updateable<RC_ReadingProjectStageTask>()
                        .SetColumns(p => new RC_ReadingProjectStageTask { IsDeleted = true })
                        .Where(p => p.ReadingProjectStageId == stage.Id)
                        .ExecuteCommandAsync();
                }

                // 删除所有阶段
                await DBSqlSugar.Updateable<RC_ReadingProjectStage>()
                    .SetColumns(p => new RC_ReadingProjectStage { IsDeleted = true })
                    .Where(p => p.ReadingProjectId == input.ProjectId)
                    .ExecuteCommandAsync();

                // 删除项目
                await DBSqlSugar.Updateable<AI_AgentTask>()
                    .SetColumns(p => new AI_AgentTask { IsDeleted = true })
                    .Where(p => p.Id == input.ProjectId)
                    .ExecuteCommandAsync();

                DBSqlSugar.CommitTran();
                return true;
            }
            catch
            {
                DBSqlSugar.RollbackTran();
                return false;
            }
        }

        /// <summary>
        /// 发布阅读理解任务到班级
        /// </summary>
        /// <param name="input">发布参数</param>
        /// <returns></returns>
        public async Task PublishReadingTaskToClass(PublishProjectTaskToClassInput input)
        {
            try
            {
                // 检查任务是否存在且属于当前教师
                var project = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId)
                    .FirstAsync();

                if (project == null)
                {
                    throw new BusException("阅读理解项目不存在或无权限访问!");
                }

                // 检查项目是否有有效的阶段和任务
                var projectStages = await DBSqlSugar.Queryable<RC_ReadingProjectStage>()
                    .Where(p => p.ReadingProjectId == input.ProjectId && p.IsDeleted == false)
                    .ToListAsync();

                if (!projectStages.Any())
                {
                    throw new BusException("项目至少需要一个有效阶段才能发布!");
                }

                var stageTasks = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                    .Where(p => projectStages.Select(s => s.Id).Contains(p.ReadingProjectStageId) && p.IsDeleted == false)
                    .ToListAsync();

                if (!stageTasks.Any())
                {
                    throw new BusException("项目至少需要一个有效任务才能发布!");
                }

                // 先删除旧的发布记录
                await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                    .SetColumns(p => new AI_AgentTaskPublish { IsDeleted = true, ModifyTime = DateTime.Now })
                    .Where(p => p.AgentTaskId == input.ProjectId)
                    .ExecuteCommandAsync();

                // 创建新的发布记录
                var publishRecords = input.ClassIds.Select(classId => new AI_AgentTaskPublish
                {
                    Id = IdHelper.GetId(),
                    AgentTaskId = input.ProjectId,
                    PublishType = 1, // 班级发布
                    PublishBusinessId = classId,
                    BeginTime = input.TimeRange[0].Value,
                    EndTime = input.TimeRange[1].Value,
                    CreateTime = DateTime.Now,
                    Creator = input.TeacherId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.TeacherId,
                    IsDeleted = false
                }).ToList();

                // 批量插入发布记录
                await DBSqlSugar.Insertable(publishRecords).ExecuteCommandAsync();

                // 更新项目发布状态
                //await DBSqlSugar.Updateable<AI_AgentTask>()
                //    .SetColumns(p => new AI_AgentTask
                //    {
                //        IsPublished = true,
                //        PublishTime = DateTime.Now
                //    })
                //    .Where(p => p.Id == input.ProjectId)
                //    .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 撤销阅读理解任务发布
        /// </summary>
        /// <param name="input">撤销发布参数</param>
        /// <returns></returns>
        public async Task UnpublishProjectTask(UnpublishProjectTaskInput input)
        {
            try
            {
                // 检查任务是否存在且属于当前教师
                var project = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId)
                    .FirstAsync();

                if (project == null)
                {
                    throw new BusException("阅读理解项目不存在或无权限访问!");
                }

                // 逻辑删除该项目的所有发布记录
                await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                    .SetColumns(p => new AI_AgentTaskPublish { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.AgentTaskId == input.ProjectId && p.Creator == input.TeacherId)
                    .ExecuteCommandAsync();

                // 更新项目发布状态为未发布
                await DBSqlSugar.Updateable<AI_AgentTask>()
                    .SetColumns(p => new AI_AgentTask
                    {
                        //IsPublished = false,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.Id == input.ProjectId)
                    .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException($"撤销阅读理解项目发布失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取学生完成情况统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ReadingTaskStudentStatisticsOutput> GetStudentStatistics(ReadingTaskStudentStatisticsInput input)
        {
            // 验证项目是否存在
            var project = await DBSqlSugar.Queryable<AI_AgentTask>()
                .Where(p => p.Id == input.ProjectId && p.IsDeleted == false)
                .FirstAsync();

            if (project == null)
            {
                throw new BusException("阅读理解项目不存在！");
            }

            // 获取项目发布的班级
            var publishedClasses = await DBSqlSugar.Queryable<AI_AgentTaskPublish>()
                .Where(p => p.AgentTaskId == input.ProjectId && p.IsDeleted == false)
                .Select(p => p.PublishBusinessId)
                .ToListAsync();

            if (publishedClasses.Count == 0)
            {
                return new ReadingTaskStudentStatisticsOutput
                {
                    TaskId = input.ProjectId,
                    Name = project.Name,
                    TotalStudentCount = 0,
                    CompletedStudentCount = 0,
                    InProgressStudentCount = 0,
                    NotStartedStudentCount = 0,
                    CompletionRate = 0,
                    AverageScore = 0,
                    StudentList = new List<StudentProjectStatisticsItemOutput>()
                };
            }

            // 获取所有学生的完成情况统计
            var studentStats = await GetDetailedStudentStatistics(input.ProjectId, publishedClasses);

            return new ReadingTaskStudentStatisticsOutput
            {
                TaskId = input.ProjectId,
                Name = project.Name,
                TotalStudentCount = studentStats.Count,
                CompletedStudentCount = studentStats.Count(s => s.ProjectStatus == 3),
                InProgressStudentCount = studentStats.Count(s => s.ProjectStatus == 2),
                NotStartedStudentCount = studentStats.Count(s => s.ProjectStatus == 1),
                CompletionRate = studentStats.Count > 0 ? (decimal)studentStats.Count(s => s.ProjectStatus == 3) / studentStats.Count * 100 : 0,
                AverageScore = studentStats.Count > 0 ? studentStats.Average(s => s.AverageScore) : 0,
                StudentList = studentStats
            };
        }

        #region 私有辅助方法

        /// <summary>
        /// 获取任务类型名称
        /// </summary>
        /// <param name="taskType"></param>
        /// <returns></returns>
        private string GetTaskTypeName(int taskType)
        {
            return taskType switch
            {
                1 => "成果评估",
                2 => "情景对话",
                3 => "知识问答",
                4 => "视频任务",
                5 => "文档任务",
                6 => "思维导图",
                7 => "选词填空",
                _ => "未知类型"
            };
        }

        /// <summary>
        /// 获取项目任务类型名称组合
        /// </summary>
        /// <param name="taskTypes"></param>
        /// <returns></returns>
        private string GetProjectTaskTypesName(List<int> taskTypes)
        {
            if (taskTypes == null || taskTypes.Count == 0)
                return "无任务";

            var typeNames = taskTypes.Select(GetTaskTypeName).ToList();
            return string.Join("、", typeNames);
        }

        /// <summary>
        /// 获取项目学生统计信息
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        private async Task<(int TotalCount, int CompletedCount)> GetProjectStudentStats(string projectId)
        {
            // 获取发布的班级
            var publishedClasses = await DBSqlSugar.Queryable<AI_AgentTaskPublish>()
                .Where(p => p.AgentTaskId == projectId && p.IsDeleted == false)
                .Select(p => p.PublishBusinessId)
                .ToListAsync();

            if (publishedClasses.Count == 0)
                return (0, 0);

            // 这里需要根据实际的学生班级关系表来查询
            // 暂时返回模拟数据
            return (0, 0);
        }

        /// <summary>
        /// 获取详细的学生统计信息
        /// </summary>
        /// <param name="taskId"></param>
        /// <param name="classIds"></param>
        /// <returns></returns>
        private Task<List<StudentProjectStatisticsItemOutput>> GetDetailedStudentStatistics(string taskId, List<string> classIds)
        {
            // 这里需要根据实际的学生完成记录来统计
            // 暂时返回空列表
            return Task.FromResult(new List<StudentProjectStatisticsItemOutput>());
        }

        /// <summary>
        /// 获取项目发布班级信息
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        private async Task<List<PublishedClassInfo>> GetPublishedClassInfo(string projectId)
        {
            // 直接关联查询 AI_AgentTaskPublish 和 Exam_Class 获取班级信息
            var classInfos = await DBSqlSugar.Queryable<AI_AgentTaskPublish, Exam_Class>((p, c) => new JoinQueryInfos(
                JoinType.Inner, p.PublishBusinessId == c.Id
            ))
            .Where((p, c) => p.AgentTaskId == projectId && p.IsDeleted == false && p.PublishType == 1 && c.Deleted == false)
            .Select((p, c) => new PublishedClassInfo
            {
                ClassId = c.Id,
                ClassName = c.ClassName,
                BeginTime = p.BeginTime,
                EndTime = p.EndTime
            })
            .ToListAsync();

            return classInfos;
        }

        /// <summary>
        /// 根据任务类型加载特有配置
        /// </summary>
        /// <param name="taskOutput"></param>
        /// <param name="task"></param>
        /// <returns></returns>
        private async Task LoadTaskSpecificConfig(ProjectStageTaskDetailsOutput taskOutput, RC_ReadingProjectStageTask task)
        {
            switch (task.TaskType)
            {
                case 4: // 视频任务
                    await LoadVideoTaskConfig(taskOutput, task);
                    break;
                case 5: // 文档任务
                    await LoadDocumentTaskConfig(taskOutput, task);
                    break;
                case 6: // 思维导图任务
                    await LoadMindMapTaskConfig(taskOutput, task);
                    break;
                case 7: // 选词填空任务
                    await LoadWordFillTaskConfig(taskOutput, task);
                    break;
            }
        }

        /// <summary>
        /// 加载视频任务配置
        /// </summary>
        private async Task LoadVideoTaskConfig(ProjectStageTaskDetailsOutput taskOutput, RC_ReadingProjectStageTask task)
        {
            // 设置视频组间解锁条件
            taskOutput.GroupTotalWatchDurationLimit = task.GroupTotalWatchDurationLimit;
            taskOutput.GroupIsWatchAllVideos = task.GroupIsWatchAllVideos;

            // 设置视频任务点解锁条件
            taskOutput.TaskTotalWatchDurationLimit = task.TaskTotalWatchDurationLimit;
            taskOutput.TaskIsWatchAllVideos = task.TaskIsWatchAllVideos;

            // 获取视频资源
            var videoResources = await DBSqlSugar.Queryable<RC_VideoResource>()
                .Where(v => v.ReadingProjectStageTaskId == task.Id && v.IsDeleted == false)
                .OrderBy(v => v.VideoOrder)
                .ToListAsync();

            taskOutput.VideoResources = new List<VideoResourceOutput>();
            foreach (var video in videoResources)
            {
                taskOutput.VideoResources.Add(new VideoResourceOutput
                {
                    Id = video.Id,
                    VideoTitle = video.VideoTitle,
                    VideoDescription = video.VideoDescription,
                    VideoUrl = video.VideoUrl,
                    Duration = video.Duration ?? 0,
                    VideoOrder = video.VideoOrder
                });
            }
        }

        /// <summary>
        /// 加载文档任务配置
        /// </summary>
        private async Task LoadDocumentTaskConfig(ProjectStageTaskDetailsOutput taskOutput, RC_ReadingProjectStageTask task)
        {
            // 设置文档组间解锁条件
            taskOutput.GroupIsReadAllDocuments = task.GroupIsReadAllDocuments;

            // 设置文档任务点解锁条件
            taskOutput.TaskIsReadAllDocuments = task.TaskIsReadAllDocuments;

            // 获取文档资源
            var documentResources = await DBSqlSugar.Queryable<RC_DocumentResource>()
                .Where(d => d.ReadingProjectStageTaskId == task.Id && d.IsDeleted == false)
                .OrderBy(d => d.DocumentOrder)
                .ToListAsync();

            taskOutput.DocumentResources = new List<DocumentResourceOutput>();
            foreach (var document in documentResources)
            {
                taskOutput.DocumentResources.Add(new DocumentResourceOutput
                {
                    Id = document.Id,
                    DocumentTitle = document.DocumentTitle,
                    DocumentDescription = document.DocumentDescription,
                    DocumentUrl = document.DocumentUrl,
                    DocumentOrder = document.DocumentOrder
                });
            }
        }

        /// <summary>
        /// 加载思维导图任务配置
        /// </summary>
        private async Task LoadMindMapTaskConfig(ProjectStageTaskDetailsOutput taskOutput, RC_ReadingProjectStageTask task)
        {
            // 获取思维导图配置
            //var mindMapConfig = await DBSqlSugar.Queryable<RC_MindMapTaskConfig>()
            //    .Where(m => m.ReadingProjectStageTaskId == task.Id && m.IsDeleted == false)
            //    .FirstAsync();

            //if (mindMapConfig != null)
            //{
            //    // 直接设置思维导图配置字段
            //   // taskOutput.MindMapRequirement = mindMapConfig.MindMapRequirement;
            //   // taskOutput.TemplateUrl = mindMapConfig.TemplateUrl;
            //}
            await Task.CompletedTask;
        }

        /// <summary>
        /// 加载选词填空任务配置
        /// </summary>
        private Task LoadWordFillTaskConfig(ProjectStageTaskDetailsOutput taskOutput, RC_ReadingProjectStageTask task)
        {
            // 直接设置选词填空配置字段
            taskOutput.QuestionContent = task.QuestionContent;
            taskOutput.CorrectAnswers = task.CorrectAnswers;
            taskOutput.DistractorWords = task.DistractorWords;
            taskOutput.CustomBackgroundImage = task.CustomBackgroundImage;

            return Task.CompletedTask;
        }

        #endregion

        /// <summary>
        /// 获取阅读理解项目综合分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetProjectSynthesizeAnalyseOutput> GetProjectSynthesizeAnalyse(GetProjectSynthesizeAnalyseInput input)
        {
            try
            {
                // 获取阅读理解项目基础信息
                GetProjectSynthesizeAnalyseOutput projectInfoOutput = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetProjectSynthesizeAnalyseOutput()
                    {
                        AgentId = p.AgentId,
                        ProjectId = p.Id,
                        ProjectName = p.Name,
                        ProjectIntroduce = p.Introduce,
                        ProjectLogo = p.TaskLogo
                    })
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (projectInfoOutput == null)
                {
                    throw new BusException("阅读理解项目Id异常!");
                }

                // 阅读理解项目阶段
                List<GetProjectSynthesizeAnalyseStageOutput> projectStages = await DBSqlSugar.Queryable<RC_ReadingProjectStage>()
                    .Where(p => p.ReadingProjectId == projectInfoOutput.ProjectId && p.IsDeleted == false)
                    .OrderBy(p => p.StageOrder)
                    .Select(p => new GetProjectSynthesizeAnalyseStageOutput()
                    {
                        Id = p.Id,
                        Name = p.StageName
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                // 阅读理解项目阶段任务 - 通过阶段关联查询
                List<RC_ReadingProjectStageTask> projectStageTasks = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                    .LeftJoin<RC_ReadingProjectStage>((t, s) => t.ReadingProjectStageId == s.Id)
                    .Where((t, s) => s.ReadingProjectId == projectInfoOutput.ProjectId && t.IsDeleted == false && s.IsDeleted == false)
                    .OrderBy((t, s) => t.ReadingProjectStageId)
                    .OrderBy((t, s) => t.TaskOrder)
                    .Select((t, s) => new RC_ReadingProjectStageTask()
                    {
                        Id = t.Id,
                        ReadingProjectStageId = t.ReadingProjectStageId,
                        TaskName = t.TaskName,
                        TaskOrder = t.TaskOrder,
                        TaskType = t.TaskType
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                // 获取班级学生数量
                List<string> studentIds = await DBSqlSugar.Queryable<Exam_Student>()
                    .Where(p => p.ClassId == input.ClassId && p.Deleted == false)
                    .Select(p => p.Id)
                    .With(SqlWith.NoLock)
                    .ToListAsync();
                projectInfoOutput.StudentCount = studentIds.Count;

                // 获取班级学生提交数据
                string stuDoTaskSql = @"SELECT
                                        	str.Id,
                                        	str.ReadingProjectStageTaskId,
                                        	str.StudentId,
                                        	str.Score,
                                        	str.IsStandard,
                                        	ROW_NUMBER() OVER (PARTITION BY str.ReadingProjectStageTaskId, str.StudentId ORDER BY str.CreateTime ASC) as [Order]
                                        FROM
                                        	RC_StudentTaskRecord str WITH ( NOLOCK )
                                        	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON str.StudentId = stu.Id
                                        	AND stu.Deleted = 0
                                        WHERE
                                        	str.IsDeleted = 0
                                        	AND stu.ClassId = @classId
                                        	AND str.ReadingProjectId = @projectId";

                var stuDoTaskInfos = await DBSqlSugar.SqlQueryable<dynamic>(stuDoTaskSql)
                    .AddParameters(new { classId = input.ClassId, projectId = input.ProjectId })
                    .ToListAsync();

                // 转换为强类型对象以便后续处理
                var taskRecords = stuDoTaskInfos.Select(x => new
                {
                    Id = (string)x.Id,
                    ReadingProjectStageTaskId = (string)x.ReadingProjectStageTaskId,
                    StudentId = (string)x.StudentId,
                    Score = (decimal)x.Score,
                    IsStandard = (bool)x.IsStandard,
                    Order = (long)x.Order
                }).ToList();

                // 平均等第计算（基于首次提交记录）
                var firstSubmissions = taskRecords.Where(p => p.Order == 1).ToList();
                if (firstSubmissions.Any())
                {
                    decimal avgScore = firstSubmissions.Average(p => p.Score);
                    projectInfoOutput.AvgLevel = BusinessUtil.GetAvgLevel(avgScore);
                    projectInfoOutput.AvgLevelCount = firstSubmissions.Count;
                }
                else
                {
                    projectInfoOutput.AvgLevel = "D";
                    projectInfoOutput.AvgLevelCount = 0;
                }

                // 完成率计算（排除对话类型任务 TaskType != 3）
                var nonDialogueTasks = projectStageTasks.Where(p => p.TaskType != 3).ToList();
                int totalRequiredSubmissions = nonDialogueTasks.Count * projectInfoOutput.StudentCount;
                int completedSubmissions = taskRecords.Count(p => p.IsStandard);
                projectInfoOutput.Finish = BusinessUtil.GetAccuracy(completedSubmissions, totalRequiredSubmissions, 1);

                // 提交率计算（去重后的提交数）
                projectInfoOutput.SubmitCount = totalRequiredSubmissions;
                int distinctSubmissions = taskRecords
                    .GroupBy(task => new { task.ReadingProjectStageTaskId, task.StudentId })
                    .Count();
                projectInfoOutput.Submit = BusinessUtil.GetAccuracy(distinctSubmissions, projectInfoOutput.SubmitCount, 1);

                // 参与率计算（基于对话记录判断学生是否参与过项目）
                if (projectStageTasks.Any())
                {
                    // 查询对话记录，判断学生参与情况
                    List<string> dialogueContentRecord = await DBSqlSugar.Queryable<AI_DialogueContentRecord>()
                        .Where(p => projectStageTasks.Select(t => t.Id).Contains(p.BusinessId) && p.IsDeleted == false)
                        .Select(p => p.Key)
                        .Distinct()
                        .With(SqlWith.NoLock)
                        .ToListAsync();

                    foreach (var studentId in studentIds)
                    {
                        if (dialogueContentRecord.Any(s => s.Contains(studentId)))
                        {
                            projectInfoOutput.ParticipationCount += 1;
                        }
                    }

                    projectInfoOutput.Participation = BusinessUtil.GetAccuracy(projectInfoOutput.ParticipationCount, projectInfoOutput.StudentCount, 1);
                }

                // 阶段完成情况统计
                foreach (var stage in projectStages)
                {
                    // 获取当前阶段的任务（排除对话类型）
                    var stageNonDialogueTasks = projectStageTasks
                        .Where(p => p.ReadingProjectStageId == stage.Id && p.TaskType != 3)
                        .ToList();

                    foreach (var task in stageNonDialogueTasks)
                    {
                        // 检查是否所有学生都完成了该任务（达标）
                        int taskCompletedCount = taskRecords
                            .Count(p => p.ReadingProjectStageTaskId == task.Id && p.IsStandard);

                        if (taskCompletedCount >= projectInfoOutput.StudentCount)
                        {
                            stage.FinishCount += 1;
                        }
                    }

                    stage.TaskCount = stageNonDialogueTasks.Count;
                    stage.Finish = BusinessUtil.GetAccuracy(stage.FinishCount, stage.TaskCount, 1);
                }
                projectInfoOutput.StageInfo = projectStages;

                // 阶段任务平均分计算（基于首次提交记录）
                foreach (var stageTask in nonDialogueTasks)
                {
                    var taskFirstSubmissions = taskRecords
                        .Where(p => p.ReadingProjectStageTaskId == stageTask.Id && p.Order == 1)
                        .ToList();

                    decimal? avgScore = taskFirstSubmissions.Any()
                        ? taskFirstSubmissions.Average(p => p.Score)
                        : null;

                    projectInfoOutput.StageTaskAvgScore.Add(new GetProjectSynthesizeAnalyseScoreOutput()
                    {
                        StageTaskName = stageTask.TaskName,
                        AvgScore = avgScore
                    });
                }

                return projectInfoOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取阅读理解项目阶段任务统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<GetProjectStageTaskCountOutput>> GetProjectStageTaskCount(GetProjectStageTaskCountInput input)
        {
            try
            {
                // 获取阅读理解项目阶段信息
                List<GetProjectStageTaskCountOutput> projectStages = await DBSqlSugar.Queryable<RC_ReadingProjectStage>()
                    .Where(p => p.ReadingProjectId == input.ProjectId && p.IsDeleted == false)
                    .OrderBy(p => p.StageOrder)
                    .Select(p => new GetProjectStageTaskCountOutput()
                    {
                        Id = p.Id,
                        Name = p.StageName
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                // 获取阅读理解项目阶段任务
                List<RC_ReadingProjectStageTask> projectStageTasks = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                    .LeftJoin<RC_ReadingProjectStage>((t, s) => t.ReadingProjectStageId == s.Id)
                    .Where((t, s) => s.ReadingProjectId == input.ProjectId && t.IsDeleted == false && s.IsDeleted == false)
                    .OrderBy((t, s) => t.TaskOrder)
                    .Select((t, s) => new RC_ReadingProjectStageTask()
                    {
                        Id = t.Id,
                        ReadingProjectStageId = t.ReadingProjectStageId,
                        TaskName = t.TaskName,
                        Target = t.Target,
                        TaskType = t.TaskType,
                        TaskOrder = t.TaskOrder
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                // 获取班级学生数量
                List<string> studentIds = await DBSqlSugar.Queryable<Exam_Student>()
                    .Where(p => p.ClassId == input.ClassId && p.Deleted == false)
                    .Select(p => p.Id)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                // 获取班级学生提交数据
                string stuTaskRecordSql = @"SELECT
                                        	str.Id,
                                        	str.ReadingProjectStageTaskId,
                                        	str.StudentId,
                                        	str.Score,
                                        	str.IsStandard,
                                        	str.AIGrade,
                                        	str.MatchedQuestions,
                                        	str.TaskStatus,
                                        	str.CreateTime,
                                        	ROW_NUMBER() OVER (PARTITION BY str.ReadingProjectStageTaskId, str.StudentId ORDER BY str.CreateTime ASC) as [Order]
                                        FROM
                                        	RC_StudentTaskRecord str WITH ( NOLOCK )
                                        	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON str.StudentId = stu.Id
                                        	AND stu.Deleted = 0
                                        WHERE
                                        	str.IsDeleted = 0
                                        	AND stu.ClassId = @classId
                                        	AND str.ReadingProjectId = @projectId";

                var stuTaskRecords = await DBSqlSugar.SqlQueryable<dynamic>(stuTaskRecordSql)
                    .AddParameters(new { classId = input.ClassId, projectId = input.ProjectId })
                    .ToListAsync();

                // 转换为强类型对象以便后续处理
                var taskRecords = stuTaskRecords.Select(x => new
                {
                    Id = Convert.ToString(x.Id),
                    ReadingProjectStageTaskId = Convert.ToString(x.ReadingProjectStageTaskId),
                    StudentId = Convert.ToString(x.StudentId),
                    Score = Convert.ToDecimal(x.Score ?? 0),
                    IsStandard = Convert.ToBoolean(x.IsStandard ?? false),
                    AIGrade = Convert.ToString(x.AIGrade ?? ""),
                    MatchedQuestions = Convert.ToString(x.MatchedQuestions ?? ""),
                    TaskStatus = Convert.ToInt32(x.TaskStatus ?? 1),
                    CreateTime = Convert.ToDateTime(x.CreateTime),
                    Order = Convert.ToInt64(x.Order)
                }).ToList();

                // 获取选词填空操作记录（用于选词填空任务统计）
                string wordFillRecordSql = @"SELECT
                                        	wfr.Id,
                                        	wfr.ReadingProjectStageTaskId,
                                        	wfr.StudentId,
                                        	wfr.CreateTime
                                        FROM
                                        	RC_StudentWordFillOperationRecord wfr WITH ( NOLOCK )
                                        	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON wfr.StudentId = stu.Id
                                        	AND stu.Deleted = 0
                                        WHERE
                                        	wfr.IsDeleted = 0
                                        	AND stu.ClassId = @classId";

                var wordFillRecords = await DBSqlSugar.SqlQueryable<dynamic>(wordFillRecordSql)
                    .AddParameters(new { classId = input.ClassId })
                    .ToListAsync();

                // 转换选词填空记录为强类型对象
                var wordFillRecordList = wordFillRecords.Select(x => new
                {
                    Id = (string)x.Id,
                    ReadingProjectStageTaskId = (string)x.ReadingProjectStageTaskId,
                    StudentId = (string)x.StudentId,
                    CreateTime = (DateTime)x.CreateTime
                }).ToList();

                // 获取对话记录（用于对话类任务的参与率统计）
                List<AI_DialogueContentRecord> dialogueContentRecord = new List<AI_DialogueContentRecord>();
                if (projectStageTasks.Any(p => p.TaskType == 2 || p.TaskType == 3)) // 情景对话或知识问答
                {
                    List<AI_DialogueContentRecord> dialogues = await DBSqlSugar.Queryable<AI_DialogueContentRecord>()
                        .Where(p => projectStageTasks.Select(t => t.Id).Contains(p.BusinessId) && p.IsDeleted == false)
                        .Select(p => new AI_DialogueContentRecord()
                        {
                            Key = p.Key,
                            BusinessId = p.BusinessId,
                            CreateTime = p.CreateTime
                        })
                        .With(SqlWith.NoLock)
                        .ToListAsync();

                    foreach (var studentId in studentIds)
                    {
                        List<AI_DialogueContentRecord> dialogue = dialogues.Where(p => p.Key.Contains(studentId)).ToList();
                        if (dialogue.Count > 0)
                        {
                            dialogueContentRecord.AddRange(dialogue);
                        }
                    }
                }

                // 处理阶段统计
                foreach (var stage in projectStages)
                {
                    // 获取当前阶段的任务
                    List<RC_ReadingProjectStageTask> stageTaskList = projectStageTasks
                        .Where(p => p.ReadingProjectStageId == stage.Id)
                        .OrderBy(p => p.TaskOrder)
                        .ToList();

                    foreach (var task in stageTaskList)
                    {
                        // 跳过视频和文档任务（不做统计）
                        if (task.TaskType == 4 || task.TaskType == 5)
                        {
                            continue;
                        }

                        GetProjectStageTaskInfoOutput stageTaskInfoOutput = new GetProjectStageTaskInfoOutput()
                        {
                            TaskType = task.TaskType,
                            Name = task.TaskName,
                            Target = task.Target
                        };

                        // 根据任务类型进行不同的统计
                        switch (task.TaskType)
                        {
                            case 6: // 思维导图任务
                                await ProcessMindMapTaskStatistics(stageTaskInfoOutput, task, taskRecords, studentIds);
                                break;
                            case 7: // 选词填空任务
                                await ProcessWordFillTaskStatistics(stageTaskInfoOutput, task, taskRecords, wordFillRecordList, studentIds);
                                break;
                            case 1: // 成果评估
                            case 2: // 情景对话
                            case 3: // 知识问答
                                await ProcessGeneralTaskStatistics(stageTaskInfoOutput, task, taskRecords, dialogueContentRecord, studentIds);
                                break;
                        }

                        stage.Tasks.Add(stageTaskInfoOutput);
                    }
                }

                return projectStages;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 处理思维导图任务统计
        /// 统计：参与率、平均等第、等第分布、高频问题
        /// </summary>
        private async Task ProcessMindMapTaskStatistics(GetProjectStageTaskInfoOutput stageTaskInfoOutput,
            RC_ReadingProjectStageTask task,
            IEnumerable<dynamic> taskRecords,
            List<string> studentIds)
        {
            // 获取该任务的提交记录
            var taskSubmissions = taskRecords.Where(p => p.ReadingProjectStageTaskId == task.Id).ToList();

            // 参与率：提交过任务的学生数 / 总学生数
            int participationCount = taskSubmissions.Select(p => p.StudentId).Distinct().Count();
            stageTaskInfoOutput.Participation = BusinessUtil.GetAccuracy(participationCount, studentIds.Count, 1);

            // 平均对话次数（思维导图任务设为0，因为不涉及对话）
            stageTaskInfoOutput.AvgDialogue = 0;

            // 上传率（与参与率相同）
            stageTaskInfoOutput.Submit = stageTaskInfoOutput.Participation;

            // 等第分布（基于首次提交记录）
            var firstSubmissions = taskSubmissions.Where(p => p.Order == 1).ToList();
            if (firstSubmissions.Any())
            {
                // 平均分
                stageTaskInfoOutput.AvgScore = firstSubmissions.Average(p => (decimal)p.Score);

                // 等第分布
                stageTaskInfoOutput.TaskLevels = firstSubmissions
                    .GroupBy(p => p.AIGrade)
                    .Where(g => !string.IsNullOrEmpty(g.Key))
                    .Select(g => new GetProjectStageTaskLevelOutput()
                    {
                        LevelName = g.Key,
                        LevelCount = g.Count()
                    })
                    .OrderByDescending(p => p.LevelName)
                    .ToList();
            }
            else
            {
                stageTaskInfoOutput.AvgScore = 0;
                stageTaskInfoOutput.TaskLevels = new List<GetProjectStageTaskLevelOutput>();
            }

            // 高频问题统计（从MatchedQuestions字段中提取问题ID，然后查询问题名称）
            var matchedQuestionIds = new List<string>();
            foreach (var submission in firstSubmissions)
            {
                if (!string.IsNullOrEmpty(submission.MatchedQuestions))
                {
                    try
                    {
                        var questionIds = JsonConvert.DeserializeObject<List<string>>(submission.MatchedQuestions);
                        if (questionIds != null)
                        {
                            matchedQuestionIds.AddRange(questionIds);
                        }
                    }
                    catch
                    {
                        // 忽略JSON解析错误
                    }
                }
            }

            // 查询高频问题名称
            var distinctQuestionIds = matchedQuestionIds.Distinct().ToList();
            if (distinctQuestionIds.Any())
            {
                var questionNames = await DBSqlSugar.Queryable<RC_ReadingProjectStageTaskQuestion>()
                    .Where(q => distinctQuestionIds.Contains(q.Id) && q.IsDeleted == false)
                    .Select(q => q.Name)
                    .ToListAsync();
                stageTaskInfoOutput.Themes = questionNames;
            }
            else
            {
                stageTaskInfoOutput.Themes = new List<string>();
            }

            // 查询趋势（基于提交时间）
            stageTaskInfoOutput.QueryCount = taskSubmissions
                .GroupBy(p => ((DateTime)p.CreateTime).ToString("yyyy/MM/dd"))
                .Select(g => new GetProjectStageTaskQueryCountOutput()
                {
                    Title = g.Key,
                    Count = g.Count()
                })
                .OrderBy(p => p.Title)
                .ToList();
        }

        /// <summary>
        /// 处理选词填空任务统计
        /// 统计：参与率、平均提交次数、等第分布、高频问题
        /// </summary>
        private async Task ProcessWordFillTaskStatistics(GetProjectStageTaskInfoOutput stageTaskInfoOutput,
            RC_ReadingProjectStageTask task,
            IEnumerable<dynamic> taskRecords,
            IEnumerable<dynamic> wordFillRecords,
            List<string> studentIds)
        {
            // 获取该任务的提交记录
            var taskSubmissions = taskRecords.Where(p => p.ReadingProjectStageTaskId == task.Id).ToList();

            // 获取该任务的选词填空操作记录
            var wordFillOperations = wordFillRecords.Where(p => p.ReadingProjectStageTaskId == task.Id).ToList();

            // 参与率：有操作记录的学生数 / 总学生数
            int participationCount = wordFillOperations.Select(p => p.StudentId).Distinct().Count();
            stageTaskInfoOutput.Participation = BusinessUtil.GetAccuracy(participationCount, studentIds.Count, 1);

            // 平均提交次数：总操作次数 / 学生总数
            stageTaskInfoOutput.AvgDialogue = Convert.ToDecimal(wordFillOperations.Count()) / studentIds.Count;

            // 上传率：提交过任务的学生数 / 总学生数
            int submitCount = taskSubmissions.Select(p => p.StudentId).Distinct().Count();
            stageTaskInfoOutput.Submit = BusinessUtil.GetAccuracy(submitCount, studentIds.Count, 1);

            // 等第分布（基于首次提交记录）
            var firstSubmissions = taskSubmissions.Where(p => p.Order == 1).ToList();
            if (firstSubmissions.Any())
            {
                // 平均分
                stageTaskInfoOutput.AvgScore = firstSubmissions.Average(p => (decimal)p.Score);

                // 等第分布
                stageTaskInfoOutput.TaskLevels = firstSubmissions
                    .GroupBy(p => p.AIGrade)
                    .Where(g => !string.IsNullOrEmpty(g.Key))
                    .Select(g => new GetProjectStageTaskLevelOutput()
                    {
                        LevelName = g.Key,
                        LevelCount = g.Count()
                    })
                    .OrderByDescending(p => p.LevelName)
                    .ToList();
            }
            else
            {
                stageTaskInfoOutput.AvgScore = 0;
                stageTaskInfoOutput.TaskLevels = new List<GetProjectStageTaskLevelOutput>();
            }

            // 高频问题统计（从MatchedQuestions字段中提取问题ID，然后查询问题名称）
            var matchedQuestionIds = new List<string>();
            foreach (var submission in firstSubmissions)
            {
                if (!string.IsNullOrEmpty(submission.MatchedQuestions))
                {
                    try
                    {
                        var questionIds = JsonConvert.DeserializeObject<List<string>>(submission.MatchedQuestions);
                        if (questionIds != null)
                        {
                            matchedQuestionIds.AddRange(questionIds);
                        }
                    }
                    catch
                    {
                        // 忽略JSON解析错误
                    }
                }
            }

            // 查询高频问题名称
            var distinctQuestionIds = matchedQuestionIds.Distinct().ToList();
            if (distinctQuestionIds.Any())
            {
                var questionNames = await DBSqlSugar.Queryable<RC_ReadingProjectStageTaskQuestion>()
                    .Where(q => distinctQuestionIds.Contains(q.Id) && q.IsDeleted == false)
                    .Select(q => q.Name)
                    .ToListAsync();
                stageTaskInfoOutput.Themes = questionNames;
            }
            else
            {
                stageTaskInfoOutput.Themes = new List<string>();
            }

            // 查询趋势（基于操作时间）
            stageTaskInfoOutput.QueryCount = wordFillOperations
                .GroupBy(p => ((DateTime)p.CreateTime).ToString("yyyy/MM/dd"))
                .Select(g => new GetProjectStageTaskQueryCountOutput()
                {
                    Title = g.Key,
                    Count = g.Count()
                })
                .OrderBy(p => p.Title)
                .ToList();
        }

        /// <summary>
        /// 处理通用任务统计（成果评估、情景对话、知识问答）
        /// </summary>
        private async Task ProcessGeneralTaskStatistics(GetProjectStageTaskInfoOutput stageTaskInfoOutput,
            RC_ReadingProjectStageTask task,
            IEnumerable<dynamic> taskRecords,
            List<AI_DialogueContentRecord> dialogueContentRecord,
            List<string> studentIds)
        {
            // 获取该任务的提交记录
            var taskSubmissions = taskRecords.Where(p => p.ReadingProjectStageTaskId == task.Id).ToList();

            // 当前任务对话key
            List<string> taskDialogueKeys = dialogueContentRecord.Where(p => p.BusinessId == task.Id).Select(p => p.Key).ToList();

            // 参与率：参与对话的学生数 / 总学生数
            int participationCount = 0;
            foreach (var studentId in studentIds)
            {
                int count = taskDialogueKeys.Where(s => s.Contains(studentId)).Count();
                if (count > 0)
                {
                    participationCount += 1;
                }
            }
            stageTaskInfoOutput.Participation = BusinessUtil.GetAccuracy(participationCount, studentIds.Count, 1);

            // 平均对话次数
            stageTaskInfoOutput.AvgDialogue = Convert.ToDecimal(taskDialogueKeys.Count) / studentIds.Count;

            // 上传率
            int submitDistinct = taskSubmissions.Select(p => p.StudentId).Distinct().Count();
            stageTaskInfoOutput.Submit = BusinessUtil.GetAccuracy(submitDistinct, studentIds.Count, 1);

            // 等第分布（基于首次提交记录）
            var firstSubmissions = taskSubmissions.Where(p => p.Order == 1).ToList();
            if (firstSubmissions.Any())
            {
                // 平均分
                stageTaskInfoOutput.AvgScore = firstSubmissions.Average(p => (decimal)p.Score);

                // 等第分布
                stageTaskInfoOutput.TaskLevels = firstSubmissions
                    .GroupBy(p => p.AIGrade)
                    .Where(g => !string.IsNullOrEmpty(g.Key))
                    .Select(g => new GetProjectStageTaskLevelOutput()
                    {
                        LevelName = g.Key,
                        LevelCount = g.Count()
                    })
                    .OrderByDescending(p => p.LevelName)
                    .ToList();
            }
            else
            {
                stageTaskInfoOutput.AvgScore = 0;
                stageTaskInfoOutput.TaskLevels = new List<GetProjectStageTaskLevelOutput>();
            }

            // 高频问题统计（从MatchedQuestions字段中提取问题ID，然后查询问题名称）
            var matchedQuestionIds = new List<string>();
            foreach (var submission in firstSubmissions)
            {
                if (!string.IsNullOrEmpty(submission.MatchedQuestions))
                {
                    try
                    {
                        var questionIds = JsonConvert.DeserializeObject<List<string>>(submission.MatchedQuestions);
                        if (questionIds != null)
                        {
                            matchedQuestionIds.AddRange(questionIds);
                        }
                    }
                    catch
                    {
                        // 忽略JSON解析错误
                    }
                }
            }

            // 查询高频问题名称
            var distinctQuestionIds = matchedQuestionIds.Distinct().ToList();
            if (distinctQuestionIds.Any())
            {
                var questionNames = await DBSqlSugar.Queryable<RC_ReadingProjectStageTaskQuestion>()
                    .Where(q => distinctQuestionIds.Contains(q.Id) && q.IsDeleted == false)
                    .Select(q => q.Name)
                    .ToListAsync();
                stageTaskInfoOutput.Themes = questionNames;
            }
            else
            {
                stageTaskInfoOutput.Themes = new List<string>();
            }

            // 查询趋势
            stageTaskInfoOutput.QueryCount = dialogueContentRecord.Where(p => p.BusinessId == task.Id)
                .GroupBy(p => p.CreateTime.Value.ToString("yyyy/MM/dd"))
                .Select(g => new GetProjectStageTaskQueryCountOutput()
                {
                    Title = g.Key,
                    Count = g.Count()
                })
                .OrderBy(p => p.Title)
                .ToList();
        }

        /// <summary>
        /// 获取阅读理解项目学生统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetProjectStudentCountOutput> GetProjectStudentCount(GetProjectStudentCountInput input)
        {
            try
            {
                // 获取班级学生信息
                List<Exam_Student> studentInfos = await DBSqlSugar.Queryable<Exam_Student>()
                    .Where(p => p.ClassId == input.ClassId && p.Deleted == false)
                    .Select(p => new Exam_Student()
                    {
                        Id = p.Id,
                        StudentNo = p.StudentNo,
                        RealName = p.RealName
                    })
                    .OrderBy(p => p.StudentNo)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                // 获取班级学生提交数据
                string stuTaskRecordSql = @"SELECT
                                        	str.Id,
                                        	str.ReadingProjectStageTaskId,
                                        	str.StudentId,
                                        	str.IsStandard,
                                            str.CreateTime
                                        FROM
                                        	RC_StudentTaskRecord str WITH ( NOLOCK )
                                        	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON str.StudentId = stu.Id
                                        	AND stu.Deleted = 0
                                        WHERE
                                        	str.IsDeleted = 0
                                        	AND stu.ClassId = @classId
                                        	AND str.ReadingProjectId = @projectId";

                var stuTaskRecords = await DBSqlSugar.SqlQueryable<dynamic>(stuTaskRecordSql)
                    .AddParameters(new { classId = input.ClassId, projectId = input.ProjectId })
                    .ToListAsync();

                // 转换为强类型对象以便后续处理
                var taskRecords = stuTaskRecords.Select(x => new
                {
                    Id = (string)x.Id,
                    ReadingProjectStageTaskId = (string)x.ReadingProjectStageTaskId,
                    StudentId = (string)x.StudentId,
                    IsStandard = (bool)x.IsStandard,
                    CreateTime = (DateTime)x.CreateTime
                }).ToList();

                // 获取阅读理解项目阶段任务数量（排除对话类型任务和视频、文档任务）
                int projectStageTaskCount = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                    .LeftJoin<RC_ReadingProjectStage>((t, s) => t.ReadingProjectStageId == s.Id)
                    .Where((t, s) => s.ReadingProjectId == input.ProjectId && t.IsDeleted == false && s.IsDeleted == false
                                     && t.TaskType != 3 && t.TaskType != 4 && t.TaskType != 5) // 排除知识问答、视频、文档任务
                    .With(SqlWith.NoLock)
                    .CountAsync();

                // 处理学生信息统计
                List<GetProjectStudentInfoOutput> studentInfoOutputs = new List<GetProjectStudentInfoOutput>();
                foreach (var student in studentInfos)
                {
                    // 获取该学生的任务记录
                    var studentTaskRecords = taskRecords.Where(p => p.StudentId == student.Id).ToList();

                    // 计算进度：达标任务数 / 总任务数
                    int standardCount = studentTaskRecords.Where(p => p.IsStandard == true).Count();
                    decimal progressBar = projectStageTaskCount > 0
                        ? BusinessUtil.GetAccuracy(standardCount, projectStageTaskCount, 1)
                        : 0;

                    // 计算未达标次数
                    int noStandardCount = studentTaskRecords.Where(p => p.IsStandard == false).Count();

                    // 获取最后一次提交时间
                    var lastSubmitRecord = studentTaskRecords.OrderByDescending(p => p.CreateTime).FirstOrDefault();
                    string submitTime = lastSubmitRecord?.CreateTime.ToString("yyyy/MM/dd HH:mm:ss") ?? "";

                    GetProjectStudentInfoOutput studentInfoOutput = new GetProjectStudentInfoOutput()
                    {
                        StudentId = student.Id,
                        StudentName = student.RealName,
                        StudentNum = student.StudentNo,
                        ProgressBar = progressBar,
                        NoStandard = noStandardCount,
                        SubmitTime = submitTime
                    };

                    studentInfoOutputs.Add(studentInfoOutput);
                }

                return new GetProjectStudentCountOutput()
                {
                    ProjectId = input.ProjectId,
                    StudentInfos = studentInfoOutputs
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取学生做阅读理解任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentDoReadingTaskListOutput> GetStudentDoReadingTaskList(GetStudentDoReadingTaskListInput input)
        {
            try
            {
                // 获取学生信息
                var studentInfo = await DBSqlSugar.Queryable<Exam_Student>()
                    .Where(p => p.Id == input.StudentId && p.Deleted == false)
                    .With(SqlWith.NoLock)
                    .FirstAsync();

                if (studentInfo == null)
                {
                    throw new BusException("学生信息不存在!");
                }

                // 获取阅读理解项目基础信息
                var readingProjectInfo = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ReadingProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentDoReadingTaskListOutput()
                    {
                        StudentId = input.StudentId,
                        StudentName = studentInfo.UserName,
                        StudentLogo = studentInfo.Photo, // 修正字段名
                        AgentId = p.AgentId,
                        ReadingProjectId = p.Id,
                        ReadingProjectName = p.Name,
                        ReadingProjectLogo = p.TaskLogo, // 修正字段名
                        ReadingProjectIntroduce = p.Introduce // 修正字段名
                    })
                    .With(SqlWith.NoLock)
                    .FirstAsync();

                if (readingProjectInfo == null)
                {
                    throw new BusException("阅读理解项目Id异常!");
                }

                // 获取项目阶段信息
                var projectStages = await DBSqlSugar.Queryable<RC_ReadingProjectStage>()
                    .Where(s => s.ReadingProjectId == input.ReadingProjectId && s.IsDeleted == false)
                    .OrderBy(s => s.StageOrder)
                    .ToListAsync();

                // 获取所有阶段任务
                var projectStageTasks = await DBSqlSugar.Queryable<RC_ReadingProjectStageTask>()
                    .Where(t => projectStages.Select(s => s.Id).Contains(t.ReadingProjectStageId) && t.IsDeleted == false)
                    .OrderBy(t => t.TaskOrder)
                    .ToListAsync();

                // 获取学生任务记录
                var studentTaskRecords = await DBSqlSugar.Queryable<RC_StudentTaskRecord>()
                    .Where(r => r.StudentId == input.StudentId
                        && r.ReadingProjectId == input.ReadingProjectId
                        && r.IsDeleted == false)
                    .ToListAsync();

                // 获取学生视频观看记录
                var videoWatchRecords = await DBSqlSugar.Queryable<RC_StudentVideoWatchRecord>()
                    .Where(v => v.StudentId == input.StudentId
                        && projectStageTasks.Select(t => t.Id).Contains(v.ReadingProjectStageTaskId)
                        && v.IsDeleted == false)
                    .ToListAsync();

                // 获取学生文档阅读记录
                var documentReadRecords = await DBSqlSugar.Queryable<RC_StudentDocumentReadRecord>()
                    .Where(d => d.StudentId == input.StudentId
                        && projectStageTasks.Select(t => t.Id).Contains(d.ReadingProjectStageTaskId)
                        && d.IsDeleted == false)
                    .ToListAsync();

                // 构建阶段信息
                foreach (var stage in projectStages)
                {
                    var stageInfo = new GetStudentDoReadingTaskListStageInfoOutput
                    {
                        Id = stage.Id,
                        Name = stage.StageName,
                        Order = stage.StageOrder
                    };

                    // 获取当前阶段的任务
                    var stageTasks = projectStageTasks
                        .Where(t => t.ReadingProjectStageId == stage.Id)
                        .OrderBy(t => t.TaskOrder)
                        .ToList();

                    foreach (var task in stageTasks)
                    {
                        var taskInfo = new GetStudentDoReadingTaskListStageTaskInfoOutput
                        {
                            Id = task.Id,
                            ReadingProjectStageId = task.ReadingProjectStageId,
                            TaskType = task.TaskType,
                            Name = task.TaskName,
                            FillWordQuestionBody = task.QuestionContent,
                            CorrectAnswers = task.CorrectAnswers,
                            CustomBackgroundImage = task.CustomBackgroundImage,
                            DistractorWords = task.DistractorWords,
                            Target = task.Target,
                            Order = task.TaskOrder,
                            RoleName = task.RoleName,
                            ToneId = task.ToneId,
                            Prologue = task.Prologue
                        };

                        // 获取该任务的学生记录
                        var taskRecords = studentTaskRecords
                            .Where(r => r.ReadingProjectStageTaskId == task.Id)
                            .OrderBy(r => r.CreateTime)
                            .ToList();

                        if (taskRecords.Any())
                        {
                            var latestRecord = taskRecords.Last();
                            taskInfo.TaskStatus = latestRecord.TaskStatus;
                            taskInfo.ProjectStatus = latestRecord.ProjectStatus;
                            taskInfo.Score = latestRecord.Score;
                            taskInfo.IsStandard = latestRecord.IsStandard;
                            taskInfo.AIGrade = latestRecord.AIGrade;
                            taskInfo.MatchedQuestions = latestRecord.MatchedQuestions;
                            taskInfo.SubmitTime = latestRecord.CreateTime?.ToString("yyyy-MM-dd HH:mm:ss");

                            // 构建提交次数信息
                            for (int i = 0; i < taskRecords.Count; i++)
                            {
                                taskInfo.Numbers.Add(new GetStudentDoReadingTaskListNumberOutput
                                {
                                    Number = i + 1,
                                    IsBackups = i < taskRecords.Count - 1, // 最后一次不是备份
                                    TaskSubmitId = taskRecords[i].Id
                                });
                            }
                        }
                        else
                        {
                            taskInfo.TaskStatus = 1; // 未开始
                            taskInfo.ProjectStatus = 1; // 未开始
                        }

                        // 根据任务类型加载特定信息
                        await LoadTaskSpecificInfoForStudent(taskInfo, task, videoWatchRecords, documentReadRecords);

                        stageInfo.ReadingStageTaskInfos.Add(taskInfo);
                    }

                    readingProjectInfo.ReadingStageInfos.Add(stageInfo);
                }

                return readingProjectInfo;
            }
            catch (Exception ex)
            {
                throw new BusException($"获取学生做阅读理解任务列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 为学生任务加载特定类型的信息
        /// </summary>
        /// <param name="taskInfo">任务信息</param>
        /// <param name="task">任务实体</param>
        /// <param name="videoWatchRecords">视频观看记录</param>
        /// <param name="documentReadRecords">文档阅读记录</param>
        /// <returns></returns>
        private async Task LoadTaskSpecificInfoForStudent(
            GetStudentDoReadingTaskListStageTaskInfoOutput taskInfo,
            RC_ReadingProjectStageTask task,
            List<RC_StudentVideoWatchRecord> videoWatchRecords,
            List<RC_StudentDocumentReadRecord> documentReadRecords)
        {
            switch (task.TaskType)
            {
                case 4: // 视频任务
                    await LoadVideoTaskInfoForStudent(taskInfo, task, videoWatchRecords);
                    break;
                case 5: // 文档任务
                    await LoadDocumentTaskInfoForStudent(taskInfo, task, documentReadRecords);
                    break;
                default:
                    // 其他任务类型暂无特殊处理
                    break;
            }
        }

        /// <summary>
        /// 为学生加载视频任务信息
        /// </summary>
        /// <param name="taskInfo">任务信息</param>
        /// <param name="task">任务实体</param>
        /// <param name="videoWatchRecords">视频观看记录</param>
        /// <returns></returns>
        private async Task LoadVideoTaskInfoForStudent(
            GetStudentDoReadingTaskListStageTaskInfoOutput taskInfo,
            RC_ReadingProjectStageTask task,
            List<RC_StudentVideoWatchRecord> videoWatchRecords)
        {
            // 获取任务的视频资源
            var videoResources = await DBSqlSugar.Queryable<RC_VideoResource>()
                .Where(v => v.ReadingProjectStageTaskId == task.Id && v.IsDeleted == false)
                .OrderBy(v => v.VideoOrder) // 修正字段名
                .ToListAsync();

            // 获取该任务的观看记录
            var taskVideoRecords = videoWatchRecords
                .Where(v => v.ReadingProjectStageTaskId == task.Id)
                .ToList();

            // 统计观看信息
            taskInfo.TotalWatchDuration = taskVideoRecords.Sum(v => v.TotalWatchDuration) / 60; // 转换为分钟
            taskInfo.WatchedVideoCount = taskVideoRecords.Count(v => v.HasWatched);

            // 构建视频资源信息
            foreach (var video in videoResources)
            {
                var watchRecord = taskVideoRecords.FirstOrDefault(v => v.VideoId == video.Id);

                taskInfo.TaskVideos.Add(new GetStudentDoReadingStageTaskVideoInfoOutput
                {
                    Id = video.Id,
                    ReadingProjectStageTaskId = video.ReadingProjectStageTaskId,
                    Title = video.VideoTitle, // 修正字段名
                    Description = video.VideoDescription, // 修正字段名
                    Url = video.VideoUrl, // 修正字段名
                    Duration = video.Duration ?? 0, // 处理可空类型
                    IsWatched = watchRecord?.HasWatched ?? false,
                    WatchDuration = watchRecord?.TotalWatchDuration ?? 0,
                    Order = video.VideoOrder // 修正字段名
                });
            }
        }

        /// <summary>
        /// 为学生加载文档任务信息
        /// </summary>
        /// <param name="taskInfo">任务信息</param>
        /// <param name="task">任务实体</param>
        /// <param name="documentReadRecords">文档阅读记录</param>
        /// <returns></returns>
        private async Task LoadDocumentTaskInfoForStudent(
            GetStudentDoReadingTaskListStageTaskInfoOutput taskInfo,
            RC_ReadingProjectStageTask task,
            List<RC_StudentDocumentReadRecord> documentReadRecords)
        {
            // 获取任务的文档资源
            var documentResources = await DBSqlSugar.Queryable<RC_DocumentResource>()
                .Where(d => d.ReadingProjectStageTaskId == task.Id && d.IsDeleted == false)
                .OrderBy(d => d.DocumentOrder) // 修正字段名
                .ToListAsync();

            // 获取该任务的阅读记录
            var taskDocumentRecords = documentReadRecords
                .Where(d => d.ReadingProjectStageTaskId == task.Id)
                .ToList();

            // 统计阅读信息
            taskInfo.ReadDocumentCount = taskDocumentRecords.Count(d => d.HasRead);

            // 构建文档资源信息
            foreach (var document in documentResources)
            {
                var readRecord = taskDocumentRecords.FirstOrDefault(d => d.DocumentId == document.Id);

                taskInfo.TaskDocuments.Add(new GetStudentDoReadingStageTaskDocumentInfoOutput
                {
                    Id = document.Id,
                    ReadingProjectStageTaskId = document.ReadingProjectStageTaskId,
                    Title = document.DocumentTitle, // 修正字段名
                    Description = document.DocumentDescription, // 修正字段名
                    Url = document.DocumentUrl, // 修正字段名
                    Size = 0, // RC_DocumentResource 没有Size字段，设为默认值
                    IsRead = readRecord?.HasRead ?? false,
                    ReadTime = readRecord?.ReadTime,
                    Order = document.DocumentOrder // 修正字段名
                });
            }
        }

        /// <summary>
        /// 获取学生提交内容详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentSubmitContentOutput> GetStudentSubmitContent(GetStudentSubmitContentInput input)
        {
            try
            {
                // 查询学生提交记录及相关信息
                string sql = @"SELECT
                                r.SubmitContent
                            FROM RC_StudentTaskRecord r WITH(NOLOCK)
                            WHERE r.Id = @taskSubmitId";

                var result = await DBSqlSugar.Ado.GetStringAsync(sql, new { taskSubmitId = input.TaskSubmitId });

                if (result == null)
                {
                    throw new BusException("提交记录不存在或已被删除！");
                }

                return new GetStudentSubmitContentOutput
                {
                    SubmitContent = result
                };
            }
            catch (Exception ex)
            {
                throw new BusException($"获取学生提交内容失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 教师端阅读理解下载学生成果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<byte[]> ReadingDownloadStudentAchievement(ReadingDownloadStudentAchievementInput input)
        {
            //临时文件夹路径
            var tempDir = Path.Combine(Path.GetTempPath(), $"ReadingDownloadStudentAchievement_{Guid.NewGuid().ToString()}");
            //创建临时目录
            Directory.CreateDirectory(tempDir);
            //本地文件地址
            List<string> localUrl = new List<string>();
            HttpClient httpClient = null;
            try
            {
                // 获取当前阅读理解成果
                string readingAchievementDtosSql = @"SELECT
                                                    	task.TaskName,
                                                    	dcr.Ask,
                                                    	dcr.[Key] 
                                                    FROM
                                                    	RC_ReadingProjectStage stage WITH ( NOLOCK )
                                                    	INNER JOIN RC_ReadingProjectStageTask task WITH ( NOLOCK ) ON stage.Id= task.ReadingProjectStageId 
                                                    	AND task.TaskType= 1 
                                                    	AND task.IsDeleted= 0
                                                    	INNER JOIN AI_DialogueContentRecord dcr WITH ( NOLOCK ) ON task.Id= dcr.BusinessId 
                                                    	AND dcr.IsDeleted= 0 
                                                    WHERE
                                                    	stage.ReadingProjectId= @readingId
                                                    	AND stage.IsDeleted=0";
                List<ReadingDownloadStudentAchievementDto> readingAchievementDtos = await DBSqlSugar.SqlQueryable<ReadingDownloadStudentAchievementDto>(readingAchievementDtosSql)
                    .AddParameters(new { readingId = input.ReadingId })
                    .ToListAsync();
                if (readingAchievementDtos.Count <= 0)
                {
                    throw new BusException("暂无学生成果!", 801);
                }

                // 获取班级学生信息
                var classStudents = await DBSqlSugar
                    .Queryable<Exam_Student>()
                    .Where(p => p.ClassId == input.ClassId && !p.Deleted)
                    .Select(p => new { p.Id, p.RealName })
                    .ToListAsync();

                //获取当前班级学生的成果
                var studentAchievements = from student in classStudents
                                          from achievement in readingAchievementDtos
                                          where achievement.Key.Contains(student.Id.ToString())
                                          select new ReadingDownloadStudentAchievementDto
                                          {
                                              TaskName = achievement.TaskName,
                                              RealName = student.RealName,
                                              Ask = achievement.Ask,
                                          };
                var studentAchievementDtos = studentAchievements.ToList();

                //收集文件信息
                List<ReadingDownloadStudentAchievementFileInfoDto> fileInfos = new List<ReadingDownloadStudentAchievementFileInfoDto>();
                foreach (var studentAchievementDto in studentAchievementDtos)
                {
                    studentAchievementDto.AskInfo = JsonConvert.DeserializeObject<AIDialogueASKDto>(studentAchievementDto.Ask);
                    if (studentAchievementDto.AskInfo != null && studentAchievementDto.AskInfo.Files.Count > 0)
                    {
                        foreach (var item in studentAchievementDto.AskInfo.Files)
                        {
                            if (string.IsNullOrEmpty(item.FileUrl))
                            {
                                continue;
                            }

                            // 验证URL格式
                            if (!Uri.TryCreate(item.FileUrl, UriKind.Absolute, out var uriResult) || (uriResult.Scheme != Uri.UriSchemeHttp && uriResult.Scheme != Uri.UriSchemeHttps))
                            {
                                continue;
                            }

                            // 处理文件名
                            string fileName = string.Empty;
                            if (!string.IsNullOrEmpty(studentAchievementDto.RealName))
                            {
                                fileName += studentAchievementDto.RealName + "_";
                            }
                            if (!string.IsNullOrEmpty(studentAchievementDto.TaskName))
                            {
                                fileName += studentAchievementDto.TaskName + "_";
                            }
                            if (!string.IsNullOrEmpty(item.FileName))
                            {
                                fileName += Path.GetFileNameWithoutExtension(item.FileName) + "_";
                            }
                            fileName += Guid.NewGuid().ToString();

                            // 提取并验证文件后缀
                            string extension = Path.GetExtension(item.FileUrl);
                            if (string.IsNullOrWhiteSpace(extension))
                            {
                                continue;
                            }

                            // 过滤非法字符
                            string pattern = @"[^a-zA-Z0-9\u4e00-\u9fa5_]";
                            fileName = Regex.Replace(fileName, pattern, "");

                            // 限制文件名长度
                            if (fileName.Length > 100)
                            {
                                fileName = fileName.Substring(0, 100);
                            }

                            string newFileName = fileName + extension;
                            fileInfos.Add(new ReadingDownloadStudentAchievementFileInfoDto
                            {
                                FileName = newFileName,
                                FileUrl = item.FileUrl
                            });
                        }
                    }
                }
                if (fileInfos.Count <= 0)
                {
                    throw new BusException("暂无学生成果文件!", 801);
                }

                //下载文件到临时文件夹
                httpClient = _httpClientFactory.CreateClient();
                httpClient.Timeout = TimeSpan.FromMinutes(120); // 设置超时时间
                foreach (var fileInfo in fileInfos)
                {
                    var filePath = Path.Combine(tempDir, fileInfo.FileName);

                    // 下载文件
                    using (var response = await httpClient.GetAsync(fileInfo.FileUrl, HttpCompletionOption.ResponseHeadersRead))
                    {
                        response.EnsureSuccessStatusCode();

                        using (var stream = await response.Content.ReadAsStreamAsync())
                        using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                        {
                            await stream.CopyToAsync(fileStream);
                        }
                    }
                    localUrl.Add(filePath);
                }

                //生成压缩包
                using (MemoryStream ms = new MemoryStream())
                {
                    using (ZipArchive zipArchive = new ZipArchive(ms, ZipArchiveMode.Create, true))
                    {
                        foreach (var filePath in localUrl)
                        {
                            if (File.Exists(filePath))
                            {
                                zipArchive.CreateEntryFromFile(filePath, Path.GetFileName(filePath));
                            }
                        }
                    }

                    byte[] zipBytes = ms.ToArray();

                    return zipBytes;
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
            finally
            {
                // 清理临时文件
                if (Directory.Exists(tempDir))
                {
                    try
                    {
                        Directory.Delete(tempDir, recursive: true);
                    }
                    catch { /* 忽略清理失败的情况 */ }
                }
                httpClient?.Dispose();
            }
        }

        /// <summary>
        /// 教师端阅读理解下载学生评估结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<byte[]> ReadingDownloadStudentResult(ReadingDownloadStudentResultInput input)
        {
            try
            {
                // 获取学生评估结果
                string studentResultDtoSql = @"SELECT
                                                	str.ReadingProjectStageTaskId,
                                                	str.StudentId,
                                                	stu.RealName,
                                                	str.AssessmentResult,
                                                	str.CreateTime,
                                                	task.TaskName 
                                                FROM
                                                	RC_StudentTaskRecord str WITH ( NOLOCK )
                                                	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON str.StudentId= stu.Id 
                                                	AND stu.Deleted= 0 
                                                	AND stu.ClassId= @classId
                                                	INNER JOIN RC_ReadingProjectStageTask task WITH ( NOLOCK ) ON str.ReadingProjectStageTaskId= task.Id 
                                                	AND task.TaskType NOT IN ( 3, 4, 5 ) 
                                                	AND task.IsDeleted= 0 
                                                WHERE
                                                	str.ReadingProjectId= @readingId 
                                                	AND str.IsDeleted=0";
                List<ReadingDownloadStudentResultDto> studentResultDtos = await DBSqlSugar.SqlQueryable<ReadingDownloadStudentResultDto>(studentResultDtoSql)
                    .AddParameters(new { classId = input.ClassId, readingId = input.ReadingId })
                    .ToListAsync();
                if (studentResultDtos.Count <= 0)
                {
                    throw new BusException("暂无评估结果!", 801);
                }

                // 去重，保留每组第一条数据
                List<ReadingDownloadStudentResultDto> distinctResultDtos = studentResultDtos
                    .Select(p => new ReadingDownloadStudentResultDto()
                    {
                        TaskName = p.TaskName,
                        ReadingProjectStageTaskId = p.ReadingProjectStageTaskId,
                        StudentId = p.StudentId,
                        RealName = p.RealName
                    })
                    .GroupBy(dto => new { dto.ReadingProjectStageTaskId, dto.StudentId, dto.RealName, dto.TaskName }) // 按字段组合分组
                    .Select(group => group.First()) // 取每组第一条数据
                    .ToList();

                //word文本处理
                List<MarkdownTextToWordZipInfo> wordTexts = new List<MarkdownTextToWordZipInfo>();
                foreach (var result in distinctResultDtos)
                {
                    //获取学生评估结果
                    List<ReadingDownloadStudentResultDto> studentResults = studentResultDtos
                        .Where(p => p.StudentId == result.StudentId && p.ReadingProjectStageTaskId == result.ReadingProjectStageTaskId)
                        .OrderBy(p => p.CreateTime)
                        .ToList();
                    if (studentResults.Count > 0)
                    {
                        //文件名称处理
                        string fileName = $"{result.RealName}_{Guid.NewGuid().ToString()}";
                        // 过滤非法字符
                        string pattern = @"[^a-zA-Z0-9\u4e00-\u9fa5_]";
                        fileName = Regex.Replace(fileName, pattern, "");
                        // 限制文件名长度
                        if (fileName.Length > 100)
                        {
                            fileName = fileName.Substring(0, 100);
                        }

                        //评估结果文本处理
                        string resultText = string.Empty;
                        foreach (var studentResult in studentResults)
                        {
                            resultText += $"第{studentResults.IndexOf(studentResult) + 1}次作答\n";
                            resultText += $"评估结果:\n{studentResult.AssessmentResult}\n";
                        }

                        wordTexts.Add(new MarkdownTextToWordZipInfo()
                        {
                            Title = result.TaskName,
                            FileName = fileName,
                            Text = resultText
                        });
                    }
                }
                if (wordTexts.Count <= 0)
                {
                    throw new BusException("暂无评估结果!", 801);
                }

                //地址
                string url = AppSetting.OfficeTool.OfficeToolUrl + AppSetting.OfficeTool.MarkdownTextToWordZipUrl;

                //参数
                MarkdownTextToWordZipInput wordZipInput = new MarkdownTextToWordZipInput()
                {
                    WordInfos = wordTexts
                };
                string jsonData = JsonConvert.SerializeObject(wordZipInput, new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore, // 忽略空值
                    Formatting = Formatting.None // 不格式化JSON，减少传输量
                });

                //http
                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(300); // 超时设置
                    var content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (HttpResponseMessage response = await httpClient.PostAsync(url, content))
                    {
                        // 确保响应成功
                        response.EnsureSuccessStatusCode();

                        // 读取压缩包内容
                        byte[] zipBytes = await response.Content.ReadAsByteArrayAsync();
                        if (zipBytes == null || zipBytes.Length == 0)
                        {
                            throw new BusException("接口返回空的压缩包数据");
                        }
                        return zipBytes;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }
    }
}
