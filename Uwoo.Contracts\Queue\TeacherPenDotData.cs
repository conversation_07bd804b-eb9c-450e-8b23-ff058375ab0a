﻿// -- Function：TeacherPenDotData.cs
// --- Project：X.PenServer.Contracts
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/31 15:57

// ReSharper disable once CheckNamespace
namespace X.PenServer.Contracts.Queue
{
	using System.Text.Json.Serialization;

	/// <summary>
	/// 教师笔迹
	/// </summary>
	public class TeacherPenDotData : PenDotData
	{
		/// <summary>
		/// 教师用户id
		/// </summary>
		[JsonPropertyName(nameof(TeacherId))]
		[JsonInclude]
		public string TeacherId { get; set; }
	}
}