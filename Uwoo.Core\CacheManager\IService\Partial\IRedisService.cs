﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.Lifecycle;

namespace Uwoo.Core.CacheManager.IService
{
    /// <summary>
    /// Redis同步接口
    /// </summary>
    public partial interface IRedisService : ISingletonService
    {
        /// <summary>
        /// 添加缓存数据
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        /// <param name="span">过期时间</param>
        /// <returns></returns>
        void Set(string key, object value, TimeSpan? span = null);

        /// <summary>
        /// 获取缓存数据
        /// </summary>
        /// <param name="key">键</param>
        /// <typeparam name="T">类型</typeparam>
        /// <returns></returns>
        T Get<T>(string key);

        long IncrBy(string key, long value = 1);

        long IncrByNoPrefix(string key, long value = 1);

        long? GetIncrBy(string key);

        /// <summary>
        /// 获取缓存数据
        /// </summary>
        /// <param name="key">键</param>
        /// <returns></returns>
        string Get(string key);

        /// <summary>
        /// 移除数据
        /// </summary>
        /// <param name="key">键</param>
        /// <returns></returns>
        void Delete(string key);

        /// <summary>
        /// 过期指定键
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="span">过期时间</param>
        /// <returns></returns>
        void Expire(string key, TimeSpan span);

        /// <summary>
        /// 是否存在指定键
        /// </summary>
        /// <param name="key">键</param>
        /// <returns></returns>
        bool IsExists(string key);

        /// <summary>
        /// 添加哈希数据
        /// </summary>
        /// <param name="key"></param>
        /// <param name="field"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        void HSet(string key, string field, object value);

        void HSet(string filed, object value);

        void HDel(string filed);

        bool HExists(string key, string field);

        /// <summary>
        /// 添加哈希数据
        /// </summary>
        /// <param name="key"></param>
        /// <param name="field"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        void HSet(string key, string field, string value);


        /// <summary>
        /// 添加哈希数据
        /// </summary>
        /// <param name="key"></param>
        /// <param name="field"></param>
        /// <param name="value"></param>
        /// <param name="ts"></param>
        /// <returns></returns>
        void HSet(string key, string field, string value, TimeSpan ts);

        /// <summary>
        /// 获取哈希数据
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="field">字段</param>
        /// <typeparam name="T">值</typeparam>
        /// <returns></returns>
        T HGet<T>(string key, string field);

        /// <summary>
        /// 获取哈希数据
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="field">字段</param>
        /// <returns></returns>
        string HGet(string key, string field);

        /// <summary>
        /// 删除哈希数据
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="filed">字段</param>
        /// <returns></returns>
        void HDelete(string key, string filed);

        /// <summary>
        /// 获取当前哈希列表
        /// </summary>
        /// <param name="key">键</param>
        /// <typeparam name="T">实体</typeparam>
        /// <returns></returns>
        Dictionary<string, T> HGetAll<T>(string key);

        /// <summary>
        /// 获取当前哈希列表
        /// </summary>
        /// <param name="key">键</param>
        /// <returns></returns>
        Dictionary<string, string> HGetAll(string key);

        /// <summary>
        /// 添加数据集合
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        /// <returns></returns>
        void SAdd(string key, params string[] value);

        void SAdd(string key, string[] value, TimeSpan? ts);

        /// <summary>
        /// 添加数据集合
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        /// <typeparam name="T">实体</typeparam>
        /// <returns></returns>
        void SAdd<T>(string key, params T[] value);

        /// <summary>
        /// 获取集合列表
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        List<string> SMembers(string key);

        /// <summary>
        /// 获取集合列表
        /// </summary>
        /// <param name="key">值</param>
        /// <typeparam name="T">实体</typeparam>
        /// <returns></returns>
        List<T> SMembers<T>(string key);

        /// <summary>
        /// 删除集合列表
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        /// <returns></returns>
        void SRemove(string key, params string[] value);

        /// <summary>
        /// 删除集合列表
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        /// <typeparam name="T">实体</typeparam>
        /// <returns></returns>
        void SRemove<T>(string key, T[] value);

        /// <summary>
        /// 判断集合是否包含当前成员
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="member">值</param>
        /// <returns></returns>
        bool IsSMember(string key, object member);

        /// <summary>
        /// 查找所有分区节点中符合给定模式的Key
        /// </summary>
        /// <param name="pattern">模糊匹配模式</param>
        /// <returns></returns>
        string[] Keys(string pattern);
    }
}
