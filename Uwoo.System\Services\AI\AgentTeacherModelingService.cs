﻿using Coldairarrow.Util;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Server.IISIntegration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NLog.Targets;
using Quartz.Util;
using SkiaSharp;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Model.CustomException;
using Uwoo.Mongo.Models;
using Uwoo.System.IServices;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.Exam;
using UwooAgent.Entity.DomainModels.Office;
using UwooAgent.Entity.DomainModels.School;
using UwooAgent.Entity.DomainModels.Student;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.Model.SemesterTime;
using UwooAgent.Model.Word;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_教师端建模
    /// </summary>
    public class AgentTeacherModelingService : ServiceBase<AI_AgentTask, IAgentTeacherModelingRepository>, IAgentTeacherModelingService, IDependency
    {
        #region DI

        private IBase_SemesterTimeService _semesterTimeService;
        private readonly IHttpClientFactory _httpClientFactory;
        public AgentTeacherModelingService(
            IBase_SemesterTimeService semesterTimeService,
            IAgentCommonService agentCommonService,
            IHttpClientFactory httpClientFactory,
            ILogger<AgentTeacherModelingService> logger)
        {
            _semesterTimeService = semesterTimeService;
            _httpClientFactory = httpClientFactory;
        }

        #endregion

        /// <summary>
        /// 保存/编辑建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<string> SaveModelingInfo(SaveModelingInfoInput input)
        {
            try
            {
                if (string.IsNullOrEmpty(input.Id))
                {
                    //获取当前学年学期
                    NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                    if (nowSemesterTime == null)
                    {
                        throw new BusException("无法获取当前学年!");
                    }

                    //智能体任务（建模）
                    AI_AgentTask agentTask = new AI_AgentTask()
                    {
                        Id = IdHelper.GetId(),
                        AgentId = input.AgentId,
                        Name = input.Name,
                        Introduce = input.Introduce,
                        Term = nowSemesterTime.NowTerm,
                        Year = nowSemesterTime.Year,
                        TaskLogo = input.TaskLogo,
                        CreateTime = DateTime.Now,
                        Creator = input.TeacherId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId,
                        IsDeleted = false,
                        SubjectId = input.SubjectId,
                        AgentTaskType = 4,
                        GradeId = input.GradeId
                    };
                    await DBSqlSugar.Insertable(agentTask).ExecuteCommandAsync();

                    //建模阶段
                    List<AI_ModelingStage> addModelingStages = new List<AI_ModelingStage>();
                    //建模阶段任务
                    List<AI_ModelingStageTask> addModelingStageTasks = new List<AI_ModelingStageTask>();
                    //建模阶段任务高频问题
                    List<AI_ModelingStageTaskQuestion> addModelingStageTaskQuestions = new List<AI_ModelingStageTaskQuestion>();
                    //建模阶段任务视频
                    List<AI_ModelingStageTaskVideo> addModelingStageTaskVideos = new List<AI_ModelingStageTaskVideo>();
                    //建模阶段任务文档
                    List<AI_ModelingStageTaskDocument> addModelingStageTaskDocuments = new List<AI_ModelingStageTaskDocument>();

                    foreach (var stage in input.ModelingStageInfos)
                    {
                        //阶段
                        AI_ModelingStage modelingStage = new AI_ModelingStage()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = agentTask.Id,
                            Name = stage.Name,
                            Describe = stage.Describe,
                            IsDefault = stage.IsDefault,
                            DefaultStageType = stage.DefaultStageType,
                            Order = input.ModelingStageInfos.IndexOf(stage) + 1,
                            CreateTime = DateTime.Now,
                            Creator = input.TeacherId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.TeacherId,
                            IsDeleted = false
                        };
                        addModelingStages.Add(modelingStage);

                        foreach (var task in stage.ModelingStageTaskInfos)
                        {
                            //任务
                            AI_ModelingStageTask modelingStageTask = new AI_ModelingStageTask()
                            {
                                Id = IdHelper.GetId(),
                                ModelingId = agentTask.Id,
                                ModelingStageId = modelingStage.Id,
                                Name = task.Name,
                                Demand = task.Demand,
                                Target = task.Target,
                                Scope = task.Scope,
                                Prologue = task.Prologue,
                                ToneId = task.ToneId,
                                RoleName = task.RoleName,
                                RoleSetting = task.RoleSetting,
                                GuideRoleSetting = task.GuideRoleSetting,
                                ScoreStandard = task.ScoreStandard,
                                TaskType = task.TaskType,
                                GroupAssessmentScore = task.GroupAssessmentScore,
                                GroupIsAssessment = task.GroupIsAssessment,
                                GroupIsReadAllDocuments = task.GroupIsReadAllDocuments,
                                GroupIsSubmit = task.GroupIsSubmit,
                                GroupIsWatchVideo = task.GroupIsWatchVideo,
                                GroupIsVideoWatchDuration = task.GroupIsVideoWatchDuration,
                                GroupVideoWatchDuration = task.GroupVideoWatchDuration,
                                TaskAssessmentScore = task.TaskAssessmentScore,
                                TaskIsAssessment = task.TaskIsAssessment,
                                TaskIsReadAllDocuments = task.TaskIsReadAllDocuments,
                                TaskIsSubmit = task.TaskIsSubmit,
                                TaskIsWatchVideo = task.TaskIsWatchVideo,
                                TaskIsVideoWatchDuration = task.TaskIsVideoWatchDuration,
                                TaskVideoWatchDuration = task.TaskVideoWatchDuration,
                                IsDefault = task.IsDefault,
                                Order = stage.ModelingStageTaskInfos.IndexOf(task) + 1,
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            };
                            addModelingStageTasks.Add(modelingStageTask);

                            //任务问题
                            foreach (var question in task.ModelingStageTaskQuestionInfos)
                            {
                                addModelingStageTaskQuestions.Add(new AI_ModelingStageTaskQuestion()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingStageTaskId = modelingStageTask.Id,
                                    Name = question.Name,
                                    Describe = question.Describe,
                                    Order = task.ModelingStageTaskQuestionInfos.IndexOf(question) + 1,
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false
                                });
                            }

                            //视频
                            foreach (var video in task.ModelingStageTaskVideoInfos)
                            {
                                addModelingStageTaskVideos.Add(new AI_ModelingStageTaskVideo()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingId = agentTask.Id,
                                    ModelingStageId = modelingStage.Id,
                                    ModelingStageTaskId = modelingStageTask.Id,
                                    Name = video.Name,
                                    Url = video.Url,
                                    Duration = video.Duration,
                                    Size = video.Size,
                                    Order = task.ModelingStageTaskVideoInfos.IndexOf(video) + 1,
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false
                                });
                            }

                            //文档
                            foreach (var document in task.ModelingStageTaskDocumentInfos)
                            {
                                addModelingStageTaskDocuments.Add(new AI_ModelingStageTaskDocument()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingId = agentTask.Id,
                                    ModelingStageId = modelingStage.Id,
                                    ModelingStageTaskId = modelingStageTask.Id,
                                    Name = document.Name,
                                    Url = document.Url,
                                    Size = document.Size,
                                    Order = task.ModelingStageTaskDocumentInfos.IndexOf(document) + 1,
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false
                                });
                            }
                        }
                    }

                    if (addModelingStages.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addModelingStages).ExecuteCommandAsync();
                    }
                    if (addModelingStageTasks.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addModelingStageTasks).ExecuteCommandAsync();
                    }
                    if (addModelingStageTaskQuestions.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addModelingStageTaskQuestions).ExecuteCommandAsync();
                    }
                    if (addModelingStageTaskVideos.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addModelingStageTaskVideos).ExecuteCommandAsync();
                    }
                    if (addModelingStageTaskDocuments.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addModelingStageTaskDocuments).ExecuteCommandAsync();
                    }

                    return agentTask.Id;
                }
                else
                {
                    //智能体建模任务
                    AI_AgentTask agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.Id && p.Creator == input.TeacherId && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (agentTask == null)
                    {
                        throw new BusException("建模Id异常!");
                    }

                    //智能体建模任务更新
                    agentTask.Name = input.Name;
                    agentTask.Introduce = input.Introduce;
                    agentTask.TaskLogo = input.TaskLogo;
                    agentTask.ModifyTime = DateTime.Now;
                    agentTask.Modifier = input.TeacherId;

                    await DBSqlSugar.Updateable(agentTask).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                    //建模阶段
                    List<AI_ModelingStage> modelingStages = await DBSqlSugar.Queryable<AI_ModelingStage>().Where(p => p.ModelingId == agentTask.Id && p.Creator == input.TeacherId && p.IsDeleted == false).With(SqlWith.NoLock).ToListAsync();
                    //建模阶段任务
                    List<AI_ModelingStageTask> modelingStageTasks = await DBSqlSugar.Queryable<AI_ModelingStageTask>().Where(p => p.ModelingId == agentTask.Id && p.Creator == input.TeacherId && p.IsDeleted == false).With(SqlWith.NoLock).ToListAsync();
                    //建模阶段任务问题
                    List<AI_ModelingStageTaskQuestion> modelingStageTaskQuestions = new List<AI_ModelingStageTaskQuestion>();
                    if (modelingStageTasks.Count > 0)
                    {
                        List<string> taskIds = modelingStageTasks.Select(p => p.Id).ToList();
                        modelingStageTaskQuestions = await DBSqlSugar.Queryable<AI_ModelingStageTaskQuestion>().Where(p => taskIds.Contains(p.ModelingStageTaskId) && p.IsDeleted == false).With(SqlWith.NoLock).ToListAsync();
                    }
                    //获取阶段任务文档
                    List<AI_ModelingStageTaskDocument> modelingStageTaskDocuments = await DBSqlSugar.Queryable<AI_ModelingStageTaskDocument>().Where(p => p.ModelingId == agentTask.Id && p.Creator == input.TeacherId && p.IsDeleted == false).With(SqlWith.NoLock).ToListAsync();
                    //获取阶段任务视频
                    List<AI_ModelingStageTaskVideo> modelingStageTaskVideos = await DBSqlSugar.Queryable<AI_ModelingStageTaskVideo>().Where(p => p.ModelingId == agentTask.Id && p.Creator == input.TeacherId && p.IsDeleted == false).With(SqlWith.NoLock).ToListAsync();

                    //建模阶段
                    List<AI_ModelingStage> addModelingStages = new List<AI_ModelingStage>();
                    List<AI_ModelingStage> updateModelingStages = new List<AI_ModelingStage>();
                    //建模阶段任务
                    List<AI_ModelingStageTask> addModelingStageTasks = new List<AI_ModelingStageTask>();
                    //建模阶段任务高频问题
                    List<AI_ModelingStageTaskQuestion> addModelingStageTaskQuestions = new List<AI_ModelingStageTaskQuestion>();
                    //建模阶段任务视频
                    List<AI_ModelingStageTaskVideo> addModelingStageTaskVideos = new List<AI_ModelingStageTaskVideo>();
                    //建模阶段任务文档
                    List<AI_ModelingStageTaskDocument> addModelingStageTaskDocuments = new List<AI_ModelingStageTaskDocument>();

                    //获取存在提交记录的阶段任务Id
                    List<string> submitTaskIds = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>()
                        .Where(p => p.ModelingId == agentTask.Id && p.IsDeleted == false)
                        .With(SqlWith.NoLock).Select(p => p.ModelingStageTaskId).Distinct().ToListAsync();

                    //删除历史任务信息的Id
                    List<string> delStageTaskIds = new List<string>();

                    foreach (var stage in input.ModelingStageInfos)
                    {
                        if (string.IsNullOrEmpty(stage.Id))
                        {
                            //阶段
                            AI_ModelingStage modelingStage = new AI_ModelingStage()
                            {
                                Id = IdHelper.GetId(),
                                ModelingId = agentTask.Id,
                                Name = stage.Name,
                                Describe = stage.Describe,
                                IsDefault = stage.IsDefault,
                                DefaultStageType = stage.DefaultStageType,
                                Order = input.ModelingStageInfos.IndexOf(stage) + 1,
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            };
                            addModelingStages.Add(modelingStage);

                            foreach (var task in stage.ModelingStageTaskInfos)
                            {
                                //任务
                                AI_ModelingStageTask modelingStageTask = new AI_ModelingStageTask()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingId = agentTask.Id,
                                    ModelingStageId = modelingStage.Id,
                                    Name = task.Name,
                                    Demand = task.Demand,
                                    Target = task.Target,
                                    Scope = task.Scope,
                                    Prologue = task.Prologue,
                                    ToneId = task.ToneId,
                                    RoleName = task.RoleName,
                                    RoleSetting = task.RoleSetting,
                                    GuideRoleSetting = task.GuideRoleSetting,
                                    ScoreStandard = task.ScoreStandard,
                                    TaskType = task.TaskType,
                                    GroupAssessmentScore = task.GroupAssessmentScore,
                                    GroupIsAssessment = task.GroupIsAssessment,
                                    GroupIsReadAllDocuments = task.GroupIsReadAllDocuments,
                                    GroupIsSubmit = task.GroupIsSubmit,
                                    GroupIsWatchVideo = task.GroupIsWatchVideo,
                                    GroupIsVideoWatchDuration = task.GroupIsVideoWatchDuration,
                                    GroupVideoWatchDuration = task.GroupVideoWatchDuration,
                                    TaskAssessmentScore = task.TaskAssessmentScore,
                                    TaskIsAssessment = task.TaskIsAssessment,
                                    TaskIsReadAllDocuments = task.TaskIsReadAllDocuments,
                                    TaskIsSubmit = task.TaskIsSubmit,
                                    TaskIsWatchVideo = task.TaskIsWatchVideo,
                                    TaskIsVideoWatchDuration = task.TaskIsVideoWatchDuration,
                                    TaskVideoWatchDuration = task.TaskVideoWatchDuration,
                                    IsDefault = task.IsDefault,
                                    Order = stage.ModelingStageTaskInfos.IndexOf(task) + 1,
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false
                                };
                                addModelingStageTasks.Add(modelingStageTask);

                                //任务问题
                                foreach (var question in task.ModelingStageTaskQuestionInfos)
                                {
                                    addModelingStageTaskQuestions.Add(new AI_ModelingStageTaskQuestion()
                                    {
                                        Id = IdHelper.GetId(),
                                        ModelingStageTaskId = modelingStageTask.Id,
                                        Name = question.Name,
                                        Describe = question.Describe,
                                        Order = task.ModelingStageTaskQuestionInfos.IndexOf(question) + 1,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false
                                    });
                                }

                                //视频
                                foreach (var video in task.ModelingStageTaskVideoInfos)
                                {
                                    addModelingStageTaskVideos.Add(new AI_ModelingStageTaskVideo()
                                    {
                                        Id = IdHelper.GetId(),
                                        ModelingId = agentTask.Id,
                                        ModelingStageId = modelingStage.Id,
                                        ModelingStageTaskId = modelingStageTask.Id,
                                        Name = video.Name,
                                        Url = video.Url,
                                        Duration = video.Duration,
                                        Size = video.Size,
                                        Order = task.ModelingStageTaskVideoInfos.IndexOf(video) + 1,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false
                                    });
                                }

                                //文档
                                foreach (var document in task.ModelingStageTaskDocumentInfos)
                                {
                                    addModelingStageTaskDocuments.Add(new AI_ModelingStageTaskDocument()
                                    {
                                        Id = IdHelper.GetId(),
                                        ModelingId = agentTask.Id,
                                        ModelingStageId = modelingStage.Id,
                                        ModelingStageTaskId = modelingStageTask.Id,
                                        Name = document.Name,
                                        Url = document.Url,
                                        Size = document.Size,
                                        Order = task.ModelingStageTaskDocumentInfos.IndexOf(document) + 1,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false,
                                    });
                                }
                            }
                        }
                        else
                        {
                            //获取建模阶段
                            AI_ModelingStage modelingStage = modelingStages.Where(p => p.Id == stage.Id).FirstOrDefault();
                            if (modelingStage != null)
                            {
                                modelingStage.Describe = stage.Describe;
                                modelingStage.Name = stage.Name;
                                modelingStage.Order = input.ModelingStageInfos.IndexOf(stage) + 1;
                                updateModelingStages.Add(modelingStage);

                                foreach (var task in stage.ModelingStageTaskInfos)
                                {
                                    if (!string.IsNullOrEmpty(task.Id))
                                    {
                                        //获取任务信息
                                        AI_ModelingStageTask stageTask = modelingStageTasks.Where(p => p.Id == task.Id).FirstOrDefault();
                                        if (stageTask != null)
                                        {
                                            //验证是否存在答题记录
                                            bool isDo = submitTaskIds.Contains(task.Id);
                                            if (isDo)
                                            {
                                                continue;
                                            }

                                            //验证是否修改
                                            bool isUpdate = false;
                                            //任务基础信息验证
                                            if (stageTask.TaskType != task.TaskType ||
                                                stageTask.Name != task.Name ||
                                                stageTask.Target != task.Target ||
                                                stageTask.ScoreStandard != task.ScoreStandard ||
                                                stageTask.Demand != task.Demand ||
                                                stageTask.Scope != task.Scope ||
                                                stageTask.RoleSetting != task.RoleSetting ||
                                                stageTask.GuideRoleSetting != task.GuideRoleSetting ||
                                                stageTask.Prologue != task.Prologue ||
                                                stageTask.ToneId != task.ToneId ||
                                                stageTask.RoleName != task.RoleName ||
                                                stageTask.TaskIsSubmit != task.TaskIsSubmit ||
                                                stageTask.TaskIsAssessment != task.TaskIsAssessment ||
                                                stageTask.TaskAssessmentScore != task.TaskAssessmentScore ||
                                                stageTask.TaskIsWatchVideo != task.TaskIsWatchVideo ||
                                                stageTask.TaskIsVideoWatchDuration != task.TaskIsVideoWatchDuration ||
                                                stageTask.TaskVideoWatchDuration != task.TaskVideoWatchDuration ||
                                                stageTask.TaskIsReadAllDocuments != task.TaskIsReadAllDocuments ||
                                                stageTask.GroupIsSubmit != task.GroupIsSubmit ||
                                                stageTask.GroupIsAssessment != task.GroupIsAssessment ||
                                                stageTask.GroupAssessmentScore != task.GroupAssessmentScore ||
                                                stageTask.GroupIsWatchVideo != task.GroupIsWatchVideo ||
                                                stageTask.GroupIsVideoWatchDuration != task.GroupIsVideoWatchDuration ||
                                                stageTask.GroupVideoWatchDuration != task.GroupVideoWatchDuration ||
                                                stageTask.GroupIsReadAllDocuments != task.GroupIsReadAllDocuments ||
                                                stageTask.Order != stage.ModelingStageTaskInfos.IndexOf(task) + 1)
                                            {
                                                isUpdate = true;
                                            }

                                            //任务问题验证
                                            if (!isUpdate)
                                            {
                                                List<AI_ModelingStageTaskQuestion> stageTaskQuestions = modelingStageTaskQuestions.Where(p => p.ModelingStageTaskId == stageTask.Id).ToList();
                                                if (stageTaskQuestions.Count != task.ModelingStageTaskQuestionInfos.Count)
                                                {
                                                    isUpdate = true;
                                                }
                                                else
                                                {
                                                    foreach (var taskQuestion in task.ModelingStageTaskQuestionInfos)
                                                    {
                                                        if (string.IsNullOrEmpty(taskQuestion.Id))
                                                        {
                                                            isUpdate = true;
                                                            break;
                                                        }
                                                        else
                                                        {
                                                            //获取问题信息
                                                            AI_ModelingStageTaskQuestion stageTaskQuestion = stageTaskQuestions.Where(p => p.Id == taskQuestion.Id).FirstOrDefault();
                                                            if (stageTaskQuestion != null)
                                                            {
                                                                if (stageTaskQuestion.Name != taskQuestion.Name
                                                                    || stageTaskQuestion.Describe != taskQuestion.Describe
                                                                    || stageTaskQuestion.Order != task.ModelingStageTaskQuestionInfos.IndexOf(taskQuestion) + 1)
                                                                {
                                                                    isUpdate = true;
                                                                    break;
                                                                }
                                                            }
                                                            else
                                                            {
                                                                isUpdate = true;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            //任务文档验证
                                            if (!isUpdate)
                                            {
                                                List<AI_ModelingStageTaskDocument> stageTaskDocuments = modelingStageTaskDocuments.Where(p => p.ModelingStageTaskId == stageTask.Id).ToList();
                                                if (stageTaskDocuments.Count != task.ModelingStageTaskDocumentInfos.Count)
                                                {
                                                    isUpdate = true;
                                                }
                                                else
                                                {
                                                    foreach (var taskDocument in task.ModelingStageTaskDocumentInfos)
                                                    {
                                                        if (string.IsNullOrEmpty(taskDocument.Id))
                                                        {
                                                            isUpdate = true;
                                                            break;
                                                        }
                                                        else
                                                        {
                                                            //获取文档信息
                                                            AI_ModelingStageTaskDocument stageTaskDocument = stageTaskDocuments.Where(p => p.Id == taskDocument.Id).FirstOrDefault();
                                                            if (stageTaskDocument != null)
                                                            {
                                                                if (stageTaskDocument.Name != taskDocument.Name
                                                                    || stageTaskDocument.Url != taskDocument.Url
                                                                    || stageTaskDocument.Size != taskDocument.Size
                                                                    || stageTaskDocument.Order != task.ModelingStageTaskDocumentInfos.IndexOf(taskDocument) + 1)
                                                                {
                                                                    isUpdate = true;
                                                                    break;
                                                                }
                                                            }
                                                            else
                                                            {
                                                                isUpdate = true;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            //任务视频验证
                                            if (!isUpdate)
                                            {
                                                List<AI_ModelingStageTaskVideo> stageTaskVideos = modelingStageTaskVideos.Where(p => p.ModelingStageTaskId == stageTask.Id).ToList();
                                                if (stageTaskVideos.Count != task.ModelingStageTaskVideoInfos.Count)
                                                {
                                                    isUpdate = true;
                                                }
                                                else
                                                {
                                                    foreach (var taskVideo in task.ModelingStageTaskVideoInfos)
                                                    {
                                                        if (string.IsNullOrEmpty(taskVideo.Id))
                                                        {
                                                            isUpdate = true;
                                                            break;
                                                        }
                                                        else
                                                        {
                                                            //获取视频信息
                                                            AI_ModelingStageTaskVideo stageTaskVideo = stageTaskVideos.Where(p => p.Id == taskVideo.Id).FirstOrDefault();
                                                            if (stageTaskVideo != null)
                                                            {
                                                                if (stageTaskVideo.Name != taskVideo.Name
                                                                    || stageTaskVideo.Url != taskVideo.Url
                                                                    || stageTaskVideo.Size != taskVideo.Size
                                                                    || stageTaskVideo.Duration != taskVideo.Duration
                                                                    || stageTaskVideo.Order != task.ModelingStageTaskVideoInfos.IndexOf(taskVideo) + 1)
                                                                {
                                                                    isUpdate = true;
                                                                    break;
                                                                }
                                                            }
                                                            else
                                                            {
                                                                isUpdate = true;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            if (!isUpdate)
                                            {
                                                continue;
                                            }

                                            delStageTaskIds.Add(task.Id);
                                        }
                                    }

                                    //任务
                                    AI_ModelingStageTask modelingStageTask = new AI_ModelingStageTask()
                                    {
                                        Id = IdHelper.GetId(),
                                        ModelingId = agentTask.Id,
                                        ModelingStageId = modelingStage.Id,
                                        Name = task.Name,
                                        Demand = task.Demand,
                                        Target = task.Target,
                                        Scope = task.Scope,
                                        Prologue = task.Prologue,
                                        ToneId = task.ToneId,
                                        RoleName = task.RoleName,
                                        RoleSetting = task.RoleSetting,
                                        GuideRoleSetting = task.GuideRoleSetting,
                                        ScoreStandard = task.ScoreStandard,
                                        TaskType = task.TaskType,
                                        GroupAssessmentScore = task.GroupAssessmentScore,
                                        GroupIsAssessment = task.GroupIsAssessment,
                                        GroupIsReadAllDocuments = task.GroupIsReadAllDocuments,
                                        GroupIsSubmit = task.GroupIsSubmit,
                                        GroupIsWatchVideo = task.GroupIsWatchVideo,
                                        GroupIsVideoWatchDuration = task.GroupIsVideoWatchDuration,
                                        GroupVideoWatchDuration = task.GroupVideoWatchDuration,
                                        TaskAssessmentScore = task.TaskAssessmentScore,
                                        TaskIsAssessment = task.TaskIsAssessment,
                                        TaskIsReadAllDocuments = task.TaskIsReadAllDocuments,
                                        TaskIsSubmit = task.TaskIsSubmit,
                                        TaskIsWatchVideo = task.TaskIsWatchVideo,
                                        TaskIsVideoWatchDuration = task.TaskIsVideoWatchDuration,
                                        TaskVideoWatchDuration = task.TaskVideoWatchDuration,
                                        IsDefault = task.IsDefault,
                                        Order = stage.ModelingStageTaskInfos.IndexOf(task) + 1,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false
                                    };
                                    addModelingStageTasks.Add(modelingStageTask);

                                    //任务问题
                                    foreach (var question in task.ModelingStageTaskQuestionInfos)
                                    {
                                        addModelingStageTaskQuestions.Add(new AI_ModelingStageTaskQuestion()
                                        {
                                            Id = IdHelper.GetId(),
                                            ModelingStageTaskId = modelingStageTask.Id,
                                            Name = question.Name,
                                            Describe = question.Describe,
                                            Order = task.ModelingStageTaskQuestionInfos.IndexOf(question) + 1,
                                            CreateTime = DateTime.Now,
                                            Creator = input.TeacherId,
                                            ModifyTime = DateTime.Now,
                                            Modifier = input.TeacherId,
                                            IsDeleted = false
                                        });
                                    }

                                    //视频
                                    foreach (var video in task.ModelingStageTaskVideoInfos)
                                    {
                                        addModelingStageTaskVideos.Add(new AI_ModelingStageTaskVideo()
                                        {
                                            Id = IdHelper.GetId(),
                                            ModelingId = agentTask.Id,
                                            ModelingStageId = modelingStage.Id,
                                            ModelingStageTaskId = modelingStageTask.Id,
                                            Name = video.Name,
                                            Url = video.Url,
                                            Duration = video.Duration,
                                            Size = video.Size,
                                            Order = task.ModelingStageTaskVideoInfos.IndexOf(video) + 1,
                                            CreateTime = DateTime.Now,
                                            Creator = input.TeacherId,
                                            ModifyTime = DateTime.Now,
                                            Modifier = input.TeacherId,
                                            IsDeleted = false
                                        });
                                    }

                                    //文档
                                    foreach (var document in task.ModelingStageTaskDocumentInfos)
                                    {
                                        addModelingStageTaskDocuments.Add(new AI_ModelingStageTaskDocument()
                                        {
                                            Id = IdHelper.GetId(),
                                            ModelingId = agentTask.Id,
                                            ModelingStageId = modelingStage.Id,
                                            ModelingStageTaskId = modelingStageTask.Id,
                                            Name = document.Name,
                                            Url = document.Url,
                                            Size = document.Size,
                                            Order = task.ModelingStageTaskDocumentInfos.IndexOf(document) + 1,
                                            CreateTime = DateTime.Now,
                                            Creator = input.TeacherId,
                                            ModifyTime = DateTime.Now,
                                            Modifier = input.TeacherId,
                                            IsDeleted = false,
                                        });
                                    }
                                }
                            }
                        }
                    }

                    //获取所有阶段任务Id和阶段Id
                    List<string> stageTaskIds = new List<string>();
                    List<string> stageIds = new List<string>();
                    foreach (var stage in input.ModelingStageInfos)
                    {
                        if (!string.IsNullOrEmpty(stage.Id))
                        {
                            stageIds.Add(stage.Id);
                        }
                        foreach (var stageTaskInfo in stage.ModelingStageTaskInfos)
                        {
                            if (!string.IsNullOrEmpty(stageTaskInfo.Id))
                            {
                                stageTaskIds.Add(stageTaskInfo.Id);
                            }
                        }
                    }

                    //删除的任务Id
                    foreach (var stageTask in modelingStageTasks)
                    {
                        if (!stageTaskIds.Contains(stageTask.Id))
                        {
                            delStageTaskIds.Add(stageTask.Id);
                        }
                    }
                    if (delStageTaskIds.Count > 0)
                    {
                        //删除任务
                        await DBSqlSugar.Deleteable<AI_ModelingStageTask>().Where(p => delStageTaskIds.Contains(p.Id)).ExecuteCommandAsync();
                        //删除提交记录
                        await DBSqlSugar.Deleteable<AI_StudentDoModelingTask>().Where(p => delStageTaskIds.Contains(p.ModelingStageTaskId)).ExecuteCommandAsync();
                        //清空高频问题
                        await DBSqlSugar.Deleteable<AI_ModelingStageTaskQuestion>().Where(p => delStageTaskIds.Contains(p.ModelingStageTaskId)).ExecuteCommandAsync();
                        //清空学生做建模阶段任务问题
                        await DBSqlSugar.Deleteable<AI_StudentDoModelingTaskQuestion>().Where(p => delStageTaskIds.Contains(p.ModelingStageTaskId)).ExecuteCommandAsync();
                        //删除任务视频
                        await DBSqlSugar.Deleteable<AI_ModelingStageTaskVideo>().Where(p => delStageTaskIds.Contains(p.ModelingStageTaskId)).ExecuteCommandAsync();
                        //删除任务文档信息
                        await DBSqlSugar.Deleteable<AI_ModelingStageTaskDocument>().Where(p => delStageTaskIds.Contains(p.ModelingStageTaskId)).ExecuteCommandAsync();
                        //删除任务视频观看记录
                        await DBSqlSugar.Deleteable<AI_StudentWatchModelingVideo>().Where(p => delStageTaskIds.Contains(p.ModelingStageTaskId)).ExecuteCommandAsync();
                        //删除任务文档信息阅读记录
                        await DBSqlSugar.Deleteable<AI_StudentReadModelingDocument>().Where(p => delStageTaskIds.Contains(p.ModelingStageTaskId)).ExecuteCommandAsync();
                        //清空缓冲池和问答记录
                        await DBSqlSugar.Deleteable<AI_ModelingContextCacheKey>().Where(p => delStageTaskIds.Contains(p.ModelingStageTaskId)).ExecuteCommandAsync();
                        await DBSqlSugar.Deleteable<AI_ModelingDialogueRecord>().Where(p => delStageTaskIds.Contains(p.ModelingStageTaskId)).ExecuteCommandAsync();
                    }

                    //删除的阶段Id
                    List<string> delStageIds = new List<string>();
                    foreach (var stage in modelingStages)
                    {
                        if (!stageIds.Contains(stage.Id))
                        {
                            delStageIds.Add(stage.Id);
                        }
                    }
                    if (delStageIds.Count > 0)
                    {
                        //删除阶段
                        await DBSqlSugar.Deleteable<AI_ModelingStage>().Where(p => delStageIds.Contains(p.Id)).ExecuteCommandAsync();
                    }

                    //更新/新增任务相关信息
                    if (updateModelingStages.Count > 0)
                    {
                        await DBSqlSugar.Updateable(updateModelingStages).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }
                    if (addModelingStages.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addModelingStages).ExecuteCommandAsync();
                    }
                    if (addModelingStageTasks.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addModelingStageTasks).ExecuteCommandAsync();
                    }
                    if (addModelingStageTaskQuestions.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addModelingStageTaskQuestions).ExecuteCommandAsync();
                    }
                    if (addModelingStageTaskVideos.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addModelingStageTaskVideos).ExecuteCommandAsync();
                    }
                    if (addModelingStageTaskDocuments.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addModelingStageTaskDocuments).ExecuteCommandAsync();
                    }

                    return agentTask.Id;
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取建模阶段任务默认信息
        /// </summary>
        /// <returns></returns>
        public List<GetModelingStageDefaultInfoOutput> GetModelingStageTaskDefaultInfo()
        {
            List<GetModelingStageDefaultInfoOutput> getModelingStageDefaults = new List<GetModelingStageDefaultInfoOutput>();

            //问题理解阶段
            getModelingStageDefaults.Add(new GetModelingStageDefaultInfoOutput()
            {
                Name = "问题理解阶段",
                Describe = "---",
                DefaultStageType = 1,
                IsDefault = true,
                ModelingStageTaskInfos = new List<GetModelingStageTaskDefaultOutput>()
                {
                    new GetModelingStageTaskDefaultOutput()
                    {
                        Name="建模探索第一步：精准理解问题本质",
                        Target="在问题理解阶段，你需要深入剖析实际问题，从中提取关键信息，明确问题涉及的核心要素，如相关主体、关键变量以及它们之间的相互关系等。这是整个建模过程的基础。",
                        RoleSetting="请基于以下多维度量化指标，对学生在建模问题理解阶段的对话流程进行评价（满分 100 分），输出 “得分情况 + 综合评价 + 改进建议” 三部分内容。\r\n评价输出要求\r\n1. 得分情况：按上述 4个维度分别打分，标注各维度得分及总分（如 “问题拆解完整性：15/20 分，要素关系分析：18/20 分…… 总分：85/100 分”）。\r\n2. 综合评价：用 100-150 字总结学生在问题理解阶段的表现，突出优势（如 “要素提取全面”）和短板（如 “模型匹配偏差”），语言客观中立。\r\n3. 改进建议：针对短板提出具体可操作的建议（如 “下次分析要素关系时，可尝试用具体数据举例说明，增强准确性”），避免空泛表述。",
                        Demand="核心目标\r\n引导学生自主完成问题拆解（明确要素与关系）和知识点关联（匹配模型工具），不直接给出答案，通过提问激发思考。\r\n第一部分：问题拆解引导（分步骤提问）\r\n锚定核心目标\r\n“请用一句话概括这个问题的最终目标（比如‘求最优解’‘解释现象’‘预测结果’）？你觉得解决它的关键是要弄清楚什么？”\r\n提取关键要素\r\n“问题中提到了哪些具体的‘量’或‘对象’（比如时间、数量、成本、比例等，包括已知和未知的）？试着列出来，不用急着判断是否重要。”\r\n筛选核心要素\r\n“在你列出的要素中，哪些是必须考虑的（缺了它问题就无法成立）？哪些可以暂时忽略（对结果影响很小）？举例说说为什么这样划分。”\r\n分析要素关系\r\n“这些核心要素之间有什么联系？比如‘A 增加时 B 会减少’‘C 由 A 和 B 共同决定’？用简单的词语（如正相关、因果、并列）或箭头（A→B）描述一下。”\r\n明确约束条件\r\n“问题中有没有限制条件（比如‘时间≤3 天’‘数量为整数’）？这些条件会让哪些要素的取值范围受到影响？”\r\n第二部分：知识点关联引导（分步骤提问）\r\n关联学科领域\r\n“你列出的要素和关系，让你想到了高中哪个学科的知识（比如数学的函数、物理的平衡模型）？为什么会这么联想？”\r\n匹配具体模型\r\n“如果用学过的模型来描述这种关系，你觉得哪个合适？比如：\r\n若要素是‘一个量随另一个量变化’，可以用一次函数 / 二次函数吗？\r\n若涉及‘多个量相互制约’，可以用方程组 / 不等式吗？\r\n若涉及‘变化趋势预测’，可以用数列 / 统计图表吗？”\r\n验证模型合理性\r\n“假设用这个模型，它能满足你之前找到的约束条件吗？比如‘函数的定义域是否符合要素的实际取值范围’？如果不符合，可能需要怎么调整？”\r\n简化与假设\r\n“实际问题太复杂时，建模需要简化（比如‘假设某个要素不变’）。结合你选的模型，你觉得可以做哪些合理假设？这些假设会让模型更简单还是更准确？”\r\n互动衔接规则（根据学生回答追问）\r\n若学生漏答要素：“再仔细看看问题，有没有提到和‘[举例一个相关要素]’类似的量？比如之前提到‘成本’，会不会还有‘单价’或‘数量’？”\r\n若学生对关系描述模糊：“你说 A 和 B 有关联，能举个具体例子吗？比如‘当 A=2 时，B 大概是多少’，试着用数据说明。”\r\n若学生关联模型错误：“你提到用二次函数，但这个问题中的要素关系是线性的（比如‘每增加 1 个，总量加 5’），再想想有没有更合适的？”\r\n若学生卡壳：“我们可以先从最简单的情况入手，比如假设‘[某个次要要素] 不变’，这时问题会变成什么样？”",
                        ScoreStandard="一、量化评价指标（总分 100 分）\r\n1. 问题拆解完整性（20 分）\r\n- 核心目标明确（5 分）：能准确概括问题最终目标（如 “求最优解”“预测趋势”），得 5 分；表述模糊但方向正确，得 3 分；未明确目标，得 0 分。\r\n- 关键要素提取全面（8 分）：提取核心要素（如时间、成本、数量等）完整且无遗漏，得 8 分；遗漏 1-2 个核心要素，得 5 分；遗漏 3 个及以上核心要素或提取大量无关要素，得 0-2 分。\r\n- 约束条件识别准确（7 分）：准确列出所有约束条件（如 “时间≤3 天”），得 7 分；遗漏 1 个关键约束，得 4 分；未识别约束条件，得 0 分。\r\n2. 要素关系分析准确性（30 分）\r\n- 关系描述清晰（15 分）：能用词语（如 “正相关”）或箭头（A→B）明确要素间关系，得 15 分；描述模糊但有逻辑，得 6 分；关系描述错误，得 0 分。\r\n- 主次关系区分合理（15 分）：能准确区分核心要素与次要要素，理由充分，得 15 分；区分基本合理但理由模糊，得 6 分；主次颠倒，得 0 分。\r\n4. 思维逻辑性（30 分）\r\n- 推理过程连贯（15 分）：从问题目标→要素提取→关系分析→模型关联的逻辑链条清晰，得 15-10分；逻辑基本连贯但有轻微跳跃，得 8-5 分；逻辑混乱，得 0 分。\r\n- 简化假设合理性（15 分）：提出的简化假设（如 “忽略次要要素”）既简化问题又不偏离核心，得 15-10 分；假设过于简化导致模型失真，得 8-5 分；未提出假设或假设不合理，得 0 分。\r\n5. 互动响应积极性（20 分）\r\n- 对引导的反馈及时（10 分）：能快速回应引导问题，主动补充信息，得 10 分；回应较慢但能配合引导，得 6 分；多次无响应或敷衍回答，得 0-3 分。\r\n- 修正调整意愿（10 分）：对引导中指出的偏差（如漏要素、模型错误）能主动修正，得 10 分；经提示后部分修正，得 6 分；拒绝调整或坚持错误，得 0 分。",
                        GuideRoleSetting="##角色：你是高中数学建模第一阶段（实际问题数学化）的引导助手，采用苏格拉底式提问法，通过追问引导学生自主思考，不直接给出答案。\r\n##核心目标：帮助学生将实际问题转化为数学问题，明确变量、关系、约束条件，搭建 “实际问题→数学符号 / 公式” 的桥梁。\r\n##语言风格：口语化，避免学术术语（如用 “变量” 而非 “自变量 / 因变量”），必要时用生活化类比（如 “把‘租金’看作 x，就像给它起个数学名字”）。\r\n##容错机制：若学生回答偏离方向，用 “你的想法很有趣，我们可以再看看…（拉回核心问题）” 引导，不否定学生思路。",
                        TaskIsSubmit=true,
                        GroupIsSubmit=true,
                        IsDefault=true,
                        TaskType=6,
                        ModelingStageTaskQuestionInfos=new List<GetModelingStageTaskQuestionDefaultOutput>()
                        {
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="题干核心概念" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="已知条件有哪些" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="问题的具体指向" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="隐含信息" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="问题的限制条件" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="题干与问题的逻辑关系" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="问题设问角度" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="题干中的矛盾点" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="范围边界" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="是否存在多层含义" }
                        }
                    },
                    new GetModelingStageTaskDefaultOutput()
                    {
                        Name="建模构建第二步：初步搭建模型框架",
                        Target="进入模型搭建环节，要结合问题理解阶段提炼的核心要素，选择合适的数学工具（像函数、方程、几何图形 ）或逻辑结构。明确模型的输入（比如问题里的已知条件 ）、输出（要解决的目标结果 ），把零散的问题要素，用清晰的数学或逻辑关系串起来，构建出能模拟问题的 “框架”，让抽象问题变得可分析、可计算。",
                        RoleSetting="请基于以下多维度量化指标，对学生初步建立的模型进行评价（满分 100 分），输出 “得分情况 + 综合评价 + 改进建议” 三部分内容。\r\n得分情况：分两部分标注得分（如 “对话过程能力：82/100 分，初步模型构建：75/100 分，总分：157/200 分”），并注明各子项得分。\r\n综合评价：150-200 字总结，分别说明对话过程（如 “能准确辨识问题意义，但因素关系描述可更严谨”）和初步模型构建（如 “数学语言表述清晰，模型选择合理，但适应性调整不足”）的优势与短板。\r\n改进建议：针对两部分短板分别提出建议",
                        ScoreStandard="1. 数学语言表述（30 分）\r\n  - 用数学符号 / 公式准确表述关键量（如 “设路程为 S，速度为 v”），得 30 分；\r\n  - 表述基本正确但存在冗余，得 18 分；\r\n  - 表述错误或未使用数学语言，得 0 分。\r\n2. 模型选择与解释（40 分）\r\n  - 选择合适模型（如 “用一次函数描述路程与时间关系”），并解释合理性，得 40 分；\r\n  - 模型基本适用但解释模糊，得 24 分；\r\n  - 模型不匹配或无解释，得 0-12 分。\r\n3. 模型适应性调整（30 分）\r\n  - 主动调整模型假设 / 变量以适配问题（如 “将‘匀速’改为‘分段变速’”），得 30 分；\r\n  - 被动接受调整建议但执行生硬，得 18 分；\r\n  - 拒绝调整或调整错误，得 0 分。",
                        TaskIsSubmit=true,
                        GroupIsSubmit=true,
                        IsDefault=true,
                        TaskType=7,
                        ModelingStageTaskQuestionInfos=new List<GetModelingStageTaskQuestionDefaultOutput>()
                        {
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="核心变量缺失" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="关键参数错误" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="边界条件遗漏" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="变量关系矛盾" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="因果链路断裂" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="层级混乱" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="与问题目标脱节" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="场景适配性不足" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="约束条件违反" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="步骤模糊" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="关键节点缺失" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="执行路径不明" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="其他" }
                        }
                    }
                }
            });

            //假设分析阶段
            getModelingStageDefaults.Add(new GetModelingStageDefaultInfoOutput()
            {
                Name = "假设分析阶段",
                Describe = "---",
                DefaultStageType = 2,
                IsDefault = true,
                ModelingStageTaskInfos = new List<GetModelingStageTaskDefaultOutput>()
                {
                    new GetModelingStageTaskDefaultOutput()
                    {
                        Name="建模构建第三步：明确简化与限定",
                        Target="面对实际问题，要像给解题划定 “规则圈”。结合问题核心，大胆假设能简化又不影响本质的条件明确变量范围、主体关系（如认定某因素短期不变 ）。这些假设是模型的 “地基”，既让复杂问题能动手分析，又得保证贴合真实逻辑，别偏离问题本质。",
                        RoleSetting="一、评估角色设定\r\n角色定位：高中数学建模假设阶段的专业评估者，需基于预设的量化评价体系（含假设合理性、创新性、模型优化效果等维度），结合学生的对话过程与模型表现，进行客观、全面的综合评价。\r\n核心职责：\r\n1. 严格对照量化评价体系打分，确保得分与学生实际表现匹配；\r\n2. 超越单纯的分数评判，从更深层维度解读学生的建模思维与能力；\r\n3. 以发展性视角给出可落地的改进建议，助力学生提升假设与模型优化能力。\r\n评价原则：\r\n- 量化与质性结合：既依据得分判断表现，又通过文字描述分析能力短板；\r\n- 过程与结果并重：不仅关注最终假设与模型质量，也重视学生提出假设、优化模型时的思考逻辑；\r\n- 鼓励与引导兼顾：肯定学生的积极尝试，对不足的地方以 “问题 + 改进方向” 的方式温和引导。\r\n二、综合评价补充维度（除量化评价体系外）\r\n1. 思维连贯性：评估学生从 “问题理解→提出假设→优化模型” 的逻辑链条是否顺畅，是否存在思路跳跃或矛盾（如假设与前期要素拆解脱节、模型优化未呼应核心约束）。\r\n2. 反思调整能力：观察学生是否能根据引导（如 “这个假设忽略了 XX 约束”）主动修正假设，或在模型优化中发现问题并迭代（如 “之前的假设导致模型误差过大，是否需要调整”）。\r\n3. 学科知识迁移：判断学生提出的假设是否关联高中所学知识（如 “用‘线性增长假设’对应数学中的一次函数模型”），而非凭空臆断。\r\n4. 问题适配性：分析假设是否贴合问题场景（如 “研究校园食堂排队问题时，假设‘学生就餐时间均匀分布’是否符合实际，而非套用通用模型”）。\r\n三、评价报告结构\r\n1. 得分概览\r\n  - 量化维度得分：列出 “假设合理性、创新性、模型优化效果” 等维度的具体得分及总分（如 “假设合理性：22/25 分，模型优化效果：9/15 分，总分：78/100 分”）；\r\n  - 关键优势项：标注得分较高的维度（如 “对假设影响的认知清晰，得分 18/20 分”）。\r\n2. 综合能力分析\r\n  - 基于补充维度的质性评价：结合 “思维连贯性、反思调整能力” 等，描述学生表现（如 “能将假设与数学函数知识关联，但模型优化时未呼应前期的‘人数为整数’约束，逻辑存在小断层”）；\r\n  - 典型案例引用：摘录学生对话中的具体表述作为依据（如 “学生提到‘假设销量不受价格影响’，未考虑市场规律，反映假设合理性需加强”）。\r\n3. 改进建议\r\n  - 针对性建议：对应量化与质性评价中的短板（如 “针对模型优化效果不足，建议先明确‘优化目标是降低误差还是简化计算’，再调整假设”）；\r\n  - 可操作方法：提供具体路径（如 “下次提出假设后，试着用‘如果忽略这个假设，模型会多出哪些变量’的方式自查”）。\r\n4. 总结\r\n  - 用 1-2 句话概括学生在本阶段的整体表现（如 “具备基本的假设提出能力，需加强假设与问题场景的适配性及模型优化的逻辑性”）。",
                        Demand="1. 基于模型引导假设：“我们现在已经有了一个初步的模型框架，你可以从模型中的变量、参数、约束条件等方面入手，思考有哪些因素可以进行假设。比如，模型中的某个变量变化范围很大，我们可以假设它在某个特定范围内变化；或者某个参数很难确定，我们可以先假设一个固定的值。”\r\n2. 启发多角度假设：“除了从模型本身出发，你还可以考虑问题的背景、实际情况等因素来提出假设。比如，在一个关于交通流量的模型中，你可以假设道路上的车辆行驶速度是恒定的，或者假设某个路口的交通信号灯时间是固定的。”\r\n3. 引导分析假设影响：“你刚才提出了一个假设，那你有没有想过，如果忽略这个假设，我们的模型会发生什么样的变化呢？比如，结果的准确性会受到影响吗？模型的复杂程度会增加多少呢？”\r\n4. 评估假设合理性：“你提出的这个假设看起来很有想法哦。不过，我们需要进一步评估它的合理性。你觉得这个假设在现实情况中是否成立呢？有没有相关的数据或事实可以支持这个假设？”\r\n5. 分析假设对模型的影响：“这个假设会对我们的模型产生多方面的影响。从模型的结构来看，它可能会改变模型的方程形式；从计算过程来看，它可能会影响计算的复杂度和效率；从结果来看，它可能会使结果的准确性和可靠性发生变化。”\r\n6. 给予改进建议：“如果这个假设不太合理，我们可以考虑对它进行调整。比如，缩小假设的范围，或者修改假设的条件。你觉得可以从哪些方面入手来改进这个假设呢？”\r\n7. 鼓励创新假设：“你的思路很开阔，还可以尝试提出一些更具创新性的假设哦。也许这些假设会给我们的模型带来意想不到的效果。不要害怕尝试，大胆地提出你的想法吧。”",
                        ScoreStandard="1. 假设合理性（25 分）\r\n  - 假设符合实际情况和问题背景，逻辑清晰，得 20-25 分。\r\n  - 假设基本合理，但在某些细节上存在不足，得 12-19 分。\r\n  - 假设不合理，与实际情况严重不符，得 0-11 分。\r\n2. 假设创新性（15 分）\r\n  - 提出了新颖、独特的假设，能够为模型带来新的思路和方法，得 12-15 分。\r\n  - 假设具有一定的创新性，但不够突出，得 7-11 分。\r\n  - 假设较为常规，缺乏创新性，得 0-6 分。\r\n3. 对假设影响的认知（20 分）\r\n  - 能够清晰、准确地分析出忽略假设会对模型产生的具体影响，得 16-20 分。\r\n  - 对假设影响的分析基本正确，但不够全面和深入，得 10-15 分。\r\n  - 对假设影响的分析存在错误或不清晰，得 0-9 分。\r\n4. 假设与模型的关联度（15 分）\r\n  - 假设紧密围绕模型的核心要素和结构，能够有效推动模型的构建和完善，得 12-15 分。\r\n  - 假设与模型有一定的关联，但关联不够紧密，得 7-11 分。\r\n  - 假设与模型关联度低，对模型构建帮助不大，得 0-6 分。\r\n5. 表达清晰度（10 分）\r\n  - 假设的表述清晰、准确，易于理解，得 8-10 分。\r\n  - 假设的表述基本清晰，但存在一些模糊或歧义的地方，得 4-7 分。\r\n  - 假设的表述混乱，难以理解，得 0-3 分。\r\n6. 模型优化效果（15 分）\r\n  - 通过假设对模型进行了有效优化，使模型的性能（如准确性、效率等）得到显著提升，得 12-15 分。\r\n  - 模型优化有一定效果，但提升幅度不大，得 7-11 分。\r\n  - 模型优化效果不明显或没有效果，得 0-6 分。",
                        GuideRoleSetting="角色：亲切且专业的高中数学建模导师。\r\n核心目标：引导学生在建模过程中合理、科学地提出假设，帮助学生理解假设的重要性以及假设对模型的影响。\r\n语言风格：采用通俗易懂、简洁明了的语言，避免使用过于专业和复杂的术语，让学生能够轻松理解和接受。语气要亲切、耐心，给予学生充分的鼓励和支持，增强学生的自信心。\r\n容错：以包容和理解的态度对待学生提出的不太合理的假设，不直接否定，而是通过引导和提问，帮助学生发现问题并进行改进。鼓励学生积极尝试和探索，营造一个开放、创新的交流氛围。",
                        Prologue="在上一阶段，我们已经成功了数学模型。现在，让我们进入假设阶段，这是数学建模中非常重要的一步。",
                        TaskIsSubmit=true,
                        GroupIsSubmit=true,
                        IsDefault=true,
                        TaskType=8,
                        ModelingStageTaskQuestionInfos=new List<GetModelingStageTaskQuestionDefaultOutput>()
                        {
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="假设与现实脱节" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="假设逻辑矛盾" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="假设缺乏依据" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="关键前提遗漏" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="边界条件未假设" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="例外情况未覆盖" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="假设与模型目标脱节" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="假设间无逻辑关联" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="假设表述模糊" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="核心变量定义不清" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="条件范围不明" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="存在冗余假设" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="非核心假设过度复杂" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="其他" }
                        }
                    }
                }
            });

            //模型评价阶段
            getModelingStageDefaults.Add(new GetModelingStageDefaultInfoOutput()
            {
                Name = "模型评价阶段",
                Describe = "---",
                DefaultStageType = 3,
                IsDefault = true,
                ModelingStageTaskInfos = new List<GetModelingStageTaskDefaultOutput>()
                {
                    new GetModelingStageTaskDefaultOutput()
                    {
                        Name="建模构建第三步：检验效果与价值",
                        Target="模型建完得 “打分”！代入典型数据看计算准不准，对比真实结果找偏差；分析假设条件要是变了，模型还能不能用；再想想解决问题时，模型好不好懂、好不好用。从准确性、稳定性、实用性多方面 “挑刺”，判断模型是 “好用的工具” 还是 “得返工的半成品”，明确改进方向。",
                        RoleSetting="角色定位\r\n高中数学建模模型评价阶段的 “引导式点评师”，既是学生建模成果的分析者，也是评价思路的启发者，需以学生的初步模型为基础，带领学生从多维度审视模型的优劣。\r\n核心目标\r\n1. 引导学生掌握模型评价的基本维度（如准确性、合理性、简洁性、适用性），学会用具体指标（如误差范围、与实际数据的吻合度）评估模型；\r\n2. 启发学生发现模型的潜在问题（如假设与结果矛盾、未覆盖核心要素），并思考改进方向；\r\n3. 帮助学生建立 “评价 — 反思 — 迭代” 的建模思维，理解评价不是终点而是优化的起点。\r\n语言风格\r\n1. 专业且通俗：用学生能理解的语言解释评价指标（如不说 “模型鲁棒性”，而说 “模型在数据略有变化时，结果是否还稳定”），结合具体案例（如 “用你建立的成绩预测模型算一下已知数据，看看误差有多大”）；\r\n2. 启发式提问：以问题链引导思考（如 “这个模型能解释所有已知情况吗？如果出现 XX 特殊案例，它还适用吗？”），避免直接下结论；\r\n3. 鼓励性基调：肯定学生模型中的亮点（如 “你在模型中考虑了‘节假日因素’，这个细节很贴合实际”），即使指出问题也附带改进方向（如 “如果能补充‘不同年级学生消费习惯差异’的数据，模型会更完善”）。\r\n容错机制\r\n1. 对评价维度理解偏差的容错：若学生仅从 “计算是否正确” 单一维度评价模型，不否定其思路，而是补充引导（如 “计算准确很重要，除此之外，你觉得这个模型是否容易被别人理解？这也是评价的一个角度哦”）；\r\n2. 对模型问题判断失误的容错：当学生误判模型优势（如认为 “忽略次要要素导致的误差是合理的” 但实际误差过大），用数据对比引导修正（如 “我们用实际数据代入试试，当误差超过 10% 时，这个模型可能就不太适合做决策了，你觉得呢？”）；\r\n3. 对改进方向模糊的容错：若学生不知如何优化模型，提供阶梯式提示（如 “先想想模型中哪个假设最可能导致误差？从调整这个假设开始，会不会更简单？”）。",
                        Demand="以问题形式启发学生思考模型评价的多个角度，如 “你觉得一个好的模型应该具备哪些特点呢？是不是既要看它能不能准确解决问题，也要看它是否容易被理解和使用？” 引导学生自主梳理出准确性、合理性、简洁性、适用性等维度，而非直接告知。\r\n1. 引导学生结合实例进行评价\r\n鼓励学生将模型与具体场景或数据结合，如 “用你构建的模型来计算一下我们之前收集的某组实际数据，看看得出的结果和实际情况是否一致？差异有多大？” 再进一步引导分析差异产生的原因，如 “为什么会出现这样的差异？是模型中的假设不合理，还是变量考虑不全面呢？”\r\n3. 引导学生发现模型的潜在问题\r\n通过设置情境或提出假设性问题，促使学生审视模型的局限性，如 “如果遇到 [某种特殊情况]，你的模型还能适用吗？”“要是我们获取的数据更加详细，你的模型是否需要调整才能更好地应对？” 引导学生从不同角度挖掘模型可能存在的问题。\r\n4. 引导学生探索模型改进方向\r\n在学生发现模型问题后，进一步引导其思考改进办法，如 “既然你发现模型在 [某方面] 存在不足，那你觉得可以从哪些地方入手进行优化呢？是调整假设，还是增加新的变量？” 鼓励学生提出具体的改进思路，并说明理由。\r\n5. 引导学生形成评价闭环\r\n当学生提出改进方案后，引导其验证改进效果，如 “按照你提出的改进思路调整模型后，再用之前的数据进行测试，看看模型的表现是否有提升？” 让学生体会 “评价 - 改进 - 再评价” 的循环过程，强化建模思维。",
                        ScoreStandard="评价体系（总分 100 分）\r\n（一）模型评价维度（70 分）\r\n评价维度完整性（20 分）\r\n全面覆盖准确性、合理性、简洁性、适用性等核心维度，得 16-20 分；\r\n覆盖 3 个核心维度，得 10-15 分；\r\n仅覆盖 1-2 个核心维度，得 0-9 分。\r\n评价依据充分性（20 分）\r\n能结合具体数据、实例或逻辑推理支撑评价结论（如 “模型预测误差超过 10%，因未考虑季节因素”），得 16-20 分；\r\n有一定依据但不够具体（如 “模型不太准确，和实际情况有差距”），得 10-15 分；\r\n无依据或依据错误，得 0-9 分。\r\n问题识别精准度（15 分）\r\n准确指出模型的关键局限性（如 “假设与实际数据矛盾”“未覆盖核心变量”），得 12-15 分；\r\n识别出部分问题但不关键（如 “计算步骤繁琐” 而非 “逻辑漏洞”），得 7-11 分；\r\n未识别问题或识别错误，得 0-6 分。\r\n改进建议可行性（15 分）\r\n提出的改进方案具体可操作（如 “增加‘周末因素’变量，重新校准函数参数”），得 12-15 分；\r\n建议方向合理但不具体（如 “需要调整模型假设”），得 7-11 分；\r\n建议不可行或与问题无关，得 0-6 分。\r\n（二）问答质量维度（30 分）\r\n思维逻辑性（10 分）\r\n评价过程逻辑清晰，从 “评价维度→依据→结论→建议” 层层递进，得 8-10 分；\r\n逻辑基本连贯但有轻微跳跃，得 5-7 分；\r\n逻辑混乱，得 0-4 分。\r\n回应有效性（10 分）\r\n能针对智能体的引导（如 “这个评价维度是否全面？”）给出明确回应，补充关键信息，得 8-10 分；\r\n回应基本有效但信息不完整，得 5-7 分；\r\n回避问题或回应无关内容，得 0-4 分。\r\n表达清晰度（10 分）\r\n评价结论、依据、建议表述准确、简洁，无歧义，得 8-10 分；\r\n表述基本清晰但有少量模糊之处，得 5-7 分；\r\n表述混乱，难以理解，得 0-4 分。",
                        GuideRoleSetting="角色定位：模型评价阶段的 “量化评估师”，需以预设评价体系为基准，结合学生与智能体的问答质量，对模型评价过程及结果进行客观打分与质性分析。\r\n核心职责：\r\n1. 严格依据评价体系的量化指标，对学生在模型评价中的表现逐项评分，确保分数与实际表现一致；\r\n2. 分析学生在问答过程中展现的思维逻辑（如是否能清晰阐述评价依据）、互动质量（如是否积极回应引导），并将其纳入评分参考；\r\n3. 综合量化得分与问答质量，形成全面的评分结果，为后续改进建议提供依据。\r\n评分原则：\r\n- 体系为基：以评价体系的维度和分值为基础框架，不偏离核心指标；\r\n- 问答为辅：将问答中的表现（如逻辑清晰度、回应及时性）作为加分或减分的微调项，而非主导因素；\r\n- 客观中立：评分仅基于学生的实际输出（模型评价内容、对话表述），不加入主观臆断。",
                        TaskIsSubmit=true,
                        GroupIsSubmit=true,
                        IsDefault=true,
                        TaskType=9,
                        ModelingStageTaskQuestionInfos=new List<GetModelingStageTaskQuestionDefaultOutput>()
                        {
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="维度缺失" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="维度冗余（无关指标过多）" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="维度错位（与模型目标不匹配）" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="无数据支撑" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="逻辑推导漏洞" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="案例不匹配" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="表面化评价" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="未分析优缺点" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="未提改进方向" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="主观臆断" },
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="忽略反例"},
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="过度美化 / 否定"},
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="未结合场景"},
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="未考虑约束条件"},
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="与假设脱节"},
                            new GetModelingStageTaskQuestionDefaultOutput(){ Name="其他"}

                        }
                    }
                }
            });

            return getModelingStageDefaults;
        }

        /// <summary>
        /// 获取建模信息
        /// </summary>
        /// <returns></returns>
        public async Task<GetModelingInfoOutput> GetModelingInfo(GetModelingInfoInput input)
        {
            try
            {
                //查询建模信息
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.Id && p.Creator == input.TeacherId && p.IsDeleted == false)
                    .Select(p => new GetModelingInfoOutput()
                    {
                        Id = p.Id,
                        AgentId = p.AgentId,
                        Name = p.Name,
                        Introduce = p.Introduce,
                        TaskLogo = p.TaskLogo,
                        SubjectId = p.SubjectId,
                        GradeId = p.GradeId,
                        TeacherId = p.Creator,
                        ModelingStageInfos = new List<GetModelingStageInfoOutput>()
                    })
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("建模信息不存在!");
                }

                //查询所有建模阶段
                var modelingStages = await DBSqlSugar.Queryable<AI_ModelingStage>()
                    .Where(p => p.ModelingId == input.Id && p.Creator == input.TeacherId && !p.IsDeleted)
                    .OrderBy(p => p.Order)
                    .With(SqlWith.NoLock)
                    .ToListAsync();
                if (!modelingStages.Any())
                {
                    return agentTask;
                }

                //查询建模所有任务
                var allStageTasks = await DBSqlSugar.Queryable<AI_ModelingStageTask>()
                    .Where(t => t.ModelingId == input.Id && t.Creator == input.TeacherId && !t.IsDeleted)
                    .OrderBy(t => t.Order)
                    .With(SqlWith.NoLock)
                    .ToListAsync();
                if (!allStageTasks.Any())
                {
                    agentTask.ModelingStageInfos = modelingStages.Select(s => new GetModelingStageInfoOutput
                    {
                        Id = s.Id,
                        Name = s.Name,
                        Describe = s.Describe,
                        DefaultStageType = s.DefaultStageType,
                        IsDefault = s.IsDefault
                    }).ToList();
                    return agentTask;
                }

                //获取存在提交记录的任务Id列表（用于判断任务是否有提交记录）
                var submitTaskIds = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>()
                    .Where(p => p.ModelingId == input.Id && !p.IsDeleted)
                    .With(SqlWith.NoLock)
                    .Select(p => p.ModelingStageTaskId)
                    .ToListAsync();

                //收集所有任务ID，查询关联数据
                var taskIds = allStageTasks.Select(t => t.Id).ToList();

                //查询所有任务问题
                var allTaskQuestions = await DBSqlSugar.Queryable<AI_ModelingStageTaskQuestion>()
                    .Where(q => taskIds.Contains(q.ModelingStageTaskId) && !q.IsDeleted)
                    .OrderBy(q => q.Order)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //查询所有任务视频
                var allTaskVideos = await DBSqlSugar.Queryable<AI_ModelingStageTaskVideo>()
                    .Where(v => taskIds.Contains(v.ModelingStageTaskId) && !v.IsDeleted)
                    .OrderBy(v => v.Order)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //查询所有任务文档
                var allTaskDocuments = await DBSqlSugar.Queryable<AI_ModelingStageTaskDocument>()
                    .Where(d => taskIds.Contains(d.ModelingStageTaskId) && !d.IsDeleted)
                    .OrderBy(d => d.Order)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //构建数据字典，便于内存关联
                var taskQuestionsDict = allTaskQuestions
                    .GroupBy(q => q.ModelingStageTaskId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                var taskVideosDict = allTaskVideos
                    .GroupBy(v => v.ModelingStageTaskId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                var taskDocumentsDict = allTaskDocuments
                    .GroupBy(d => d.ModelingStageTaskId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                foreach (var stage in modelingStages)
                {
                    var stageOutput = new GetModelingStageInfoOutput
                    {
                        Id = stage.Id,
                        Name = stage.Name,
                        Describe = stage.Describe,
                        DefaultStageType = stage.DefaultStageType,
                        IsDefault = stage.IsDefault,
                        ModelingStageTaskInfos = new List<GetModelingStageTaskInfoOutput>()
                    };

                    //获取当前阶段的所有任务
                    var stageTasks = allStageTasks.Where(t => t.ModelingStageId == stage.Id).ToList();
                    foreach (var task in stageTasks)
                    {
                        //获取当前任务的关联数据
                        taskQuestionsDict.TryGetValue(task.Id, out var taskQuestions);
                        taskVideosDict.TryGetValue(task.Id, out var taskVideos);
                        taskDocumentsDict.TryGetValue(task.Id, out var taskDocuments);

                        var taskOutput = new GetModelingStageTaskInfoOutput
                        {
                            Id = task.Id,
                            TaskType = task.TaskType,
                            Name = task.Name,
                            Target = task.Target,
                            ScoreStandard = task.ScoreStandard,
                            Demand = task.Demand,
                            Scope = task.Scope,
                            RoleSetting = task.RoleSetting,
                            GuideRoleSetting = task.GuideRoleSetting,
                            Prologue = task.Prologue,
                            ToneId = task.ToneId,
                            RoleName = task.RoleName,
                            TaskIsSubmit = task.TaskIsSubmit,
                            TaskIsAssessment = task.TaskIsAssessment,
                            TaskAssessmentScore = task.TaskAssessmentScore,
                            TaskIsWatchVideo = task.TaskIsWatchVideo,
                            TaskIsVideoWatchDuration = task.TaskIsVideoWatchDuration,
                            TaskVideoWatchDuration = task.TaskVideoWatchDuration,
                            TaskIsReadAllDocuments = task.TaskIsReadAllDocuments,
                            GroupIsSubmit = task.GroupIsSubmit,
                            GroupIsAssessment = task.GroupIsAssessment,
                            GroupAssessmentScore = task.GroupAssessmentScore,
                            GroupIsWatchVideo = task.GroupIsWatchVideo,
                            GroupIsVideoWatchDuration = task.GroupIsVideoWatchDuration,
                            GroupVideoWatchDuration = task.GroupVideoWatchDuration,
                            GroupIsReadAllDocuments = task.GroupIsReadAllDocuments,
                            IsDefault = task.IsDefault,
                            IsDo = submitTaskIds.Contains(task.Id),

                            //转换问题列表
                            ModelingStageTaskQuestionInfos = taskQuestions?.Select(q => new GetModelingStageTaskQuestionInfoOutput
                            {
                                Id = q.Id,
                                Name = q.Name,
                                Describe = q.Describe
                            }).ToList() ?? new List<GetModelingStageTaskQuestionInfoOutput>(),

                            //转视频列表
                            ModelingStageTaskVideoInfos = taskVideos?.Select(v => new GetModelingStageTaskVideoInfo
                            {
                                Id = v.Id,
                                Name = v.Name,
                                Url = v.Url,
                                Size = v.Size,
                                Duration = v.Duration
                            }).ToList() ?? new List<GetModelingStageTaskVideoInfo>(),

                            //转换文档列表
                            ModelingStageTaskDocumentInfos = taskDocuments?.Select(d => new GetModelingStageTaskDocumentInfo
                            {
                                Id = d.Id,
                                Name = d.Name,
                                Url = d.Url,
                                Size = d.Size
                            }).ToList() ?? new List<GetModelingStageTaskDocumentInfo>()
                        };

                        stageOutput.ModelingStageTaskInfos.Add(taskOutput);
                    }

                    agentTask.ModelingStageInfos.Add(stageOutput);
                }

                return agentTask;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 删除建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<bool> DeleteModelingInfo(DeleteModelingInfoInput input)
        {
            try
            {
                //查询建模主信息，验证权限
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ModelingId && p.Creator == input.TeacherId && p.IsDeleted == false)
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("建模信息不存在或无删除权限!");
                }

                //获取当前时间（用于统一设置删除时间）
                var deleteTime = DateTime.Now;

                //逻辑删除学生提交记录
                await DBSqlSugar.Updateable<AI_StudentDoModelingTask>()
                    .SetColumns(p => new AI_StudentDoModelingTask
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.ModelingId == input.ModelingId && !p.IsDeleted)
                    .ExecuteCommandAsync();

                //逻辑删除学生做任务问题
                await DBSqlSugar.Updateable<AI_StudentDoModelingTaskQuestion>()
                    .SetColumns(p => new AI_StudentDoModelingTaskQuestion
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                   .Where(p => p.ModelingId == input.ModelingId && !p.IsDeleted)
                    .ExecuteCommandAsync();

                //逻辑删除对话记录
                await DBSqlSugar.Updateable<AI_ModelingDialogueRecord>()
                    .SetColumns(p => new AI_ModelingDialogueRecord
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.ModelingId == input.ModelingId && !p.IsDeleted)
                    .ExecuteCommandAsync();

                //逻辑删除缓存记录
                await DBSqlSugar.Updateable<AI_ModelingContextCacheKey>()
                    .SetColumns(p => new AI_ModelingContextCacheKey
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.ModelingId == input.ModelingId && !p.IsDeleted)
                    .ExecuteCommandAsync();

                //逻辑删除建模阶段
                await DBSqlSugar.Updateable<AI_ModelingStage>()
                    .SetColumns(p => new AI_ModelingStage
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.ModelingId == input.ModelingId)
                    .ExecuteCommandAsync();

                //查询所有阶段任务
                var stageTasks = await DBSqlSugar.Queryable<AI_ModelingStageTask>()
                    .Where(p => p.ModelingId == input.ModelingId && !p.IsDeleted)
                    .Select(p => p.Id)
                    .ToListAsync();
                if (stageTasks.Any())
                {
                    //逻辑删除任务问题
                    await DBSqlSugar.Updateable<AI_ModelingStageTaskQuestion>()
                        .SetColumns(p => new AI_ModelingStageTaskQuestion
                        {
                            IsDeleted = true,
                            ModifyTime = deleteTime,
                            Modifier = input.TeacherId
                        })
                        .Where(p => stageTasks.Contains(p.ModelingStageTaskId) && !p.IsDeleted)
                        .ExecuteCommandAsync();
                }

                //逻辑删除任务视频
                await DBSqlSugar.Updateable<AI_ModelingStageTaskVideo>()
                    .SetColumns(p => new AI_ModelingStageTaskVideo
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.ModelingId == input.ModelingId && !p.IsDeleted)
                    .ExecuteCommandAsync();

                //逻辑删除任务视频观看记录
                await DBSqlSugar.Updateable<AI_StudentWatchModelingVideo>()
                    .SetColumns(p => new AI_StudentWatchModelingVideo
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.ModelingId == input.ModelingId && !p.IsDeleted)
                    .ExecuteCommandAsync();

                //逻辑删除任务文档
                await DBSqlSugar.Updateable<AI_ModelingStageTaskDocument>()
                    .SetColumns(p => new AI_ModelingStageTaskDocument
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.ModelingId == input.ModelingId && !p.IsDeleted)
                    .ExecuteCommandAsync();

                //逻辑删除任务文档阅读记录
                await DBSqlSugar.Updateable<AI_StudentReadModelingDocument>()
                    .SetColumns(p => new AI_StudentReadModelingDocument
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.ModelingId == input.ModelingId && !p.IsDeleted)
                    .ExecuteCommandAsync();

                //逻辑删除阶段任务
                await DBSqlSugar.Updateable<AI_ModelingStageTask>()
                    .SetColumns(p => new AI_ModelingStageTask
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.ModelingId == input.ModelingId)
                    .ExecuteCommandAsync();

                //逻辑删除发布记录
                await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                    .SetColumns(p => new AI_AgentTaskPublish
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.AgentTaskId == input.ModelingId)
                    .ExecuteCommandAsync();

                //最后逻辑删除主任务
                await DBSqlSugar.Updateable<AI_AgentTask>()
                    .SetColumns(p => new AI_AgentTask
                    {
                        IsDeleted = true,
                        ModifyTime = deleteTime,
                        Modifier = input.TeacherId
                    })
                    .Where(p => p.Id == input.ModelingId)
                    .ExecuteCommandAsync();

                return true;
            }
            catch (Exception ex)
            {
                throw new BusException($"删除建模信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发布建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task PublishModelingInfo(PublishModelingInfoInput input)
        {
            try
            {
                // 检查任务是否存在且属于当前教师
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.ModelingId && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("建模任务不存在或无权限访问!");
                }
                else
                {
                    //智能体建模任务更新
                    agentTask.ModifyTime = DateTime.Now;
                    agentTask.Modifier = input.TeacherId;
                    agentTask.ScorePublishType = input.ScorePublishType;
                    agentTask.ScorePublishTime = input.ScorePublishTime;

                    await DBSqlSugar.Updateable(agentTask).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                }

                // 检查是否有有效的阶段和任务
                var modelingStages = await DBSqlSugar.Queryable<AI_ModelingStage>().Where(p => p.ModelingId == input.ModelingId && p.IsDeleted == false).ToListAsync();
                if (!modelingStages.Any())
                {
                    throw new BusException("至少需要一个有效阶段才能发布!");
                }

                var modelingStageIds = modelingStages.Select(p => p.Id).ToList();
                var modelingStageTasks = await DBSqlSugar.Queryable<AI_ModelingStageTask>().Where(p => modelingStageIds.Contains(p.ModelingStageId) && p.IsDeleted == false).ToListAsync();
                if (!modelingStageTasks.Any())
                {
                    throw new BusException("至少需要一个有效任务才能发布!");
                }

                // 逻辑删除该任务之前的所有发布记录
                await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                    .SetColumns(p => new AI_AgentTaskPublish { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.AgentTaskId == input.ModelingId && p.Creator == input.TeacherId).ExecuteCommandAsync();

                List<AI_AgentTaskPublish> publishList = new List<AI_AgentTaskPublish>();
                // 发布记录（班级发布）
                if (input.ClassIds.Count > 0)
                {
                    foreach (var classId in input.ClassIds)
                    {
                        if (string.IsNullOrEmpty(classId))
                        {
                            continue; // 跳过空ID，避免异常
                        }

                        publishList.Add(new AI_AgentTaskPublish
                        {
                            Id = IdHelper.GetId(),
                            AgentTaskId = input.ModelingId,
                            BeginTime = input.TimeRange[0],
                            EndTime = input.TimeRange[1],
                            PublishType = 1, // 1-班级
                            PublishBusinessId = classId,
                            CreateTime = DateTime.Now,
                            Creator = input.TeacherId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.TeacherId,
                            IsDeleted = false
                        });
                    }
                }
                // 发布记录（学生发布）
                else if (input.StudentIds.Count > 0)
                {
                    foreach (var studentId in input.StudentIds)
                    {
                        if (string.IsNullOrEmpty(studentId))
                        {
                            continue; // 跳过空ID，避免异常
                        }

                        publishList.Add(new AI_AgentTaskPublish
                        {
                            Id = IdHelper.GetId(),
                            AgentTaskId = input.ModelingId,
                            BeginTime = input.TimeRange[0],
                            EndTime = input.TimeRange[1],
                            PublishType = 2, // 2-学生
                            PublishBusinessId = studentId,
                            CreateTime = DateTime.Now,
                            Creator = input.TeacherId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.TeacherId,
                            IsDeleted = false
                        });
                    }
                }

                //验证是否有有效发布记录
                if (!publishList.Any())
                {
                    throw new BusException("未找到有效发布对象，发布失败");
                }

                //保存发布记录
                await DBSqlSugar.Insertable(publishList).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException($"发布建模信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 撤销建模发布
        /// </summary>
        /// <param name="input">撤销发布参数</param>
        /// <returns></returns>
        public async Task UnpublishModelingInfo(UnpublishModelingInfoInput input)
        {
            try
            {
                // 检查任务是否存在且属于当前教师
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.ModelingId && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("建模不存在或无权限访问!");
                }

                // 逻辑删除该任务的所有发布记录
                await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                    .SetColumns(p => new AI_AgentTaskPublish { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.AgentTaskId == input.ModelingId && p.Creator == input.TeacherId).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取建模列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageReturn<GetModelingListOutput>> GetModelingList(GetModelingListInput input)
        {
            try
            {
                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!", 801);
                }

                //参数
                List<SugarParameter> parameters = new List<SugarParameter>();
                parameters.Add(new SugarParameter("@year", nowSemesterTime.Year));
                parameters.Add(new SugarParameter("@term", nowSemesterTime.NowTerm));
                parameters.Add(new SugarParameter("@teacherId", input.TeacherId));
                parameters.Add(new SugarParameter("@agentId", input.AgentId));
                parameters.Add(new SugarParameter("@subjectId", input.SubjectId));
                parameters.Add(new SugarParameter("@gradeId", input.GradeId));
                string where = string.Empty;
                string whereTwo = string.Empty;
                if (!string.IsNullOrEmpty(input.Name))
                {
                    where += $" AND t.Name LIKE '%{input.Name}%' ";
                }

                if (!string.IsNullOrEmpty(input.ClassId))
                {
                    where += $" AND atp.PublishBusinessId=@classId  ";
                    parameters.Add(new SugarParameter("@classId", input.ClassId));
                }

                if (input.PublishStatus != 0)
                {
                    whereTwo += $" AND PublishStatus=@publishStatus ";
                    parameters.Add(new SugarParameter("@publishStatus", input.PublishStatus));
                }

                //获取建模列表
                string sql = $@"SELECT
                                	AgentId,
                                	ModelingId,
                                	Name,
                                	Introduce,
                                	CreateTime,
                                	TaskLogo,
                                	PublishStatus 
                                FROM
                                	(
                                	SELECT
                                		t.AgentId,
                                		t.Id AS ModelingId,
                                		t.Name,
                                		t.Introduce,
                                		t.CreateTime,
                                		COALESCE ( t.TaskLogo, a.Logo ) AS TaskLogo,
                                	    CASE WHEN atp.AgentTaskId IS NOT NULL THEN 1 ELSE 2 END AS PublishStatus,
                                		ROW_NUMBER ( ) OVER ( PARTITION BY t.Id ORDER BY atp.CreateTime DESC ) AS rn 
                                	FROM
                                		AI_AgentTask t WITH ( NOLOCK )
                                		INNER JOIN AI_AgentBaseInfo a WITH ( NOLOCK ) ON t.AgentId = a.Id 
                                		AND a.IsDeleted = 0
                                		LEFT JOIN AI_AgentTaskPublish atp WITH ( NOLOCK ) ON t.Id = atp.AgentTaskId 
                                		AND atp.IsDeleted = 0 
                                	WHERE
                                		t.IsDeleted = 0 
                                		AND t.[Year] = @year 
                                		AND t.Term = @term 
                                		AND t.SubjectId = @subjectId 
                                		AND t.AgentId =@agentId
                                		AND t.Creator = @teacherId
                                		AND t.GradeId = @gradeId {where}
                                	) AS sub 
                                WHERE
                                	rn = 1 {whereTwo}";
                RefAsync<int> totalNumber = 0;
                List<GetModelingListOutput> taskListOutputs = await DBSqlSugar.SqlQueryable<GetModelingListOutput>(sql)
                    .AddParameters(parameters)
                    .OrderBy("CreateTime DESC,PublishStatus DESC")
                    .ToPageListAsync(input.PageIndex, input.PageSize, totalNumber);

                if (taskListOutputs.Count > 0)
                {
                    List<string> ids = taskListOutputs.Select(p => p.ModelingId).ToList();

                    //获取建模发布信息
                    string publishInfoSql = $@"SELECT
                                                	atp.AgentTaskId,
	                                                atp.PublishType,
                                                	class.Id AS ClassId,
                                                	class.ClassName,
                                                	student.Id AS StudentId,
                                                	student.RealName AS StudentName 
                                                FROM
                                                	AI_AgentTaskPublish atp WITH ( NOLOCK )
                                                	LEFT JOIN Exam_Class class WITH ( NOLOCK ) ON atp.PublishBusinessId= class.Id
                                                	LEFT JOIN Exam_Student student WITH ( NOLOCK ) ON atp.PublishBusinessId= student.Id 
                                                WHERE
                                                	atp.IsDeleted= 0 
                                                	AND atp.Creator=@teacherId 
                                                	AND atp.AgentTaskId IN ( @agentTaskIds )";
                    List<GetModelingPublishInfoDto> publishInfos = await DBSqlSugar.SqlQueryable<GetModelingPublishInfoDto>(publishInfoSql)
                        .AddParameters(new { teacherId = input.TeacherId, agentTaskIds = ids }).ToListAsync();
                    foreach (var taskListOutput in taskListOutputs)
                    {
                        taskListOutput.ClassInfos = publishInfos.Where(p => p.AgentTaskId == taskListOutput.ModelingId && p.PublishType == 1)
                            .Select(p => new GetModelingListClassInfoOutput()
                            {
                                ClassId = p.ClassId,
                                ClassName = p.ClassName,
                            }).ToList();

                        taskListOutput.StudentInfos = publishInfos.Where(p => p.AgentTaskId == taskListOutput.ModelingId && p.PublishType == 2)
                            .Select(p => new GetModelingListStudentInfoOutput()
                            {
                                StudentId = p.StudentId,
                                StudentName = p.StudentName
                            }).ToList();
                    }
                }

                return new PageReturn<GetModelingListOutput>()
                {
                    Datas = taskListOutputs,
                    TotalCount = totalNumber
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取建模综合分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetModelingSynthesizeAnalyseOutput> GetModelingSynthesizeAnalyse(GetModelingSynthesizeAnalyseInput input)
        {
            try
            {
                //获取建模基础信息
                GetModelingSynthesizeAnalyseOutput modelingInfoOutput = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ModelingId && p.IsDeleted == false)
                    .Select(p => new GetModelingSynthesizeAnalyseOutput()
                    {
                        AgentId = p.AgentId,
                        ModelingId = p.Id,
                        ModelingName = p.Name,
                        ModelingIntroduce = p.Introduce,
                        ModelingLogo = p.TaskLogo
                    })
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (modelingInfoOutput == null)
                {
                    throw new BusException("建模Id异常!");
                }

                //建模阶段
                List<GetModelingSynthesizeAnalyseStageOutput> modelingStages = await DBSqlSugar.Queryable<AI_ModelingStage>()
                    .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.IsDeleted == false)
                    .OrderBy(p => p.Order)
                    .Select(p => new GetModelingSynthesizeAnalyseStageOutput()
                    {
                        Id = p.Id,
                        Name = p.Name
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //建模阶段任务
                List<AI_ModelingStageTask> modelingStageTasks = await DBSqlSugar.Queryable<AI_ModelingStageTask>()
                    .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.IsDeleted == false)
                    .OrderBy(p => p.ModelingStageId)
                    .OrderBy(p => p.Order)
                    .Select(p => new AI_ModelingStageTask()
                    {
                        Id = p.Id,
                        ModelingStageId = p.ModelingStageId,
                        Name = p.Name,
                        Order = p.Order,
                        TaskType = p.TaskType
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //获取班级学生数量
                List<string> studentIds = await DBSqlSugar.Queryable<Exam_Student>().Where(p => p.ClassId == input.ClassId && p.Deleted == false).Select(p => p.Id).With(SqlWith.NoLock).ToListAsync();
                modelingInfoOutput.StudentCount = studentIds.Count;

                //获取班级学生提交数据
                string stuDoTaskSql = @"SELECT
                                        	dop.Id,
                                        	dop.ModelingStageId,
                                        	dop.ModelingStageTaskId,
                                        	dop.StudentId,
                                        	dop.Score,
                                        	dop.IsStandard,
                                        	dop.[Order] 
                                        FROM
                                        	AI_StudentDoModelingTask dop WITH ( NOLOCK )
                                        	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON dop.StudentId= stu.Id 
                                        	AND stu.Deleted= 0 
                                        WHERE
                                        	dop.IsDeleted= 0 
                                        	AND stu.ClassId= @classId 
                                        	AND dop.ModelingId=@modelingId";
                List<AI_StudentDoModelingTask> stuDoTaskInfos = await DBSqlSugar.SqlQueryable<AI_StudentDoModelingTask>(stuDoTaskSql).AddParameters(new { classId = input.ClassId, modelingId = input.ModelingId }).ToListAsync();

                //平均等第
                decimal? avgScore = stuDoTaskInfos.Where(p => p.Order == 1).Count() > 0 ? stuDoTaskInfos.Where(p => p.Order == 1).Average(p => p.Score) : 0;
                modelingInfoOutput.AvgLevel = BusinessUtil.GetAvgLevel(avgScore.HasValue ? avgScore.Value : 0);
                modelingInfoOutput.AvgLevelCount = stuDoTaskInfos.Where(p => p.Order == 1).Count();

                //完成率
                modelingInfoOutput.Finish = BusinessUtil.GetAccuracy(stuDoTaskInfos.Count(p => p.IsStandard == true), modelingStageTasks.Count(p => p.TaskType != 3) * modelingInfoOutput.StudentCount, 1);

                //提交率
                modelingInfoOutput.SubmitCount = modelingStageTasks.Count(p => p.TaskType != 3) * modelingInfoOutput.StudentCount;
                int submitDistinct = stuDoTaskInfos.GroupBy(task => new { task.ModelingStageTaskId, task.StudentId }).Select(group => group.First()).Count();
                modelingInfoOutput.Submit = BusinessUtil.GetAccuracy(submitDistinct, modelingInfoOutput.SubmitCount, 1);

                //参与率
                if (modelingStageTasks.Count > 0)
                {
                    //验证是否存在对话
                    List<string> dialogueContentRecord = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                        .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.IsDeleted == false)
                        .Select(p => p.StudentId).Distinct().With(SqlWith.NoLock).ToListAsync();
                    foreach (var studentId in studentIds)
                    {
                        if (dialogueContentRecord.Contains(studentId))
                        {
                            modelingInfoOutput.ParticipationCount += 1;
                        }
                    }

                    modelingInfoOutput.Participation = BusinessUtil.GetAccuracy(modelingInfoOutput.ParticipationCount, modelingInfoOutput.StudentCount, 1);
                }

                //阶段完成情况
                foreach (var stage in modelingStages)
                {
                    //获取当前阶段的任务
                    List<AI_ModelingStageTask> modelingStageTask = modelingStageTasks.Where(p => p.ModelingStageId == stage.Id && p.TaskType != 3).ToList();
                    foreach (var task in modelingStageTask)
                    {
                        //是否完成
                        if (modelingInfoOutput.StudentCount > 0 && stuDoTaskInfos.Where(p => p.ModelingStageTaskId == task.Id && p.IsStandard == true).Count() >= modelingInfoOutput.StudentCount)
                        {
                            stage.FinishCount += 1;
                        }
                    }
                    stage.TaskCount = modelingStageTask.Count;
                    stage.Finish = BusinessUtil.GetAccuracy(stage.FinishCount, stage.TaskCount, 1);
                }
                modelingInfoOutput.StageInfo = modelingStages;

                //阶段任务平均分
                foreach (var stageTask in modelingStageTasks.Where(p => p.TaskType != 3))
                {
                    modelingInfoOutput.StageTaskAvgScore.Add(new GetModelingSynthesizeAnalyseScoreOutput()
                    {
                        StageTaskName = stageTask.Name,
                        AvgScore = stuDoTaskInfos.Where(p => p.ModelingStageTaskId == stageTask.Id && p.Order == 1).Count() > 0 ? stuDoTaskInfos.Where(p => p.ModelingStageTaskId == stageTask.Id && p.Order == 1).Average(p => p.Score) : 0
                    });
                }

                return modelingInfoOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取建模阶段任务统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<GetModelingStageTaskCountOutput>> GetModelingStageTaskCount(GetModelingStageTaskCountInput input)
        {
            try
            {
                //建模阶段
                List<GetModelingStageTaskCountOutput> modelingStages = await DBSqlSugar.Queryable<AI_ModelingStage>()
                    .Where(p => p.ModelingId == input.ModelingId && p.IsDeleted == false)
                    .OrderBy(p => p.Order)
                    .Select(p => new GetModelingStageTaskCountOutput()
                    {
                        Id = p.Id,
                        Name = p.Name
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //建模阶段任务
                List<AI_ModelingStageTask> modelingStageTasks = await DBSqlSugar.Queryable<AI_ModelingStageTask>()
                    .Where(p => p.ModelingId == input.ModelingId && p.IsDeleted == false)
                    .OrderBy(p => p.Order)
                    .Select(p => new AI_ModelingStageTask()
                    {
                        Id = p.Id,
                        ModelingStageId = p.ModelingStageId,
                        Target = p.Target,
                        Name = p.Name,
                        Order = p.Order,
                        TaskType = p.TaskType
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //获取班级学生Id
                List<string> studentIds = await DBSqlSugar.Queryable<Exam_Student>().Where(p => p.ClassId == input.ClassId && p.Deleted == false).Select(p => p.Id).With(SqlWith.NoLock).ToListAsync();

                //获取班级学生提交数据
                string stuDoTaskSql = @"SELECT
                                        	dop.Id,
                                        	dop.ModelingStageTaskId,
                                        	dop.StudentId,
                                        	dop.Score,
                                        	dop.Level,
                                        	dop.[Order] 
                                        FROM
                                        	AI_StudentDoModelingTask dop WITH ( NOLOCK )
                                        	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON dop.StudentId= stu.Id 
                                        	AND stu.Deleted= 0 
                                        WHERE
                                        	dop.IsDeleted= 0 
                                        	AND stu.ClassId= @classId 
                                        	AND dop.ModelingId=@modelingId";
                List<AI_StudentDoModelingTask> stuDoTaskInfos = await DBSqlSugar.SqlQueryable<AI_StudentDoModelingTask>(stuDoTaskSql).AddParameters(new { classId = input.ClassId, modelingId = input.ModelingId }).ToListAsync();

                //对话主题
                string themeSql = @"SELECT
                                    	doq.ModelingStageTaskId,
                                    	pstq.Name 
                                    FROM
                                    	AI_StudentDoModelingTaskQuestion doq WITH ( NOLOCK )
                                    	INNER JOIN AI_ModelingStageTaskQuestion pstq WITH ( NOLOCK ) ON doq.QuestionId= pstq.Id 
                                    	AND pstq.IsDeleted= 0
                                    	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON doq.StudentId= stu.Id 
                                    	AND stu.Deleted= 0
                                    WHERE
                                    	doq.IsDeleted= 0 
                                    	AND doq.ModelingId= @modelingId 
                                    	AND stu.ClassId= @classId 
                                    GROUP BY
                                    	doq.ModelingStageTaskId,
                                    	pstq.Name";
                List<GetDoModelingStageTaskThemes> taskThemes = await DBSqlSugar.SqlQueryable<GetDoModelingStageTaskThemes>(themeSql).AddParameters(new { classId = input.ClassId, modelingId = input.ModelingId }).ToListAsync();

                //对话记录
                string dialogueContentRecordSql = @"SELECT
                                                        md.Id,
                                                    	md.ModelingStageTaskId,
                                                    	md.StudentId,
                                                        md.CreateTime
                                                    FROM
                                                    	AI_ModelingDialogueRecord md WITH ( NOLOCK )
                                                    	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON md.StudentId= stu.Id 
                                                        AND stu.Deleted= 0
                                                    WHERE
                                                    	md.IsDeleted= 0 
                                                    	AND stu.ClassId=@classId
                                                        AND md.ModelingId=@modelingId";
                List<AI_ModelingDialogueRecord> dialogueContentRecord = await DBSqlSugar.SqlQueryable<AI_ModelingDialogueRecord>(dialogueContentRecordSql)
                    .AddParameters(new { classId = input.ClassId, modelingId = input.ModelingId }).ToListAsync();

                //处理阶段
                foreach (var stage in modelingStages)
                {
                    //获取当前阶段的任务
                    List<AI_ModelingStageTask> modelingStageTask = modelingStageTasks.Where(p => p.ModelingStageId == stage.Id).OrderBy(p => p.Id).ToList();
                    foreach (var task in modelingStageTask)
                    {
                        GetModelingStageTaskInfoCountOutput stageTaskInfoOutput = new GetModelingStageTaskInfoCountOutput()
                        {
                            TaskType = task.TaskType,
                            Name = task.Name,
                            Target = task.Target
                        };

                        //参与率、查询率
                        int participationCount = dialogueContentRecord.Where(p => p.ModelingStageTaskId == task.Id).Select(p => p.StudentId).Distinct().Count();
                        stageTaskInfoOutput.Participation = BusinessUtil.GetAccuracy(participationCount, studentIds.Count, 1);

                        //平均对话次数、平均查询次数
                        stageTaskInfoOutput.AvgDialogue = studentIds.Count == 0 ? 0 : Convert.ToDecimal(dialogueContentRecord.Count(p => p.ModelingStageTaskId == task.Id)) / studentIds.Count;

                        //上传率
                        int submitDistinct = stuDoTaskInfos.Where(p => p.ModelingStageTaskId == task.Id).Select(p => p.StudentId).Distinct().Count();
                        stageTaskInfoOutput.Submit = BusinessUtil.GetAccuracy(submitDistinct, studentIds.Count, 1);

                        //等第分布
                        stageTaskInfoOutput.TaskLevels = stuDoTaskInfos.Where(p => p.ModelingStageTaskId == task.Id && p.Order == 1)
                            .Select(p => new GetModelingStageTaskLevelOutput()
                            {
                                LevelName = p.Level
                            }).DistinctBy(p => p.LevelName).OrderBy(p => p.LevelName).ToList();
                        foreach (var level in stageTaskInfoOutput.TaskLevels)
                        {
                            level.LevelCount = stuDoTaskInfos.Where(p => p.ModelingStageTaskId == task.Id && p.Level == level.LevelName && p.Order == 1).Count();
                        }

                        //主题
                        stageTaskInfoOutput.Themes = taskThemes.Where(p => p.ModelingStageTaskId == task.Id).Select(p => p.Name).Distinct().ToList();

                        //平均得分
                        stageTaskInfoOutput.AvgScore = stuDoTaskInfos.Where(p => p.ModelingStageTaskId == task.Id && p.Order == 1).Count() > 0 ? stuDoTaskInfos.Where(p => p.ModelingStageTaskId == task.Id && p.Order == 1).Average(p => p.Score) : 0;

                        //查询趋势
                        stageTaskInfoOutput.QueryCount = dialogueContentRecord.Where(p => p.ModelingStageTaskId == task.Id).Select(p => new GetModelingStageTaskQueryCountOutput()
                        {
                            Title = p.CreateTime.ToString("yyyy/MM/dd")
                        }).DistinctBy(p => p.Title).ToList();
                        foreach (var query in stageTaskInfoOutput.QueryCount)
                        {
                            query.Count = dialogueContentRecord.Where(p => p.ModelingStageTaskId == task.Id && p.CreateTime.ToString("yyyy/MM/dd") == query.Title).Count();
                        }
                        stageTaskInfoOutput.QueryCount = stageTaskInfoOutput.QueryCount.OrderBy(p => p.Title).ToList();

                        stage.Tasks.Add(stageTaskInfoOutput);
                    }
                }

                return modelingStages;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取建模学生统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetModelingStudentCountOutput> GetModelingStudentCount(GetModelingStudentCountInput input)
        {
            try
            {
                //获取班级学生
                List<Exam_Student> studentInfos = await DBSqlSugar.Queryable<Exam_Student>()
                    .Where(p => p.ClassId == input.ClassId && p.Deleted == false)
                    .Select(p => new Exam_Student()
                    {
                        Id = p.Id,
                        StudentNo = p.StudentNo,
                        RealName = p.RealName
                    })
                    .OrderBy(p => p.StudentNo)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //获取班级学生提交数据
                string stuDoTaskSql = @"SELECT
                                        	dop.Id,
                                        	dop.ModelingStageTaskId,
                                        	dop.StudentId,
                                        	dop.IsStandard,
                                        	dop.CreateTime 
                                        FROM
                                        	AI_StudentDoModelingTask dop WITH ( NOLOCK )
                                        	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON dop.StudentId= stu.Id 
                                        	AND stu.Deleted= 0 
                                        WHERE
                                        	dop.IsDeleted= 0 
                                        	AND stu.ClassId= @classId 
                                        	AND dop.ModelingId=@modelingId";
                List<AI_StudentDoModelingTask> stuDoTaskInfos = await DBSqlSugar.SqlQueryable<AI_StudentDoModelingTask>(stuDoTaskSql).AddParameters(new { classId = input.ClassId, modelingId = input.ModelingId }).ToListAsync();

                //建模阶段任务数量
                int modelingStageTaskCount = await DBSqlSugar.Queryable<AI_ModelingStageTask>().Where(p => p.ModelingId == input.ModelingId && p.IsDeleted == false && p.TaskType != 3).With(SqlWith.NoLock).CountAsync();

                //学生信息
                List<GetModelingStudentInfoOutput> studentInfoOutputs = new List<GetModelingStudentInfoOutput>();
                foreach (var student in studentInfos)
                {
                    GetModelingStudentInfoOutput studentInfoOutput = new GetModelingStudentInfoOutput()
                    {
                        StudentId = student.Id,
                        StudentName = student.RealName,
                        StudentNum = student.StudentNo,
                        ProgressBar = BusinessUtil.GetAccuracy(stuDoTaskInfos.Where(p => p.StudentId == student.Id && p.IsStandard == true).Count(), modelingStageTaskCount, 1),
                        NoStandard = stuDoTaskInfos.Where(p => p.StudentId == student.Id && p.IsStandard == false).Count(),
                        SubmitTime = stuDoTaskInfos.Where(p => p.StudentId == student.Id).OrderByDescending(p => p.CreateTime).FirstOrDefault()?.CreateTime.ToString("yyyy/MM/dd HH:mm:ss")
                    };
                    studentInfoOutputs.Add(studentInfoOutput);
                }

                return new GetModelingStudentCountOutput()
                {
                    ModelingId = input.ModelingId,
                    StudentInfos = studentInfoOutputs
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取学生做建模任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentDoModelingTaskListOutput> GetStudentDoModelingTaskList(GetStudentDoModelingTaskListInput input)
        {
            try
            {
                //获取学生信息
                Exam_Student studentInfo = await DBSqlSugar.Queryable<Exam_Student>().Where(p => p.Id == input.StudentId && p.Deleted == false).With(SqlWith.NoLock).FirstAsync();
                if (studentInfo == null)
                {
                    throw new BusException("学生信息不存在!");
                }

                //获取建模基础信息
                GetStudentDoModelingTaskListOutput modelingInfoOutput = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ModelingId && p.IsDeleted == false)
                    .Select(p => new GetStudentDoModelingTaskListOutput()
                    {
                        AgentId = p.AgentId,
                        ModelingId = p.Id,
                        ModelingName = p.Name,
                        ModelingIntroduce = p.Introduce,
                        ModelingLogo = p.TaskLogo
                    })
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (modelingInfoOutput == null)
                {
                    throw new BusException("建模Id异常!");
                }
                modelingInfoOutput.StudentId = studentInfo.Id;
                modelingInfoOutput.StudentName = studentInfo.RealName;
                modelingInfoOutput.StudentLogo = string.IsNullOrEmpty(studentInfo.Photo) ? "https://userphoto.obs.cn-east-2.myhuaweicloud.com/uploadFile131e561d405a4f6a834a6a79d27ded7e.20210305132909.png" : studentInfo.Photo;

                //建模阶段
                List<GetStudentDoModelingTaskListStageInfoOutput> modelingStages = await DBSqlSugar.Queryable<AI_ModelingStage>()
                    .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.IsDeleted == false)
                    .Select(p => new GetStudentDoModelingTaskListStageInfoOutput()
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //建模阶段任务
                List<GetStudentDoModelingTaskListStageTaskInfoOutput> modelingStageTasks = await DBSqlSugar.Queryable<AI_ModelingStageTask>()
                    .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.IsDeleted == false)
                    .Select(p => new GetStudentDoModelingTaskListStageTaskInfoOutput()
                    {
                        Id = p.Id,
                        ModelingStageId = p.ModelingStageId,
                        Name = p.Name,
                        Target = p.Target,
                        TaskType = p.TaskType,
                        Order = p.Order,
                        ToneId = p.ToneId,
                        RoleName = p.RoleName,
                        Prologue = p.Prologue
                    })
                    .OrderBy(p => p.Order)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //获取学生做建模任务信息
                List<AI_StudentDoModelingTask> studentDoModelingTasks = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>()
                    .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false)
                    .Select(p => new AI_StudentDoModelingTask()
                    {
                        Id = p.Id,
                        ModelingStageId = p.ModelingStageId,
                        ModelingStageTaskId = p.ModelingStageTaskId,
                        IsStandard = p.IsStandard,
                        Order = p.Order,
                        CreateTime = p.CreateTime
                    })
                    .OrderBy(p => p.Order)
                    .ToListAsync();

                //获取建模阶段任务视频
                string taskVideoSql = @"SELECT
                                    	video.Id,
                                    	video.ModelingStageTaskId,
                                    	video.Name,
                                    	video.Url,
                                    	video.[Size],
                                    	video.Duration,
                                    	video.[Order],
                                    	( CASE WHEN sw.Id IS NOT NULL THEN 1 ELSE 0 END ) AS IsWatch,
                                    	sw.TotalWatchDuration 
                                    FROM
                                    	AI_ModelingStageTaskVideo video WITH ( NOLOCK )
                                    	LEFT JOIN AI_StudentWatchModelingVideo sw WITH ( NOLOCK ) ON video.Id= sw.ModelingStageTaskVideoId 
                                    	AND sw.IsDeleted= 0 
                                    	AND sw.StudentId= @studentId 
                                        AND sw.ModelingId= @modelingId
                                    WHERE
                                    	video.IsDeleted= 0 
                                    	AND video.ModelingId= @modelingId";
                List<GetStudentDoModelingStageTaskVideoInfoOutput> taskVideoInfos = await DBSqlSugar.SqlQueryable<GetStudentDoModelingStageTaskVideoInfoOutput>(taskVideoSql)
                    .AddParameters(new { studentId = input.StudentId, modelingId = input.ModelingId }).ToListAsync();

                //获取建模阶段任务文档
                string taskDocSql = @"SELECT
                                        	doc.Id,
                                        	doc.ModelingStageTaskId,
                                        	doc.Name,
                                        	doc.Url,
                                        	doc.[Size],
                                        	doc.[Order],
                                        	( CASE WHEN sr.Id IS NOT NULL THEN 1 ELSE 0 END ) AS IsRead 
                                        FROM
                                        	AI_ModelingStageTaskDocument doc WITH ( NOLOCK )
                                        	LEFT JOIN AI_StudentReadModelingDocument sr WITH ( NOLOCK ) ON doc.Id= sr.ModelingStageTaskDocumentId 
                                        	AND sr.IsDeleted= 0 
                                        	AND sr.StudentId= @studentId 
                                            AND sr.ModelingId= @modelingId
                                        WHERE
                                        	doc.IsDeleted= 0 
                                        	AND doc.ModelingId= @modelingId";
                List<GetStudentDoModelingStageTaskDocumentInfoOutput> taskDocInfos = await DBSqlSugar.SqlQueryable<GetStudentDoModelingStageTaskDocumentInfoOutput>(taskDocSql)
                    .AddParameters(new { studentId = input.StudentId, modelingId = input.ModelingId }).ToListAsync();

                //获取学生未达标问答记录
                List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                    .Where(p => p.ModelingId == input.ModelingId && p.StudentId == input.StudentId && p.IsBackups == true && p.IsDeleted == false)
                    .Select(p => new AI_ModelingDialogueRecord()
                    {
                        StudentDoModelingTaskId = p.StudentDoModelingTaskId
                    }).ToListAsync();

                //处理阶段信息
                foreach (var stage in modelingStages)
                {
                    //获取当前阶段的任务
                    List<GetStudentDoModelingTaskListStageTaskInfoOutput> modelingStageTask = modelingStageTasks.Where(p => p.ModelingStageId == stage.Id).OrderBy(p => p.Order).ToList();
                    foreach (var task in modelingStageTask)
                    {
                        //视频信息
                        if (task.TaskType == 4)
                        {
                            task.TaskVideos = taskVideoInfos.Where(p => p.ModelingStageTaskId == task.Id).OrderBy(p => p.Order).ToList();
                        }
                        //文档信息
                        if (task.TaskType == 5)
                        {
                            task.TaskDocuments = taskDocInfos.Where(p => p.ModelingStageTaskId == task.Id).OrderBy(p => p.Order).ToList();
                        }

                        //获取学生作答记录
                        List<AI_StudentDoModelingTask> doStageTasks = studentDoModelingTasks.Where(p => p.ModelingStageTaskId == task.Id).OrderBy(p => p.Order).ToList();
                        foreach (var doTask in doStageTasks)
                        {
                            GetStudentDoModelingTaskListNumberOutput doTaskNumberOutput = new GetStudentDoModelingTaskListNumberOutput();
                            doTaskNumberOutput.Number = doStageTasks.IndexOf(doTask) + 1;
                            doTaskNumberOutput.TaskSubmitId = doTask.Id;
                            if (doTask.IsStandard)
                            {
                                doTaskNumberOutput.IsBackups = false;
                            }
                            else
                            {
                                //验证是否存在
                                if (dialogueContentRecords.Where(p => p.StudentDoModelingTaskId == doTask.Id).FirstOrDefault() == null)
                                {
                                    doTaskNumberOutput.IsBackups = false;
                                }
                                else
                                {
                                    doTaskNumberOutput.IsBackups = true;
                                }
                            }
                            task.Numbers.Add(doTaskNumberOutput);
                        }

                        if (doStageTasks.Count > 0)
                        {
                            task.SubmitTime = doStageTasks.OrderByDescending(p => p.Order).FirstOrDefault()?.CreateTime.ToString("yyyy/MM/dd HH:mm:ss");
                        }
                    }
                    stage.ModelingStageTaskInfos = modelingStageTask;
                }

                modelingInfoOutput.ModelingStageInfos = modelingStages;
                return modelingInfoOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 教师端建模下载学生成果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<byte[]> ModelingDownloadStudentAchievement(ModelingDownloadStudentAchievementInput input)
        {
            //临时文件夹路径
            var tempDir = Path.Combine(Path.GetTempPath(), $"ModelingDownloadStudentAchievement_{Guid.NewGuid().ToString()}");
            //创建临时目录
            Directory.CreateDirectory(tempDir);
            //本地文件地址
            List<string> localUrl = new List<string>();
            HttpClient httpClient = null;
            try
            {
                // 获取学生成果
                string studentAchievementDtosSql = @"SELECT
                                                    task.Name,
                                                    stu.RealName,
                                                    mdr.Ask
                                                FROM
                                                    AI_ModelingStageTask task WITH ( NOLOCK )
                                                    INNER JOIN AI_ModelingDialogueRecord mdr WITH ( NOLOCK ) ON task.Id= mdr.ModelingStageTaskId 
                                                    AND mdr.IsDeleted= 0
                                                    INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON mdr.StudentId= stu.Id 
                                                    AND stu.Deleted= 0 
                                                    AND stu.ClassId= @classId 
                                                WHERE
                                                    task.ModelingId= @modelingId 
                                                    AND task.TaskType= 1 
                                                    AND task.IsDeleted=0";
                List<ModelingDownloadStudentAchievementDto> studentAchievementDtos = await DBSqlSugar.SqlQueryable<ModelingDownloadStudentAchievementDto>(studentAchievementDtosSql)
                    .AddParameters(new { classId = input.ClassId, modelingId = input.ModelingId })
                    .ToListAsync();
                if (studentAchievementDtos.Count <= 0)
                {
                    throw new BusException("暂无学生成果!", 801);
                }

                //收集文件信息
                List<ModelingDownloadStudentAchievementFileInfoDto> fileInfos = new List<ModelingDownloadStudentAchievementFileInfoDto>();
                foreach (var studentAchievementDto in studentAchievementDtos)
                {
                    studentAchievementDto.AskInfo = JsonConvert.DeserializeObject<AIDialogueASKDto>(studentAchievementDto.Ask);
                    if (studentAchievementDto.AskInfo != null && studentAchievementDto.AskInfo.Files.Count > 0)
                    {
                        foreach (var item in studentAchievementDto.AskInfo.Files)
                        {
                            if (string.IsNullOrEmpty(item.FileUrl))
                            {
                                continue;
                            }

                            // 验证URL格式
                            if (!Uri.TryCreate(item.FileUrl, UriKind.Absolute, out var uriResult) || (uriResult.Scheme != Uri.UriSchemeHttp && uriResult.Scheme != Uri.UriSchemeHttps))
                            {
                                continue;
                            }

                            // 处理文件名
                            string fileName = string.Empty;
                            if (!string.IsNullOrEmpty(studentAchievementDto.RealName))
                            {
                                fileName += studentAchievementDto.RealName + "_";
                            }
                            if (!string.IsNullOrEmpty(studentAchievementDto.Name))
                            {
                                fileName += studentAchievementDto.Name + "_";
                            }
                            if (!string.IsNullOrEmpty(item.FileName))
                            {
                                fileName += Path.GetFileNameWithoutExtension(item.FileName) + "_";
                            }
                            fileName += Guid.NewGuid().ToString();

                            // 提取并验证文件后缀
                            string extension = Path.GetExtension(item.FileUrl);
                            if (string.IsNullOrWhiteSpace(extension))
                            {
                                continue;
                            }

                            // 过滤非法字符
                            string pattern = @"[^a-zA-Z0-9\u4e00-\u9fa5_]";
                            fileName = Regex.Replace(fileName, pattern, "");

                            // 限制文件名长度
                            if (fileName.Length > 100)
                            {
                                fileName = fileName.Substring(0, 100);
                            }

                            string newFileName = fileName + extension;
                            fileInfos.Add(new ModelingDownloadStudentAchievementFileInfoDto
                            {
                                FileName = newFileName,
                                FileUrl = item.FileUrl
                            });
                        }
                    }
                }
                if (fileInfos.Count <= 0)
                {
                    throw new BusException("暂无学生成果文件!", 801);
                }

                //下载文件到临时文件夹
                httpClient = _httpClientFactory.CreateClient();
                httpClient.Timeout = TimeSpan.FromMinutes(120); // 设置超时时间
                foreach (var fileInfo in fileInfos)
                {
                    var filePath = Path.Combine(tempDir, fileInfo.FileName);

                    // 下载文件
                    using (var response = await httpClient.GetAsync(fileInfo.FileUrl, HttpCompletionOption.ResponseHeadersRead))
                    {
                        response.EnsureSuccessStatusCode();

                        using (var stream = await response.Content.ReadAsStreamAsync())
                        using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                        {
                            await stream.CopyToAsync(fileStream);
                        }
                    }
                    localUrl.Add(filePath);
                }

                //生成压缩包
                using (MemoryStream ms = new MemoryStream())
                {
                    using (ZipArchive zipArchive = new ZipArchive(ms, ZipArchiveMode.Create, true))
                    {
                        foreach (var filePath in localUrl)
                        {
                            if (File.Exists(filePath))
                            {
                                zipArchive.CreateEntryFromFile(filePath, Path.GetFileName(filePath));
                            }
                        }
                    }

                    byte[] zipBytes = ms.ToArray();

                    return zipBytes;
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
            finally
            {
                // 清理临时文件
                if (Directory.Exists(tempDir))
                {
                    try
                    {
                        Directory.Delete(tempDir, recursive: true);
                    }
                    catch { /* 忽略清理失败的情况 */ }
                }
                httpClient?.Dispose();
            }
        }

        /// <summary>
        /// 教师端建模下载学生评估结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<byte[]> ModelingDownloadStudentResult(ModelingDownloadStudentResultInput input)
        {
            try
            {
                // 获取学生评估结果
                string studentResultDtoSql = @"SELECT
                                                	dmt.ModelingStageTaskId,
                                                	dmt.StudentId,
                                                	stu.RealName,
                                                	dmt.AssessmentResult,
                                                	dmt.[Order],
                                                    task.Name
                                                FROM
                                                	AI_StudentDoModelingTask dmt WITH ( NOLOCK )
                                                	INNER JOIN Exam_Student stu WITH ( NOLOCK ) ON dmt.StudentId= stu.Id 
                                                	AND stu.Deleted= 0 
                                                	AND stu.ClassId= @classId
                                                	INNER JOIN AI_ModelingStageTask task WITH ( NOLOCK ) ON dmt.ModelingStageTaskId= task.Id 
                                                	AND task.TaskType NOT IN ( 3, 4, 5 ) 
                                                	AND task.IsDeleted= 0 
                                                WHERE
                                                	dmt.ModelingId= @modelingId 
                                                	AND dmt.IsDeleted=0";
                List<ModelingDownloadStudentResultDto> studentResultDtos = await DBSqlSugar.SqlQueryable<ModelingDownloadStudentResultDto>(studentResultDtoSql)
                    .AddParameters(new { classId = input.ClassId, modelingId = input.ModelingId })
                    .ToListAsync();
                if (studentResultDtos.Count <= 0)
                {
                    throw new BusException("暂无评估结果!", 801);
                }

                // 去重，保留每组第一条数据
                List<ModelingDownloadStudentResultDto> distinctResultDtos = studentResultDtos
                    .Select(p => new ModelingDownloadStudentResultDto()
                    {
                        Name = p.Name,
                        ModelingStageTaskId = p.ModelingStageTaskId,
                        StudentId = p.StudentId,
                        RealName = p.RealName
                    })
                    .GroupBy(dto => new { dto.ModelingStageTaskId, dto.StudentId, dto.RealName, dto.Name }) // 按字段组合分组
                    .Select(group => group.First()) // 取每组第一条数据
                    .ToList();

                //word文本处理
                List<MarkdownTextToWordZipInfo> wordTexts = new List<MarkdownTextToWordZipInfo>();
                foreach (var result in distinctResultDtos)
                {
                    //获取学生评估结果
                    List<ModelingDownloadStudentResultDto> studentResults = studentResultDtos
                        .Where(p => p.StudentId == result.StudentId && p.ModelingStageTaskId == result.ModelingStageTaskId)
                        .OrderBy(p => p.Order)
                        .ToList();
                    if (studentResults.Count > 0)
                    {
                        //文件名称处理
                        string fileName = $"{result.RealName}_{Guid.NewGuid().ToString()}";
                        // 过滤非法字符
                        string pattern = @"[^a-zA-Z0-9\u4e00-\u9fa5_]";
                        fileName = Regex.Replace(fileName, pattern, "");
                        // 限制文件名长度
                        if (fileName.Length > 100)
                        {
                            fileName = fileName.Substring(0, 100);
                        }

                        //评估结果文本处理
                        string resultText = string.Empty;
                        foreach (var studentResult in studentResults)
                        {
                            resultText += $"第{studentResults.IndexOf(studentResult) + 1}次作答\n";
                            resultText += $"评估结果:\n{studentResult.AssessmentResult}\n";
                        }

                        wordTexts.Add(new MarkdownTextToWordZipInfo()
                        {
                            Title = result.Name,
                            FileName = fileName,
                            Text = resultText
                        });
                    }
                }
                if (wordTexts.Count <= 0)
                {
                    throw new BusException("暂无评估结果!", 801);
                }

                //地址
                string url = AppSetting.OfficeTool.OfficeToolUrl + AppSetting.OfficeTool.MarkdownTextToWordZipUrl;

                //参数
                MarkdownTextToWordZipInput wordZipInput = new MarkdownTextToWordZipInput()
                {
                    WordInfos = wordTexts
                };
                string jsonData = JsonConvert.SerializeObject(wordZipInput, new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore, // 忽略空值
                    Formatting = Formatting.None // 不格式化JSON，减少传输量
                });

                //http
                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(300); // 超时设置
                    var content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (HttpResponseMessage response = await httpClient.PostAsync(url, content))
                    {
                        // 确保响应成功
                        response.EnsureSuccessStatusCode();

                        // 读取压缩包内容
                        byte[] zipBytes = await response.Content.ReadAsByteArrayAsync();
                        if (zipBytes == null || zipBytes.Length == 0)
                        {
                            throw new BusException("接口返回空的压缩包数据");
                        }
                        return zipBytes;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }
    }
}
