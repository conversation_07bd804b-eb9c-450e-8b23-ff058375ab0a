using Uwoo.System.IRepositories;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.Base;

namespace Uwoo.System.Repositories
{
    public class Base_SemesterTimeRepository : RepositoryBase<Base_SemesterTime>
    , IBase_SemesterTimeRepository
    {
        public Base_SemesterTimeRepository(VOLContext dbContext)
        : base(dbContext)
        {

        }
        public static IBase_SemesterTimeRepository Instance
        {
            get
            {
                return AutofacContainerModule.GetService<IBase_SemesterTimeRepository>
                ();
            }
        }
    }
}
