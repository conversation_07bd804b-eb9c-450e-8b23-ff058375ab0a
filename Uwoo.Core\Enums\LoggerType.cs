﻿namespace Uwoo.Core.Enums
{
	public enum LoggerType
	{
		System = 0,
		Info,
		Success,
		<PERSON>rror,
		Authorzie,
		Global,
		Login,
		Exception,
		ApiException,
		HandleError,
		OnActionExecuted,
		GetUserInfo,
		Edit,
		Search,
		Add,
		Del,
		AppHome,
		ApiLogin,
		ApiPINLogin,
		ApiRegister,
		ApiModifyPwd,
		ApiSendPIN,
		ApiAuthorize,
		Ask,
		JoinMeeting,
		JoinUs,
		EditUserInfo,
		Sell,
		Buy,
		ReportPrice,
		Reply,
		TechData,
		TechSecondData,
		DelPublicQuestion,
		DelexpertQuestion,
		CreateTokenError,
		IPhoneTest,
		SDKSuccess,
		SDKSendError,
		ExpertAuthority,
		ParEmpty,
		NoToken,
		ReplaceToeken,
		KafkaException
	}
}
