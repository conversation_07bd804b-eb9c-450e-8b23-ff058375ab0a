﻿//  -- Function：ICorrectPenLogService.cs
//  --- Project：X.PenServer.Interfaces
//  ---- Remark：
//  ---- Author：Lucifer
//  ------ Date：2023/10/25 01:28:34

using Uwoo.Mongo.Models;

namespace Uwoo.Mongo.Interfaces.Business;

/// <inheritdoc />
public interface ICorrectPenLogService : IMongoAutoService<CorrectPenLog>
{
    /// <summary>
    /// 获取学生笔迹列表
    /// </summary>
    /// <param name="colname">集合名称</param>
    /// <param name="page">页码集合</param>
    /// <param name="userid">用户id</param>
    /// <returns></returns>
    List<CorrectPenLog> GetAll(string colname, List<int> page, string userid);

	/// <summary>
	/// 获取笔迹列表
	/// </summary>
	/// <param name="colname">集合名称</param>
	/// <param name="page">页码</param>
	/// <param name="userid">用户id</param>
	/// <param name="year"></param>
	/// <returns></returns>
	List<CorrectPenLog> GetAll(string colname, string userid, int page,int? year= null);

    /// <summary>
    /// 删除学生订正笔迹
    /// </summary>
    /// <param name="colname">集合名称</param>
    /// <param name="page">页码集合</param>
    /// <param name="userid">用户id集合</param>
    void DeletePenLog(string colname, List<int> page, List<string> userid);
}