﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Enums
{
	/// <summary>
	/// OCR识别结果类型
	/// </summary>
	[Flags]
	public enum OcrAnswerType
	{
		/// <summary>
		/// 文本类型
		/// </summary>
		[Description("文本")]
		Text = 0,

		/// <summary>
		/// 英文
		/// </summary>
		[Description("英文")]
		English = 1,

		/// <summary>
		/// 公式
		/// </summary>
		[Description("公式")]
		Formula = 2,

		/// <summary>
		/// 中文
		/// </summary>
		[Description("中文")]
		Chinese = 3,

		/// <summary>
		/// 单项字母
		/// </summary>
		/// <remarks>选择题专用</remarks>
		[Description("字母")]
		Letter = 4,

		/// <summary>
		/// 判断题
		/// </summary>
		/// <remarks>对错号专用</remarks>
		[Description("对错符号")]
		Judge = 5,

		/// <summary>
		/// 纯数字
		/// </summary>
		[Description("纯数字")]
		Number = 6,

		/// <summary>
		/// 符号
		/// </summary>
		/// <remarks>逻辑符号</remarks>
		[Description("逻辑符号")]
		Mark = 7,

		/// <summary>
		/// 比较符号
		/// </summary>
		/// <remarks>判断大小或等于</remarks>
		[Description("比较符号")]
		CompareMark = 8,

		/// <summary>
		/// 中文数字混合
		/// </summary>
		[Description("中文数字混合")]
		ChineseNumberMix = 9
	}
}
