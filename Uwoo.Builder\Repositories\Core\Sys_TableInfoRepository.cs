﻿using Uwoo.Builder.IRepositories.Core;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Entity.DomainModels.Core;

namespace Uwoo.Builder.Repositories.Core
{
	public partial class Sys_TableInfoRepository : RepositoryBase<Sys_TableInfo>, ISys_TableInfoRepository
	{
		public Sys_TableInfoRepository(VOLContext dbContext)
		: base(dbContext)
		{

		}
		public static ISys_TableInfoRepository GetService
		{
			get { return AutofacContainerModule.GetService<ISys_TableInfoRepository>(); }
		}
	}
}

