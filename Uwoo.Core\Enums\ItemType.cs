using System.ComponentModel;

namespace Uwoo.Core.Enums
{
    public enum ItemType
    {
        /// <summary>
        /// 多项选择题
        /// </summary>
        [Description("多项选择题")]
        MultipleChoice = 10,
        /// <summary>
        /// 画圈题
        /// </summary>
        [Description("画圈题")]
        Circle = 101,
        /// <summary>
        /// 判断题
        /// </summary>
        [Description("判断题")]
        TrueOrFalse = 11,
        /// <summary>
        /// 单项选择题
        /// </summary>
        [Description("单项选择题")]
        SingleChoice = 2,
        /// <summary>
        /// 简答题
        /// </summary>
        [Description("简答题")]
        ShortAnswer = 23,
        /// <summary>
        /// 主观题
        /// </summary>
        [Description("主观题")]
        Subjective = 36,
        /// <summary>
        /// 可变行列表
        /// </summary>
        [Description("可变行列表")]
        VariableRowList = 40,
        /// <summary>
        /// 可变行填空
        /// </summary>
        [Description("可变行填空")]
        VariableRowFillInBlank = 41,
        /// <summary>
        /// 不可变行填空
        /// </summary>
        [Description("不可变行填空")]
        ImmutableRowFillInBlank = 42,
        /// <summary>
        /// 可增加填空
        /// </summary>
        [Description("可增加填空")]
        AddableFillInBlank = 43,
        /// <summary>
        /// 可选择填空
        /// </summary>
        [Description("可选择填空")]
        SelectableFillInBlank = 44,
        /// <summary>
        /// 可为空填空
        /// </summary>
        [Description("可为空填空")]
        NullableFillInBlank = 45,
        /// <summary>
        /// 可下拉选择选择
        /// </summary>
        [Description("可下拉选择选择")]
        DropdownSelect = 46,
        /// <summary>
        /// 思考问答题
        /// </summary>
        [Description("思考问答题")]
        ThinkingQuestion = 47,
        /// <summary>
        /// 不可变行表格
        /// </summary>
        [Description("不可变行表格")]
        ImmutableRowTable = 48,
        /// <summary>
        /// 问答题
        /// </summary>
        [Description("问答题")]
        QuestionAndAnswer = 49,
        /// <summary>
        /// 填空题
        /// </summary>
        [Description("填空题")]
        FillInBlank = 5,
        /// <summary>
        /// 连线题
        /// </summary>
        [Description("连线题")]
        ConnectTheDots = 50,
        /// <summary>
        /// 表格题
        /// </summary>
        [Description("表格题")]
        Table = 51,
        /// <summary>
        /// 选词填空题
        /// </summary>
        [Description("选词填空题")]
        WordSelectionFillInBlank = 52,
        /// <summary>
        /// 拖拽题
        /// </summary>
        [Description("拖拽题")]
        DragAndDrop = 53,
        /// <summary>
        /// 图片填空题
        /// </summary>
        [Description("图片填空题")]
        ImageFillInBlank = 60,
        /// <summary>
        /// 图片题
        /// </summary>
        [Description("图片题")]
        Image = 61,
        /// <summary>
        /// 多项单选题
        /// </summary>
        [Description("多项单选题")]
        MultipleSingleChoice = 70,
        /// <summary>
        /// 特殊选择题
        /// </summary>
        [Description("特殊选择题")]
        SpecialChoice = 71,
        /// <summary>
        /// 填空判断题
        /// </summary>
        [Description("填空判断题")]
        FillInBlankJudgement = 72,
        /// <summary>
        /// 组合题型
        /// </summary>
        [Description("组合题型")]
        Combination = 73,

        [Description("语音题")]
        VoiceItem = 201,
    }
}