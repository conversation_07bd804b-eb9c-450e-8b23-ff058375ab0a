﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// AI_对话记录
	/// </summary>
	[Table("AI_DialogueContentRecord")]
    public class AI_DialogueContentRecord
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// key（我们自己组成的缓存Key)
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// 问
        /// </summary>
        public string Ask { get; set; }

        /// <summary>
        /// 答
        /// </summary>
        public string Answer { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 消息类型(1系统消息、2用户消息)
        /// </summary>
        public int? MessageType { get; set; }

        /// <summary>
        /// 业务Id（如：智能体任务Id）
        /// </summary>
        public string BusinessId { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }
    }
}
