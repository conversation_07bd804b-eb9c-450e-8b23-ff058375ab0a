﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Question
{
    /// <summary>
    /// 题库_题目配置实体
    /// </summary>
    [Table("Exam_QuestionConfig")]
    public class Exam_QuestionConfig
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 题库_题目Id
        /// </summary>
        public string QuestionId { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 内容选项，例如 A、B、C、D 等等
        /// </summary>
        public string Option { get; set; }

        /// <summary>
        /// 错因Id
        /// </summary>
        public string ErrorId { get; set; }

        /// <summary>
        /// 类型(1答案、2多答案(同义答案)、3选项、4特殊题型配置、5错因、6错因汇总、7关联性错因（填空题）、8评分标准、9个人分析设置)
        /// </summary>
        public int? Type { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? Sort { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }
    }
}
