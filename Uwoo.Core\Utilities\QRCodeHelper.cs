﻿using Net.Codecrete.QrCodeGenerator;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.Extensions;

namespace Uwoo.Core.Utilities
{
	/// <summary>
	/// 二维码生成帮助类
	/// </summary>
	public class QRCodeHelper
	{
		#region 生成二维码

		/// <summary>
		/// 生成二维码，默认边长为250px
		/// </summary>
		/// <param name="content">二维码内容</param>
		/// <returns></returns>
		public static SKImage BuildQRCode(string content)
		{
			return BuildQRCode(content, 10, SKColors.White, SKColors.Black);
		}

		/// <summary>
		/// 生成二维码,自定义边长
		/// </summary>
		/// <param name="content">二维码内容</param>
		/// <param name="imgSize">二维码边长px</param>
		/// <returns></returns>
		public static SKImage BuildQRCode(string content, int imgSize)
		{
			return BuildQRCode(content, imgSize, SKColors.White, SKColors.Black);
		}

		/// <summary>
		/// 生成二维码
		/// 注：自定义边长以及颜色
		/// </summary>
		/// <param name="content">二维码内容</param>
		/// <param name="imgSize">二维码边长px</param>
		/// <param name="background">二维码底色</param>
		/// <param name="foreground">二维码前景色</param>
		/// <returns></returns>
		public static SKImage BuildQRCode(string content, int imgSize, SKColor background, SKColor foreground)
		{
			return BuildQRCode_Logo(content, imgSize, background, foreground, null);
		}

		/// <summary>
		/// 生成二维码并添加Logo
		/// 注：默认生成边长为250px的二维码
		/// </summary>
		/// <param name="content">二维码内容</param>
		/// <param name="logo">logo图片</param>
		/// <returns></returns>
		public static SKImage BuildQRCode_Logo(string content, SKBitmap logo)
		{
			return BuildQRCode_Logo(content, 10, SKColors.White, SKColors.Black, logo);
		}

		/// <summary>
		/// 生成二维码并添加Logo
		/// 注：自定义边长
		/// </summary>
		/// <param name="content">二维码内容</param>
		/// <param name="imgSize">二维码边长px</param>
		/// <param name="logo">logo图片</param>
		/// <returns></returns>
		public static SKImage BuildQRCode_Logo(string content, int imgSize, SKBitmap logo)
		{
			return BuildQRCode_Logo(content, imgSize, SKColors.White, SKColors.Black, logo);
		}

		/// <summary>
		/// 生成二维码并添加Logo
		/// 注：自定义边长以及颜色
		/// </summary>
		/// <param name="content">二维码内容</param>
		/// <param name="imgSize">二维码边长px</param>
		/// <param name="background">二维码底色</param>
		/// <param name="foreground">二维码前景色</param>
		/// <param name="logo">logo图片</param>
		/// <returns></returns>
		public static SKImage BuildQRCode_Logo(string content, int imgSize, SKColor background, SKColor foreground, SKBitmap logo)
		{
			var qr = QrCode.EncodeText(content, QrCode.Ecc.High);
			var bitmap = qr.ToBitmap(imgSize, 1, foreground, background);
			var img = SKImage.FromBitmap(bitmap);
			return img;
		}

		#endregion

		#region 生成条形码

		/// <summary>
		/// 生成条形码
		/// 注：默认宽150px,高50px
		/// </summary>
		/// <param name="content">条形码内容</param>
		/// <returns></returns>
		public static SKImage BuildBarCode(string content)
		{
			return BuildBarCode(content, 150, 50);
		}

		/// <summary>
		/// 生成条形码
		/// 注：自定义尺寸
		/// </summary>
		/// <param name="content">条形码内容</param>
		/// <param name="width">宽度px</param>
		/// <param name="height">高度px</param>
		/// <returns></returns>
		public static SKImage BuildBarCode(string content, int width, int height)
		{
			throw new Exception("暂不支持!");
		}

		#endregion

		#region 读取码内容

		/// <summary>
		/// 从二维码读取内容
		/// </summary>
		/// <param name="image">二维码</param>
		/// <returns></returns>
		public static string ReadContent(SKBitmap image)
		{
			throw new Exception("暂不支持!");
		}

		#endregion
	}
}
