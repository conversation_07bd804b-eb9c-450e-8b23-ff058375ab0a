using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Collections.Specialized;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Uwoo.Core.Utilities
{
    public static class SignUtil
    {
        /// <summary>
        /// 计算签名
        /// </summary>
        /// <param name="ak">AccessKey</param>
        /// <param name="sk">SecretKey</param>
        /// <param name="method">请求方法：GET/POST等</param>
        /// <param name="path">接口Path（如 /api/v1/knowledge）</param>
        /// <param name="timestamp">时间戳字符串</param>
        /// <param name="body">业务参数（通常为请求体，无则空字符串）</param>
        /// <returns>签名字符串</returns>
        public static string CalcSign(string ak, string sk, string method, string path, string timestamp, string body)
        {
            // 按官方文档顺序拼接签名字符串
            string signStr = $"{method}\n{path}\n{timestamp}\n{body}\n{ak}";
            // HMAC-SHA256 计算签名
            var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(sk));
            var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(signStr));
            return Convert.ToBase64String(hash);
        }
    }

    public class Sign
    {
        private readonly string _region;
        private readonly string _service;
        private readonly string _schema;
        private readonly string _host;
        private readonly string _path;
        private readonly string _ak;
        private readonly string _sk;

        private static readonly Encoding Utf8 = Encoding.UTF8;

        private readonly HttpClient _httpClient;

        public Sign(string region, string service, string schema, string host, string path, string ak, string sk)
        {
            _region = region;
            _service = service;
            _schema = schema;
            _host = host;
            _path = path;
            _ak = ak;
            _sk = sk;
            _httpClient = new HttpClient();
        }

        public HttpResponseMessage Request(HttpMethod method, List<KeyValuePair<string, string>> queryList,
            byte[]? body, string contentType,
            DateTimeOffset date, string action, string version)
        {
            body ??= Array.Empty<byte>();
            if (string.IsNullOrWhiteSpace(contentType))
            {
                contentType = "application/x-www-form-urlencoded";
            }

            string xContentSha256 = ToHexString(HashSha256(body));
            string xDate = date.UtcDateTime.ToString("yyyyMMdd'T'HHmmss'Z'");
            string shortXDate = xDate[..8];
            string signHeader = "host;x-date;x-content-sha256;content-type";

            var realQueryList = new NameValueCollection();
            queryList.ForEach(s => realQueryList.Add(s.Key, s.Value));
            realQueryList.Add("Action", action);
            realQueryList.Add("Version", version);

            var query = string.Join("&", realQueryList.AllKeys.ToImmutableSortedSet().Select(key =>
            {
                var values = realQueryList.GetValues(key)?.ToImmutableSortedSet() ?? ImmutableSortedSet<string>.Empty;
                return string.Join("&",
                    values.Select(value => $"{HttpUtility.UrlEncode(key)}={HttpUtility.UrlEncode(value)}"));
            }));
            string canonicalStringBuilder =
                $"{method}\n" +
                $"{_path}\n" +
                $"{query}\n" +
                $"host:{_host}\n" +
                $"x-date:{xDate}\n" +
                $"x-content-sha256:{xContentSha256}\n" +
                $"content-type:{contentType}\n" +
                $"\n" +
                $"{signHeader}\n" +
                $"{xContentSha256}";

            string hashCanonicalString = ToHexString(HashSha256(Utf8.GetBytes(canonicalStringBuilder)));
            string credentialScope = $"{shortXDate}/{_region}/{_service}/request";
            string signString = $"HMAC-SHA256\n{xDate}\n{credentialScope}\n{hashCanonicalString}";

            byte[] signKey = GenSigningSecretKeyV4(_sk, shortXDate, _region, _service);
            string signature = ToHexString(HmacSha256(signKey, signString));

            Uri url = new Uri($"{_schema}://{_host}{_path}?{query}");//{_schema}://
            var request = new HttpRequestMessage();
            request.Method = method;
            request.RequestUri = url;
            request.Headers.TryAddWithoutValidation("Host", _host);
            request.Headers.Add("X-Date", xDate);
            request.Headers.Add("tenant_id", "2103336394");
            request.Headers.Add("X-Content-Sha256", xContentSha256);
            request.Headers.TryAddWithoutValidation("Authorization",
                $"HMAC-SHA256 Credential={_ak}/{credentialScope}, SignedHeaders={signHeader}, Signature={signature}");
            HttpContent content = new ByteArrayContent(body);
            content.Headers.ContentType = new MediaTypeHeaderValue(contentType);
            request.Content = content;

            return _httpClient.Send(request);
        }

        private byte[] GenSigningSecretKeyV4(string secretKey, string date, string region, string service)
        {
            byte[] kDate = HmacSha256(Utf8.GetBytes(secretKey), date);
            byte[] kRegion = HmacSha256(kDate, region);
            byte[] kService = HmacSha256(kRegion, service);
            return HmacSha256(kService, "request");
        }

        private static byte[] HmacSha256(byte[] secret, string text)
        {
            using HMACSHA256 mac = new HMACSHA256(secret);
            var hash = mac.ComputeHash(Encoding.UTF8.GetBytes(text));
            return hash;
        }

        private static byte[] HashSha256(byte[] data)
        {
            using SHA256 sha = SHA256.Create();
            var hash = sha.ComputeHash(data);
            return hash;
        }

        private static string ToHexString(byte[]? bytes)
        {
            if (bytes == null)
            {
                return "";
            }

            StringBuilder sb = new StringBuilder();
            foreach (var t in bytes)
            {
                sb.Append(t.ToString("X2"));
            }

            return sb.ToString().ToLower();
        }
    }
}