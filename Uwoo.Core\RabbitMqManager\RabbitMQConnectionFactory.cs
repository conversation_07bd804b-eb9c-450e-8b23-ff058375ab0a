using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RabbitMQ.Client;
using Uwoo.Core.Configuration;

namespace Uwoo.Core.RabbitMQ
{
    public class RabbitMQConnectionFactory
    {
        public IConnection CreateConnection()
        {
            var factory = new ConnectionFactory()
            {
                HostName = AppSetting.RabbitMQConfig.HostName,
                UserName = AppSetting.RabbitMQConfig.UserName,
                Password = AppSetting.RabbitMQConfig.Password,
                Port = AppSetting.RabbitMQConfig.Port,
                VirtualHost = AppSetting.RabbitMQConfig.VirtualHost,
                AutomaticRecoveryEnabled = true
            };
            return factory.CreateConnection();
        }
    }
}