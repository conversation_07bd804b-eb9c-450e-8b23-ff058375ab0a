﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Enums
{
    /// <summary>
	/// 拍照设置
	/// </summary>
	public enum PhotoShoot
    {
        /// <summary>
        /// 不上传
        /// </summary>
        [Description("不上传")]
        NoUpload = 1,

        /// <summary>
        /// 必须上传
        /// </summary>
        [Description("必须上传")]
        MustUpload = 2,

        /// <summary>
        /// 可选上传
        /// </summary>
        [Description("可选上传")]
        SelectUpload = 3,

        /// <summary>
        /// 核心素养上传
        /// </summary>
        [Description("核心素养上传")]
        CoreLiteracyUpload = 4
    }
}
