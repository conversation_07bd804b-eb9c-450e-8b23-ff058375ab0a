﻿//   -- Function：RealtimePenStateModel.cs
//   --- Project：Uwoo.ContractModels
//   ---- Remark：
//   ---- Author：Lucifer
//   ------ Date：2023/09/19 16:27

using System.Text.Json.Serialization;

// ReSharper disable once CheckNamespace
namespace X.PenServer.Contracts.Socket
{
	/// <summary>
	/// 实时连接状态
	/// </summary>
	public class RealtimePenStateModel : RealtimeMessageBase
	{
		/// <summary>
		/// 状态
		/// </summary>
		/// <remarks>0:断开 1:连接</remarks>
		[JsonPropertyName("state")]
		[JsonInclude]
		public int State { get; set; }
	}
}