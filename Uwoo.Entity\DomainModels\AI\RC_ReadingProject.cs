using SqlSugar;
using System;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 阅读理解项目化实践项目表
    /// </summary>
    [SugarTable("RC_ReadingProject")]
    public class RC_ReadingProject : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string AgentId { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string Introduce { get; set; }

        /// <summary>
        /// 任务图标
        /// </summary>
        public string TaskLogo { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public string SubjectId { get; set; }

        /// <summary>
        /// 学年
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// 学期（1:上学期，2:下学期）
        /// </summary>
        public int? Term { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string GradeId { get; set; }

        /// <summary>
        /// 是否已发布
        /// </summary>
        public bool IsPublished { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }
    }
}
