﻿using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Core.Utilities.Response;
using UwooAgent.Model.AI.AgentTeachingPlanAnalysis;

namespace UwooAgent.System.IServices.AI
{
    public interface IAgentTeachingPlanAnalysisService
    {
        Task<List<TeachingPlanDTO>> GetTeachingPlanLibrary(string userId,string SubjectId, string GradeId);

        Task<List<PaperDTO>> GetStudentPaperList(string userId, int subjectId,string classId);

        //WebResponseContent Upload(List<Microsoft.AspNetCore.Http.IFormFile> files);
        /// <summary>
        /// 学生成绩分布统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        List<StudentsGradeDistributionModel> StudentsGradeDistribution(StudentGradeDistributionInput input);

        Task<string> GenerateTeachingPlanAnalysis(GenerateTeachingPlanAnalysisInput input);
        Task<string> GetWordContent(string wordFileUrl);
        Task<string> GetXmlDocumentContent(string pptxFileUrl);
    }
}
