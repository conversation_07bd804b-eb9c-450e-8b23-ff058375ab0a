﻿// -- Function：PenMappingRedisService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/30 17:28

namespace Uwoo.Mongo.Services.Redis.Implement;

using Uwoo.Mongo.Interfaces.Redis.Business;

/// <summary>
/// 点阵笔用户映射服务
/// </summary>
public class PenMappingRedisService : RedisService, IPenMappingRedisService
{
    /// <inheritdoc />
    public override string Prefix => RedisKeys.PEN_MAPPING;

    /// <inheritdoc />
    public async Task<string> HGetMappingUserAsync(string mac)
    {
        const string key = "MacToUser";
        return await HGetAsync(key, mac);
    }

    /// <inheritdoc />
    public async Task HSetMappingUserAsync(string mac, string userid)
    {
        const string key = "MacToUser";
        await HSetAsync(key, mac, userid);
    }

    /// <inheritdoc />
    public string HGetMappingUser(string mac)
    {
        const string key = "MacToUser";
        return HGet(key, mac);
    }

    /// <inheritdoc />
    public void HSetMappingUser(string mac, string userid)
    {
        const string key = "MacToUser";
        HSet(key, mac, userid);
    }

    /// <inheritdoc />
    public void HSetPenRole(string mac, int usertype)
    {
        const string key = "PenRole";
        HSet(key, mac, usertype.ToString());
    }

    /// <inheritdoc />
    public void HDeleteMappingUser(string mac)
    {
        const string key = "MacToUser";
        HDelete(key, mac);
    }


	/// <inheritdoc />
	public void HDeletePenRole(string mac)
    {
        const string key = "PenRole";
        HDelete(key, mac);
    }
}