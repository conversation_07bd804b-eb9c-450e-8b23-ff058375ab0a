﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Utilities
{
	public static class SqlsugarHelper
	{
		/// <summary>
		/// 获取分页数据的sql语句    字符串数组>0:查询数据Sql 1:查询总数Sql
		/// </summary>
		/// <param name="sql">查询语句</param>
		/// <param name="pageSize">每页的条数</param>
		/// <param name="currPage">当前页码</param>
		/// <returns>字符串数组>0:查询数据Sql 1:查询总数Sql</returns>
		public static string GetPageSql(string sql, int pageSize, int currPage)
		{
			sql = sql.ToLower();
			//验证select from
			string patternCount = @"^select (?:(?!select|from)[\s\S])*(\(select (?:(?!from)[\s\S])* from [^\)]*\)(?:(?!select|from)[^\(])*)*from";

			//获取order by的字符串
			string orderBySql = sql.Substring(sql.LastIndexOf("order by"));

			//获取select 到 from 
			string selectSql = System.Text.RegularExpressions.Regex.Matches(sql, patternCount)[0].Value;
			selectSql = selectSql.Substring(0, selectSql.LastIndexOf("from"));

			string Special = "$_^*";

			sql += Special;
			sql = sql.Replace(orderBySql + Special, "");

			//查询的字段
			string fieldSql = selectSql + ",ROW_NUMBER() OVER (" + orderBySql + ") as rank from";

			sql = "select * from (" + System.Text.RegularExpressions.Regex.Replace(sql, patternCount, fieldSql) + ") as t where t.rank between " + (pageSize * (currPage - 1) + 1) + " and " + (pageSize * currPage);
			return sql;
		}

		/// <summary>
		/// 获取分页数据的sql语句    字符串数组>0:查询数据Sql 1:查询总数Sql
		/// </summary>
		/// <param name="sql">查询语句</param>
		/// <param name="pageSize">每页的条数</param>
		/// <param name="currPage">当前页码</param>
		/// <returns>字符串数组>0:查询数据Sql 1:查询总数Sql</returns>
		public static string GetCountSql(string sql)
		{
			return string.Format(" select count(1) from ({0}) cc ", sql);
		}

		#region 主库查询方法扩展

		/// <summary>
		/// 主库查询
		/// </summary>
		public static T MasterFirst<T>(this ISugarQueryable<T> sugarQueryable)
		{
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = true;
			T t = sugarQueryable.First();
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = false;
			return t;
		}

		/// <summary>
		/// 主库查询
		/// </summary>
		public static T MasterFirst<T>(this ISugarQueryable<T> sugarQueryable, Expression<Func<T, bool>> expression)
		{
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = true;
			T t = sugarQueryable.First(expression);
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = false;
			return t;
		}

		/// <summary>
		/// 主库查询
		/// </summary>
		public static bool MasterAny<T>(this ISugarQueryable<T> sugarQueryable, Expression<Func<T, bool>> expression = null)
		{
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = true;
			bool t = expression == null ? sugarQueryable.Any() : sugarQueryable.Any(expression);
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = false;
			return t;
		}

		/// <summary>
		/// 主库查询
		/// </summary>
		public static List<T> MasterToList<T>(this ISugarQueryable<T> sugarQueryable)
		{
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = true;
			List<T> list = sugarQueryable.ToList();
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = false;
			return list;
		}

		/// <summary>
		/// 主库查询
		/// </summary>
		public static List<T> MasterToPageList<T>(this ISugarQueryable<T> sugarQueryable, int pageIndex, int pageSize, ref int totalNumber)
		{
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = true;
			List<T> list = sugarQueryable.ToPageList(pageIndex, pageSize, ref totalNumber);
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = false;
			return list;
		}


		/// <summary>
		/// 主库查询
		/// </summary>
		public static TResult MasterMax<T, TResult>(this ISugarQueryable<T> sugarQueryable, Expression<Func<T, TResult>> expression)
		{
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = true;
			TResult t = sugarQueryable.Max(expression);
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = false;
			return t;
		}

		/// <summary>
		/// 主库查询
		/// </summary>
		public static TResult MasterSum<T, TResult>(this ISugarQueryable<T> sugarQueryable, Expression<Func<T, TResult>> expression)
		{
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = true;
			TResult t = sugarQueryable.Sum(expression);
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = false;
			return t;
		}

		/// <summary>
		/// 主库查询
		/// </summary>
		public static int MasterCount<T>(this ISugarQueryable<T> sugarQueryable)
		{
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = true;
			int t = sugarQueryable.Count();
			sugarQueryable.Context.Ado.IsDisableMasterSlaveSeparation = false;
			return t;
		}
		#endregion
	}
}
