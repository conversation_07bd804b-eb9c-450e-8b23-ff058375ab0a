﻿using Confluent.Kafka;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.Const;
using Uwoo.Core.Enums;
using Uwoo.Core.Extensions;
using Uwoo.Core.Services;

namespace Uwoo.Core.Middleware
{

    public class ExceptionHandlerMiddleWare
    {
        private readonly RequestDelegate next;
        public ExceptionHandlerMiddleWare(RequestDelegate next)
        {
            this.next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            try
            {
                context.Request.EnableBuffering();
                (context.RequestServices.GetService(typeof(ActionObserver)) as ActionObserver).RequestDate = DateTime.Now;
                await next(context);
                //app.UseMiddleware<ExceptionHandlerMiddleWare>()放在  app.UseRouting()后才可以在await next(context);前执行
                Endpoint endpoint = context.Features.Get<IEndpointFeature>()?.Endpoint;
                if (endpoint != null && endpoint is RouteEndpoint routeEndpoint)
                {
                    ActionLog log = endpoint.Metadata.GetMetadata<ActionLog>();
                    if (log != null && log.Write)
                    {
                        Logger.Add(log?.LogType, null, null, null, status: LoggerStatus.Info);
                    }
                }
                else
                {
                    Logger.Info(LoggerType.Info);
                }
            }
            catch (Exception exception)
            {
                //sse接口不处理异常信息
                if (context.Request.Path.HasValue
                    && context.Request.Path.Value != "/AgentStudentOralCommunication/AgentStudentOralCommunication/AgentStudentOralCommunicationDialogue"
                    && context.Request.Path.Value != "/AgentIntelligentQuestion/AgentIntelligentQuestion/GenerateQuestionsStream"
                    && context.Request.Path.Value != "/api/AgentRCStudent/ReadingDialogue"
                    && context.Request.Path.Value != "/api/AgentRCStudent/ReadingKnowledge"
                    && context.Request.Path.Value != "/AgentCommon/AgentCommon/TextChangeVoice"
                    && context.Request.Path.Value != "/AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/CreateTeachingPlan"
                    && context.Request.Path.Value != "/AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/TeachingPlanCreateRecordOptimize"
                    && context.Request.Path.Value != "/AgentTeacherProject/AgentTeacherProject/AICreateTaskProperty"
                    && context.Request.Path.Value != "/AgentStudentProject/AgentStudentProject/StudentDialogue"
                    && context.Request.Path.Value != "/AgentStudentProject/AgentStudentProject/StudentKnowledge"
                    && context.Request.Path.Value != "/AgentStudentModeling/AgentStudentModeling/StudentModelingDialogue"
                    && context.Request.Path.Value != "/AgentStudentModeling/AgentStudentModeling/StudentModelingKnowledge"
                    && context.Request.Path.Value != "/AgentStudentModeling/AgentStudentModeling/StudentModelingComprehend"
                    && context.Request.Path.Value != "/AgentStudentModeling/AgentStudentModeling/StudentModelingHypothesis"
                    && context.Request.Path.Value != "/AgentStudentModeling/AgentStudentModeling/StudentModelingEvaluate")
                {
                    var env = context.RequestServices.GetService(typeof(IWebHostEnvironment)) as IWebHostEnvironment;
                    string message = exception.Message + exception.InnerException;
                    Logger.Error(LoggerType.Exception, message);
                    if (!env.IsDevelopment())
                    {
                        //message = "服务器处理异常";
                    }
                    else
                    {
                        Console.WriteLine($"服务器处理出现异常:{message}");
                    }
                    context.Response.StatusCode = 500;
                    context.Response.ContentType = ApplicationContentType.JSON;
                    await context.Response.WriteAsync(new { Msg = message, Success = false, ErrorCode = 0 }.Serialize(), Encoding.UTF8);
                }
            }
        }
    }
}
