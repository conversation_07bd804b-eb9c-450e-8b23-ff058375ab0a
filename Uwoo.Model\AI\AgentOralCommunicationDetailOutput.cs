﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 智能体_口语交际详情输出
    /// </summary>
    public class AgentOralCommunicationDetailOutput
    {
        /// <summary>
        /// 是否存在问答数据
        /// </summary>
        public bool IsData { get; set; }

        /// <summary>
        /// 智能体Log
        /// </summary>
        public string? Logo { get; set; }

        /// <summary>
        /// 口语交际数据
        /// </summary>
        public SaveTeacherOralCommunicationInput OralCommunicationInfo { get; set; } = new SaveTeacherOralCommunicationInput();
    }
}
