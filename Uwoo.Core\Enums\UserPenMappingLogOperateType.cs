﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Enums
{
	/// <summary>
	/// <para><AUTHOR> <PERSON></para>
	/// <para>@Date   2024-01-10 14:05</para>
	/// <para>@Description 设备管理操作日志类型</para>
	/// </summary>
	public enum UserPenMappingLogOperateType
	{
		/// <summary>
		/// 绑定
		/// </summary>
		Bind = 1,
		/// <summary>
		/// 绑定变更
		/// </summary>
		BindChange = 2,
		/// <summary>
		/// 解绑
		/// </summary>
		Unbind = 3,
		/// <summary>
		/// 版本更新
		/// </summary>
		VersionChange = 4,
		/// <summary>
		/// 管理员批量导入新生
		/// </summary>
		BatchImportFromAdmin = 5
	}
}
