﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// 智能体_建模阶段
	/// </summary>
	[Table("AI_ModelingStage")]
    public class AI_ModelingStage
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 建模Id
        /// </summary>
        public string ModelingId { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string Describe { get; set; }

        /// <summary>
        /// 是否默认(默认禁止删除)
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 默认阶段类型(1问题理解、2假设分析、3模型评价)
        /// </summary>
        public int DefaultStageType { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
