﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Model
{
	public class AjaxResult
	{
		public AjaxResult()
		{
			this.Success = true;
		}

		/// <summary>
		/// 是否成功
		/// </summary>
		public bool Success { get; set; }

		/// <summary>
		/// 错误代码
		/// </summary>
		public int ErrorCode { get; set; }

		/// <summary>
		/// 返回消息
		/// </summary>
		public string? Msg { get; set; }
	}

	public class AjaxResult<T> : AjaxResult
	{
		/// <summary>
		/// 返回数据
		/// </summary>
		public T? Data { get; set; }

		/// <summary>
		/// 总数据量（仅分页时有效）
		/// </summary>
		public int Total { get; set; }
	}
}
