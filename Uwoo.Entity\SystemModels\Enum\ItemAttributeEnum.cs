﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Entity.SystemModels.Enum
{
    /// <summary>
    /// 题目属性
    /// </summary>
    public enum ItemAttributeEnum
    {
        /// <summary>
        /// 学习水平
        /// </summary>
        [Description("学习水平")]
        LearningLevel = 1,

        /// <summary>
        /// 难度系数
        /// </summary>
        [Description("难度系数")]
        Difficulty = 2,

        /// <summary>
        /// 内容领域
        /// </summary>
        [Description("内容领域")]
        ContentArea = 3,

        /// <summary>
        /// 核心素养
        /// </summary>
        [Description("核心素养")]
        CoreCompetencies = 4,

        /// <summary>
        /// 主题
        /// </summary>
        [Description("主题")]
        Theme = 5,

        /// <summary>
        /// 情景
        /// </summary>
        [Description("情景")]
        Scene = 6,

        /// <summary>
        /// 单元目标
        /// </summary>
        [Description("单元目标")]
        UnitObjectives = 7,

        /// <summary>
        /// 章节目标
        /// </summary>
        [Description("章节目标")]
        ChapterObjectives = 8,

        /// <summary>
        /// 题目目标
        /// </summary>
        [Description("题目目标")]
        TopicObjective = 9,
    }
}
