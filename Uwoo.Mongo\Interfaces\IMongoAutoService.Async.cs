﻿// -- Function：IMongoAutoAsyncService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/24 16:12

using System.Linq.Expressions;
using MongoDB.Driver;
using Uwoo.Mongo.Models;

namespace Uwoo.Mongo.Interfaces;

/// <summary>
/// Mongo服务接口
/// </summary>
/// <remarks>自动根据年份分表</remarks>
/// <typeparam name="T">实体</typeparam>
public partial interface IMongoAutoService<T> where T : MongoBaseModel, new()
{
    /// <summary>
    /// 通过主键获取数据
    /// </summary>
    /// <param name="id">主键id</param>
    /// <param name="colname">集合名称</param>
    /// <returns></returns>
    Task<T> GetAsync(long id, string colname = "");

    /// <summary>
    /// 通过条件查询单条数据
    /// </summary>
    /// <param name="predicate">条件</param>
    /// <param name="colname">集合名称</param>
    /// <returns></returns>
    Task<T> GetAsync(Expression<Func<T, bool>> predicate = null, string colname = "");

    /// <summary>
    /// 通过条件查询数据列表
    /// </summary>
    /// <param name="predicate">条件</param>
    /// <param name="colname">集合名称</param>
    /// <param name="options">查询选项</param>
    /// <returns></returns>
    Task<List<T>> GetAllAsync(Expression<Func<T, bool>> predicate = null, string colname = "", FindOptions options = null);

    /// <summary>
    /// 添加单条数据
    /// </summary>
    /// <param name="entity">数据实体</param>
    /// <param name="colname">集合名称</param>
    /// <returns></returns>
    Task AddAsync(T entity, string colname = "");

    /// <summary>
    /// 批量添加数据
    /// </summary>
    /// <param name="entities">数据实体列表</param>
    /// <param name="colname">集合名称</param>
    /// <returns></returns>
    Task AddManyAsync(IEnumerable<T> entities, string colname = "");

    /// <summary>
    /// 根据条件更新单条数据
    /// </summary>
    /// <param name="predicate">条件</param>
    /// <param name="entity">数据实体</param>
    /// <param name="colname">集合名称</param>
    /// <returns></returns>
    Task<bool> UpdateAsync(Expression<Func<T, bool>> predicate, T entity, string colname = "");

    /// <summary>
    /// 根据主键更新单条数据
    /// </summary>
    /// <param name="id">主键id</param>
    /// <param name="entity">数据实体</param>
    /// <param name="colname">集合名称</param>
    /// <returns></returns>
    Task<bool> UpdateAsync(long id, T entity, string colname = "");

    /// <summary>
    /// 根据id删除单条数据
    /// </summary>
    /// <param name="id">主键id</param>
    /// <param name="colname">集合名称</param>
    /// <returns></returns>
    Task<bool> DeleteAsync(long id, string colname = "");

    /// <summary>
    /// 根据条件删除单条数据
    /// </summary>
    /// <param name="predicate">条件</param>
    /// <param name="colname">集合名称</param>
    /// <returns></returns>
    Task<bool> DeleteAsync(Expression<Func<T, bool>> predicate, string colname = "");

    /// <summary>
    /// 根据条件批量删除数据
    /// </summary>
    /// <param name="predicate">条件</param>
    /// <param name="colname">集合名称</param>
    /// <returns></returns>
    Task<bool> DeleteManyAsync(Expression<Func<T, bool>> predicate, string colname = "");
}