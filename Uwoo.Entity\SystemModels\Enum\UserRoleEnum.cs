﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Entity.SystemModels.Enum
{
    /// <summary>
    /// 用户角色枚举类
    /// </summary>
    public enum UserRoleEnum
    {
        /// <summary>
        /// 超级管理员
        /// </summary>
        [Description("超级管理员")]
        SuperAdmin = 1,

        /// <summary>
        /// 全国管理员
        /// </summary>
        [Description("全国管理员")]
        CountryAdmin = 6,

        /// <summary>
        /// 省管理员
        /// </summary>
        [Description("省管理员")]
        ProvinceAdmin = 7,

        /// <summary>
        /// 市管理员
        /// </summary>
        [Description("市管理员")]
        CityAdmin = 8,

        /// <summary>
        /// 区管理员
        /// </summary>
        [Description("区管理员")]
        DistrictAdmin = 9,

        /// <summary>
        /// 校管理员
        /// </summary>
        [Description("校管理员")]
        SchoolAdmin = 10,

        /// <summary>
        /// 教师
        /// </summary>
        [Description("教师")]
        Teacher = 11,

        /// <summary>
        /// 学生
        /// </summary>
        [Description("学生")]
        Student = 12,
    }
}
