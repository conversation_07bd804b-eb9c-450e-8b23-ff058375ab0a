﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.System.IRepositories.AI;

namespace UwooAgent.System.Repositories.AI
{
    public class AgentStudentReadingComprehensionRepository : RepositoryBase<RC_StudentTaskRecord>, IAgentStudentReadingComprehensionRepository
    {
        public AgentStudentReadingComprehensionRepository(VOLContext dbContext) : base(dbContext)
        {

        }
    }
}
