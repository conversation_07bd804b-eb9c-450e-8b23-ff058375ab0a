﻿using Coldairarrow.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.CustomException;
using UwooAgent.Entity.DomainModels.School;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_教师首页控制器
    /// </summary>
    [Route("/AgentTeacherHomePage/[controller]/[action]")]
    [ApiController]
    public class AgentTeacherHomePageController : ApiBaseController<IAgentTeacherHomePageService>
    {
        #region DI
        private readonly IAgentTeacherHomePageService _agentTeacherHomePageService;
        public AgentTeacherHomePageController(IAgentTeacherHomePageService agentTeacherHomePageService)
        {
            _agentTeacherHomePageService = agentTeacherHomePageService;
        }
        #endregion

        /// <summary>
        /// 获取智能体列表信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<AgentTeacherHomePageListOutPut> GetAgentListInfo(AgentTeacherHomePageListInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId) ||
                string.IsNullOrEmpty(input.SubjectId) ||
                string.IsNullOrEmpty(input.SchoolId))
            {
                throw new BusException("参数异常!", 801);
            }
            return await _agentTeacherHomePageService.GetAgentListInfo(input);
        }

        /// <summary>
        /// 智能体收藏
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task AgentCollection(AgentTeacherCollectionInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId) || string.IsNullOrEmpty(input.AgentId))
            {
                throw new BusException("参数异常!", 801);
            }
            await _agentTeacherHomePageService.AgentCollection(input);
        }
    }
}
