﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Extensions
{
	public static class DateTimeExtension
	{
		/// <summary>
		/// 转为转换为Unix时间戳格式(精确到秒)
		/// </summary>
		/// <param name="time">时间</param>
		/// <returns></returns>
		public static int ToUnixTimeStamp(this DateTime time)
		{
			DateTime startTime = new DateTime(1970, 1, 1).ToLocalTime();
			return (int)(time - startTime).TotalSeconds;
		}
	}
}
