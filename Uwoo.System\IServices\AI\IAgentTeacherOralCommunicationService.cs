﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_教师端口语交际
    /// </summary>
    public interface IAgentTeacherOralCommunicationService : IService<AI_OralCommunicationTask>
    {
        /// <summary>
        /// 教师保存/编辑口语交际
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<string> SaveTeacherOralCommunication(SaveTeacherOralCommunicationInput input);

        /// <summary>
        /// 获取智能体口语交际详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<AgentOralCommunicationDetailOutput> GetOralCommunicationDetail(AgentOralCommunicationDetailInput input);

        /// <summary>
        /// 删除口语交际任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task DelOralCommunication(DelOralCommunicationInput input);

        /// <summary>
        /// 智能体_教师端口语交际列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PageReturn<AgentTeacherOralCommunicationListOutput>> GetOralCommunicationList(AgentTeacherOralCommunicationListInput input);

        /// <summary>
        /// 智能体_教师端获取智能体任务分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task<GetOralCommunicationAnalyseOutput> GetOralCommunicationAnalyse(GetOralCommunicationAnalyseInput input);

        /// <summary>
        /// 教师获取学生口语交际评估结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetOralCommunicationStudentResultOutput> GetOralCommunicationStudentResult(GetOralCommunicationStudentResultInput input);

        /// <summary>
        /// 发布口语交际
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task PublishOralCommunication(PublishOralCommunicationInput input);

        /// <summary>
        /// 撤销发布口语交际
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task DelPublishOralCommunication(DelPublishOralCommunicationInput input);
    }
}
