﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取项目化实践综合分析输出
    /// </summary>
    public class GetProjectSynthesizeAnalyseOutput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 项目化实践Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目化实践名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目化实践Logo
        /// </summary>
        public string? ProjectLogo { get; set; }

        /// <summary>
        /// 项目化实践背景
        /// </summary>
        public string? ProjectIntroduce { get; set; }

        /// <summary>
        /// 平均等第
        /// </summary>
        public string? AvgLevel { get; set; }

        /// <summary>
        /// 平均等第基于多少份首次提交计算
        /// </summary>
        public int AvgLevelCount { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal Finish { get; set; }

        /// <summary>
        /// 提交率
        /// </summary>
        public decimal Submit { get; set; }

        /// <summary>
        /// 需提交数量
        /// </summary>
        public decimal SubmitCount { get; set; }

        /// <summary>
        /// 参与率
        /// </summary>
        public decimal Participation { get; set; }

        /// <summary>
        /// 参与人数
        /// </summary>
        public int ParticipationCount { get; set; }

        /// <summary>
        /// 班级学生人数
        /// </summary>
        public int StudentCount { get; set; }

        /// <summary>
        /// 阶段完成情况
        /// </summary>
        public List<GetProjectSynthesizeAnalyseStageOutput> StageInfo { get; set; } = new List<GetProjectSynthesizeAnalyseStageOutput>();

        /// <summary>
        /// 阶段任务平均分
        /// </summary>
        public List<GetProjectSynthesizeAnalyseScoreOutput> StageTaskAvgScore { get; set; } = new List<GetProjectSynthesizeAnalyseScoreOutput>();
    }

    /// <summary>
    /// 获取项目化实践综合分析阶段输出
    /// </summary>
    public class GetProjectSynthesizeAnalyseStageOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 任务数量
        /// </summary>
        public int TaskCount { get; set; }

        /// <summary>
        /// 完成数量
        /// </summary>
        public decimal FinishCount { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal Finish { get; set; }
    }

    /// <summary>
    /// 获取项目化实践综合分析平均分输出
    /// </summary>
    public class GetProjectSynthesizeAnalyseScoreOutput
    {
        /// <summary>
        /// 阶段任务名称
        /// </summary>
        public string? StageTaskName { get; set; }

        /// <summary>
        /// 平均分
        /// </summary>
        public decimal? AvgScore { get; set; }
    }
}
