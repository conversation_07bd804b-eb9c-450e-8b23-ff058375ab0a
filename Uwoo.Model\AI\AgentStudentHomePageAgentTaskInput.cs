﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 智能体学生端首页输入
    /// </summary>
    public class AgentStudentHomePageAgentTaskInput
    {
        /// <summary>
        /// 页码（默认1）
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 每页数量（默认10）
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public string? SchoolId { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 智能体任务状态（1进行中、2已结束）
        /// </summary>
        public int AgentTaskState { get; set; }
    }
}
