/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
namespace Uwoo.Entity.DomainModels
{
	[Entity(TableCnName = "审批流程配置", TableName = "Sys_WorkFlow", DetailTable = new Type[] { typeof(Sys_WorkFlowStep) }, DetailTableCnName = "审批步骤")]
	public partial class Sys_WorkFlow : BaseEntity
	{
		/// <summary>
		///
		/// </summary>
		[SugarColumn(IsPrimaryKey = true)]
		[Key]
		[Display(Name = "WorkFlow_Id")]
		[Column(TypeName = "uniqueidentifier")]
		[Required(AllowEmptyStrings = false)]
		public Guid WorkFlow_Id { get; set; }

		/// <summary>
		///流程名称
		/// </summary>
		[Display(Name = "流程名称")]
		[MaxLength(200)]
		[Column(TypeName = "nvarchar(200)")]
		[Editable(true)]
		[Required(AllowEmptyStrings = false)]
		public string WorkName { get; set; }

		/// <summary>
		///表名
		/// </summary>
		[Display(Name = "表名")]
		[MaxLength(200)]
		[Column(TypeName = "nvarchar(200)")]
		[Editable(true)]
		[Required(AllowEmptyStrings = false)]
		public string WorkTable { get; set; }

		/// <summary>
		///功能菜单
		/// </summary>
		[Display(Name = "功能菜单")]
		[MaxLength(200)]
		[Column(TypeName = "nvarchar(200)")]
		[Editable(true)]
		public string WorkTableName { get; set; }

		/// <summary>
		///权重(相同条件权重大的优先匹配)
		/// </summary>
		[Display(Name = "权重(相同条件权重大的优先匹配)")]
		[Column(TypeName = "int")]
		[Editable(true)]
		public int? Weight { get; set; }

		/// <summary>
		///是否启用
		/// </summary>
		[Display(Name = "是否启用")]
		[Column(TypeName = "tinyint")]
		[Editable(true)]
		public byte? Enable { get; set; }

		/// <summary>
		///审核中数据是否可以编辑
		/// </summary>
		[Display(Name = "审核中数据是否可以编辑")]
		[Column(TypeName = "int")]
		[Editable(true)]
		public int? AuditingEdit { get; set; }

		/// <summary>
		///节点信息
		/// </summary>
		[Display(Name = "节点信息")]
		[Column(TypeName = "nvarchar(max)")]
		[Editable(true)]
		public string NodeConfig { get; set; }

		/// <summary>
		///连接配置
		/// </summary>
		[Display(Name = "连接配置")]
		[Column(TypeName = "nvarchar(max)")]
		[Editable(true)]
		public string LineConfig { get; set; }

		/// <summary>
		///备注
		/// </summary>
		[Display(Name = "备注")]
		[MaxLength(500)]
		[Column(TypeName = "nvarchar(500)")]
		[Editable(true)]
		public string Remark { get; set; }

		/// <summary>
		///
		/// </summary>
		[Display(Name = "CreateID")]
		[Column(TypeName = "int")]
		public int? CreateID { get; set; }

		/// <summary>
		///创建人
		/// </summary>
		[Display(Name = "创建人")]
		[MaxLength(30)]
		[Column(TypeName = "nvarchar(30)")]
		public string Creator { get; set; }

		/// <summary>
		///创建时间
		/// </summary>
		[Display(Name = "创建时间")]
		[Column(TypeName = "datetime")]
		public DateTime? CreateDate { get; set; }

		/// <summary>
		///
		/// </summary>
		[Display(Name = "ModifyID")]
		[Column(TypeName = "int")]
		public int? ModifyID { get; set; }

		/// <summary>
		///修改人
		/// </summary>
		[Display(Name = "修改人")]
		[MaxLength(30)]
		[Column(TypeName = "nvarchar(30)")]
		public string Modifier { get; set; }

		/// <summary>
		///修改时间
		/// </summary>
		[Display(Name = "修改时间")]
		[Column(TypeName = "datetime")]
		public DateTime? ModifyDate { get; set; }

		[Display(Name = "审批步骤")]
		[ForeignKey("WorkFlow_Id")]
		[Navigate(NavigateType.OneToMany, nameof(WorkFlow_Id), nameof(WorkFlow_Id))]
		public List<Sys_WorkFlowStep> Sys_WorkFlowStep { get; set; }

	}
}