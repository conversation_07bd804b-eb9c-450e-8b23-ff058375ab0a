﻿// -- Function：ObjectiveAnswerResultData.cs
// --- Project：X.PenServer.Contracts
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2024/1/31 16:10
// ReSharper disable once CheckNamespace
namespace X.PenServer.Contracts.Queue;

using System.Text.Json.Serialization;

/// <summary>
/// 客观题作答结果
/// </summary>
/// <remarks>英语单元答题卡</remarks>
public class EnglishUnitObjectiveAnswerResultData : AnswerResultData
{
	/// <summary>
	/// 学生id
	/// </summary>
	[JsonPropertyName(nameof(UserId))]
	[JsonInclude]
	public string UserId { get; set; }
}