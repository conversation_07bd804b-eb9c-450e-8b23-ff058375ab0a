﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Model.JWT;

namespace Uwoo.Model
{
	public class HttpReqResDto
	{
		public string? url { get; set; }
		public string? method { get; set; }
		public string? contentType { get; set; }
		public string? body { get; set; }
		public int totalms { get; set; }
		public string? resData { get; set; }
		public JWTPayload? auth { get; set; }
		public string? content { get; set; }
	}
}
