﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Base
{
    /// <summary>
    /// 用户角色表
    /// </summary>
    [Table("Base_UserRole")]
    public class Base_UserRole
    {

        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public String Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public String CreatorId { get; set; }

        /// <summary>
        /// 否已删除
        /// </summary>
        public bool? Deleted { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public String UserId { get; set; }

        /// <summary>
        /// 角色Id
        /// </summary>
        public String RoleId { get; set; }

    }
}
