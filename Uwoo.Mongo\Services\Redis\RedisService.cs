﻿// -- Function：RedisService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/30 16:00

namespace Uwoo.Mongo.Services.Redis;

using Uwoo.Mongo.Interfaces.Redis;

/// <inheritdoc />
public abstract class RedisService : IRedisService
{
    /// <inheritdoc />
    public abstract string Prefix { get; }

    /// <inheritdoc />
    public async Task SetAsync(string key, object value, TimeSpan? span = null)
    {
        if (span.HasValue)
        {
            await XRedisHelper.SetAsync(Prefix + "|" + key, value, span.Value);
        }
        else
        {
            await XRedisHelper.SetAsync(Prefix + "|" + key, value);
        }
    }

    /// <inheritdoc />
    public async Task<T> GetAsync<T>(string key)
    {
        return await XRedisHelper.GetAsync<T>(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public async Task<string> GetAsync(string key)
    {
        return await XRedisHelper.GetAsync(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(string key)
    {
        await XRedisHelper.DelAsync(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public async Task ExpireAsync(string key, TimeSpan span)
    {
        await XRedisHelper.ExpireAsync(Prefix + "|" + key, span);
    }

    /// <inheritdoc />
    public async Task<bool> IsExistsAsync(string key)
    {
        return await XRedisHelper.ExistsAsync(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public async Task HSetAsync(string key, string field, object value)
    {
        await XRedisHelper.HSetAsync(Prefix + "|" + key, field, value);
    }

    /// <inheritdoc />
    public async Task HSetAsync(string key, string field, string value)
    {
        await XRedisHelper.HSetAsync(Prefix + "|" + key, field, value);
    }

    /// <inheritdoc />
    public async Task<T> HGetAsync<T>(string key, string field)
    {
        return await XRedisHelper.HGetAsync<T>(Prefix + "|" + key, field);
    }

    /// <inheritdoc />
    public async Task<string> HGetAsync(string key, string field)
    {
        return await XRedisHelper.HGetAsync(Prefix + "|" + key, field);
    }

    /// <inheritdoc />
    public async Task HDeleteAsync(string key, string filed)
    {
        await XRedisHelper.HDelAsync(Prefix + "|" + key, filed);
    }

    /// <inheritdoc />
    public async Task<Dictionary<string, T>> HGetAllAsync<T>(string key)
    {
        return await XRedisHelper.HGetAllAsync<T>(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public async Task<Dictionary<string, string>> HGetAllAsync(string key)
    {
        return await XRedisHelper.HGetAllAsync(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public async Task SAddAsync(string key, params string[] value)
    {
        await XRedisHelper.SAddAsync(Prefix + "|" + key, value);
    }

    /// <inheritdoc />
    public async Task SAddAsync<T>(string key, params T[] value)
    {
        await XRedisHelper.SAddAsync(Prefix + "|" + key, value);
    }

    /// <inheritdoc />
    public async Task<List<string>> SMembersAsync(string key)
    {
        var result = await XRedisHelper.SMembersAsync<string>(Prefix + "|" + key);
        return result.ToList();
    }

    /// <inheritdoc />
    public async Task<List<T>> SMembersAsync<T>(string key)
    {
        var result = await XRedisHelper.SMembersAsync<T>(Prefix + "|" + key);
        return result.ToList();
    }

    /// <inheritdoc />
    public async Task SRemoveAsync(string key, params string[] value)
    {
        await XRedisHelper.SRemAsync(Prefix + "|" + key, value);
    }

    /// <inheritdoc />
    public async Task SRemoveAsync<T>(string key, T[] value)
    {
        await XRedisHelper.SRemAsync(Prefix + "|" + key, value);
    }

    /// <inheritdoc />
    public async Task<bool> IsSMemberAsync(string key, object member)
    {
        return await XRedisHelper.SIsMemberAsync(Prefix + "|" + key, member);
    }

    /// <inheritdoc />
    public async Task<string[]> KeysAsync(string pattern)
    {
        return await XRedisHelper.KeysAsync(pattern);
    }

    /// <inheritdoc />
    public void Set(string key, object value, TimeSpan? span = null)
    {
        if (span.HasValue)
        {
            XRedisHelper.Set(Prefix + "|" + key, value, span.Value);
        }
        else
        {
            XRedisHelper.Set(Prefix + "|" + key, value);
        }
    }

    /// <inheritdoc />
    public T Get<T>(string key)
    {
        return XRedisHelper.Get<T>(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public string Get(string key)
    {
        return XRedisHelper.Get(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public void Delete(string key)
    {
        XRedisHelper.Del(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public void Expire(string key, TimeSpan span)
    {
        XRedisHelper.Expire(Prefix + "|" + key, span);
    }

    /// <inheritdoc />
    public bool IsExists(string key)
    {
        return XRedisHelper.Exists(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public void HSet(string key, string field, object value)
    {
        XRedisHelper.HSet(Prefix + "|" + key, field, value);
    }

    /// <inheritdoc />
    public void HSet(string key, string field, string value)
    {
        XRedisHelper.HSet(Prefix + "|" + key, field, value);
    }

    /// <inheritdoc />
    public T HGet<T>(string key, string field)
    {
        return XRedisHelper.HGet<T>(Prefix + "|" + key, field);
    }

    /// <inheritdoc />
    public string HGet(string key, string field)
    {
        return XRedisHelper.HGet(Prefix + "|" + key, field);
    }

    /// <inheritdoc />
    public void HDelete(string key, string filed)
    {
        XRedisHelper.HDel(Prefix + "|" + key, filed);
    }

    /// <inheritdoc />
    public Dictionary<string, T> HGetAll<T>(string key)
    {
        return XRedisHelper.HGetAll<T>(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public Dictionary<string, string> HGetAll(string key)
    {
        return XRedisHelper.HGetAll(Prefix + "|" + key);
    }

    /// <inheritdoc />
    public void SAdd(string key, params string[] value)
    {
        XRedisHelper.SAdd(Prefix + "|" + key, value);
    }

    /// <inheritdoc />
    public void SAdd(string key,  string[] value, TimeSpan? ts = null)
    {
        XRedisHelper.SAdd(Prefix + "|" + key, value);
        if (ts != null)
        {
            XRedisHelper.Expire(Prefix + "|" + key, ts.Value);
        }
    }

	/// <inheritdoc />
	public void SAdd<T>(string key, params T[] value)
    {
        XRedisHelper.SAdd(Prefix + "|" + key, value);
    }

    /// <inheritdoc />
    public List<string> SMembers(string key)
    {
        return XRedisHelper.SMembers<string>(Prefix + "|" + key).ToList();
    }

    /// <inheritdoc />
    public List<T> SMembers<T>(string key)
    {
        return XRedisHelper.SMembers<T>(Prefix + "|" + key).ToList();
    }

    /// <inheritdoc />
    public void SRemove(string key, params string[] value)
    {
        XRedisHelper.SRem(Prefix + "|" + key, value);
    }

    /// <inheritdoc />
    public void SRemove<T>(string key, T[] value)
    {
        XRedisHelper.SRem(Prefix + "|" + key, value);
    }

    /// <inheritdoc />
    public bool IsSMember(string key, object member)
    {
        return XRedisHelper.SIsMember(Prefix + "|" + key, member);
    }

    /// <inheritdoc />
    public string[] Keys(string pattern)
    {
        return XRedisHelper.Keys(pattern);
    }
}