/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 */
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Entity.DomainModels;
using UwooAgent.Entity.DomainModels.Base;
using UwooAgent.Model.SemesterTime;

namespace Uwoo.System.IServices
{
    public interface IBase_SemesterTimeService : IService<Base_SemesterTime>
    {
        /// <summary>
		/// 获取当前学年学期时间
		/// </summary>
		/// <returns></returns>
		Task<NowSemesterTime> GetNowYearSemesterTime();
    }
}
