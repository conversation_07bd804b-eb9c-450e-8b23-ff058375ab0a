﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 豆包我的应用入参
    /// </summary>
    public class DouBaoMyAppDto
    {
        /// <summary>
        /// 推理接入点（Endpoint）ID
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 响应内容是否流式返回：false：模型生成完所有内容后一次性返回结果、true：按 SSE 协议逐块返回模型生成内容，并以一条 data: [DONE] 消息结束
        /// </summary>
        public bool stream { get; set; }

        /// <summary>
        /// 消息体
        /// </summary>
        public List<DouBaoMyAppMessages> messages { get; set; } = new List<DouBaoMyAppMessages>();
    }

    /// <summary>
    /// 豆包我的应用消息体
    /// </summary>
    public class DouBaoMyAppMessages
    {
        /// <summary>
        /// 发出该消息的对话参与者的角色
        /// </summary>
        public string? role { get; set; }

        /// <summary>
        /// 视觉理解模型请求的消息内容，消息发送角色为用户
        /// </summary>
        public string? content { get; set; }
    }
}
