﻿// -- Function：WorkbookPenLogService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2024/3/19 14:35
namespace Uwoo.Mongo.Services.Mongo;

using MongoDB.Driver;
using Uwoo.Contracts.Config;
using Uwoo.Mongo.Infrastructure;
using Uwoo.Mongo.Interfaces.Business;
using Uwoo.Mongo.Models;

/// <inheritdoc cref="IWorkbookPenLogService" />
public class WorkbookPenLogService : MongoAutoService<WorkbookPenLog>, IWorkbookPenLogService
{
	/// <inheritdoc />
	public WorkbookPenLogService(IMongoConfig config) : base(config)
	{
	}

	/// <inheritdoc />
	protected override void CreateIndex(IMongoCollection<WorkbookPenLog> collection)
	{
		var paperid_builder = Builders<WorkbookPenLog>.IndexKeys
			.Ascending(x => x.PaperId)
			.Ascending(x => x.ItemId)
			.Ascending(x => x.UserId)
			.Ascending(x => x.Mac)
			.Ascending(x => x.LineNo)
			.Ascending(x => x.Blank)
			.Ascending(x => x.Page)
			.Ascending(x => x.AddTime);
		collection.Indexes.CreateIndex(paperid_builder, collection.CollectionNamespace.CollectionName + "_PaperId_Key");

		var itemid_builder = Builders<WorkbookPenLog>.IndexKeys
			.Ascending(x => x.ItemId)
			.Ascending(x => x.UserId)
			.Ascending(x => x.Mac)
			.Ascending(x => x.PaperId)
			.Ascending(x => x.LineNo)
			.Ascending(x => x.Blank)
			.Ascending(x => x.PageId)
			.Ascending(x => x.AddTime);
		collection.Indexes.CreateIndex(itemid_builder, collection.CollectionNamespace.CollectionName + "_ItemId_Key");
	}

	/// <inheritdoc />
	public List<WorkbookPenLog> GetWorkbookPenLogListByItemId(string colname,string paperId,string userId,string itemId) 
	{
		var mongo = GetConnection(colname);
		var list = mongo.Find(x => x.PaperId.Equals(paperId, StringComparison.OrdinalIgnoreCase) && x.UserId == userId && x.ItemId == itemId).ToList();
		return list.OrderBy(x => x.Mid).ToList();
	}

	/// <inheritdoc />
	public List<WorkbookPenLog> GetWorkbookPenLogsByUserId(string colname, string paperId, string userId, int? year = null)
	{
		var mongo = GetConnection(colname, year);
		var list = mongo.Find(x => x.PaperId.Equals(paperId, StringComparison.OrdinalIgnoreCase) && x.UserId == userId).ToList();
		return list.OrderBy(x => x.Mid).ToList();
	}

	/// <inheritdoc />
	public void DeleteWorkbookPenLog(string colname, string paperId, List<string> userid)
	{
		var mongo = GetConnection(colname);
		mongo.DeleteMany(x => paperId.Contains(x.PaperId) && userid.Contains(x.UserId));
	}

	/// <inheritdoc />
	public List<string> GetUserList(string colname, string paperId, List<string> userid, int? year = null)
	{
		var mongo = GetConnection(colname, year);
		var builder = Builders<WorkbookPenLog>.Filter;
		var filter = builder.Eq(x => x.PaperId, paperId) & builder.In(x => x.UserId, userid);
		var result = mongo.Distinct(x => x.UserId, filter).ToList();
		return result;
	}

	/// <inheritdoc />
	public List<string> GetUserListByItemId(string colname, string paperId, string itemId, List<string> userid, int? year = null)
	{
		var mongo = GetConnection(colname, year);
		var builder = Builders<WorkbookPenLog>.Filter;
		var filter = builder.Eq(x => x.PaperId, paperId) & builder.Eq(x => x.ItemId, itemId) & builder.In(x => x.UserId, userid);
		var result = mongo.Distinct(x => x.UserId, filter).ToList();
		return result;
	}

	/// <inheritdoc />
	public void DeletePenLog(string colname, string paperId, List<string> userid)
	{
		var mongo = GetConnection(colname);
		mongo.DeleteMany(x => x.PaperId== paperId && userid.Contains(x.UserId));
	}
}