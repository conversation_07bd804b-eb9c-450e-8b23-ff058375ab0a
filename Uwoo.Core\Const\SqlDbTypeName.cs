﻿namespace Uwoo.Core.Const
{
	public struct SqlDbTypeName
	{
		public const string NVarChar = "nvarchar";
		public const string VarChar = "varchar";
		public const string NChar = "nchar";
		public const string Char = "char";
		public const string Text = "text";
		public const string Int = "int";
		public const string BigInt = "bigint";
		public const string DateTime = "datetime";
		public const string Date = "date";
		public const string SmallDateTime = "smalldatetime";
		public const string SmallDate = "smalldate";
		public const string Float = "float";
		public const string Decimal = "decimal";
		public const string Double = "double";
		public const string Bit = "bit";
		public const string Bool = "bool";
		public const string UniqueIdentifier = "uniqueidentifier";

	}
}
