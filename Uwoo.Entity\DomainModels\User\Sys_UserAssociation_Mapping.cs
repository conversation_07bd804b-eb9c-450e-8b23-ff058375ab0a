﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Entity.DomainModels
{
    /// <summary>
    /// 用户学生教师关联关系表
    /// </summary>
    [Entity(TableCnName = "用户学生教师关联关系表", TableName = "Sys_UseRassociation_Mapping")]
    public class Sys_UserAssociation_Mapping
    {
        /// <summary>
        ///
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Key]
        [Display(Name = "Id")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public string Id { get; set; }

        /// <summary>
        ///用户Id
        /// </summary>
        [Display(Name = "用户Id")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string UserId { get; set; }

        /// <summary>
        ///教师id,学生id,三方账户唯一id
        /// </summary>
        [Display(Name = "教师id,学生id,三方账户唯一id")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string RassociationId { get; set; }

        /// <summary>
        ///1 学生，其他（可能是校管理，区管理，教师）
        /// </summary>
        [Display(Name = "1 学生，其他（可能是校管理，区管理，教师）")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int UserType { get; set; }

        /// <summary>
        ///来源
        /// </summary>
        [Display(Name = "来源")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string Source { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "CreateTime")]
        [Column(TypeName = "datetime")]
        [Editable(true)]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "Deleted")]
        [Column(TypeName = "bit")]
        [Editable(true)]
        public bool? Deleted { get; set; }
    }
}
