﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Core.Utilities
{
    public static class ConfigHelper
    {
        private static IConfiguration Config { get; set; }

        /// <summary>
        /// 配置注册
        /// </summary>
        /// <param name="config"></param>
        public static void Register(IConfiguration config)
        {
            Config = config;
        }

        /// <summary>
        /// 从AppSettings获取key的值
        /// </summary>
        /// <param name="key">key</param>
        /// <returns></returns>
        public static string GetValue(string key)
        {
            return Config[key];
        }

        /// <summary>
        /// 获取连接字符串
        /// </summary>
        /// <param name="nameOfCon">连接字符串名</param>
        /// <returns></returns>
        public static string GetConnectionString(string nameOfCon)
        {
            return Config.GetConnectionString(nameOfCon);
        }
    }
}
