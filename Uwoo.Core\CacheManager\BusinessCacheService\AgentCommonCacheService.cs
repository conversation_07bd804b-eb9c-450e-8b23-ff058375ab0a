﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.CacheManager.Service;
using Uwoo.Core.Utilities;
using UwooAgent.Core.CacheManager.IBusinessCacheService;

namespace UwooAgent.Core.CacheManager.BusinessCacheService
{
    /// <summary>
    /// 智能体通用缓存
    /// </summary>
    public class AgentCommonCacheService : RedisService, IAgentCommonCacheService
    {
        /// <summary>
        /// 如果缓存为db1,就要在构造函数中传1
        /// </summary>
        public AgentCommonCacheService() : base(10)
        { }

        public override string Prefix => RedisKeys.AgentCommonCache;

        /// <summary>
        /// 缓存锁
        /// </summary>
        /// <param name="key">key</param>
        /// <returns></returns>
        public bool SetRedisLock(string key)
        {
            key = RedisKeys.AgentCommonStudentSubmitLockCache + "|" + key;
            if (IsExists(key))
            {
                return false;
            }
            Set(key, 1, new TimeSpan(0, 2, 0));
            return true;
        }

        /// <summary>
        /// 释放缓存锁
        /// </summary>
        /// <param name="key">key</param>
        /// <returns></returns>
        public void DelRedisLock(string key)
        {
            key = RedisKeys.AgentCommonStudentSubmitLockCache + "|" + key;
            Delete(key);
        }
    }
}
