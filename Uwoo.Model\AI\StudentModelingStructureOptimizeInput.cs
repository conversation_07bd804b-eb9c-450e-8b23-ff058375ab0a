﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 学生端建模模型构建优化接口入参
    /// </summary>
    public class StudentModelingStructureOptimizeInput
    {
        /// <summary>
        /// 建模Id
        /// </summary>
        public string? ModelingId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 变量定义
        /// </summary>
        public string? Variable { get; set; }

        /// <summary>
        /// 表达式
        /// </summary>
        public string? Expression { get; set; }
    }
}
