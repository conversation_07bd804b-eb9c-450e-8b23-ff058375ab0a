﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.CustomException;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_教师端建模
    /// </summary>
    [Route("/AgentTeacherModeling/[controller]/[action]")]
    [ApiController]
    public class AgentTeacherModelingController : ApiBaseController<IAgentTeacherModelingService>
    {
        #region DI
        private readonly IAgentTeacherModelingService _agentTeacherModelingService;
        public AgentTeacherModelingController(IAgentTeacherModelingService agentTeacherModelingService)
        {
            _agentTeacherModelingService = agentTeacherModelingService;
        }
        #endregion

        /// <summary>
        /// 保存/编辑建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<string> SaveModelingInfo(SaveModelingInfoInput input)
        {
            if (input == null)
            {
                throw new BusException("建模信息入参不能为null！");
            }
            if (string.IsNullOrWhiteSpace(input.Name))
            {
                throw new BusException("项目名称不能为空！", 801);
            }
            if (input.Name.Length > 100)  // 假设项目名称最大长度100，可根据实际调整
            {
                throw new BusException("项目名称长度不能超过100个字符！", 801);
            }
            if (string.IsNullOrWhiteSpace(input.AgentId))
            {
                throw new BusException("智能体Id不能为空！");
            }
            if (!string.IsNullOrWhiteSpace(input.Introduce) && input.Introduce.Length > 500)
            {
                throw new BusException("项目背景介绍长度不能超过500个字符！", 801);
            }
            if (string.IsNullOrWhiteSpace(input.SubjectId))
            {
                throw new BusException("学科Id不能为空！");
            }
            if (string.IsNullOrWhiteSpace(input.GradeId))
            {
                throw new BusException("年级Id不能为空！");
            }
            if (string.IsNullOrWhiteSpace(input.TeacherId))
            {
                throw new BusException("教师Id不能为空！");
            }
            if (string.IsNullOrWhiteSpace(input.SchoolId))
            {
                throw new BusException("学校Id不能为空！");
            }

            //阶段列表整体验证
            if (input.ModelingStageInfos == null || !input.ModelingStageInfos.Any())
            {
                throw new BusException("建模阶段不能为空！", 801);
            }

            //验证默认阶段整体规则（必须包含3个默认阶段：1、2、3，唯一且顺序正确）
            var defaultStages = input.ModelingStageInfos.Where(s => s.IsDefault).ToList();

            //必须有3个默认阶段
            if (defaultStages.Count != 3)
            {
                throw new BusException("必须包含三个默认阶段：问题理解、假设分析、模型评价！");
            }

            //默认阶段类型必须包含1、2、3且无重复
            var defaultStageTypes = defaultStages.Select(s => s.DefaultStageType).ToList();
            var requiredTypes = new[] { 1, 2, 3 };
            if (!requiredTypes.All(t => defaultStageTypes.Contains(t)))
            {
                throw new BusException("默认阶段必须包含问题理解（1）、假设分析（2）、模型评价（3），缺一不可！");
            }
            if (defaultStageTypes.Distinct().Count() != 3)
            {
                throw new BusException("默认阶段类型不能重复，必须分别为1、2、3！");
            }

            //验证默认阶段顺序（问题理解＜假设分析＜模型评价）
            var stage1 = input.ModelingStageInfos.First(s => s.IsDefault && s.DefaultStageType == 1);
            var stage2 = input.ModelingStageInfos.First(s => s.IsDefault && s.DefaultStageType == 2);
            var stage3 = input.ModelingStageInfos.First(s => s.IsDefault && s.DefaultStageType == 3);
            int index1 = input.ModelingStageInfos.IndexOf(stage1);
            int index2 = input.ModelingStageInfos.IndexOf(stage2);
            int index3 = input.ModelingStageInfos.IndexOf(stage3);
            if (!(index1 < index2 && index2 < index3))
            {
                throw new BusException("默认阶段顺序错误，必须按照问题理解、假设分析、模型评价的顺序排列！");
            }

            //逐个验证阶段信息
            foreach (var stage in input.ModelingStageInfos)
            {
                if (stage == null)
                {
                    throw new BusException("建模阶段信息不能为null！");
                }

                //阶段基础信息验证
                if (string.IsNullOrWhiteSpace(stage.Name))
                {
                    throw new BusException("阶段名称不能为空！", 801);
                }
                if (stage.Name.Length > 50)
                {
                    throw new BusException("阶段名称长度不能超过50个字符！", 801);
                }
                if (!string.IsNullOrWhiteSpace(stage.Describe) && stage.Describe.Length > 100)
                {
                    throw new BusException("阶段描述（目标）长度不能超过100个字符！", 801);
                }

                //阶段任务列表验证
                if (stage.ModelingStageTaskInfos == null || !stage.ModelingStageTaskInfos.Any())
                {
                    throw new BusException($"阶段「{stage.Name}」的任务不能为空！", 801);
                }

                //逐个验证阶段任务
                foreach (var task in stage.ModelingStageTaskInfos)
                {
                    if (task == null)
                    {
                        throw new BusException("建模阶段任务信息不能为null！");
                    }

                    //任务基础信息验证
                    if (string.IsNullOrWhiteSpace(task.Name))
                    {
                        throw new BusException($"阶段「{stage.Name}」的任务名称不能为空！", 801);
                    }
                    if (task.Name.Length > 30)
                    {
                        throw new BusException($"阶段「{stage.Name}」的任务名称长度不能超过30个字符！", 801);
                    }
                    if (string.IsNullOrWhiteSpace(task.Target))
                    {
                        throw new BusException($"任务「{task.Name}」的目标不能为空！", 801);
                    }
                    if (task.Target.Length > 200)
                    {
                        throw new BusException($"任务「{task.Name}」的目标长度不能超过200个字符！", 801);
                    }

                    //任务类型验证
                    var validTaskTypes = new[] { 1, 2, 3, 4, 5, 6, 7, 8, 9 };
                    if (!validTaskTypes.Contains(task.TaskType))
                    {
                        throw new BusException($"任务「{task.Name}」的任务类型只能是（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:问题理解、7:模型构建、8:模型假设、9:模型评价）！");
                    }

                    //任务类型特有验证
                    if (task.TaskType == 1)  // 成果评估
                    {
                        if (string.IsNullOrWhiteSpace(task.ScoreStandard))
                        {
                            throw new BusException($"任务「{task.Name}」的评分标准不能为空！", 801);
                        }
                        if (task.ScoreStandard.Length > 2000)
                        {
                            throw new BusException($"任务「{task.Name}」的评分标准长度不能超过2000个字符！", 801);
                        }
                    }
                    else if (task.TaskType == 2)  // 情景对话
                    {
                        if (string.IsNullOrWhiteSpace(task.ScoreStandard))
                        {
                            throw new BusException($"任务「{task.Name}」的评分标准不能为空！", 801);
                        }
                        if (task.ScoreStandard.Length > 2000)
                        {
                            throw new BusException($"任务「{task.Name}」的评分标准长度不能超过2000个字符！", 801);
                        }
                        if (string.IsNullOrWhiteSpace(task.Demand))
                        {
                            throw new BusException($"任务「{task.Name}」的要求（情景对话要求）不能为空！", 801);
                        }
                        if (task.Demand.Length > 2000)
                        {
                            throw new BusException($"任务「{task.Name}」的要求（情景对话要求）长度不能超过2000个字符！", 801);
                        }
                    }
                    else if (task.TaskType == 3)  // 知识问答
                    {
                        if (string.IsNullOrWhiteSpace(task.Scope))
                        {
                            throw new BusException($"任务「{task.Name}」的问答范围不能为空！", 801);
                        }
                        if (task.Scope.Length > 2000)
                        {
                            throw new BusException($"任务「{task.Name}」的问答范围长度不能超过2000个字符！", 801);
                        }
                    }
                    else if (task.TaskType == 4)  // 视频
                    {
                        if (task.ModelingStageTaskVideoInfos == null || !task.ModelingStageTaskVideoInfos.Any())
                        {
                            throw new BusException($"任务「{task.Name}」的视频不能为空！", 801);
                        }
                    }
                    else if (task.TaskType == 5)  // 文档
                    {
                        if (task.ModelingStageTaskDocumentInfos == null || !task.ModelingStageTaskDocumentInfos.Any())
                        {
                            throw new BusException($"任务「{task.Name}」的文档不能为空！", 801);
                        }
                    }
                    else if (task.TaskType == 6 || task.TaskType == 8 || task.TaskType == 9)
                    {
                        if (string.IsNullOrWhiteSpace(task.GuideRoleSetting))
                        {
                            throw new BusException($"任务「{task.Name}」的引导角色设定不能为空！", 801);
                        }
                    }

                    if (task.TaskType != 4 && task.TaskType != 5)
                    {
                        //角色设定验证
                        if (string.IsNullOrWhiteSpace(task.RoleSetting))
                        {
                            throw new BusException($"任务「{task.Name}」的评估角色设定不能为空！", 801);
                        }
                        if (task.RoleSetting.Length > 2000)
                        {
                            throw new BusException($"任务「{task.Name}」的评估角色设定长度不能超过2000个字符！", 801);
                        }

                        // AI评估分数默认值处理
                        if (task.GroupIsAssessment && task.GroupAssessmentScore <= 0)
                        {
                            task.GroupAssessmentScore = 60;
                        }
                        if (task.TaskIsAssessment && task.TaskAssessmentScore <= 0)
                        {
                            task.TaskAssessmentScore = 60;
                        }
                    }

                    // 高频问题验证
                    if (task.ModelingStageTaskQuestionInfos == null || !task.ModelingStageTaskQuestionInfos.Any())
                    {
                        throw new BusException($"任务「{task.Name}」至少需要添加一个高频问题！", 801);
                    }
                    foreach (var question in task.ModelingStageTaskQuestionInfos)
                    {
                        if (question == null)
                        {
                            throw new BusException("任务高频问题信息不能为null！");
                        }
                        if (string.IsNullOrWhiteSpace(question.Name))
                        {
                            throw new BusException($"任务「{task.Name}」的高频问题名称不能为空！", 801);
                        }
                        if (!string.IsNullOrWhiteSpace(question.Describe) && question.Describe.Length > 300)
                        {
                            throw new BusException($"任务「{task.Name}」的高频问题描述长度不能超过300个字符！", 801);
                        }
                    }
                }

                //验证默认阶段的任务规则（仅针对默认阶段）
                if (stage.IsDefault)
                {
                    // 问题理解阶段（DefaultStageType=1）
                    if (stage.DefaultStageType == 1)
                    {
                        var tasks = stage.ModelingStageTaskInfos;
                        // 必须包含任务6和7
                        bool hasType6 = tasks.Any(t => t.TaskType == 6);
                        bool hasType7 = tasks.Any(t => t.TaskType == 7);
                        if (!hasType6 || !hasType7)
                        {
                            throw new BusException($"问题理解阶段「{stage.Name}」的任务必须包含问题理解（6）和模型构建（7），缺一不可！");
                        }
                        // 任务6和7必须唯一
                        int count6 = tasks.Count(t => t.TaskType == 6);
                        int count7 = tasks.Count(t => t.TaskType == 7);
                        if (count6 != 1)
                        {
                            throw new BusException($"问题理解阶段「{stage.Name}」的问题理解（6）任务必须唯一！");
                        }
                        if (count7 != 1)
                        {
                            throw new BusException($"问题理解阶段「{stage.Name}」的模型构建（7）任务必须唯一！");
                        }
                        // 任务6必须在任务7前面
                        int index6 = tasks.FindIndex(t => t.TaskType == 6);
                        int index7 = tasks.FindIndex(t => t.TaskType == 7);
                        if (index6 >= index7)
                        {
                            throw new BusException($"问题理解阶段「{stage.Name}」的问题理解（6）任务必须在模型构建（7）任务前面！");
                        }
                    }
                    //假设分析阶段（DefaultStageType=2）
                    else if (stage.DefaultStageType == 2)
                    {
                        var tasks = stage.ModelingStageTaskInfos;
                        // 必须包含任务8且唯一
                        bool hasType8 = tasks.Any(t => t.TaskType == 8);
                        if (!hasType8)
                        {
                            throw new BusException($"假设分析阶段「{stage.Name}」的任务必须包含模型假设（8）！");
                        }
                        int count8 = tasks.Count(t => t.TaskType == 8);
                        if (count8 != 1)
                        {
                            throw new BusException($"假设分析阶段「{stage.Name}」的模型假设（8）任务必须唯一！");
                        }
                    }
                    //模型评价阶段（DefaultStageType=3）
                    else if (stage.DefaultStageType == 3)
                    {
                        var tasks = stage.ModelingStageTaskInfos;
                        // 必须包含任务9且唯一
                        bool hasType9 = tasks.Any(t => t.TaskType == 9);
                        if (!hasType9)
                        {
                            throw new BusException($"模型评价阶段「{stage.Name}」的任务必须包含模型评价（9）！");
                        }
                        int count9 = tasks.Count(t => t.TaskType == 9);
                        if (count9 != 1)
                        {
                            throw new BusException($"模型评价阶段「{stage.Name}」的模型评价（9）任务必须唯一！");
                        }
                    }
                    // 默认阶段类型必须是1/2/3
                    else
                    {
                        throw new BusException($"默认阶段「{stage.Name}」的类型必须为1（问题理解）、2（假设分析）或3（模型评价）！");
                    }
                }
            }

            return await _agentTeacherModelingService.SaveModelingInfo(input);
        }

        /// <summary>
        /// 获取建模阶段任务默认信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public List<GetModelingStageDefaultInfoOutput> GetModelingStageTaskDefaultInfo()
        {
            return _agentTeacherModelingService.GetModelingStageTaskDefaultInfo();
        }

        /// <summary>
        /// 获取建模信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetModelingInfoOutput> GetModelingInfo(GetModelingInfoInput input)
        {
            if (string.IsNullOrEmpty(input.Id))
            {
                throw new BusException("建模Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }
            return await _agentTeacherModelingService.GetModelingInfo(input);
        }

        /// <summary>
        /// 删除建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<bool> DeleteModelingInfo(DeleteModelingInfoInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId))
            {
                throw new BusException("建模Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }
            return await _agentTeacherModelingService.DeleteModelingInfo(input);
        }

        /// <summary>
        /// 发布建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task PublishModelingInfo(PublishModelingInfoInput input)
        {
            // 1. 基础参数验证
            if (string.IsNullOrEmpty(input.ModelingId))
            {
                throw new BusException("建模Id不能为空");
            }
            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }

            // 2. 发布类型验证（班级和学生不能同时发布，且必须选择一种）
            bool isClassPublish = input.ClassIds?.Any() == true;
            bool isStudentPublish = input.StudentIds?.Any() == true;

            if (isClassPublish && isStudentPublish)
            {
                throw new BusException("发布类型冲突，不能同时发布给班级和学生");
            }

            if (!isClassPublish && !isStudentPublish)
            {
                throw new BusException("请选择发布对象，班级或学生至少选择一项");
            }

            // 3. 时间范围验证
            if (input.TimeRange == null || input.TimeRange.Count != 2)
            {
                throw new BusException("时间范围格式不正确，需包含开始时间和结束时间");
            }

            DateTime? beginTime = input.TimeRange[0];
            DateTime? endTime = input.TimeRange[1];

            if (!beginTime.HasValue || !endTime.HasValue)
            {
                throw new BusException("开始时间和结束时间不能为空");
            }

            if (beginTime > endTime)
            {
                throw new BusException("开始时间不能晚于结束时间");
            }

            if (input.ScorePublishType != 1 && input.ScorePublishType != 2 && input.ScorePublishType != 3)
            {
                input.ScorePublishType = 1;
            }
            if (input.ScorePublishType == 3)
            {
                if (!input.ScorePublishTime.HasValue)
                {
                    throw new BusException("请设置评分公布时间！", 801);
                }
            }

            await _agentTeacherModelingService.PublishModelingInfo(input);
        }

        /// <summary>
        /// 撤销建模发布
        /// </summary>
        /// <param name="input">撤销发布参数</param>
        /// <returns></returns>
        [HttpPost]
        public async Task UnpublishModelingInfo(UnpublishModelingInfoInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId))
            {
                throw new BusException("建模Id不能为空");
            }
            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }
            await _agentTeacherModelingService.UnpublishModelingInfo(input);
        }

        /// <summary>
        /// 获取建模列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageReturn<GetModelingListOutput>> GetModelingList(GetModelingListInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId)
               || string.IsNullOrEmpty(input.SchoolId)
               || string.IsNullOrEmpty(input.AgentId)
               || string.IsNullOrEmpty(input.SubjectId)
               || string.IsNullOrEmpty(input.GradeId))
            {
                throw new BusException("参数异常!");
            }
            if (input.PageIndex <= 0)
            {
                input.PageIndex = 1;
            }
            if (input.PageSize <= 0)
            {
                input.PageSize = 10;
            }
            if (input.PublishStatus != 1 && input.PublishStatus != 2)
            {
                input.PublishStatus = 0;
            }
            return await _agentTeacherModelingService.GetModelingList(input);
        }

        /// <summary>
        /// 获取建模综合分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetModelingSynthesizeAnalyseOutput> GetModelingSynthesizeAnalyse(GetModelingSynthesizeAnalyseInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId) || string.IsNullOrEmpty(input.ClassId))
            {
                throw new BusException("参数异常!");
            }
            return await _agentTeacherModelingService.GetModelingSynthesizeAnalyse(input);
        }

        /// <summary>
        /// 获取建模阶段任务统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<GetModelingStageTaskCountOutput>> GetModelingStageTaskCount(GetModelingStageTaskCountInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId) || string.IsNullOrEmpty(input.ClassId))
            {
                throw new BusException("参数异常!");
            }
            return await _agentTeacherModelingService.GetModelingStageTaskCount(input);
        }

        /// <summary>
        /// 获取建模学生统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetModelingStudentCountOutput> GetModelingStudentCount(GetModelingStudentCountInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId) || string.IsNullOrEmpty(input.ClassId))
            {
                throw new BusException("参数异常!");
            }
            return await _agentTeacherModelingService.GetModelingStudentCount(input);
        }

        /// <summary>
        /// 获取学生做建模任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentDoModelingTaskListOutput> GetStudentDoModelingTaskList(GetStudentDoModelingTaskListInput input)
        {
            if (string.IsNullOrEmpty(input.ModelingId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("参数异常!");
            }
            return await _agentTeacherModelingService.GetStudentDoModelingTaskList(input);
        }

        /// <summary>
        /// 教师端建模下载学生成果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ModelingDownloadStudentAchievement(ModelingDownloadStudentAchievementInput input)
        {
            if (string.IsNullOrEmpty(input.ClassId) || string.IsNullOrEmpty(input.ModelingId))
            {
                throw new BusException("参数异常!");
            }
            byte[] stream = await _agentTeacherModelingService.ModelingDownloadStudentAchievement(input);
            //返回
            return File(stream, "application/zip", "Modeling.zip");
        }

        /// <summary>
        /// 教师端建模下载学生评估结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ModelingDownloadStudentResult(ModelingDownloadStudentResultInput input)
        {
            if (string.IsNullOrEmpty(input.ClassId) || string.IsNullOrEmpty(input.ModelingId))
            {
                throw new BusException("参数异常!");
            }
            byte[] stream = await _agentTeacherModelingService.ModelingDownloadStudentResult(input);
            //返回
            return File(stream, "application/zip", "Modeling.zip");
        }
    }
}
