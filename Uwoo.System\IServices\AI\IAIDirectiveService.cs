using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// AI指令管理服务接口
    /// </summary>
    public interface IAIDirectiveService : IDependency
    {
        /// <summary>
        /// 获取AI指令分页列表
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>分页结果</returns>
        Task<AIDirectivePageResult> GetAIDirectiveListAsync(AIDirectiveQueryInput input);

        /// <summary>
        /// 根据ID获取AI指令详情
        /// </summary>
        /// <param name="id">指令ID</param>
        /// <returns>指令详情</returns>
        Task<AIDirectiveDetailDto?> GetAIDirectiveByIdAsync(string id);

        /// <summary>
        /// 创建AI指令
        /// </summary>
        /// <param name="input">创建参数</param>
        /// <returns>操作结果</returns>
        Task<AIDirectiveOperationResult> CreateAIDirectiveAsync(CreateAIDirectiveInput input);

        /// <summary>
        /// 更新AI指令
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>操作结果</returns>
        Task<AIDirectiveOperationResult> UpdateAIDirectiveAsync(UpdateAIDirectiveInput input);

        /// <summary>
        /// 软删除AI指令
        /// </summary>
        /// <param name="id">指令ID</param>
        /// <returns>操作结果</returns>
        Task<AIDirectiveOperationResult> DeleteAIDirectiveAsync(string id);

        /// <summary>
        /// 恢复已删除的AI指令
        /// </summary>
        /// <param name="id">指令ID</param>
        /// <returns>操作结果</returns>
        Task<AIDirectiveOperationResult> RestoreAIDirectiveAsync(string id);

        /// <summary>
        /// 获取AI指令类型统计
        /// </summary>
        /// <returns>类型统计列表</returns>
        Task<List<AIDirectiveTypeStatistics>> GetAIDirectiveTypeStatisticsAsync();

        /// <summary>
        /// 搜索AI指令
        /// </summary>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="limit">返回数量限制</param>
        /// <returns>搜索结果</returns>
        Task<List<AIDirectiveListDto>> SearchAIDirectivesAsync(string keyword, int limit = 10);
    }
}
