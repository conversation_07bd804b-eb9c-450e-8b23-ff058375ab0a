﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// AI模型基础信息
	/// </summary>
	[Table("AI_ModelBaseInfo")]
    public class AI_ModelBaseInfo
    {
        /// <summary>
		/// Id
		/// </summary>
		[SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 模型名称
        /// </summary>
        public string ModelName { get; set; }

        /// <summary>
        /// 模型Key（豆包模型Id）
        /// </summary>
        public string Modelkey { get; set; }

        /// <summary>
        /// 是否支持上下文
        /// </summary>
        public bool? IsContext { get; set; }

        /// <summary>
        /// 模型描述
        /// </summary>
        public string ModelDescribe { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int OrderId { get; set; }
    }
}
