﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取学生做智能体任务信息
    /// </summary>
    public class GetStudentDoAgentTaskInfoDto
    {
        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生学号
        /// </summary>
        public string? StudentNum { get; set; }

        /// <summary>
        /// 学生姓名
        /// </summary>
        public string? StudentName { get; set; }

        /// <summary>
        /// 学生等第
        /// </summary>
        public string? StudentLevel { get; set; }

        /// <summary>
        /// 学生分数
        /// </summary>
        public decimal? StudentScore { get; set; }

        /// <summary>
        /// 是否提交
        /// </summary>
        public bool IsSubmit { get; set; }
    }
}
