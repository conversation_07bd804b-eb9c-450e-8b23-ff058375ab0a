using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SystemIO = System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using UwooAgent.System.IServices;
using System.Xml.Linq;
using System.Reflection;
using UwooAgent.Model;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Microsoft.Extensions.Logging;

namespace UwooAgent.Core.Middleware
{
    /// <summary>
    /// 自定义 XML-RPC 中间件，适用于 ASP.NET Core
    /// </summary>
    public class XmlRpcMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<XmlRpcMiddleware> _logger;

        // JSON设置：支持snake_case到PascalCase的映射
        private static readonly JsonSerializerSettings JsonSettings = new JsonSerializerSettings
        {
            ContractResolver = new DefaultContractResolver
            {
                NamingStrategy = new SnakeCaseNamingStrategy()
            },
            DateParseHandling = DateParseHandling.None
        };

        public XmlRpcMiddleware(RequestDelegate next, ILogger<XmlRpcMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IUniLoginRpcService xmlRpcService)
        {
            if (context.Request.Path == "/api/rpc/sync" && context.Request.Method == "POST")
            {
                // 处理 XML-RPC 请求
                await ProcessXmlRpcRequest(context, xmlRpcService);
                return;
            }

            await _next(context);
        }

        private async Task ProcessXmlRpcRequest(HttpContext context, IUniLoginRpcService service)
        {
            try
            {
                // 设置响应头
                context.Response.ContentType = "text/xml; charset=GBK";

                // 读取请求体，支持GBK编码
                var requestXml = await ReadRequestXmlAsync(context.Request);

                _logger.LogInformation("XML-RPC Request: {RequestXml}", requestXml);

                // 解析 XML-RPC 请求
                var (methodName, parameters) = ParseXmlRpcRequest(requestXml);

                // 调用对应的服务方法
                var result = await InvokeServiceMethod(service, methodName, parameters);

                // 生成 XML-RPC 响应
                var responseXml = GenerateXmlRpcResponse(result);

                _logger.LogInformation("XML-RPC Response: {ResponseXml}", responseXml);

                // 写入响应
                await context.Response.WriteAsync(responseXml, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "XML-RPC Error: {Message}", ex.Message);

                // 生成错误响应
                var errorResponse = GenerateXmlRpcFault(-1, ex.Message);
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync(errorResponse, Encoding.UTF8);
            }
        }

        /// <summary>
        /// 按XML声明自动识别编码读取请求体（支持GBK）
        /// </summary>
        private static async Task<string> ReadRequestXmlAsync(HttpRequest request)
        {
            using var ms = new SystemIO.MemoryStream();
            await request.Body.CopyToAsync(ms);
            var bytes = ms.ToArray();

            // 默认UTF-8，尝试从XML声明中解析encoding
            var head = Encoding.ASCII.GetString(bytes, 0, Math.Min(bytes.Length, 200));
            var encName = TryExtractXmlEncoding(head) ?? "utf-8";

            try
            {
                // 支持国标编码
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                var enc = Encoding.GetEncoding(encName);
                return enc.GetString(bytes);
            }
            catch
            {
                // 如果编码不支持，回退到UTF-8
                return Encoding.UTF8.GetString(bytes);
            }
        }

        /// <summary>
        /// 从XML头部提取编码信息
        /// </summary>
        private static string TryExtractXmlEncoding(string head)
        {
            try
            {
                var idx = head.IndexOf("encoding=", StringComparison.OrdinalIgnoreCase);
                if (idx < 0) return null;
                
                var start = head.IndexOf('"', idx);
                if (start < 0) start = head.IndexOf('\'', idx);
                if (start < 0) return null;
                
                var end = head.IndexOf(head[start], start + 1);
                if (end < 0) return null;
                
                return head.Substring(start + 1, end - start - 1).Trim();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 解析 XML-RPC 请求
        /// </summary>
        private (string methodName, object[] parameters) ParseXmlRpcRequest(string requestXml)
        {
            var doc = XDocument.Parse(requestXml);
            var methodCall = doc.Element("methodCall");
            var methodName = methodCall?.Element("methodName")?.Value;

            var parameters = new List<object>();
            var paramsElement = methodCall?.Element("params");

            if (paramsElement != null)
            {
                foreach (var param in paramsElement.Elements("param"))
                {
                    var value = param.Element("value");
                    parameters.Add(ParseXmlRpcValue(value));
                }
            }

            return (methodName, parameters.ToArray());
        }

        /// <summary>
        /// 解析 XML-RPC 值
        /// </summary>
        private object ParseXmlRpcValue(XElement valueElement)
        {
            if (valueElement == null) return null;

            try
            {
                // 检查各种数据类型
                var stringElement = valueElement.Element("string");
                if (stringElement != null) return stringElement.Value ?? string.Empty;

                var intElement = valueElement.Element("int") ?? valueElement.Element("i4");
                if (intElement != null) 
                {
                    if (int.TryParse(intElement.Value, out var intVal))
                        return intVal;
                    return 0; // 默认值
                }

                var boolElement = valueElement.Element("boolean");
                if (boolElement != null) return boolElement.Value == "1";

                var doubleElement = valueElement.Element("double");
                if (doubleElement != null) 
                {
                    if (double.TryParse(doubleElement.Value, out var doubleVal))
                        return doubleVal;
                    return 0.0; // 默认值
                }

                var dtElement = valueElement.Element("dateTime.iso8601");
                if (dtElement != null) return dtElement.Value ?? string.Empty; // 保持字符串格式

                var structElement = valueElement.Element("struct");
                if (structElement != null)
                {
                    var dict = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                    foreach (var member in structElement.Elements("member"))
                    {
                        var name = member.Element("name")?.Value;
                        var value = member.Element("value");
                        if (!string.IsNullOrEmpty(name))
                        {
                            dict[name] = ParseXmlRpcValue(value);
                        }
                    }
                    return dict;
                }

                // 如果没有明确类型，返回文本值
                return valueElement.Value ?? string.Empty;
            }
            catch (Exception ex)
            {
                // 记录解析错误并返回默认值
                global::System.Diagnostics.Debug.WriteLine($"Parse error: {ex.Message}");
                return valueElement?.Value ?? string.Empty;
            }
        }

        /// <summary>
        /// 调用服务方法
        /// </summary>
        private Task<object> InvokeServiceMethod(IUniLoginRpcService service, string methodName, object[] parameters)
        {
            // 根据 methodName 映射到具体的服务方法
            object result = methodName switch
            {
                "user.updateUserCommon" => service.UpdateUser(ConvertToUniUserQo(parameters[0])),
                "user.syncUser" => service.SyncUser(ConvertToUniUserQo(parameters[0])),
                "user.addUser" => service.AddUser(ConvertToUniUserQo(parameters[0])),
                "user.deleteUser" => service.DeleteUser(parameters[0]?.ToString()),
                "group.addGroup" => service.AddGroup(ConvertToUniGroupQo(parameters[0])),
                "group.updateGroup" => service.UpdateGroup(ConvertToUniGroupQo(parameters[0])),
                "group.syncGroup" => service.SyncGroup(ConvertToUniGroupQo(parameters[0])),
                "group.deleteGroup" => service.DeleteGroup(Convert.ToInt32(parameters[0])),
                _ => throw new InvalidOperationException($"Unknown method: {methodName}")
            };

            return Task.FromResult(result);
        }

        /// <summary>
        /// 转换为 UniUserQo 对象
        /// </summary>
        private UniUserQo ConvertToUniUserQo(object parameter)
        {
            try
            {
                if (parameter is Dictionary<string, object> dict)
                {
                    var json = JsonConvert.SerializeObject(dict);
                    _logger.LogDebug("Converting to UniUserQo: {Json}", json);
                    var result = JsonConvert.DeserializeObject<UniUserQo>(json, JsonSettings);
                    return result ?? new UniUserQo();
                }
                return parameter as UniUserQo ?? new UniUserQo();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting to UniUserQo");
                return new UniUserQo();
            }
        }

        /// <summary>
        /// 转换为 UniGroupQo 对象
        /// </summary>
        private UniGroupQo ConvertToUniGroupQo(object parameter)
        {
            try
            {
                if (parameter is Dictionary<string, object> dict)
                {
                    var json = JsonConvert.SerializeObject(dict);
                    _logger.LogDebug("Converting to UniGroupQo: {Json}", json);
                    var result = JsonConvert.DeserializeObject<UniGroupQo>(json, JsonSettings);
                    return result ?? new UniGroupQo();
                }
                return parameter as UniGroupQo ?? new UniGroupQo();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting to UniGroupQo");
                return new UniGroupQo();
            }
        }

        /// <summary>
        /// 生成 XML-RPC 响应
        /// </summary>
        private string GenerateXmlRpcResponse(object result)
        {
            var doc = new XDocument(
                new XDeclaration("1.0", "utf-8", null),
                new XElement("methodResponse",
                    new XElement("params",
                        new XElement("param",
                            new XElement("value",
                                GenerateXmlRpcValue(result)
                            )
                        )
                    )
                )
            );

            return doc.ToString();
        }

        /// <summary>
        /// 生成 XML-RPC 值
        /// </summary>
        private XElement GenerateXmlRpcValue(object value)
        {
            return value switch
            {
                null => new XElement("string", ""),
                bool b => new XElement("boolean", b ? "1" : "0"),
                int i => new XElement("int", i.ToString()),
                string s => new XElement("string", s),
                _ => new XElement("string", value.ToString())
            };
        }

        /// <summary>
        /// 生成 XML-RPC 错误响应
        /// </summary>
        private string GenerateXmlRpcFault(int faultCode, string faultString)
        {
            var doc = new XDocument(
                new XDeclaration("1.0", "utf-8", null),
                new XElement("methodResponse",
                    new XElement("fault",
                        new XElement("value",
                            new XElement("struct",
                                new XElement("member",
                                    new XElement("name", "faultCode"),
                                    new XElement("value",
                                        new XElement("int", faultCode.ToString())
                                    )
                                ),
                                new XElement("member",
                                    new XElement("name", "faultString"),
                                    new XElement("value",
                                        new XElement("string", faultString)
                                    )
                                )
                            )
                        )
                    )
                )
            );

            return doc.ToString();
        }
    }
}
