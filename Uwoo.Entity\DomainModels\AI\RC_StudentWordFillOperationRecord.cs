using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 阅读理解学生选词填空操作记录表
    /// </summary>
    [SugarTable("RC_StudentWordFillOperationRecord")]
    public class RC_StudentWordFillOperationRecord
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string StudentId { get; set; }

        /// <summary>
        /// 阅读理解项目阶段任务Id
        /// </summary>
        public string ReadingProjectStageTaskId { get; set; }

        /// <summary>
        /// 操作序号（按时间顺序递增）
        /// </summary>
        public int OperationOrder { get; set; }

        /// <summary>
        /// 题目序号（第几个空）
        /// </summary>
        public int QuestionIndex { get; set; }

        /// <summary>
        /// 选择的词汇
        /// </summary>
        public string SelectedWord { get; set; }

        ///// <summary>
        ///// 填空位置标识
        ///// </summary>
        //public string BlankId { get; set; }

        ///// <summary>
        ///// 操作类型（1:选择答案, 2:修改答案, 3:清空答案）
        ///// </summary>
        //public int OperationType { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperationTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
