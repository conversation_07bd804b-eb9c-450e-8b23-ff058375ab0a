﻿// -- Function：IPaperLogRedisService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/31 17:40

namespace Uwoo.Mongo.Interfaces.Redis.Business;

using Uwoo.Contracts.Paper;
using Uwoo.Contracts.Redis;
using Uwoo.Mongo.Models;
using X.PenServer.Contracts.Queue;

/// <summary>
/// 做卷相关笔迹缓存
/// </summary>
public interface IPaperLogRedisService : IRedisService
{
    /// <summary>
    /// 获取当前一键赋码的试卷
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <remarks>卷码纸专用</remarks>
    /// <returns></returns>
    Task<CurrentPaperId> GetCurrentPaperIdAsync(string classid);

    /// <summary>
    /// 获取学生正在书写的页面
    /// </summary>
    /// <param name="userid">学生用户id</param>
    /// <returns></returns>
    Task<WorkbookPage> GetStudentCurrentDoPaperPageInfoAsync(string userid);

    /// <summary>
    /// 删除学生正在书写的页面
    /// </summary>
    /// <param name="userid">学生用户id</param>
    /// <returns></returns>
    Task DeleteStudentCurrentDoPaperPageInfoAsync(string userid);

    /// <summary>
    /// 设置学生正在书写的页面
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <param name="workpage">当前作答子页面</param>
    /// <returns></returns>
    Task SetStudentCurrentDoPaperPageInfoAsync(string userid, WorkbookPage workpage);

    /// <summary>
    /// 缓存卷码区域书写笔迹
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <param name="dots">点位数据</param>
    /// <returns></returns>
    Task HSetUserPaperNumAsync(string userid, List<PenDot> dots);

    /// <summary>
    /// 获取卷码区域书写点位笔迹
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <returns></returns>
    Task<List<PenDot>> HGetUserPaperNumAsync(string userid);

    /// <summary>
    /// 缓存识别的卷码
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <param name="paperno">卷码</param>
    /// <returns></returns>
    Task SaveRecognitionNumberAsync(string userid, string paperno);

    /// <summary>
    /// 清理临时卷码区域书写笔迹
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <returns></returns>
    Task HDelUserPaperNumAsync(string userid);

    /// <summary>
    /// 获取卷码对应的试卷信息
    /// </summary>
    /// <param name="paperno">卷码</param>
    /// <returns></returns>
    Task<string> HGetPaperNumAsync(string paperno);

    /// <summary>
    /// 设置卷码对应的试卷信息
    /// </summary>
    /// <param name="paperno">卷码</param>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task HSetPaperNumAsync(string paperno, string paperid);

    /// <summary>
    /// 获取该试卷是否开启了互批
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task<string> HGetCorrectAsync(string classid, string paperid);

    /// <summary>
    /// 是否包含当前页码
    /// </summary>
    /// <param name="pageid">页码id</param>
    /// <returns></returns>
    Task<bool> IsPageMemberAsync(string pageid);

    /// <summary>
    /// 获取被当前用户互批的学生id
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="userid">用户id</param>
    /// <returns></returns>
    Task<string> HGetEachCorrectStudentAsync(string paperid, string userid);

    /// <summary>
    /// 设置当前学生和被批改的学生对应关系
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="current_studentid">当前学生id</param>
    /// <param name="studentid">被批改的学生id</param>
    /// <returns></returns>
    Task HSetEachCorrectStudentAsync(string paperid, string current_studentid, string studentid);

    /// <summary>
    /// 保存当前学生正在书写的试卷子页面笔迹数据
    /// </summary>
    /// <param name="studentid">学生id</param>
    /// <param name="paperid">试卷id</param>
    /// <param name="pageid">页码id</param>
    /// <param name="teacher_log">笔迹</param>
    /// <returns></returns>
    Task SAddStudentCurrentPaperInfoAsync(string studentid, string paperid, string pageid, TeacherPenLog teacher_log);

    /// <summary>
    /// 删除卷码识别数据
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <returns></returns>
    Task DeleteRecognitionNumberAsync(string userid);

    /// <summary>
    /// 设置当前卷码
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <param name="classid">班级id</param>
    /// <param name="paperid">试卷id</param>
    /// <remarks>两小时失效</remarks>
    void SetCurrentPaperId(string teacherid, string classid, string paperid);

    /// <summary>
    /// 设置当前作答的题目id
    /// </summary>
    /// <param name="teacherid"></param>
    /// <param name="classid"></param>
    /// <param name="paperid"></param>
    /// <param name="itemid"></param>
    /// <param name="isFullLine"></param>
    void SetCurrentItemId(string teacherid, string classid, string paperid, string itemid,bool isFullLine=false);

	/// <summary>
	/// 获取试卷的作答方式 
	/// </summary>
	/// <param name="classId"></param>
	/// <param name="paperId"></param>
	/// <param name="modeNum">1 整卷作答；2单题作答</param>
	void SetPaperAnswerMode(string classId, string paperId, int modeNum);

	/// <inheritdoc />
	int? GetPaperAnswerMode(string classId, string paperId);

    /// <summary>
    /// 清除试卷作答方式
    /// </summary>
    /// <param name="classid"></param>
    /// <param name="paperid"></param>
    void RemovePaperAnswerMode(string classid, string paperid);

	/// <inheritdoc />
	void RemoveSingleItemAnswerState(string classid, string paperid);

	/// <inheritdoc />
	CurrentItem GetCurrentItem(string classid, string paperid);

	/// <inheritdoc />
	void RemoveCurrentItemId(string classid,string paperid);

    /// <summary>
    /// 清除当前卷码
    /// </summary>
    /// <param name="classid"></param>
    void RemoveCurrentPaperId(string classid);

	/// <summary>
	/// 获取当前一键赋码的试卷
	/// </summary>
	/// <param name="classid">班级id</param>
	/// <remarks>卷码纸专用</remarks>
	/// <returns></returns>
	CurrentPaperId GetCurrentPaperId(string classid);

    /// <summary>
    /// 设置当前卷码
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <param name="classid">班级id</param>
    /// <param name="paperid">试卷id</param>
    /// <remarks>10天过期</remarks>
    void SetCurrentPaperIdPermanent(string teacherid, string classid, string paperid);

    /// <summary>
    /// 清除当前持久化券码
    /// </summary>
    /// <param name="classid"></param>
    void RemoveCurrentPaperIdPermanent(string classid);

    /// <summary>
    /// 获取当前一键赋码的试卷
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <remarks>卷码纸专用</remarks>
    /// <returns></returns>
    CurrentPaperId GetCurrentPaperIdPermanent(string classid);

    /// <summary>
    /// 设置当前离线卷码
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <param name="classid">班级id</param>
    /// <param name="paperid">试卷id</param>
    /// <remarks>卷码纸专用</remarks>
    void SetCurrentPaperIdOffline(string teacherid, string classid, string paperid);

    /// <summary>
    /// 添加页码缓存
    /// </summary>
    /// <param name="pageid"></param>
    void SAddPageId(string pageid);

    /// <summary>
    /// 开启学生互批状态
    /// </summary>
    /// <param name="classid"></param>
    /// <param name="paperid"></param>
    void HSetCorrect(string classid, string paperid);

    /// <summary>
    /// 删除学生互批状态
    /// </summary>
    /// <param name="classid"></param>
    /// <param name="paperid"></param>
    void HDelCorrect(string classid, string paperid);

	/// <summary>
	/// 开启或删除学生互评
	/// </summary>
	/// <param name="classid"></param>
	/// <param name="paperid"></param>
	/// <param name="type"></param>
	void HSetAndDelComment(string classid, string paperid, int type);

    /// <summary>
    /// 获取学生互评的状态
    /// </summary>
    /// <param name="classid"></param>
    /// <param name="paperid"></param>
    /// <returns></returns>
    int HGetCommentState(string classid, string paperid);

	/// <summary>
	/// 获取学生互批数据
	/// </summary>
	/// <param name="studentid">学生id</param>
	/// <param name="paperid">试卷id</param>
	/// <param name="pageid">页码id</param>
	/// <returns></returns>
	List<TeacherPenLog> SMemberStudentCurrentPaperInfo(string studentid, string paperid, string pageid);
}