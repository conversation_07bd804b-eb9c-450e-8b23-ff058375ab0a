﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 学生端项目化实践阶段任务提交豆包输出接口输出
    /// </summary>
    public class StudentProjectSubmitDouBaoOutput
    {
        /// <summary>
        /// 分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 等第
        /// </summary>
        public string? Level { get; set; }

        /// <summary>
        /// 评估结果
        /// </summary>
        public string? AssessmentResult { get; set; }

        /// <summary>
        /// 主题Id
        /// </summary>
        public List<string> QuestionIds { get; set; } = new List<string>();
    }
}
