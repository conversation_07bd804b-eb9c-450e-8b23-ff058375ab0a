/*
 *Author：jxx
 *Contact：<EMAIL>
 *代码由框架生成,此处任何更改都可能导致被代码生成器覆盖
 *所有业务编写全部应在Partial文件夹下Base_SemesterTimeService与IBase_SemesterTimeService中编写
 */
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Entity.DomainModels;
using Uwoo.Model.CustomException;
using Uwoo.System.IRepositories;
using Uwoo.System.IServices;
using UwooAgent.Core.CacheManager.IBusinessCacheService;
using UwooAgent.Entity.DomainModels.Base;
using UwooAgent.Model.SemesterTime;

namespace Uwoo.System.Services
{
    public class Base_SemesterTimeService : ServiceBase<Base_SemesterTime, IBase_SemesterTimeRepository>, IBase_SemesterTimeService, IDependency
    {
        #region DI
        private readonly IBase_SemesterTimeRepository _repository;//访问数据库
        private readonly ISchoolCacheService _schoolCacheService;

        [ActivatorUtilitiesConstructor]
        public Base_SemesterTimeService(
            IBase_SemesterTimeRepository dbRepository,
            ISchoolCacheService schoolCacheService
            )
        : base(dbRepository)
        {
            _repository = dbRepository;
            _schoolCacheService = schoolCacheService;
        }
        #endregion

        /// <summary>
        /// 获取当前学年学期时间
        /// </summary>
        /// <returns></returns>
        public async Task<NowSemesterTime> GetNowYearSemesterTime()
        {
            string sql = "SELECT * FROM Base_SemesterTime where Deleted=0 and getdate()>=YearStartDate and getdate()<=YearEndDate";
            NowSemesterTime nowSemesterTime = await DBSqlSugar.SqlQueryable<Base_SemesterTime>(sql)
                .Select(p => new NowSemesterTime
                {
                    Year = p.Year,
                    YearStartDate = p.YearStartDate,
                    YearEndDate = p.YearEndDate,
                    FallStartDate = p.FallStartDate,
                    FallEndDate = p.FallEndDate,
                    SpringStartDate = p.SpringStartDate,
                    SpringEndDate = p.SpringEndDate
                }).FirstAsync();
            if (nowSemesterTime != null)
            {
                if (nowSemesterTime.FallStartDate <= DateTime.Now && nowSemesterTime.FallEndDate >= DateTime.Now)
                {
                    nowSemesterTime.NowTerm = 1;
                }
                else
                {
                    nowSemesterTime.NowTerm = 2;
                }
            }
            return nowSemesterTime;
        }
    }
}
