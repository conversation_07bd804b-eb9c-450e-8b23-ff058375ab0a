﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Utilities
{
	public class CacheKeyRuleHelper
	{
		/// <summary>
		/// 按照一定规则，获取函数CacheKey
		/// </summary>
		/// <typeparam name="T"></typeparam>
		/// <param name="prefix"></param>
		/// <param name="functionInputDto">函数的入参</param>
		/// <param name="version">版本</param>
		/// <returns></returns>
		public static string GetFunctionCacheKey<T>(string prefix, T functionInputDto, string version) where T : class
		{
			if (string.IsNullOrWhiteSpace(prefix))
			{
				return $"{TypeHelper.GetPropertyNameValuesStringJoinKey(functionInputDto)}_{version}";
			}
			return $"{prefix}_{TypeHelper.GetPropertyNameValuesStringJoinKey(functionInputDto)}_{version}";
		}
	}
}
