﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取教案内容要求信息输出
    /// </summary>
    public class GetTeachingPlanContentDemandOutput
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Describe { get; set; }

        /// <summary>
        /// 类型（1：系统，2：自定义）
        /// </summary>
        public int Type { get; set; }
    }
}
