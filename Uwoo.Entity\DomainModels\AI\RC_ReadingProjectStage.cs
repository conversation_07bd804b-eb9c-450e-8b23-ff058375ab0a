using SqlSugar;
using System;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 阅读理解项目化实践阶段表
    /// </summary>
    [SugarTable("RC_ReadingProjectStage")]
    public class RC_ReadingProjectStage : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 阅读理解项目Id
        /// </summary>
        public string ReadingProjectId { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string StageName { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string StageDescribe { get; set; }

        /// <summary>
        /// 阶段排序
        /// </summary>
        public int StageOrder { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }
    }
}
