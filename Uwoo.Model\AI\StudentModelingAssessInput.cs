﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 学生端建模阶段任务作品评估接口入参
    /// </summary>
    public class StudentModelingAssessInput
    {
        /// <summary>
        /// 建模阶段任务ID
        /// </summary>
        public string? ModelingStageTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 文件信息
        /// </summary>
        public List<StudentModelingAssessFileInfoInput> Files { get; set; } = new List<StudentModelingAssessFileInfoInput>();
    }

    /// <summary>
    /// 学生端建模阶段任务作品评估接口入参
    /// </summary>
    public class StudentModelingAssessFileInfoInput
    {
        /// <summary>
        /// 文件地址
        /// </summary>
        public string? FileUrl { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }
    }
}
