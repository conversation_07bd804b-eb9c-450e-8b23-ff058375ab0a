using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;
using Uwoo.Model.CustomException;
using UwooAgent.Model;

namespace Uwoo.WebApi.Controllers.AI
{
    /// <summary>
    /// 阅读理解智能体教师端控制器
    /// </summary>
    [Route("api/AgentRCTeacher/[action]")]
    [ApiController]
    public class ReadingComprehensionTeacherController : ApiBaseController<IReadingComprehensionTeacherService>
    {
        private readonly IReadingComprehensionTeacherService _readingComprehensionTeacherService;

        public ReadingComprehensionTeacherController(IReadingComprehensionTeacherService readingComprehensionTeacherService)
        {
            _readingComprehensionTeacherService = readingComprehensionTeacherService;
        }

        /// <summary>
        /// 保存阅读理解任务信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<SaveReadingTaskOutput> SaveReadingTask(SaveReadingTaskInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.Name))
            {
                throw new BusException("项目名称不能为空!");
            }
            if (string.IsNullOrWhiteSpace(input.GradeId))
            {
                throw new BusException("年级Id不能为空！");
            }
            // 验证项目化实践阶段
            if (input.ProjectStageInfos == null || input.ProjectStageInfos.Count == 0)
            {
                throw new BusException("项目化实践阶段不能为空!");
            }

            // 验证每个阶段和任务
            foreach (var stage in input.ProjectStageInfos)
            {
                if (string.IsNullOrEmpty(stage.Name))
                {
                    throw new BusException("阶段名称不能为空!");
                }

                if (stage.ProjectStageTaskInfos == null || stage.ProjectStageTaskInfos.Count == 0)
                {
                    throw new BusException($"阶段「{stage.Name}」必须包含至少一个任务!");
                }

                // 验证每个任务
                foreach (var task in stage.ProjectStageTaskInfos)
                {
                    if (string.IsNullOrEmpty(task.Name))
                    {
                        throw new BusException($"阶段「{stage.Name}」中的任务名称不能为空!");
                    }

                    if (task.TaskType < 1 || task.TaskType > 7)
                    {
                        throw new BusException("任务类型错误!支持的类型：1-成果评估、2-情景对话、3-知识问答、4-视频、5-文档、6-思维导图、7-选词填空");
                    }

                    // 根据任务类型进行特定验证
                    ValidateTaskByType(task, stage.Name);
                }
            }

            return await _readingComprehensionTeacherService.SaveReadingTaskNew(input);
        }

        /// <summary>
        /// 获取阅读理解任务详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ReadingTaskDetailsOutput> GetReadingTaskDetails(ReadingTaskDetailsInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("阅读理解项目Id不能为空!");
            }
            return await _readingComprehensionTeacherService.GetReadingTaskDetails(input);
        }

        /// <summary>
        /// 获取阅读理解项目详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ProjectTaskDetailsOutput> GetProjectTaskDetails(ProjectTaskDetailsInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("阅读理解项目Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }
            return await _readingComprehensionTeacherService.GetProjectTaskDetails(input);
        }

        /// <summary>
        /// 获取阅读理解任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ReadingTaskListOutput> GetReadingTaskList(ReadingTaskListInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }
            return await _readingComprehensionTeacherService.GetReadingTaskList(input);
        }

        /// <summary>
        /// 获取阅读理解项目列表（与项目化实践接口保持一致）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageReturn<GetProjectTaskListOutput>> GetProjectTaskList(GetProjectTaskListInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId)
                || string.IsNullOrEmpty(input.SchoolId)
                || string.IsNullOrEmpty(input.AgentId)
                || string.IsNullOrEmpty(input.SubjectId)
                || string.IsNullOrEmpty(input.GradeId))
            {
                throw new BusException("参数异常!");
            }
            if (!new[] { 1, 2, 3 }.Contains(input.AgentTaskType))
            {
                throw new BusException("智能体任务类型只能是1（口语交际）、2（项目化实践）、3（阅读理解）！");
            }
            if (input.PageIndex <= 0)
            {
                input.PageIndex = 1;
            }
            if (input.PageSize <= 0)
            {
                input.PageSize = 10;
            }
            if (input.PublishStatus != 1 && input.PublishStatus != 2)
            {
                input.PublishStatus = 0;
            }
            return await _readingComprehensionTeacherService.GetProjectTaskList(input);
        }

        /// <summary>
        /// 删除阅读理解任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<bool> DeleteReadingTask(DeleteReadingTaskInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("阅读理解项目Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }
            return await _readingComprehensionTeacherService.DeleteReadingTask(input);
        }

        /// <summary>
        /// 发布阅读理解任务到班级
        /// </summary>
        /// <param name="input">发布参数</param>
        /// <returns></returns>
        [HttpPost]
        public async Task PublishReadingTaskToClass(PublishProjectTaskToClassInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("阅读理解项目Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }

            if (input.ClassIds == null || !input.ClassIds.Any())
            {
                throw new BusException("至少需要选择一个班级!");
            }

            if (input.TimeRange == null || input.TimeRange.Count != 2 || !input.TimeRange[0].HasValue || !input.TimeRange[1].HasValue)
            {
                throw new BusException("请提供有效的时间范围!");
            }

            if (input.TimeRange[0].Value >= input.TimeRange[1].Value)
            {
                throw new BusException("开始时间必须小于结束时间!");
            }
            await _readingComprehensionTeacherService.PublishReadingTaskToClass(input);
        }

        /// <summary>
        /// 撤销阅读理解任务发布
        /// </summary>
        /// <param name="input">撤销发布参数</param>
        /// <returns></returns>
        [HttpPost]
        public async Task UnpublishProjectTask(UnpublishProjectTaskInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("阅读理解项目Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }
            await _readingComprehensionTeacherService.UnpublishProjectTask(input);
        }

        /// <summary>
        /// 获取学生完成情况统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ReadingTaskStudentStatisticsOutput> GetStudentStatistics(ReadingTaskStudentStatisticsInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("阅读理解项目Id不能为空!");
            }
            return await _readingComprehensionTeacherService.GetStudentStatistics(input);
        }

        /// <summary>
        /// 获取阅读理解项目综合分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetProjectSynthesizeAnalyseOutput> GetProjectSynthesizeAnalyse(GetProjectSynthesizeAnalyseInput input)
        {
            if (string.IsNullOrEmpty(input.ClassId) || string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("参数异常!");
            }
            return await _readingComprehensionTeacherService.GetProjectSynthesizeAnalyse(input);
        }

        /// <summary>
        /// 获取阅读理解项目阶段任务统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<GetProjectStageTaskCountOutput>> GetProjectStageTaskCount(GetProjectStageTaskCountInput input)
        {
            if (string.IsNullOrEmpty(input.ClassId) || string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("参数异常!");
            }
            return await _readingComprehensionTeacherService.GetProjectStageTaskCount(input);
        }

        /// <summary>
        /// 获取阅读理解项目学生统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetProjectStudentCountOutput> GetProjectStudentCount(GetProjectStudentCountInput input)
        {
            if (string.IsNullOrEmpty(input.ClassId) || string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("参数异常!");
            }
            return await _readingComprehensionTeacherService.GetProjectStudentCount(input);
        }

        /// <summary>
        /// 获取学生做阅读理解任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentDoReadingTaskListOutput> GetStudentDoReadingTaskList(GetStudentDoReadingTaskListInput input)
        {
            if (string.IsNullOrEmpty(input.ReadingProjectId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("参数异常!");
            }
            return await _readingComprehensionTeacherService.GetStudentDoReadingTaskList(input);
        }

        /// <summary>
        /// 根据任务类型进行特定验证
        /// </summary>
        /// <param name="task"></param>
        /// <param name="stageName"></param>
        private void ValidateTaskByType(ReadingProjectStageTaskInfoInput task, string stageName)
        {
            switch (task.TaskType)
            {
                case 1: // 成果评估
                case 2: // 情景对话
                case 3: // 知识问答
                    // 基础任务类型，无特殊验证要求
                    break;
                case 4: // 视频任务
                    if (task.VideoResources == null || task.VideoResources.Count == 0)
                    {
                        throw new BusException($"阶段「{stageName}」中的视频任务「{task.Name}」必须包含至少一个视频资源!");
                    }
                    if (task.VideoResources.Count > 5)
                    {
                        throw new BusException($"阶段「{stageName}」中的视频任务「{task.Name}」最多支持5个视频资源!");
                    }
                    break;
                case 5: // 文档任务
                    if (task.DocumentResources == null || task.DocumentResources.Count == 0)
                    {
                        throw new BusException($"阶段「{stageName}」中的文档任务「{task.Name}」必须包含至少一个文档资源!");
                    }
                    break;
                case 6: // 思维导图任务
                    // 思维导图任务暂无特殊验证要求
                    break;
                case 7: // 选词填空任务
                    if (string.IsNullOrEmpty(task.QuestionContent))
                    {
                        throw new BusException($"阶段「{stageName}」中的选词填空任务「{task.Name}」必须设置题目内容!");
                    }
                    if (task.CorrectAnswers == null || task.CorrectAnswers.Count == 0)
                    {
                        throw new BusException($"阶段「{stageName}」中的选词填空任务「{task.Name}」必须设置正确答案!");
                    }
                    break;
            }
        }

        /// <summary>
        /// 获取学生提交内容详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentSubmitContentOutput> GetStudentSubmitContent(GetStudentSubmitContentInput input)
        {
            if (string.IsNullOrEmpty(input.TaskSubmitId))
            {
                throw new BusException("任务提交记录ID不能为空!");
            }

            return await _readingComprehensionTeacherService.GetStudentSubmitContent(input);
        }

        /// <summary>
        /// 教师端阅读理解下载学生成果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ReadingDownloadStudentAchievement(ReadingDownloadStudentAchievementInput input)
        {
            if (string.IsNullOrEmpty(input.ClassId) || string.IsNullOrEmpty(input.ReadingId))
            {
                throw new BusException("参数异常!");
            }
            byte[] stream = await _readingComprehensionTeacherService.ReadingDownloadStudentAchievement(input);
            //返回
            return File(stream, "application/zip", "Reading.zip");
        }

        /// <summary>
        /// 教师端阅读理解下载学生评估结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ReadingDownloadStudentResult(ReadingDownloadStudentResultInput input)
        {
            if (string.IsNullOrEmpty(input.ClassId) || string.IsNullOrEmpty(input.ReadingId))
            {
                throw new BusException("参数异常!");
            }
            byte[] stream = await _readingComprehensionTeacherService.ReadingDownloadStudentResult(input);
            //返回
            return File(stream, "application/zip", "Reading.zip");
        }
    }
}
