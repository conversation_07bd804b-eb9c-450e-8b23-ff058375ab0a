﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.CacheManager.IService;
using UwooAgent.Entity.DomainModels.School;

namespace UwooAgent.Core.CacheManager.IBusinessCacheService
{
    /// <summary>
    /// 学校缓存
    /// </summary>
    public interface ISchoolCacheService : IRedisService
    {
        /// <summary>
        /// 保存学校信息
        /// </summary>
        /// <param name="school"></param>
        void SaveSchoolInfo(Exam_School school);

        /// <summary>
        /// 获取学校信息
        /// </summary>
        /// <param name="schoolId"></param>
        Exam_School GetSchoolInfo(string schoolId);

        Exam_School GetSchoolInfo(string schoolId, Func<string, Exam_School> GetSchoolById);

    }
}
