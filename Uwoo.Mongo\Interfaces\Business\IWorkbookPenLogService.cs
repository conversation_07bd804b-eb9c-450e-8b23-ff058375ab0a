﻿// -- Function：IWorkbookPenLogService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2024/3/19 16:53
namespace Uwoo.Mongo.Interfaces.Business;

using Uwoo.Mongo.Interfaces.Lifecycle;
using Uwoo.Mongo.Models;

/// <summary>
/// 练习薄
/// </summary>
public interface IWorkbookPenLogService : IMongoAutoService<WorkbookPenLog>
{

	/// <inheritdoc />
	public List<WorkbookPenLog> GetWorkbookPenLogListByItemId(string colname, string paperId, string userId, string itemId);

	/// <inheritdoc />
	public List<WorkbookPenLog> GetWorkbookPenLogsByUserId(string colname, string paperId, string userId, int? year = null);

	/// <summary>
	/// 删除练习簿笔迹
	/// </summary>
	/// <param name="colname"></param>
	/// <param name="paperId"></param>
	/// <param name="userid"></param>
	void DeleteWorkbookPenLog(string colname, string paperId, List<string> userid);

	/// <summary>
	/// 获取已作答用户列表
	/// </summary>
	/// <param name="colname">集合名称</param>
	/// <param name="paperId"></param>
	/// <param name="userid">用户id集合</param>
	/// <param name="year">学年</param>
	/// <returns></returns>
	List<string> GetUserList(string colname, string paperId, List<string> userid, int? year = null);

	/// <summary>
	/// 根据试题id 获取已作答的学生id
	/// </summary>
	/// <param name="colname"></param>
	/// <param name="paperId"></param>
	/// <param name="itemId"></param>
	/// <param name="userid"></param>
	/// <param name="year">学年</param>
	/// <returns></returns>
	List<string> GetUserListByItemId(string colname, string paperId, string itemId, List<string> userid, int? year = null);

	/// <summary>
	/// 删除学生笔迹
	/// </summary>
	/// <param name="colname">集合名称</param>
	/// <param name="paperId">paperId</param>
	/// <param name="userid">用户id集合</param>
	void DeletePenLog(string colname, string paperId, List<string> userid);
}