﻿using Uwoo.Builder.IRepositories.Core;
using Uwoo.Builder.IServices;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Entity.DomainModels.Core;

namespace Uwoo.Builder.Services
{
	public partial class Sys_TableInfoService : ServiceBase<Sys_TableInfo, ISys_TableInfoRepository>, ISys_TableInfoService, IDependency
	{
		public Sys_TableInfoService(ISys_TableInfoRepository repository)
			 : base(repository)
		{
			Init(repository);
		}
		public static ISys_TableInfoService Instance
		{
			get { return AutofacContainerModule.GetService<ISys_TableInfoService>(); }
		}
	}
}

