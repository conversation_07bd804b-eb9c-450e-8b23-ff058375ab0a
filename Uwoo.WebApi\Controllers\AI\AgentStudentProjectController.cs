﻿using Coldairarrow.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.CustomException;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;
using UwooAgent.System.Services.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_学生端项目化实践
    /// </summary>
    [Route("/AgentStudentProject/[controller]/[action]")]
    [ApiController]
    public class AgentStudentProjectController : ApiBaseController<IAgentStudentProjectService>
    {
        #region DI
        private readonly IAgentStudentProjectService _agentStudentProjectService;
        private readonly IAgentCommonService _agentCommonService;
        public AgentStudentProjectController(
            IAgentStudentProjectService agentStudentProjectService,
            IAgentCommonService agentCommonService)
        {
            _agentStudentProjectService = agentStudentProjectService;
            _agentCommonService = agentCommonService;
        }
        #endregion

        /// <summary>
        /// 获取学生端项目化实践信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentProjectInfoOutput> GetStudentProjectInfo(GetStudentProjectInfoInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("项目化实践任务Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.ClassId))
            {
                throw new BusException("班级Id不能为空!");
            }
            return await _agentStudentProjectService.GetStudentProjectInfo(input);
        }

        /// <summary>
        /// 学生端作品评估
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentAssessOutput> StudentAssess(StudentAssessInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectStageTaskId))
            {
                throw new BusException("项目化实践阶段任务Id不能为空!");
            }
            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("学生Id不能为空!");
            }
            if (input.Files == null || input.Files.Count <= 0)
            {
                throw new BusException("文件信息不能为空!");
            }
            else
            {
                foreach (var file in input.Files)
                {
                    if (string.IsNullOrEmpty(file.FileUrl))
                    {
                        throw new BusException($"第{input.Files.IndexOf(file) + 1}个文件地址不能为空!");
                    }
                    if (string.IsNullOrEmpty(file.FileName))
                    {
                        throw new BusException($"第{input.Files.IndexOf(file) + 1}个文件名称不能为空!");
                    }
                }
            }

            return await _agentStudentProjectService.StudentAssess(input);
        }

        /// <summary>
        /// 学生端情景对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task StudentDialogue(StudentDialogueInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.ProjectStageTaskId)
                    || string.IsNullOrEmpty(input.StudentId)
                    || (string.IsNullOrEmpty(input.Msg) && string.IsNullOrEmpty(input.AudioUrl)))
                {
                    throw new Exception("参数异常!");
                }
                if (!string.IsNullOrEmpty(input.AudioUrl))
                {
                    string fileExtension = Path.GetExtension(input.AudioUrl);
                    if (fileExtension.ToLower() != ".wav" && fileExtension.ToLower() != ".mp3")
                    {
                        throw new Exception("非常抱歉，目前我们仅支持 wav、mp3 音频格式，还请您谅解!");
                    }
                    input.Msg = await _agentCommonService.AudioFileChangeText(new AudioFileChangeTextInput()
                    {
                        FileUrl = input.AudioUrl,
                    });
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentStudentProjectService.StudentDialogue(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 学生端情景对话提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<StudentDialogueSubmitOutput> StudentDialogueSubmit(StudentDialogueSubmitInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectStageTaskId) || string.IsNullOrEmpty(input.StudentId))
            {
                throw new Exception("参数异常!");
            }

            return await _agentStudentProjectService.StudentDialogueSubmit(input);
        }

        /// <summary>
        /// 学生端知识问答
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task StudentKnowledge(StudentKnowledgeInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.ProjectStageTaskId)
                    || string.IsNullOrEmpty(input.StudentId)
                    || (string.IsNullOrEmpty(input.Msg) && string.IsNullOrEmpty(input.AudioUrl)))
                {
                    throw new Exception("参数异常!");
                }
                if (!string.IsNullOrEmpty(input.AudioUrl))
                {
                    string fileExtension = Path.GetExtension(input.AudioUrl);
                    if (fileExtension.ToLower() != ".wav" && fileExtension.ToLower() != ".mp3")
                    {
                        throw new Exception("非常抱歉，目前我们仅支持 wav、mp3 音频格式，还请您谅解!");
                    }
                    input.Msg = await _agentCommonService.AudioFileChangeText(new AudioFileChangeTextInput()
                    {
                        FileUrl = input.AudioUrl,
                    });
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentStudentProjectService.StudentKnowledge(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 学生端项目化实践阶段任务提交未达标对话记录备份
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task StudentSubmitNoStandardBackups(StudentSubmitNoStandardBackupsInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectStageTaskId)
                || string.IsNullOrEmpty(input.StudentId)
                || string.IsNullOrEmpty(input.TaskSubmitId)
                || string.IsNullOrEmpty(input.AgentId))
            {
                throw new Exception("参数异常!");
            }
            await _agentStudentProjectService.StudentSubmitNoStandardBackups(input);
        }

        /// <summary>
        /// 获取学生做项目化实践阶段任务结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentDoTaskResultOutput> GetStudentDoTaskResult(GetStudentDoTaskResultInput input)
        {
            if (string.IsNullOrEmpty(input.StudentId) || string.IsNullOrEmpty(input.TaskSubmitId))
            {
                throw new Exception("参数异常!");
            }
            return await _agentStudentProjectService.GetStudentDoTaskResult(input);
        }

        /// <summary>
        /// 获取学生端未达标列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentNoStandardListOutput> GetStudentNoStandardList(GetStudentNoStandardListInput input)
        {
            if (string.IsNullOrEmpty(input.StudentId) || string.IsNullOrEmpty(input.ProjectId))
            {
                throw new Exception("参数异常!");
            }
            return await _agentStudentProjectService.GetStudentNoStandardList(input);
        }
    }
}
