﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model
{
    public class UniGroupQo
    {
        /// <summary>
        /// GroupId
        /// </summary>
        [DataMember(Name = "group_id")]
        public int? GroupId { get; set; }

        /// <summary>
        /// GroupName
        /// </summary>
        [DataMember(Name = "group_name")]
        public string GroupName { get; set; }

        /// <summary>
        /// ParentId
        /// </summary>
        [DataMember(Name = "parent_id")]
        public int? ParentId { get; set; }

        /// <summary>
        /// ThreadId
        /// </summary>
        [DataMember(Name = "thread_id")]
        public int? ThreadId { get; set; }

        /// <summary>
        ///  (组标记，0: 普通组，1: 学校，2: 年级，3: 班级)
        /// </summary>
        [DataMember(Name = "group_flag")]
        public int? GroupFlag { get; set; }
    }
}
