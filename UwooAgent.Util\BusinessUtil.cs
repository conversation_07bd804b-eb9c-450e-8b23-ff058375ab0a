﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UwooAgent.Util.Model;
using UwooAgent.Util.EnumClass;

namespace UwooAgent.Util
{
	public class BusinessUtil
	{
		/// <summary>
		/// 本学期开学时间
		/// </summary>
		public static string SchoolOpenTime()
		{
			var now = DateTime.Now;
			if (now.Month >= 9 || (now.Month == 8 && now.Day > 23))
			{
				return RedisHelper.HGet("SchoolTerm", "TermStart");// 本学期开始时间
				//return "2024-09-02 00:00:00";
			}
			else
			{
				if(now < DateTime.Parse(RedisHelper.HGet("SchoolTerm", "TermEnd")))
				{
					return  RedisHelper.HGet("SchoolTerm", "TermStart");
				}
				return RedisHelper.HGet("SchoolTerm", "TermEnd");// 本学期结束时间
			}
		}

		/// <summary>
		/// 配置测试学期，如果不想测试了，就改为null
		/// </summary>
		public readonly static int? ConfigTestTerm;

		static BusinessUtil()
		{
#if DEBUG
			ConfigTestTerm = 2;
#endif
		}

		/// <summary>
		/// 当前学年时间段范围
		/// </summary>
		/// <param name="now"></param>
		/// <returns></returns>
		public static Tuple<DateTime, DateTime> CurrentTermDateTimeRange(DateTime now)
		{
			//return new Tuple<DateTime, DateTime>(new DateTime(now.Year, 9, 1, 0, 0, 0), new DateTime(now.Year + 1, 9, 1, 0, 0, 0));

			//当前时间月份大于9表示今年已经是新学年    
			if (now.Month >= 9 || (now.Month == 8 && now.Day > 23))
			{
				return new Tuple<DateTime, DateTime>(new DateTime(now.Year, 9, 1, 0, 0, 0), new DateTime(now.Year + 1, 9, 1, 0, 0, 0));
			}
			else if (now.Month < 9)  //当前时间月份小于9表示去年创建的学年
			{
				//#region 9月1日删除
				//if (now > Convert.ToDateTime("2022-8-26 00:00:00") && now < Convert.ToDateTime("2022-9-1 00:00:00"))
				//{
				//    return new Tuple<DateTime, DateTime>(new DateTime(now.Year, 9, 1, 0, 0, 0), new DateTime(now.Year + 1, 9, 1, 0, 0, 0));
				//}
				//#endregion

				return new Tuple<DateTime, DateTime>(new DateTime(now.Year - 1, 9, 1, 0, 0, 0), new DateTime(now.Year, 9, 1, 0, 0, 0));
			}
			throw new BusException("时间错误");
		}

		/// <summary>
		/// return  1 上学期  2 下学期
		/// </summary>
		/// <param name="now"></param>
		/// <returns></returns>
		public static int CurrentTerm(DateTime now)
		{
			if (now.Month >= 9 || (now.Month == 8 && now.Day > 23))
			{
				return 1;
			}
			else if (now.Month < 9)
			{
				//DateTime data = new DateTime(now.Year, 1, 1, new System.Globalization.ChineseLunisolarCalendar());
				//System.Globalization.Calendar calendar = System.Globalization.CultureInfo.InvariantCulture.Calendar;

				//int year = calendar.GetYear(data);
				//int month = calendar.GetMonth(data);
				//int day = calendar.GetDayOfMonth(data);

				////正月初八 之前，属于上学期
				//var lanternFestival = data.AddDays(7);
				//if (lanternFestival < now)
				//{
				//	return 2;
				//}
				if (now < DateTime.Parse(RedisHelper.HGet("SchoolTerm", "TermEnd")))
				{
					return 1;
				}
				return 2;
			}
			throw new BusException("时间错误");
		}

		/// <summary>
		/// return  1 上学期  2 下学期
		/// <para>为方便测试时获取学期制定的方法，BusinessUtil.ConfigTestTerm 字段设置为NULL,会自动获取当前学期</para>
		/// </summary>
		/// <returns></returns>
		public static int CurrentTermForEasyTestManage()
		{
			return CurrentTermForEasyTestManage(DateTime.Now);
		}

		/// <summary>
		/// return  1 上学期  2 下学期
		/// <para>指定学科，方便整个学科用是同一个学期的逻辑</para>
		/// <para>为方便测试时获取学期制定的方法，BusinessUtil.ConfigTestTerm 字段设置为NULL,会自动获取当前学期</para>
		/// </summary>
		/// <param name="subjectId">学科</param>
		/// <returns></returns>
		public static int CurrentTermForEasyTestManage(int subjectId)
		{
			if (subjectId == 22)
				return CurrentTerm(DateTime.Now);

			return CurrentTermForEasyTestManage(DateTime.Now);
		}

		/// <summary>
		/// return  1 上学期  2 下学期
		/// <para>指定学科，方便整个学科用是同一个学期的逻辑</para>
		/// <para>为方便测试时获取学期制定的方法，BusinessUtil.ConfigTestTerm 字段设置为NULL,会自动获取当前学期</para>
		/// </summary>
		/// <param name="subject">学科</param>
		/// <returns></returns>
		public static int CurrentTermForEasyTestManage(Subject subject)
		{
			if (subject == Subject.Math)
				return CurrentTerm(DateTime.Now);

			return CurrentTermForEasyTestManage(DateTime.Now);
		}

		/// <summary>
		/// return  1 上学期  2 下学期
		/// <para>为方便测试时获取学期制定的方法，BusinessUtil.ConfigTestTerm 字段设置为NULL,会自动获取当前学期</para>
		/// </summary>
		/// <param name="now"></param>
		/// <returns></returns>
		public static int CurrentTermForEasyTestManage(DateTime now)
		{
			if (ConfigTestTerm.HasValue)
				return ConfigTestTerm.Value;

			return CurrentTerm(now);
		}

		/// <summary>
		/// return  1 上学期  2 下学期
		/// <para>为方便测试时获取学期制定的方法，BusinessUtil.ConfigTestTerm 字段设置为NULL,会自动获取当前学期</para>
		/// <para>term：前端主动传入的值（这也是最合理的业务），如果没有，就取服务端默认的配置学期</para>
		/// </summary>
		/// <param name="term">前端主动传入的term</param>
		/// <returns></returns>
		public static int CurrentTermForEasyTestManage(int? term)
		{
			if (term.HasValue)
				return term.Value;

			return CurrentTermForEasyTestManage(DateTime.Now);
		}

		/// <summary>
		/// 获取制作年份
		/// </summary>
		/// <param name="year"></param>
		/// <returns></returns>
		public static Tuple<DateTime, DateTime> GetProductionTime(int? year)
		{
			if (year == 2020)
			{
				return new Tuple<DateTime, DateTime>(new DateTime(2020, 9, 1, 0, 0, 0), new DateTime(2021, 8, 1, 0, 0, 0));
			}
			else if (year == 2021)
			{
				return new Tuple<DateTime, DateTime>(new DateTime(2021, 9, 1, 0, 0, 0), new DateTime(2022, 8, 1, 0, 0, 0));
			}
			else if (year == 2022)
			{
				return new Tuple<DateTime, DateTime>(new DateTime(2022, 9, 1, 0, 0, 0), new DateTime(2023, 8, 1, 0, 0, 0));
			}
			else if (year == 2023)
			{
				return new Tuple<DateTime, DateTime>(new DateTime(2023, 9, 1, 0, 0, 0), new DateTime(2024, 8, 1, 0, 0, 0));
			}
			else if (year == 2024)
			{
				return new Tuple<DateTime, DateTime>(new DateTime(2024, 9, 2, 0, 0, 0), new DateTime(2025, 8, 1, 0, 0, 0));
			}
			else if (year == 2025)
			{
				return new Tuple<DateTime, DateTime>(new DateTime(2025, 9, 1, 0, 0, 0), new DateTime(2026, 8, 1, 0, 0, 0));
			}
			else if (year == 2017)
			{
				return new Tuple<DateTime, DateTime>(new DateTime(2017, 9, 1, 0, 0, 0), new DateTime(2018, 8, 1, 0, 0, 0));
			}
			else if (year == 2018)
			{
				return new Tuple<DateTime, DateTime>(new DateTime(2018, 9, 1, 0, 0, 0), new DateTime(2019, 8, 1, 0, 0, 0));
			}
			else if (year == 2019)
			{
				return new Tuple<DateTime, DateTime>(new DateTime(2019, 9, 1, 0, 0, 0), new DateTime(2020, 8, 1, 0, 0, 0));
			}
			return null;
		}

		/// <summary>
		/// 获得当前学期字符串
		/// </summary>
		/// <param name="now"></param>
		/// <returns></returns>
		public static string GetCurrentTerm(DateTime now)
		{
			var currentTerm = CurrentTermDateTimeRange(now);
			var term = CurrentTerm(now);
			return $"{currentTerm.Item1.Year}_{term}";
			//if (currentTerm.Item1.Month >= 9)
			//{
			//	return $"{currentTerm.Item1.Year}_1";
			//}
			//else
			//{

			//	//#region 9月1日删除
			//	//if (now > Convert.ToDateTime("2022-8-26 00:00:00") && now < Convert.ToDateTime("2022-9-1 00:00:00"))
			//	//{
			//	//    return $"{currentTerm.Item1.Year}_1";
			//	//}
			//	//#endregion

			//	return $"{currentTerm.Item1.Year}_2";
			//}
		}

		/// <summary>
		/// 查某个时间当前学期的开始和结束时间，默认本学期
		/// </summary>
		/// <param name="dtTime">默认当前时间</param>
		/// <param name="Term">1 上学期，2下学期</param>
		/// <returns></returns>
		public static Tuple<DateTime, DateTime> OneTermDateTimeRange(DateTime dtTime, int Term = 0)
		{
			//int day = 20;
			var dtRange = CurrentTermDateTimeRange(dtTime);
			if (Term == 0)
			{
				var dtTerm = CurrentTerm(dtTime);
				if (dtTerm == 1)
				{
					//上学期 时间在
					return new Tuple<DateTime, DateTime>(new DateTime(dtRange.Item1.Year, 9, 1, 0, 0, 0), new DateTime(dtRange.Item2.Year, 2, 8, 0, 0, 0));
				}
				else
				{
					//下学期 时间在
					return new Tuple<DateTime, DateTime>(new DateTime(dtRange.Item2.Year, 2, 8, 0, 0, 0), new DateTime(dtRange.Item2.Year, 9, 1, 0, 0, 0));
				}
			}
			else if (Term == 1)
			{
				//查某个时间上学期
				return new Tuple<DateTime, DateTime>(new DateTime(dtRange.Item1.Year, 9, 1, 0, 0, 0), new DateTime(dtRange.Item2.Year, 2, 8, 0, 0, 0));
			}
			else if (Term == 2)
			{
				//查某个时间下学期
				return new Tuple<DateTime, DateTime>(new DateTime(dtRange.Item2.Year, 2, 8, 0, 0, 0), new DateTime(dtRange.Item2.Year, 9, 1, 0, 0, 0));
			}
			// 上下学期
			else if (Term == 3)
			{
				return new Tuple<DateTime, DateTime>(new DateTime(dtRange.Item1.Year, 9, 1, 0, 0, 0), new DateTime(dtRange.Item2.Year, 9, 1, 0, 0, 0));
			}

			return new Tuple<DateTime, DateTime>(new DateTime(dtRange.Item1.Year, 9, 1, 0, 0, 0), new DateTime(dtRange.Item2.Year, 2, 8, 0, 0, 0));
		}

		/// <summary>
		/// 获取当前学年
		/// </summary>
		/// <param name="now"></param>
		/// <returns></returns>
		public static int GetCurrentYear(DateTime now)
		{
			if (now.Month >= 9 || (now.Month == 8 && now.Day > 23))
			{
				return now.Year;
			}
			else
			{
				//#region 9月1日删除
				//if (now > Convert.ToDateTime("2022-8-26 00:00:00") && now < Convert.ToDateTime("2022-9-1 00:00:00"))
				//{
				//    return now.Year;
				//}
				//#endregion
				return now.Year - 1;
			}
		}
		/// <summary>
		/// 小学题型
		/// </summary>
		/// <returns></returns>
		public static string ItemType(string typeid)
		{
			switch (typeid)
			{
				case "10":
					return "多项选择题";
				case "11":
					return "判断题";
				case "2":
					return "单项选择题";
				case "23":
					return "应用题";
				case "36":
					return "主观题";
				case "39":
					return "题型";
				case "5":
					return "填空题";
				default:
					return "";
			}
		}

		/// <summary>
		/// 初中题型
		/// </summary>
		/// <returns></returns>
		public static string MiddleItemType(string typeid)
		{
			switch (typeid)
			{
				case "1":
					return "填空题";
				case "2":
					return "选择题";
				case "3":
					return "简答题";
				case "4":
					return "证明题";
				case "5":
					return "作图题";
				case "6":
					return "综合题";
				case "7":
					return "阅读题";
				case "8":
					return "判断题";
				case "9":
					return "其他题型";
				default:
					return "";
			}
		}

		public static string Examine(string examineId)
		{
			switch (examineId)
			{
				case "1":
					return "识记";
				case "2":
					return "理解";
				case "3":
					return "运用";
				case "4":
					return "综合";
				case "5":
					return "拓展";
				case "6":
					return "问卷调查";
				default:
					return "";
			}
		}
		public static string GetExamine(string examine)
		{
			switch (examine)
			{
				case "1":
					return "A";
				case "2":
					return "B";
				case "4":
					return "C";
				case "5":
					return "D";
			}
			return "";
		}

		public static string Plate(string plateId)
		{
			switch (plateId)
			{
				case "1":
					return "数与运算";
				case "2":
					return "图形与几何";
				case "3":
					return "解决问题";
				case "4":
					return "统计与概率";
				case "5":
					return "概念";
				case "7":
					return "方程与代数";
				case "8":
					return "识记";
				default:
					return "";
			}
		}

		/// <summary>
		/// 获取小学年级学期
		/// </summary>
		/// <returns></returns>
		public static string GetGradeTerm(string gradeTerm)
		{
			switch (gradeTerm)
			{
				case "1_1":
					return "一上";
				case "1_2":
					return "一下";
				case "2_1":
					return "二上";
				case "2_2":
					return "二下";
				case "3_1":
					return "三上";
				case "3_2":
					return "三下";
				case "4_1":
					return "四上";
				case "4_2":
					return "四下";
				case "5_1":
					return "五上";
				case "5_2":
					return "五下";
				default:
					break;
			}
			return "";
		}

		/// <summary>
		/// 获取所有年级学期
		/// </summary>
		/// <returns></returns>
		public static string GetAllGradeTerm(string gradeTerm)
		{
			switch (gradeTerm)
			{
				case "1_1":
					return "一上";
				case "1_2":
					return "一下";
				case "2_1":
					return "二上";
				case "2_2":
					return "二下";
				case "3_1":
					return "三上";
				case "3_2":
					return "三下";
				case "4_1":
					return "四上";
				case "4_2":
					return "四下";
				case "5_1":
					return "五上";
				case "5_2":
					return "五下";
				case "6_1":
					return "六上";
				case "6_2":
					return "六下";
				case "7_1":
					return "七上";
				case "7_2":
					return "七下";
				case "8_1":
					return "八上";
				case "8_2":
					return "八下";
				case "9_1":
					return "九上";
				case "9_2":
					return "九下";
				default:
					break;
			}
			return "";
		}
		public static bool IsEducation_SystemContainGrade(string education_System, int grade)
		{
			if (education_System == "P5")
			{
				if (grade == 1 || grade == 2 || grade == 3 || grade == 4 || grade == 5)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else if (education_System == "J4")
			{
				if (grade == 6 || grade == 7 || grade == 8 || grade == 9)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else if (education_System == "S3")
			{
				if (grade == 10 || grade == 11 || grade == 12)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else if (education_System == "J_S")
			{
				if (grade == 6 || grade == 7 || grade == 8 || grade == 9 || grade == 10 || grade == 11 || grade == 12)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else if (education_System == "P_J")
			{
				if (grade == 1 || grade == 2 || grade == 3 || grade == 4 || grade == 5 || grade == 6 || grade == 7 || grade == 8 || grade == 9)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else if (education_System == "P_J_S")
			{
				if (grade == 1 || grade == 2 || grade == 3 || grade == 4 || grade == 5 || grade == 6 || grade == 7 || grade == 8 || grade == 9 || grade == 10 || grade == 11 || grade == 12)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 获取初中年级学期
		/// </summary>
		/// <returns></returns>
		public static string GetMiddleGradeTerm(string gradeTerm)
		{
			switch (gradeTerm)
			{
				case "6_1":
					return "六上";
				case "6_2":
					return "六下";
				case "7_1":
					return "七上";
				case "7_2":
					return "七下";
				case "8_1":
					return "八上";
				case "8_2":
					return "八下";
				case "9_1":
					return "九上";
				case "9_2":
					return "九下";
				default:
					break;
			}
			return "";
		}

		public static string GetGradeStr(int grade)
		{
			switch (grade)
			{
				case 1:
					return "一";
				case 2:
					return "二";
				case 3:
					return "三";
				case 4:
					return "四";
				case 5:
					return "五";
				case 6:
					return "六";
				case 7:
					return "七";
				case 8:
					return "八";
				case 9:
					return "九";
				case 10:
					return "十";
				case 11:
					return "十一";
				case 12:
					return "十二";
				default:
					break;
			}
			return "";
		}

		public static string GetGradeName(int grade)
		{
			switch (grade)
			{
				case 1:
					return "一年级";
				case 2:
					return "二年级";
				case 3:
					return "三年级";
				case 4:
					return "四年级";
				case 5:
					return "五年级";
				case 6:
					return "六年级";
				case 7:
					return "七年级";
				case 8:
					return "八年级";
				case 9:
					return "九年级";
				case 10:
					return "高一";
				case 11:
					return "高二";
				case 12:
					return "高三";
				default:
					break;
			}
			return "";
		}

		public static int GetGradeInt(string grade)
		{
			switch (grade)
			{
				case "一":
					return 1;
				case "二":
					return 2;
				case "三":
					return 3;
				case "四":
					return 4;
				case "五":
					return 5;
				case "六":
					return 6;
				case "七":
					return 7;
				case "八":
					return 8;
				case "九":
					return 9;
				case "高一":
					return 10;
				case "高二":
					return 11;
				case "高三":
					return 12;
				case "十":
					return 10;
				case "十一":
					return 11;
				case "十二":
					return 12;
				default:
					break;
			}
			return 0;
		}


		//上周一日期
		public static DateTime GetLastMondayDate()
		{
			DateTime dt = DateTime.Now;
			int today = (int)dt.DayOfWeek;
			if (dt.DayOfWeek.ToString() != "Sunday")//也可以使用today!=0
			{
				return dt.AddDays(-today - 6).Date;
			}
			else
			{
				return dt.AddDays(-today - 13).Date;//若今天是周日，获取到的上周一的日期是本周周一的日期，所以要减去7天
			}
		}
		//本周周一的最小时间（上周日的最大时间）
		public static DateTime GetLastSundayDate()
		{
			DateTime dt = DateTime.Now;
			int today = (int)dt.DayOfWeek;
			if (dt.DayOfWeek.ToString() != "Sunday")//也可以使用today!=0
			{
				return dt.AddDays(1 - today).Date;
			}
			else
			{
				return dt.AddDays(-6 - today).Date;//若今天是周日，获取到的周一日期是下周一的日期，所以要减去7天
			}
		}

		/// <summary>
		/// 获取本学期开学了多少天
		/// </summary>
		/// <param name="time"></param>
		/// <returns></returns>
		public static int GetTimeDay(DateTime time)
		{
			//上半年
			DateTime start = OneTermDateTimeRange(time).Item1;
			DateTime end = Convert.ToDateTime(time.Date.ToShortDateString());
			TimeSpan sp = end.Subtract(start);
			return sp.Days;
		}

		//public static char GetFirstChar(string name)
		//{
		//	var c = name.First();
		//	if (('a' <= c && c <= 'z') || ('A' <= c && c <= 'Z'))
		//	{
		//		return c;
		//	}
		//	else
		//	{
		//		try
		//		{
		//			ChineseChar cc = new ChineseChar(c);
		//			if (cc.Pinyins.Count > 0 && cc.Pinyins[0].Length > 0)
		//			{
		//				return cc.Pinyins[0][0];
		//			}
		//		}
		//		catch (Exception)
		//		{
		//			return c;
		//		}
		//		return c;
		//	}
		//}

		public static string GetSubjectName(string sujectId)
		{
			return GetSubjectName((Subject)Convert.ToInt32(sujectId));
		}

		public static string GetSubjectName(Subject suject)
		{
			if (suject == Subject.Math)
				return "数学";

			else if (suject == Subject.Chinese)
				return "语文";

			return string.Empty;
		}
	}
}
