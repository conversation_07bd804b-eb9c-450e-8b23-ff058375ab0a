using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;

namespace UwooAgent.System.IRepositories.AI
{
    /// <summary>
    /// AI指令管理Repository接口
    /// </summary>
    public interface IAIDirectiveRepository : IDependency, IRepository<AI_Directive>
    {
        
    }
}
