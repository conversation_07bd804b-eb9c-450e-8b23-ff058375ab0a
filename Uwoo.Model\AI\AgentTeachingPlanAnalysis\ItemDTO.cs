﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UwooAgent.Entity.DomainModels.Student;

namespace UwooAgent.Model.AI.AgentTeachingPlanAnalysis
{
    public class ItemDTO

    {
        /// <summary>
        /// 作业ID
        /// </summary>
        public string PaperId { get; set; }
        /// <summary>
        /// 作业名称
        /// </summary>

        public string PaperTitle { get; set; }
        /// <summary>
        /// 题目ID
        /// </summary>

        public string ItemId { get; set; }
        /// <summary>
        /// 题目名称
        /// </summary>

        public string ItemTitle { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public String UserId { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get;set; }
        /// <summary>
        /// 用户答案
        /// </summary>
        public String UserAnswer { get; set; }
        /// <summary>
        /// 得分
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 是否正确
        /// </summary>
        public bool Result { get; set; }
        /// <summary>
        /// 做题时间
        /// </summary>
        //[SugarColumn(IsOnlyIgnoreUpdate = true)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 单题做题时间
        /// </summary>
        public int ATime { get; set; }

        /// <summary>
        /// 试卷提交类型  0:在线练习提交   1:点阵笔作答提交;2 智慧扫描提交
        /// </summary>
        public int? SubType { get; set; } = 0;
    }
}

