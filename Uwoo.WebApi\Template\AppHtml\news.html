﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title> {title} </title>
    <meta name="Generator" content="vue.netcore">
    <meta id="viewport" name="viewport"
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
    <meta name="Author" content="">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="Keywords" content="">
    <meta name="Description" content="">
    <style>
        body {
            position: relative;
            height: 100%;
            max-width: 880px;
            margin: 0 auto;
            padding: 15px;
        }

        .title h2 {
            text-align: center;
            font-weight: 700;
            color: #4d4f53;
            letter-spacing: 1px;
            line-height: 54px;
            padding-bottom: 8px;
            margin-bottom: 13px;
            border-bottom: 1px dashed #ccc;
        }

        .title .source {
            text-align: center;
        }

        .title .date,
        .title .author,
        .title .count {
            letter-spacing: 1px;
            margin-left: 50px;
            font-size: 14px;
            color: #8c8b8b;
        }

        #count {
            font-size: 14px;
            color: #8c8b8b;
        }

        .tm-content {
            margin-top: 40px;
            font-size: 18px;
            letter-spacing: 1px;
        }
    </style>
</head>
<body>
    <div class="title">
        <h2>
            {title}
        </h2>
        <div class="source">
            <span class="author">来源：vol-core</span>
            <span class="date">发布时间：{date}</span>
            <span class="count">浏览次数：</span><span id="count">0</span>
        </div>
    </div>
    <div class="tm-content">
        {content}
    </div>
</body>

</html>
<script>
    var count = 0;
    window.onload = function () {
        var params = window.location.href.split("?");
        if (!params || params.length <= 1) {
            return;
        }
        params[1].split("&").forEach(function (item) {
            var query = item.split("=");
            if (query.length > 0 && query[0] == "c") {
                document.getElementById("count").innerText = (~~query[1]) + 1;
            }
        })
    }
</script>