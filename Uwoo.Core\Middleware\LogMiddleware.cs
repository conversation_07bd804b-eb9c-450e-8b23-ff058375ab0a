﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.LogManager;

namespace Uwoo.Core.Middleware
{
	public class LogMiddleware
	{
		private readonly RequestDelegate _next;
		private readonly IAccessLogger _logger;

		public LogMiddleware(RequestDelegate next, IAccessLogger logger)
		{
			_next = next;
			_logger = logger;
		}

		public async Task InvokeAsync(HttpContext context)
		{
			var watch = System.Diagnostics.Stopwatch.StartNew();
			await _next(context);
			watch.Stop();
			var elapsedMs = watch.Elapsed.TotalSeconds;
			// Get the client's IP address (from X-Forwarded-For if behind a reverse proxy)
			string clientIp;

			if (context.Request.Headers.ContainsKey("X-Forwarded-For"))
			{
				// X-Forwarded-For can contain multiple IPs, the first one is usually the client's IP
				clientIp = context.Request.Headers["X-Forwarded-For"].ToString().Split(',')[0];
			}
			else
			{
				clientIp = context.Connection.RemoteIpAddress?.MapToIPv4().ToString();
			}
			clientIp = clientIp.IsNullOrEmpty() ? context.Connection.RemoteIpAddress.ToString() : clientIp;
			//var logFormat = $"{context.Connection.RemoteIpAddress} -- " +
			//				$"[{DateTime.Now:dd/MMM/yyyy:HH:mm:ss zzz}] " +
			//				$"\"{context.Request.Method} {context.Request.Path} {context.Request.Protocol}\" " +
			//				$"{context.Response.StatusCode} " +
			//				$"{context.Response.ContentLength ?? 0} " +
			//				$"\"{context.Request.Headers["Referer"]}\" " +
			//				$"\"{context.Request.Headers["User-Agent"]}\" " +
			//				$"\"{elapsedMs}ms\"";
			var logFormat = $@"{clientIp} - - [{DateTime.Now:dd/MMM/yyyy:HH:mm:ss} +0800] ""{context.Request.Method} {context.Request.Path} {context.Request.Protocol}"" {context.Response.StatusCode} {context.Response.ContentLength ?? 0} ""{context.Request.Headers["Referer"]} {context.Request.Headers["User-Agent"]}"" rt={elapsedMs}";
			_logger.LogAccess(logFormat);
		}
	}
}
