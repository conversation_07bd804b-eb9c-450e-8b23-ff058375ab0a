using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI指令列表DTO
    /// </summary>
    public class AIDirectiveListDto
    {
        /// <summary>
        /// Id
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 唯一key不可重复
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 指令名称（从Key中提取的友好名称）
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 指令类型（从Key中提取）
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 指令内容预览（截取前100个字符）
        /// </summary>
        public string DirectivePreview { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusText => IsDeleted ? "已删除" : "正常";
    }

    /// <summary>
    /// AI指令详情DTO
    /// </summary>
    public class AIDirectiveDetailDto
    {
        /// <summary>
        /// Id
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 唯一key不可重复
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 指令名称（从Key中提取的友好名称）
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 指令类型（从Key中提取）
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 完整指令内容
        /// </summary>
        public string Directive { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusText => IsDeleted ? "已删除" : "正常";

        /// <summary>
        /// 指令内容字符数
        /// </summary>
        public int DirectiveLength => Directive?.Length ?? 0;

        /// <summary>
        /// 指令内容行数
        /// </summary>
        public int DirectiveLines => string.IsNullOrEmpty(Directive) ? 0 : Directive.Split('\n').Length;
    }

    /// <summary>
    /// AI指令查询输入参数
    /// </summary>
    public class AIDirectiveQueryInput
    {
        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 搜索关键词（搜索Key和指令内容）
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 指令类型筛选
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 状态筛选（null=全部，true=正常，false=已删除）
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// 排序字段（CreateTime、Key）
        /// </summary>
        public string SortField { get; set; } = "CreateTime";

        /// <summary>
        /// 排序方向（asc、desc）
        /// </summary>
        public string SortDirection { get; set; } = "desc";
    }

    /// <summary>
    /// 创建AI指令输入参数
    /// </summary>
    public class CreateAIDirectiveInput
    {
        /// <summary>
        /// 唯一key不可重复
        /// </summary>
        [Required(ErrorMessage = "Key不能为空")]
        [StringLength(200, ErrorMessage = "Key长度不能超过200个字符")]
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 指令内容
        /// </summary>
        [Required(ErrorMessage = "指令内容不能为空")]
        public string Directive { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更新AI指令输入参数
    /// </summary>
    public class UpdateAIDirectiveInput
    {
        /// <summary>
        /// Id
        /// </summary>
        [Required(ErrorMessage = "Id不能为空")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 指令内容
        /// </summary>
        [Required(ErrorMessage = "指令内容不能为空")]
        public string Directive { get; set; } = string.Empty;
    }

    /// <summary>
    /// AI指令分页查询结果
    /// </summary>
    public class AIDirectivePageResult
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<AIDirectiveListDto> Items { get; set; } = new List<AIDirectiveListDto>();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)Total / PageSize);
    }

    /// <summary>
    /// AI指令类型统计
    /// </summary>
    public class AIDirectiveTypeStatistics
    {
        /// <summary>
        /// 指令类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 类型显示名称
        /// </summary>
        public string TypeDisplayName { get; set; } = string.Empty;
    }

    /// <summary>
    /// AI指令操作结果
    /// </summary>
    public class AIDirectiveOperationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 数据（创建或更新后的指令信息）
        /// </summary>
        public AIDirectiveDetailDto? Data { get; set; }
    }
}
