﻿// -- Function：StudentInfoService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/31 15:29

namespace Uwoo.Mongo.Services.Mongo;

using MongoDB.Driver;
using Uwoo.Contracts.Config;
using Uwoo.Mongo.Infrastructure;
using Uwoo.Mongo.Interfaces.Business;
using Uwoo.Mongo.Models;

/// <summary>
/// 学生信息
/// </summary>
public class StudentInfoService : MongoService<StudentInfo>, IStudentInfoService
{
    /// <inheritdoc />
    public StudentInfoService(IMongoConfig config) : base(config)
    {
    }

    /// <inheritdoc />
    protected override void CreateIndex(IMongoCollection<StudentInfo> collection)
    {
	    var builder = Builders<StudentInfo>.IndexKeys
		    .Ascending(x => x.UserId)
		    .Ascending(x => x.StudentNo)
		    .Ascending(x => x.SchoolId)
		    .Ascending(x => x.UserName)
		    .Ascending(x => x.RealName)
		    .Ascending(x => x.Mobile);
	    collection.Indexes.CreateIndex(builder, collection.CollectionNamespace.CollectionName);
    }
}