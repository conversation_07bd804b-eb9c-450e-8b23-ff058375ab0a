﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.System.IRepositories.AI;

namespace UwooAgent.System.Repositories.AI
{
    /// <summary>
    /// 智能体_教师端口语交际
    /// </summary>
    public class AgentTeacherOralCommunicationRepository : RepositoryBase<AI_OralCommunicationTask>, IAgentTeacherOralCommunicationRepository
    {
        public AgentTeacherOralCommunicationRepository(VOLContext dbContext) : base(dbContext)
        {

        }

        public static IAgentTeacherOralCommunicationRepository Instance
        {
            get
            {
                return AutofacContainerModule.GetService<IAgentTeacherOralCommunicationRepository>();
            }
        }
    }
}
