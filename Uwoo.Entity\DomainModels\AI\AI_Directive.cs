﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// AI指令
    /// </summary>
    [Table("AI_Directive")]
    public class AI_Directive:BaseEntity
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 唯一key不可重复
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// 指令
        /// </summary>
        public string Directive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
