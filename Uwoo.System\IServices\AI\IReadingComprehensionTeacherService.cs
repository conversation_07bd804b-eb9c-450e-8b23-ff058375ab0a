using Coldairarrow.Util;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Model.CustomException;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.Model.SemesterTime;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 阅读理解智能体教师端服务接口
    /// </summary>
    public interface IReadingComprehensionTeacherService
    {
        /// <summary>
        /// 保存阅读理解项目化实践任务信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<SaveReadingTaskOutput> SaveReadingTaskNew(SaveReadingTaskInput input);

        /// <summary>
        /// 保存阅读理解任务信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<SaveReadingTaskOutput> SaveReadingTask(SaveReadingTaskInput input);

        /// <summary>
        /// 获取阅读理解任务详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<ReadingTaskDetailsOutput> GetReadingTaskDetails(ReadingTaskDetailsInput input);

        /// <summary>
        /// 获取阅读理解项目详情（与项目化实践接口保持一致）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<ProjectTaskDetailsOutput> GetProjectTaskDetails(ProjectTaskDetailsInput input);

        /// <summary>
        /// 获取阅读理解任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<ReadingTaskListOutput> GetReadingTaskList(ReadingTaskListInput input);

        /// <summary>
        /// 获取阅读理解项目列表（与项目化实践接口保持一致）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PageReturn<GetProjectTaskListOutput>> GetProjectTaskList(GetProjectTaskListInput input);

        /// <summary>
        /// 删除阅读理解任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<bool> DeleteReadingTask(DeleteReadingTaskInput input);

        /// <summary>
        /// 发布阅读理解任务到班级
        /// </summary>
        /// <param name="input">发布参数</param>
        /// <returns></returns>
        Task PublishReadingTaskToClass(PublishProjectTaskToClassInput input);

        /// <summary>
        /// 撤销阅读理解任务发布
        /// </summary>
        /// <param name="input">撤销发布参数</param>
        /// <returns></returns>
        Task UnpublishProjectTask(UnpublishProjectTaskInput input);

        /// <summary>
        /// 获取学生完成情况统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<ReadingTaskStudentStatisticsOutput> GetStudentStatistics(ReadingTaskStudentStatisticsInput input);

        /// <summary>
        /// 获取阅读理解项目综合分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetProjectSynthesizeAnalyseOutput> GetProjectSynthesizeAnalyse(GetProjectSynthesizeAnalyseInput input);

        /// <summary>
        /// 获取阅读理解项目阶段任务统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<GetProjectStageTaskCountOutput>> GetProjectStageTaskCount(GetProjectStageTaskCountInput input);

        /// <summary>
        /// 获取阅读理解项目学生统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetProjectStudentCountOutput> GetProjectStudentCount(GetProjectStudentCountInput input);

        /// <summary>
        /// 获取学生做阅读理解任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentDoReadingTaskListOutput> GetStudentDoReadingTaskList(GetStudentDoReadingTaskListInput input);

        /// <summary>
        /// 获取学生提交内容详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentSubmitContentOutput> GetStudentSubmitContent(GetStudentSubmitContentInput input);

        /// <summary>
        /// 教师端阅读理解下载学生成果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<byte[]> ReadingDownloadStudentAchievement(ReadingDownloadStudentAchievementInput input);

        /// <summary>
        /// 教师端阅读理解下载学生评估结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<byte[]> ReadingDownloadStudentResult(ReadingDownloadStudentResultInput input);
    }
}
