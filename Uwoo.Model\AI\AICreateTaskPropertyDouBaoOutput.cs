﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI创建项目化实践阶段任务相关属性豆包输出
    /// </summary>
    public class AICreateTaskPropertyDouBaoOutput
    {
        /// <summary>
        /// 本次请求的模型输出内容
        /// </summary>
        public List<AICreateTaskPropertyDouBaoChoicesOutput> choices { get; set; } = new List<AICreateTaskPropertyDouBaoChoicesOutput>();

        /// <summary>
        /// 本次请求创建时间的 Unix 时间戳（秒）
        /// </summary>
        public long created { get; set; }

        /// <summary>
        /// 本次请求的唯一标识
        /// </summary>
        public string? id { get; set; }

        /// <summary>
        /// 本次请求实际使用的模型名称和版本
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 本次请求是否使用了TPM保障包。
        /// scale：本次请求使用TPM保障包额度。
        /// default：本次请求未使用TPM保障包额度
        /// </summary>
        public string? service_tier { get; set; }

        /// <summary>
        /// 本次请求的token用量
        /// </summary>
        public AICreateTaskPropertyDouBaoUsageOutput usage { get; set; } = new AICreateTaskPropertyDouBaoUsageOutput();
    }

    /// <summary>
    /// 本次请求的模型输出内容
    /// </summary>
    public class AICreateTaskPropertyDouBaoChoicesOutput
    {
        /// <summary>
        /// 模型停止生成 token 的原因
        /// </summary>
        public string? finish_reason { get; set; }

        /// <summary>
        /// 当前元素在 choices 列表的索引
        /// </summary>
        public int index { get; set; }

        /// <summary>
        /// 当前内容的对数概率信息
        /// </summary>
        public object logprobs { get; set; } = new object();

        /// <summary>
        /// 模型输出的内容
        /// </summary>
        public AICreateTaskPropertyDouBaoMessageOutput message { get; set; } = new AICreateTaskPropertyDouBaoMessageOutput();

        /// <summary>
        /// 模型输出的增量内容
        /// </summary>
        public AICreateTaskPropertyDouBaoDeltaOutput delta { get; set; } = new AICreateTaskPropertyDouBaoDeltaOutput();
    }

    /// <summary>
    /// 模型输出的增量内容
    /// </summary>
    public class AICreateTaskPropertyDouBaoDeltaOutput
    {
        /// <summary>
        /// 内容输出的角色
        /// </summary>
        public string? role { get; set; }

        /// <summary>
        /// 模型生成的消息内容
        /// </summary>
        public string? content { get; set; }

        /// <summary>
        /// 模型处理问题的思维链内容
        /// </summary>
        public string? reasoning_content { get; set; }
    }

    /// <summary>
    /// 模型输出的内容
    /// </summary>
    public class AICreateTaskPropertyDouBaoMessageOutput
    {
        /// <summary>
        /// 模型生成的消息内容
        /// </summary>
        public string? content { get; set; }

        /// <summary>
        /// 模型处理问题的思维链内容
        /// </summary>
        public string? reasoning_content { get; set; }

        /// <summary>
        /// 内容输出的角色
        /// </summary>
        public string? role { get; set; }
    }

    /// <summary>
    /// 本次请求的token用量
    /// </summary>
    public class AICreateTaskPropertyDouBaoUsageOutput
    {
        /// <summary>
        /// 模型输出内容花费的 token。
        /// </summary>
        public int completion_tokens { get; set; }

        /// <summary>
        /// 输入给模型处理的内容 token 数量。
        /// </summary>
        public int prompt_tokens { get; set; }

        /// <summary>
        /// 本次请求消耗的总 token 数量（输入 + 输出）
        /// </summary>
        public int total_tokens { get; set; }
    }
}
