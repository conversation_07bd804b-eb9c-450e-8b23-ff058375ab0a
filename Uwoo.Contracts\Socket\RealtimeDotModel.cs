﻿// Author: Lucifer
//   Date: 2023/09/18 15:44
//   Name: RealtimeDotModel.cs
// Remark:



// ReSharper disable once CheckNamespace
namespace X.PenServer.Contracts.Socket
{
	using System.Text.Json.Serialization;
	using X.PenServer.Contracts.Queue;

	/// <inheritdoc />
	public class RealtimeDotModel
	{
		/// <summary>
		/// 试卷id
		/// </summary>
		[JsonPropertyName(nameof(PaperId))]
		[JsonInclude]
		public string PaperId { get; set; }

		/// <summary>
		/// 实际采集的页码id
		/// </summary>
		[JsonPropertyName("Page")]
		[JsonInclude]
		public int PageId { get; set; }

		/// <summary>
		/// 用户id
		/// </summary>
		[JsonPropertyName(nameof(UserId))]
		[JsonInclude]
		public string UserId { get; set; }

		/// <summary>
		/// 用户类型
		/// </summary>
		/// <remarks>0:学生 1:教师</remarks>
		[JsonPropertyName("userType")]
		[JsonInclude]
		public int UserType { get; set; }

		/// <summary>
		/// 学生班级id
		/// </summary>
		[JsonPropertyName(nameof(ClassId))]
		[JsonInclude]
		public string ClassId { get; set; }

		/// <summary>
		/// 点位
		/// </summary>
		[JsonPropertyName(nameof(Dots))]
		[JsonInclude]
		public List<PenDot> Dots { get; set; }
	}
}