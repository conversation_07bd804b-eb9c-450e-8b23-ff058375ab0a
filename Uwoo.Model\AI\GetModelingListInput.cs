﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取建模列表入参
    /// </summary>
    public class GetModelingListInput
    {
        /// <summary>
        /// 页码（默认1）
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 每页数量（默认10）
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public string? SchoolId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string? GradeId { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 建模名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 发布状态（0全部、1已发布、2未发布）默认0全部
        /// </summary>
        public int PublishStatus { get; set; }
    }
}
