﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.System.IRepositories.AI;

namespace UwooAgent.System.Repositories.AI
{
    /// <summary>
    /// 智能体_学生端口语交际
    /// </summary>
    public class AgenStudentOralCommunicationRepository : RepositoryBase<AI_OralCommunicationTask>, IAgenStudentOralCommunicationRepository
    {
        public AgenStudentOralCommunicationRepository(VOLContext dbContext) : base(dbContext)
        {

        }

        public static IAgenStudentOralCommunicationRepository Instance
        {
            get
            {
                return AutofacContainerModule.GetService<IAgenStudentOralCommunicationRepository>();
            }
        }
    }
}
