﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取AI对话内容记录出参
    /// </summary>
    public class GetAIDialogueContentRecordOutput
    {
        /// <summary>
        /// 会话Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 问
        /// </summary>
        public string? Ask { get; set; }

        /// <summary>
        /// “问”结构化数据
        /// </summary>
        public AIDialogueASKDto AskInfo { get; set; } = new AIDialogueASKDto();

        /// <summary>
        /// 答
        /// </summary>
        public string? Answer { get; set; }

        /// <summary>
        /// 消息类型(1系统消息、2用户消息)
        /// </summary>
        public int MessageType { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
}
