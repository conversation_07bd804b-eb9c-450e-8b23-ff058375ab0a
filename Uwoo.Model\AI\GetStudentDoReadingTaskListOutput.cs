using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取学生做阅读理解任务列表输出
    /// </summary>
    public class GetStudentDoReadingTaskListOutput
    {
        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生姓名
        /// </summary>
        public string? StudentName { get; set; }

        /// <summary>
        /// 学生头像
        /// </summary>
        public string? StudentLogo { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 阅读理解项目Id
        /// </summary>
        public string? ReadingProjectId { get; set; }

        /// <summary>
        /// 阅读理解项目名称
        /// </summary>
        public string? ReadingProjectName { get; set; }

        /// <summary>
        /// 阅读理解项目Logo
        /// </summary>
        public string? ReadingProjectLogo { get; set; }

        /// <summary>
        /// 阅读理解项目背景介绍
        /// </summary>
        public string? ReadingProjectIntroduce { get; set; }

        /// <summary>
        /// 阅读理解项目阶段
        /// </summary>
        public List<GetStudentDoReadingTaskListStageInfoOutput> ReadingStageInfos { get; set; } = new List<GetStudentDoReadingTaskListStageInfoOutput>();
    }

    /// <summary>
    /// 获取学生做阅读理解阶段信息输出
    /// </summary>
    public class GetStudentDoReadingTaskListStageInfoOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 阅读理解阶段任务
        /// </summary>
        public List<GetStudentDoReadingTaskListStageTaskInfoOutput> ReadingStageTaskInfos { get; set; } = new List<GetStudentDoReadingTaskListStageTaskInfoOutput>();
    }

    /// <summary>
    /// 获取学生做阅读理解阶段任务信息输出
    /// </summary>
    public class GetStudentDoReadingTaskListStageTaskInfoOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阅读理解项目阶段Id
        /// </summary>
        public string? ReadingProjectStageId { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:思维导图、7:选词填空）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 开场白
        /// </summary>
        public string? Prologue { get; set; }

        /// <summary>
        /// 音色Id
        /// </summary>
        public string? ToneId { get; set; }

        /// <summary>
        /// 角色名称（用于AI展示名称）
        /// </summary>
        public string? RoleName { get; set; }

        /// <summary>
        /// 选词填空题目内容（JSON格式）
        /// </summary>
        public string? FillWordQuestionBody { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? DistractorWords { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? CorrectAnswers { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? CustomBackgroundImage { get; set; }

        /// <summary>
        /// 学生做阅读理解阶段任务次数
        /// </summary>
        public List<GetStudentDoReadingTaskListNumberOutput> Numbers { get; set; } = new List<GetStudentDoReadingTaskListNumberOutput>();

        /// <summary>
        /// 任务视频资源
        /// </summary>
        public List<GetStudentDoReadingStageTaskVideoInfoOutput> TaskVideos { get; set; } = new List<GetStudentDoReadingStageTaskVideoInfoOutput>();

        /// <summary>
        /// 任务文档资源
        /// </summary>
        public List<GetStudentDoReadingStageTaskDocumentInfoOutput> TaskDocuments { get; set; } = new List<GetStudentDoReadingStageTaskDocumentInfoOutput>();

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 最后一次提交时间
        /// </summary>
        public string? SubmitTime { get; set; }

        #region 阅读理解特有字段

        /// <summary>
        /// 匹配的高频问题列表（JSON格式）
        /// </summary>
        public string? MatchedQuestions { get; set; }

        /// <summary>
        /// 任务完成状态（1:未开始，2:进行中，3:已完成）
        /// </summary>
        public int TaskStatus { get; set; }

        /// <summary>
        /// 项目完成状态（1:未开始，2:进行中，3:已完成）
        /// </summary>
        public int ProjectStatus { get; set; }

        /// <summary>
        /// 评估分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 是否达标
        /// </summary>
        public bool IsStandard { get; set; }

        /// <summary>
        /// AI评价等第
        /// </summary>
        public string? AIGrade { get; set; }

        /// <summary>
        /// 视频观看总时长（分钟）- 视频任务特有
        /// </summary>
        public int? TotalWatchDuration { get; set; }

        /// <summary>
        /// 已观看视频数量 - 视频任务特有
        /// </summary>
        public int? WatchedVideoCount { get; set; }

        /// <summary>
        /// 已阅读文档数量 - 文档任务特有
        /// </summary>
        public int? ReadDocumentCount { get; set; }

        #endregion
    }

    /// <summary>
    /// 学生做阅读理解阶段任务次数
    /// </summary>
    public class GetStudentDoReadingTaskListNumberOutput
    {
        /// <summary>
        /// 次数
        /// </summary>
        public int Number { get; set; }

        /// <summary>
        /// 是否备份
        /// </summary>
        public bool IsBackups { get; set; }

        /// <summary>
        /// 任务提交记录Id
        /// </summary>
        public string? TaskSubmitId { get; set; }
    }

    /// <summary>
    /// 获取学生端阅读理解阶段任务视频信息输出
    /// </summary>
    public class GetStudentDoReadingStageTaskVideoInfoOutput
    {
        /// <summary>
        /// 视频Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阅读理解项目阶段任务Id
        /// </summary>
        public string? ReadingProjectStageTaskId { get; set; }

        /// <summary>
        /// 视频标题
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 视频描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 视频地址
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// 视频时长（秒）
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 是否已观看
        /// </summary>
        public bool IsWatched { get; set; }

        /// <summary>
        /// 累计观看时长（秒）
        /// </summary>
        public int WatchDuration { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Order { get; set; }
    }

    /// <summary>
    /// 获取学生端阅读理解阶段任务文档信息输出
    /// </summary>
    public class GetStudentDoReadingStageTaskDocumentInfoOutput
    {
        /// <summary>
        /// 文档Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阅读理解项目阶段任务Id
        /// </summary>
        public string? ReadingProjectStageTaskId { get; set; }

        /// <summary>
        /// 文档标题
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 文档描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 文档地址
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// 文档类型
        /// </summary>
        public string? DocumentType { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public int Size { get; set; }

        /// <summary>
        /// 是否已阅读
        /// </summary>
        public bool IsRead { get; set; }

        /// <summary>
        /// 阅读时间
        /// </summary>
        public DateTime? ReadTime { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Order { get; set; }
    }
}
