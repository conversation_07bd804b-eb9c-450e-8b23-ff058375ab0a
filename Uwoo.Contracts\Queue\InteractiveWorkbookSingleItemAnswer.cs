﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace X.PenServer.Contracts.Queue
{
	/// <summary>
	/// 互动练习簿
	/// </summary>
	public class InteractiveWorkbookSingleItemAnswer
	{
		/// <inheritdoc />
		public string UserId { get; set; }

		/// <inheritdoc />
		public string ItemId { get; set; }

		/// <inheritdoc />
		public string PaperId { get; set; }

		/// <inheritdoc />
		public string ClassId { get; set; }
	}
}
