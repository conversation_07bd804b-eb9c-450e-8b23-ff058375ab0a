﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Core.Utilities
{
    /// <summary>
    /// AI智能体相关key
    /// </summary>
    public class AIAgentKeys
    {
        /// <summary>
        /// 学生端口语交际对话式提示词Key
        /// </summary>
        public const string StudentOralCommunicationDialogue_Directive = "StudentOralCommunicationDialogue_Directive";

        /// <summary>
        /// 学生端口语交际指令式提示词Key
        /// </summary>
        public const string StudentOralCommunicationInstruct_Directive = "StudentOralCommunicationInstruct_Directive";

        /// <summary>
        /// 学生端口语交际提交提示词Key
        /// </summary>
        public const string StudentOralCommunicationSubmit_Directive = "StudentOralCommunicationSubmit_Directive";

        /// <summary>
        /// 教师端标题创建教案提示词Key
        /// </summary>
        public const string TeacherTitleCreateTeachingPlan_Directive = "TeacherTitleCreateTeachingPlan_Directive";

        /// <summary>
        /// 教师端文本创建教案提示词Key
        /// </summary>
        public const string TeacherTextCreateTeachingPlan_Directive = "TeacherTextCreateTeachingPlan_Directive";

        /// <summary>
        /// 教师端章节创建教案提示词Key
        /// </summary>
        public const string TeacherChapterCreateTeachingPlan_Directive = "TeacherChapterCreateTeachingPlan_Directive";

        /// <summary>
        /// 教师端文档创建教案提示词Key
        /// </summary>
        public const string TeacherDocumentCreateTeachingPlan_Directive = "TeacherDocumentCreateTeachingPlan_Directive";

        /// <summary>
        /// 智能出题-知识点出题提示词Key
        /// </summary>
        public const string IntelligentQuestionKnowledgePoint_Directive = "IntelligentQuestionKnowledgePoint_Directive";

        /// <summary>
        /// 智能出题-文本出题提示词Key
        /// </summary>
        public const string IntelligentQuestionText_Directive = "IntelligentQuestionText_Directive";

        /// <summary>
        /// 智能出题-附件出题提示词Key
        /// </summary>
        public const string IntelligentQuestionAttachment_Directive = "IntelligentQuestionAttachment_Directive";

        /// <summary>
        /// 智能出题-章节出题提示词Key
        /// </summary>
        public const string IntelligentQuestionChapter_Directive = "IntelligentQuestionChapter_Directive";

        /// <summary>
        /// 智能出题-重新生成题目提示词Key
        /// </summary>
        public const string IntelligentQuestionRegenerate_Directive = "IntelligentQuestionRegenerate_Directive";

        /// <summary>
        /// 教师端教案创建记录文本润色提示词key
        /// </summary>
        public const string TeachingPlanCreateRecordOptimize_Directive = "TeachingPlanCreateRecordOptimize_Directive";

        /// <summary>
        /// 教师端项目化实践_AI创建成果评估任务相关属性提示词Key
        /// </summary>
        public const string TeacherProjectAICreateOutcomeProperty_Directive = "TeacherProjectAICreateOutcomeProperty_Directive";

        /// <summary>
        /// 教师端项目化实践_AI创建情景对话任务相关属性提示词
        /// </summary>
        public const string TeacherProjectAICreateDialogueProperty_Directive = "TeacherProjectAICreateDialogueProperty_Directive";

        /// <summary>
        /// 教师端项目化实践_AI创建知识问答任务相关属性提示词
        /// </summary>
        public const string TeacherProjectAICreateKeyknowledgeProperty_Directive = "TeacherProjectAICreateKeyknowledgeProperty_Directive";

        /// <summary>
        /// 教师端项目化实践_AI创建思维导图任务相关属性提示词
        /// </summary>
        public const string TeacherProjectAICreateKeyMindMapProperty_Directive = "TeacherProjectAICreateKeyMindMapProperty_Directive";

        /// <summary>
        /// 教师端项目化实践_AI创建选词填空任务相关属性提示词
        /// </summary>
        public const string TeacherProjectAICreateKeyWordFileProperty_Directive = "TeacherProjectAICreateKeyWordFileProperty_Directive";

        /// <summary>
        /// 学生端项目化实践情景对话提交提示词Key
        /// </summary>
        public const string StudentProjectDialogueSubmit_Directive = "StudentProjectDialogueSubmit_Directive";

        /// <summary>
        /// 学生端建模情景对话提交提示词Key
        /// </summary>
        public const string StudentModelingDialogueSubmit_Directive = "StudentModelingDialogueSubmit_Directive";

        /// <summary>
        /// 学生端建模问题理解提交提示词Key
        /// </summary>
        public const string StudentModelingComprehendSubmit_Directive = "StudentModelingComprehendSubmit_Directive";

        /// <summary>
        /// 学生端建模构建图片识别提示词Key
        /// </summary>
        public const string StudentModelingStructureImgOcr_Directive = "StudentModelingStructureImgOcr_Directive";

        /// <summary>
        /// 学生端建模模型构建提交提示词Key
        /// </summary>
        public const string StudentModelingStructureSubmit_Directive = "StudentModelingStructureSubmit_Directive";

        /// <summary>
        /// 学生端建模模型假设提交提示词Key
        /// </summary>
        public const string StudentModelingHypothesisSubmit_Directive = "StudentModelingHypothesisSubmit_Directive";

        /// <summary>
        /// 学生端建模模型评价提交提示词Key
        /// </summary>
        public const string StudentModelingEvaluateSubmit_Directive = "StudentModelingEvaluateSubmit_Directive";

        /// <summary>
        /// 学生端建模情景对话系统提示词Key
        /// </summary>
        public const string StudentModelingDialogue_Directive = "StudentModelingDialogue_Directive";

        /// <summary>
        /// 学生端阅读理解情景对话系统提示词Key
        /// </summary>
        public const string StudentReadingDialogue_Directive = "StudentReadingDialogue_Directive";

        /// <summary>
        /// 获取学生端-口语交际key
        /// </summary>
        /// <param name="agentId">智能体Id</param>
        /// <param name="agentTaskId">智能体任务Id</param>
        /// <param name="studentId">学生Id</param>
        /// <returns></returns>
        public static string GetStudentOralCommunicationKey(string agentId, string agentTaskId, string studentId)
        {
            if (!string.IsNullOrEmpty(agentId) && !string.IsNullOrEmpty(agentTaskId) && !string.IsNullOrEmpty(studentId))
            {
                return $"StudentOralCommunication|{agentId}|{agentTaskId}|{studentId}";
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 获取预览-口语交际key
        /// </summary>
        /// <param name="agentId">智能体Id</param>
        /// <param name="agentTaskId">智能体任务Id</param>
        /// <param name="temporaryId">预览临时Id</param>
        /// <returns></returns>
        public static string GetPreviewOralCommunicationKey(string agentId, string agentTaskId, string temporaryId)
        {
            if (!string.IsNullOrEmpty(agentId) && !string.IsNullOrEmpty(agentTaskId) && !string.IsNullOrEmpty(temporaryId))
            {
                return $"PreviewOralCommunication|{agentId}|{agentTaskId}|{temporaryId}";
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 获取教师端-教案创建记录润色key
        /// </summary>
        /// <param name="teachingPlanCreateRecordId"></param>
        /// <param name="teacherId"></param>
        /// <returns></returns>
        public static string GetTeachingPlanCreateRecordOptimize(string teachingPlanCreateRecordId, string teacherId)
        {
            if (!string.IsNullOrEmpty(teachingPlanCreateRecordId) && !string.IsNullOrEmpty(teacherId))
            {
                return $"TeachingPlanCreateRecordOptimize|{teachingPlanCreateRecordId}|{teacherId}";
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 获取学生端-项目化实践未达标key
        /// </summary>
        /// <param name="agentId">智能体Id</param>
        /// <param name="stagesTaskId">项目化实践阶段任务Id</param>
        /// <param name="studentId">学生Id</param>
        /// <param name="doTaskId">未达标提交记录Id</param>
        /// <returns></returns>
        public static string GetStudentProjectNoStandardKey(string agentId, string stagesTaskId, string studentId, string doTaskId)
        {
            if (!string.IsNullOrEmpty(agentId) &&
                !string.IsNullOrEmpty(stagesTaskId) &&
                !string.IsNullOrEmpty(studentId) &&
                !string.IsNullOrEmpty(doTaskId))
            {
                return $"StudentProjectStageTaskNoStandard|{agentId}|{stagesTaskId}|{studentId}|{doTaskId}";
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 获取学生端-项目化实践key
        /// </summary>
        /// <param name="agentId">智能体Id</param>
        /// <param name="stagesTaskId">项目化实践阶段任务Id</param>
        /// <param name="studentId">学生Id</param>
        /// <returns></returns>
        public static string GetStudentProjecKey(string agentId, string stagesTaskId, string studentId)
        {
            if (!string.IsNullOrEmpty(agentId) &&
                !string.IsNullOrEmpty(stagesTaskId) &&
                !string.IsNullOrEmpty(studentId))
            {
                return $"StudentProjectStageTask|{agentId}|{stagesTaskId}|{studentId}";
            }
            else
            {
                return null;
            }
        }
    }
}
