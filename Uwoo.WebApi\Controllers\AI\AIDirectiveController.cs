using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// AI指令管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AIDirectiveController : ApiBaseController<IAIDirectiveService>
    {
        public AIDirectiveController(IAIDirectiveService service) : base(service)
        {
        }

        /// <summary>
        /// 获取AI指令分页列表
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>分页结果</returns>
        [HttpPost("GetList")]
        public async Task<IActionResult> GetList([FromBody] AIDirectiveQueryInput input)
        {
            try
            {
                if (input == null)
                {
                    input = new AIDirectiveQueryInput();
                }

                // 参数验证
                if (input.Page < 1) input.Page = 1;
                if (input.PageSize < 1 || input.PageSize > 100) input.PageSize = 20;

                var result = await Service.GetAIDirectiveListAsync(input);

                return Ok(new AjaxResult<AIDirectivePageResult>
                {
                    Success = true,
                    Msg = "获取AI指令列表成功",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"获取AI指令列表失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 根据ID获取AI指令详情
        /// </summary>
        /// <param name="id">指令ID</param>
        /// <returns>指令详情</returns>
        [HttpGet("GetDetail/{id}")]
        public async Task<IActionResult> GetDetail(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "指令ID不能为空"
                    });
                }

                var result = await Service.GetAIDirectiveByIdAsync(id);

                if (result == null)
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "指令不存在"
                    });
                }

                return Ok(new AjaxResult<AIDirectiveDetailDto>
                {
                    Success = true,
                    Msg = "获取AI指令详情成功",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"获取AI指令详情失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 创建AI指令
        /// </summary>
        /// <param name="input">创建参数</param>
        /// <returns>创建结果</returns>
        [HttpPost("Create")]
        public async Task<IActionResult> Create([FromBody] CreateAIDirectiveInput input)
        {
            try
            {
                if (input == null)
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "请求参数不能为空"
                    });
                }

                if (!ModelState.IsValid)
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "参数验证失败"
                    });
                }

                var result = await Service.CreateAIDirectiveAsync(input);

                if (result.Success)
                {
                    return Ok(new AjaxResult<AIDirectiveDetailDto>
                    {
                        Success = true,
                        Msg = result.Message,
                        Data = result.Data
                    });
                }
                else
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"创建AI指令失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 更新AI指令
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        [HttpPut("Update")]
        public async Task<IActionResult> Update([FromBody] UpdateAIDirectiveInput input)
        {
            try
            {
                if (input == null)
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "请求参数不能为空"
                    });
                }

                if (!ModelState.IsValid)
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "参数验证失败"
                    });
                }

                var result = await Service.UpdateAIDirectiveAsync(input);

                if (result.Success)
                {
                    return Ok(new AjaxResult<AIDirectiveDetailDto>
                    {
                        Success = true,
                        Msg = result.Message,
                        Data = result.Data
                    });
                }
                else
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"更新AI指令失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 删除AI指令（软删除）
        /// </summary>
        /// <param name="id">指令ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("Delete/{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "指令ID不能为空"
                    });
                }

                var result = await Service.DeleteAIDirectiveAsync(id);

                if (result.Success)
                {
                    return Ok(new AjaxResult
                    {
                        Success = true,
                        Msg = result.Message
                    });
                }
                else
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"删除AI指令失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 恢复已删除的AI指令
        /// </summary>
        /// <param name="id">指令ID</param>
        /// <returns>恢复结果</returns>
        [HttpPost("Restore/{id}")]
        public async Task<IActionResult> Restore(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "指令ID不能为空"
                    });
                }

                var result = await Service.RestoreAIDirectiveAsync(id);

                if (result.Success)
                {
                    return Ok(new AjaxResult<AIDirectiveDetailDto>
                    {
                        Success = true,
                        Msg = result.Message,
                        Data = result.Data
                    });
                }
                else
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"恢复AI指令失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 获取AI指令类型统计
        /// </summary>
        /// <returns>类型统计</returns>
        [HttpGet("GetTypeStatistics")]
        public async Task<IActionResult> GetTypeStatistics()
        {
            try
            {
                var result = await Service.GetAIDirectiveTypeStatisticsAsync();
                return Ok(new AjaxResult<List<AIDirectiveTypeStatistics>>
                {
                    Success = true,
                    Msg = "获取AI指令类型统计成功",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"获取AI指令类型统计失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 搜索AI指令
        /// </summary>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="limit">返回数量限制</param>
        /// <returns>搜索结果</returns>
        [HttpGet("Search")]
        public async Task<IActionResult> Search([FromQuery] string keyword, [FromQuery] int limit = 10)
        {
            try
            {
                if (string.IsNullOrEmpty(keyword))
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "搜索关键词不能为空"
                    });
                }

                if (limit < 1 || limit > 50) limit = 10;

                var result = await Service.SearchAIDirectivesAsync(keyword, limit);
                return Ok(new AjaxResult<List<AIDirectiveListDto>>
                {
                    Success = true,
                    Msg = "搜索AI指令成功",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"搜索AI指令失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 批量删除AI指令
        /// </summary>
        /// <param name="ids">指令ID列表</param>
        /// <returns>批量删除结果</returns>
        [HttpPost("BatchDelete")]
        public async Task<IActionResult> BatchDelete([FromBody] string[] ids)
        {
            try
            {
                if (ids == null || ids.Length == 0)
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "请选择要删除的指令"
                    });
                }

                int successCount = 0;
                int failedCount = 0;
                var errors = new List<string>();

                foreach (var id in ids)
                {
                    try
                    {
                        var result = await Service.DeleteAIDirectiveAsync(id);
                        if (result.Success)
                        {
                            successCount++;
                        }
                        else
                        {
                            failedCount++;
                            errors.Add($"ID {id}: {result.Message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        failedCount++;
                        errors.Add($"ID {id}: {ex.Message}");
                    }
                }

                var message = $"批量删除完成，成功 {successCount} 个，失败 {failedCount} 个";
                if (errors.Count > 0)
                {
                    message += $"。错误详情：{string.Join("; ", errors)}";
                }

                return Ok(new AjaxResult<object>
                {
                    Success = true,
                    Msg = message,
                    Data = new { SuccessCount = successCount, FailedCount = failedCount, Errors = errors }
                });
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"批量删除AI指令失败: {ex.Message}"
                });
            }
        }
    }
}
