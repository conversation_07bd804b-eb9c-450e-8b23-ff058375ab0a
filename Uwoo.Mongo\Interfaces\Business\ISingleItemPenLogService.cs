﻿// -- Function：ISingleItemPenLogService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2024/04/04 15:04:47

using Uwoo.Mongo.Interfaces.Lifecycle;
using Uwoo.Mongo.Models;

namespace Uwoo.Mongo.Interfaces.Business;

/// <summary>
/// 单题模式
/// </summary>
public interface ISingleItemPenLogService: IMongoAutoService<SingleItemPenLog>
{

	/// <inheritdoc />
	List<string> GetUserList(string colname, int pageId, string itemId, List<string> userid);

	/// <summary>
	/// 获取已作答用户列表
	/// </summary>
	/// <param name="colname">集合名称</param>
	/// <param name="page">页码集合</param>
	/// <param name="userid">用户id集合</param>
	/// <param name="year">学年</param>
	/// <returns></returns>
	List<string> GetUserList(string colname, List<int> page, List<string> userid, int? year = null);

	/// <inheritdoc />
	List<SingleItemPenLog> GetAll(string colname, string userid, int page, string itemId);

	/// <inheritdoc />
	List<SingleItemPenLog> GetAll(string colname, List<int> page, string userid, int? year = null);

	/// <inheritdoc />
	void DeleteSinglePenLog(string colname, string paperId, List<string> userid);

	/// <inheritdoc />
	Task CorrectMDLog(string colname, int page, string userId, int moveX, int moveY);
}