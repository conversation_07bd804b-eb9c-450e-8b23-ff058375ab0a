using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 知识库管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class KnowledgeController : ApiBaseController<IKnowledgeService>
    {
        public KnowledgeController(IKnowledgeService service) : base(service)
        {
        }

        /// <summary>
        /// 获取知识库分页列表
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>分页结果</returns>
        [HttpPost("GetList")]
        public async Task<IActionResult> GetList([FromBody] KnowledgeQueryInput input)
        {
            try
            {
                if (input == null)
                {
                    input = new KnowledgeQueryInput();
                }

                // 参数验证
                if (input.Page < 1) input.Page = 1;
                if (input.PageSize < 1 || input.PageSize > 100) input.PageSize = 20;

                var result = await Service.GetKnowledgeListAsync(input);

                return Ok(new AjaxResult<KnowledgePageResult>
                {
                    Success = true,
                    Msg = "获取知识库列表成功",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"获取知识库列表失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 根据ID获取知识库详情
        /// </summary>
        /// <param name="id">知识库ID</param>
        /// <returns>知识库详情</returns>
        [HttpGet("GetDetail/{id}")]
        public async Task<IActionResult> GetDetail(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "知识库ID不能为空"
                    });
                }

                var result = await Service.GetKnowledgeByIdAsync(id);

                if (result == null)
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "知识库不存在"
                    });
                }

                return Ok(new AjaxResult<KnowledgeDetailDto>
                {
                    Success = true,
                    Msg = "获取知识库详情成功",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"获取知识库详情失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 创建知识库
        /// </summary>
        /// <param name="input">创建参数</param>
        /// <returns>创建结果</returns>
        [HttpPost("Create")]
        public async Task<IActionResult> Create([FromBody] CreateKnowledgeInput input)
        {
            try
            {
                if (input == null)
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "请求参数不能为空"
                    });
                }

                if (!ModelState.IsValid)
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "参数验证失败"
                    });
                }

                var result = await Service.CreateKnowledgeAsync(input);

                if (result.Success)
                {
                    return Ok(new AjaxResult<KnowledgeDetailDto>
                    {
                        Success = true,
                        Msg = result.Message,
                        Data = result.Data
                    });
                }
                else
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"创建知识库失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 更新知识库
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        [HttpPut("Update")]
        public async Task<IActionResult> Update([FromBody] UpdateKnowledgeInput input)
        {
            try
            {
                if (input == null)
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "请求参数不能为空"
                    });
                }

                if (!ModelState.IsValid)
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "参数验证失败"
                    });
                }

                var result = await Service.UpdateKnowledgeAsync(input);

                if (result.Success)
                {
                    return Ok(new AjaxResult<KnowledgeDetailDto>
                    {
                        Success = true,
                        Msg = result.Message,
                        Data = result.Data
                    });
                }
                else
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"更新知识库失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 删除知识库
        /// </summary>
        /// <param name="id">知识库ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("Delete/{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "知识库ID不能为空"
                    });
                }

                var result = await Service.DeleteKnowledgeAsync(id);

                if (result.Success)
                {
                    return Ok(new AjaxResult
                    {
                        Success = true,
                        Msg = result.Message
                    });
                }
                else
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"删除知识库失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 检查知识库名称是否存在
        /// </summary>
        /// <param name="name">知识库名称</param>
        /// <param name="excludeId">排除的ID（用于更新时检查）</param>
        /// <returns>检查结果</returns>
        [HttpGet("CheckName")]
        public async Task<IActionResult> CheckName([FromQuery] string name, [FromQuery] string? excludeId = null)
        {
            try
            {
                if (string.IsNullOrEmpty(name))
                {
                    return Ok(new AjaxResult
                    {
                        Success = false,
                        Msg = "知识库名称不能为空"
                    });
                }

                var exists = await Service.IsKnowledgeNameExistsAsync(name, excludeId);

                return Ok(new AjaxResult<bool>
                {
                    Success = true,
                    Msg = exists ? "名称已存在" : "名称可用",
                    Data = exists
                });
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"检查名称失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 获取知识库统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        [HttpGet("GetStatistics")]
        public async Task<IActionResult> GetStatistics()
        {
            try
            {
                var result = await Service.GetKnowledgeStatisticsAsync();
                return Ok(new AjaxResult<Dictionary<string, int>>
                {
                    Success = true,
                    Msg = "获取知识库统计成功",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return Ok(new AjaxResult
                {
                    Success = false,
                    Msg = $"获取知识库统计失败: {ex.Message}"
                });
            }
        }
    }
}
