﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.SemesterTime
{
    /// <summary>
	/// 当前学年学期时间
	/// </summary>
	public class NowSemesterTime
    {
        /// <summary>
        /// 学届（学年）
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// 学届（学年）开始时间
        /// </summary>
        public DateTime? YearStartDate { get; set; }

        /// <summary>
        /// 学届（学年）结束时间
        /// </summary>
        public DateTime? YearEndDate { get; set; }

        /// <summary>
        /// 上学期开始时间
        /// </summary>
        public DateTime? FallStartDate { get; set; }

        /// <summary>
        /// 上学期结束时间
        /// </summary>
        public DateTime? FallEndDate { get; set; }

        /// <summary>
        /// 下学期开始时间
        /// </summary>
        public DateTime? SpringStartDate { get; set; }

        /// <summary>
        /// 下学期结束时间
        /// </summary>
        public DateTime? SpringEndDate { get; set; }

        /// <summary>
        /// 当前学期（1上学期、2下学期）
        /// </summary>
        public int? NowTerm { get; set; }
    }
}
