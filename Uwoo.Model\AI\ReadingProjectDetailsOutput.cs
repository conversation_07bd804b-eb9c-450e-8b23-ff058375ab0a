using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 阅读理解项目详情输出
    /// </summary>
    public class ReadingProjectDetailsOutput
    {
        /// <summary>
        /// 项目Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 任务图标
        /// </summary>
        public string? TaskLogo { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string? GradeId { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public string? SchoolId { get; set; }

        /// <summary>
        /// 是否已发布
        /// </summary>
        public bool IsPublished { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 项目阶段列表
        /// </summary>
        public List<ReadingProjectStageOutput> Stages { get; set; } = new List<ReadingProjectStageOutput>();

        /// <summary>
        /// 发布的班级列表
        /// </summary>
        public List<ReadingProjectClassOutput> PublishedClasses { get; set; } = new List<ReadingProjectClassOutput>();
    }

    /// <summary>
    /// 阅读理解项目阶段输出
    /// </summary>
    public class ReadingProjectStageOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? StageId { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string? Describe { get; set; }

        /// <summary>
        /// 阶段排序
        /// </summary>
        public int StageOrder { get; set; }

        /// <summary>
        /// 阶段任务列表
        /// </summary>
        public List<ReadingProjectStageTaskOutput> Tasks { get; set; } = new List<ReadingProjectStageTaskOutput>();
    }

    /// <summary>
    /// 阅读理解项目阶段任务输出
    /// </summary>
    public class ReadingProjectStageTaskOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:思维导图、7:选词填空）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务类型名称
        /// </summary>
        public string? TaskTypeName { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 评分标准
        /// </summary>
        public string? ScoreStandard { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string? Demand { get; set; }

        /// <summary>
        /// 问答范围
        /// </summary>
        public string? Scope { get; set; }

        /// <summary>
        /// 角色设定
        /// </summary>
        public string? RoleSetting { get; set; }

        /// <summary>
        /// 是否开启提交条件（组间任务设置）
        /// </summary>
        public bool GroupIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（组间任务设置）
        /// </summary>
        public bool GroupIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（组间任务设置）
        /// </summary>
        public decimal GroupAssessmentScore { get; set; }

        /// <summary>
        /// 是否开启提交条件（任务点设置）
        /// </summary>
        public bool TaskIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（任务点设置）
        /// </summary>
        public bool TaskIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（任务点设置）
        /// </summary>
        public decimal TaskAssessmentScore { get; set; }

        /// <summary>
        /// 任务排序
        /// </summary>
        public int TaskOrder { get; set; }

        /// <summary>
        /// 任务高频问题列表
        /// </summary>
        public List<ReadingProjectTaskQuestionOutput> Questions { get; set; } = new List<ReadingProjectTaskQuestionOutput>();

        /// <summary>
        /// 任务特殊配置（根据任务类型）
        /// </summary>
        public ReadingProjectTaskConfigOutput? TaskConfig { get; set; }
    }

    /// <summary>
    /// 阅读理解项目任务高频问题输出
    /// </summary>
    public class ReadingProjectTaskQuestionOutput
    {
        /// <summary>
        /// 问题Id
        /// </summary>
        public string? QuestionId { get; set; }

        /// <summary>
        /// 问题名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 问题描述
        /// </summary>
        public string? Describe { get; set; }

        /// <summary>
        /// 问题排序
        /// </summary>
        public int QuestionOrder { get; set; }
    }

    /// <summary>
    /// 阅读理解项目任务特殊配置输出
    /// </summary>
    public class ReadingProjectTaskConfigOutput
    {
        #region 视频任务配置（TaskType=4）

        /// <summary>
        /// 总观看时长要求（分钟）
        /// </summary>
        public int? TotalWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否要求观看所有视频
        /// </summary>
        public bool IsWatchAllVideos { get; set; }

        /// <summary>
        /// 视频资源列表
        /// </summary>
        public List<VideoResourceOutput> VideoResources { get; set; } = new List<VideoResourceOutput>();

        #endregion

        #region 文档任务配置（TaskType=5）

        /// <summary>
        /// 是否需要阅读全部文档
        /// </summary>
        public bool IsReadAllDocuments { get; set; }

        /// <summary>
        /// 文档资源列表
        /// </summary>
        public List<DocumentResourceOutput> DocumentResources { get; set; } = new List<DocumentResourceOutput>();

        #endregion

        #region 选词填空任务配置（TaskType=7）

        /// <summary>
        /// 题目内容
        /// </summary>
        public string? QuestionContent { get; set; }

        /// <summary>
        /// 正确答案列表
        /// </summary>
        public List<string> CorrectAnswers { get; set; } = new List<string>();

        /// <summary>
        /// 干扰项列表
        /// </summary>
        public List<string> DistractorWords { get; set; } = new List<string>();

        /// <summary>
        /// 自定义背景图片地址
        /// </summary>
        public string? CustomBackgroundImage { get; set; }

        #endregion
    }

    /// <summary>
    /// 阅读理解项目发布班级输出
    /// </summary>
    public class ReadingProjectClassOutput
    {
        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 班级名称
        /// </summary>
        public string? ClassName { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 发布开始时间
        /// </summary>
        public DateTime? PublishStartTime { get; set; }

        /// <summary>
        /// 发布结束时间
        /// </summary>
        public DateTime? PublishEndTime { get; set; }
    }
}
