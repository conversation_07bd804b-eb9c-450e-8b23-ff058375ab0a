<?xml version="1.0"?>
<doc>
    <assembly>
        <name>UwooAgent.Contracts</name>
    </assembly>
    <members>
        <member name="T:Uwoo.Contracts.Config.IMongoConfig">
            <summary>
            
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.IMongoConfig.ConnectionString">
            <summary>
            
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.IMongoConfig.DatabaseName">
            <summary>
            
            </summary>
        </member>
        <member name="T:Uwoo.Contracts.Config.IRabbitConfig">
            <summary>
            
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.IRabbitConfig.HostName">
            <summary>
            主机地址
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.IRabbitConfig.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.IRabbitConfig.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.IRabbitConfig.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.IRabbitConfig.Heartbeat">
            <summary>
            心跳时间 , 以秒为单位
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.IRabbitConfig.VirtualHost">
            <summary>
            虚拟路径
            </summary>
        </member>
        <member name="T:Uwoo.Contracts.Config.MongoConfig">
            <summary>
            
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.MongoConfig.ConnectionString">
            <summary>
            
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.MongoConfig.DatabaseName">
            <summary>
            
            </summary>
        </member>
        <member name="T:Uwoo.Contracts.Config.RabbitConfig">
            <summary>
            RabbitMQ配置
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.RabbitConfig.HostName">
            <summary>
            主机地址
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.RabbitConfig.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.RabbitConfig.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.RabbitConfig.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.RabbitConfig.Heartbeat">
            <summary>
            心跳时间 , 以秒为单位
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Config.RabbitConfig.VirtualHost">
            <summary>
            虚拟路径
            </summary>
        </member>
        <member name="T:Uwoo.Contracts.Paper.PaperFinishRange">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Contracts.Paper.PaperFinishRange.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.PaperFinishRange.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.PaperFinishRange.Range">
            <summary>
            选项对应的坐标位置
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.PaperFinishRange.Type">
            <summary>
            1 框选 答案  2 画圈题
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.PaperFinishRange.PageId">
            <summary>
            页码
            </summary>
        </member>
        <member name="T:Uwoo.Contracts.Paper.PaperNum">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Contracts.Paper.PaperNum.Id">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Contracts.Paper.PaperNum.Num">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Contracts.Paper.PaperNum.PaperId">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Contracts.Paper.PaperNum.Schoolid">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Contracts.Paper.PaperNum.CreateTime">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Contracts.Paper.WorkbookPage">
            <summary>
            
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.Id">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.ImgUrl">
            <summary>
            图片路径
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.CreatedTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.Grade">
            <summary>
            年级
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.Term">
            <summary>
            学期
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.Page">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.PaperType">
            <summary>
            类型 1练习册 2试卷
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.PaperId">
            <summary>
            试卷Id
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.PageId">
            <summary>
            明鼎对应页码Id
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.height">
            <summary>
            高
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.width">
            <summary>
            宽
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.SubjectId">
            <summary>
            学科Id
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Paper.WorkbookPage.IsCombination">
            <summary>
            是否单页包含多套试卷组合
            </summary>
        </member>
        <member name="T:Uwoo.Contracts.Redis.CurrentPaperId">
            <summary>
            当前正在作答试卷
            </summary>
            <remarks>卷码纸专用</remarks>
        </member>
        <member name="P:Uwoo.Contracts.Redis.CurrentPaperId.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Redis.CurrentPaperId.ClassId">
            <summary>
            班级id
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Redis.CurrentPaperId.TeacherId">
            <summary>
            教师id
            </summary>
        </member>
        <member name="T:Uwoo.Contracts.Redis.CurrentItem">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Contracts.Redis.CurrentItem.ItemId">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Contracts.Redis.CurrentItem.IsFullLine">
            <inheritdoc />
        </member>
        <member name="T:Uwoo.Contracts.Redis.TeacherCorrectingStudent">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Contracts.Redis.TeacherCorrectingStudent.StudentId">
            <summary>
            学生id
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Redis.TeacherCorrectingStudent.Page">
            <summary>
            正在批改的页码
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Redis.TeacherCorrectingStudent.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Redis.TeacherCorrectingStudent.ClassId">
            <summary>
            班级id
            </summary>
        </member>
        <member name="T:Uwoo.Contracts.Redis.TeacherCorrectingViewModel">
            <inheritdoc />
        </member>
        <member name="P:Uwoo.Contracts.Redis.TeacherCorrectingViewModel.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="P:Uwoo.Contracts.Redis.TeacherCorrectingViewModel.ClassId">
            <summary>
            班级id
            </summary>
        </member>
        <member name="T:X.PenServer.Contracts.Queue.AnswerResultData">
            <summary>
            答题卡作答结果
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.AnswerResultData.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.AnswerResultData.ItemNo">
            <summary>
            题目编号
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.AnswerResultData.Mac">
            <summary>
            点阵笔编号
            </summary>
            <remarks>学生作答时为学生笔编号, 教师批阅时为教师笔编号</remarks>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.AnswerResultData.PageId">
            <summary>
            业务页码id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.AnswerResultData.Page">
            <summary>
            实际采集到的页码
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.AnswerResultData.Result">
            <summary>
            作答结果
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.AnswerResultData.Score">
            <summary>
            题目总分值
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.AnswerResultData.IsMultiScore">
            <summary>
            题目是否包含多个分值
            </summary>
        </member>
        <member name="T:X.PenServer.Contracts.Queue.CardAnswerResultData">
            <summary>
            客观题答题卡
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.CardAnswerResultData.RequestId">
            <summary>
            请求id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.CardAnswerResultData.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.CardAnswerResultData.ItemNo">
            <summary>
            题目编号
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.CardAnswerResultData.Mac">
            <summary>
            点阵笔编号
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.CardAnswerResultData.UserId">
            <summary>
            用户id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.CardAnswerResultData.Result">
            <summary>
            作答结果
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.CardAnswerResultData.IsEraser">
            <summary>
            是否为橡皮擦清理之后作答的结果
            </summary>
        </member>
        <member name="T:X.PenServer.Contracts.Queue.EnglishUnitMarkingStudentData">
            <summary>
            开始批阅学生
            </summary>
            <remarks>选中对应学号</remarks>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.EnglishUnitMarkingStudentData.TeacherId">
            <summary>
            教师id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.EnglishUnitMarkingStudentData.StudentId">
            <summary>
            学生id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.EnglishUnitMarkingStudentData.StudentNo">
            <summary>
            学生编号
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.EnglishUnitMarkingStudentData.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="T:X.PenServer.Contracts.Queue.EnglishUnitObjectiveAnswerResultData">
            <summary>
            客观题作答结果
            </summary>
            <remarks>英语单元答题卡</remarks>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.EnglishUnitObjectiveAnswerResultData.UserId">
            <summary>
            学生id
            </summary>
        </member>
        <member name="T:X.PenServer.Contracts.Queue.EnglishUnitSubjectiveAnswerResultData">
            <summary>
            主观题作答教师批阅结果
            </summary>
            <remarks>英语单元答题卡</remarks>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.EnglishUnitSubjectiveAnswerResultData.SubItemNo">
            <summary>
            子题目编号
            </summary>
            <remarks>一道题目多个空</remarks>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.EnglishUnitSubjectiveAnswerResultData.TeacherId">
            <summary>
            教师id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.EnglishUnitSubjectiveAnswerResultData.StudentId">
            <summary>
            学生id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.EnglishUnitSubjectiveAnswerResultData.StudentNo">
            <summary>
            学生编号
            </summary>
        </member>
        <member name="T:X.PenServer.Contracts.Queue.InteractiveWorkbookSingleItemAnswer">
            <summary>
            互动练习簿
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.InteractiveWorkbookSingleItemAnswer.UserId">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.InteractiveWorkbookSingleItemAnswer.ItemId">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.InteractiveWorkbookSingleItemAnswer.PaperId">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.InteractiveWorkbookSingleItemAnswer.ClassId">
            <inheritdoc />
        </member>
        <member name="T:X.PenServer.Contracts.Queue.PenDot">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDot.Oid">
            <summary>
            雪花id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDot.X">
            <summary>
            X
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDot.Y">
            <summary>
            Y
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDot.Type">
            <summary>
            点位类型: 1.常规点位 2.结束点位
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDot.Page">
            <summary>
            采集到的页码
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDot.Time">
            <summary>
            时间
            </summary>
        </member>
        <member name="T:X.PenServer.Contracts.Queue.PenDotData">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotData.RequestId">
            <summary>
            请求id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotData.Page">
            <summary>
            实际采集的页码id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotData.PageId">
            <summary>
            对应平台业务页码id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotData.Mac">
            <summary>
            Mac
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotData.Time">
            <summary>
            时间
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotData.Dots">
            <summary>
            点位数据
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotData.UserId">
            <summary>
            用户id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotData.DataType">
            <summary>
            数据类型: 1.在线 2.离线
            </summary>
        </member>
        <member name="T:X.PenServer.Contracts.Queue.PenDotFinalData">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotFinalData.Dots">
            <summary>
            点位坐标
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotFinalData.PageId">
            <summary>
            页码id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotFinalData.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotFinalData.UserId">
            <summary>
            用户id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.PenDotFinalData.DoStatus">
            <summary>
            状态: 0.未开始 1.作答中 2.已识别 3.已批阅
            </summary>
        </member>
        <member name="T:X.PenServer.Contracts.Queue.TeacherCorrectData">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectData.PaperId">
            <summary>
            正在批改的试卷Id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectData.StudentId">
            <summary>
            批改的学生的Id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectData.PageId">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectData.StartTime">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectData.EndTime">
            <inheritdoc />
        </member>
        <member name="T:X.PenServer.Contracts.Queue.TeacherReviewStateDto">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherReviewStateDto.PaperId">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherReviewStateDto.ClassId">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherReviewStateDto.FirstReviewTime">
            <inheritdoc />
        </member>
        <member name="T:X.PenServer.Contracts.Queue.TeacherCorrectRecognitionInputDto">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectRecognitionInputDto.PageId">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectRecognitionInputDto.PaperId">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectRecognitionInputDto.StudentId">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectRecognitionInputDto.OcrType">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectRecognitionInputDto.StartTime">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectRecognitionInputDto.EndTime">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectRecognitionInputDto.Remark">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherCorrectRecognitionInputDto.IsTestLog">
            <inheritdoc />
        </member>
        <member name="T:X.PenServer.Contracts.Queue.TestTeacherOcrLogInputDto">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TestTeacherOcrLogInputDto.OcrType">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TestTeacherOcrLogInputDto.ClassId">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TestTeacherOcrLogInputDto.PaperId">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TestTeacherOcrLogInputDto.Remark">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TestTeacherOcrLogInputDto.StartTime">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TestTeacherOcrLogInputDto.EndTime">
            <inheritdoc />
        </member>
        <member name="T:X.PenServer.Contracts.Queue.TeacherPenDotData">
            <summary>
            教师笔迹
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Queue.TeacherPenDotData.TeacherId">
            <summary>
            教师用户id
            </summary>
        </member>
        <member name="T:X.PenServer.Contracts.Socket.RealtimeBatteryModel">
            <summary>
            实时电量
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimeBatteryModel.Battery">
            <summary>
            电量
            </summary>
            <remarks>电量百分比: 0 ~ 10</remarks>
        </member>
        <member name="T:X.PenServer.Contracts.Socket.RealtimeCorrectStudentModel">
            <summary>
            正在批改学生
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimeCorrectStudentModel.State">
            <summary>
            状态
            </summary>
            <remarks>0:取消 1:批改</remarks>
        </member>
        <member name="T:X.PenServer.Contracts.Socket.RealtimeDotModel">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimeDotModel.PaperId">
            <summary>
            试卷id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimeDotModel.PageId">
            <summary>
            实际采集的页码id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimeDotModel.UserId">
            <summary>
            用户id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimeDotModel.UserType">
            <summary>
            用户类型
            </summary>
            <remarks>0:学生 1:教师</remarks>
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimeDotModel.ClassId">
            <summary>
            学生班级id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimeDotModel.Dots">
            <summary>
            点位
            </summary>
        </member>
        <member name="T:X.PenServer.Contracts.Socket.RealtimeMessageBase">
            <inheritdoc />
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimeMessageBase.ClassId">
            <summary>
            班级id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimeMessageBase.UserId">
            <summary>
            用户id
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimeMessageBase.UserType">
            <summary>
            用户类型
            </summary>
            <remarks>0:学生 1:教师</remarks>
        </member>
        <member name="T:X.PenServer.Contracts.Socket.RealtimePenStateModel">
            <summary>
            实时连接状态
            </summary>
        </member>
        <member name="P:X.PenServer.Contracts.Socket.RealtimePenStateModel.State">
            <summary>
            状态
            </summary>
            <remarks>0:断开 1:连接</remarks>
        </member>
    </members>
</doc>
