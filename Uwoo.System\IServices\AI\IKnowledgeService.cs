using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 知识库管理服务接口
    /// </summary>
    public interface IKnowledgeService : IDependency
    {
        /// <summary>
        /// 获取知识库分页列表
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>分页结果</returns>
        Task<KnowledgePageResult> GetKnowledgeListAsync(KnowledgeQueryInput input);

        /// <summary>
        /// 根据ID获取知识库详情
        /// </summary>
        /// <param name="id">知识库ID</param>
        /// <returns>知识库详情</returns>
        Task<KnowledgeDetailDto?> GetKnowledgeByIdAsync(string id);

        /// <summary>
        /// 创建知识库
        /// </summary>
        /// <param name="input">创建参数</param>
        /// <returns>操作结果</returns>
        Task<KnowledgeOperationResult> CreateKnowledgeAsync(CreateKnowledgeInput input);

        /// <summary>
        /// 更新知识库
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>操作结果</returns>
        Task<KnowledgeOperationResult> UpdateKnowledgeAsync(UpdateKnowledgeInput input);

        /// <summary>
        /// 删除知识库
        /// </summary>
        /// <param name="id">知识库ID</param>
        /// <returns>操作结果</returns>
        Task<KnowledgeOperationResult> DeleteKnowledgeAsync(string id);

        /// <summary>
        /// 检查知识库名称是否存在
        /// </summary>
        /// <param name="name">知识库名称</param>
        /// <param name="excludeId">排除的ID（用于更新时检查）</param>
        /// <returns>是否存在</returns>
        Task<bool> IsKnowledgeNameExistsAsync(string name, string? excludeId = null);

        /// <summary>
        /// 获取知识库统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<Dictionary<string, int>> GetKnowledgeStatisticsAsync();
    }
}
