﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_教师端教案
    /// </summary>
    public interface IAgentTeacherTeachingPlanService : IService<AI_TeachingPlan>
    {
        /// <summary>
        /// 保存教案内容要求
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task SaveTeachingPlanContentDemand(SaveTeachingPlanContentDemandInput input);

        /// <summary>
        /// 删除教案内容要求
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task DelTeachingPlanContentDemand(DelTeachingPlanContentDemandInput input);

        /// <summary>
        /// 获取教案内容要求
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<GetTeachingPlanContentDemandOutput>> GetTeachingPlanContentDemand(GetTeachingPlanContentDemandInput input);

        /// <summary>
        /// 创建教案
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task CreateTeachingPlan(CreateTeachingPlanInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 修改教案创建记录中的教案文本
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task UpdateTeachingPlanCreateRecord(UpdateTeachingPlanCreateRecordInput input);

        /// <summary>
        /// 获取教案创建记录列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task<PageReturn<GetTeachingPlanCreateRecordOutput>> GetTeachingPlanCreateRecord(GetTeachingPlanCreateRecordInput input);

        /// <summary>
        /// 教案创建记录文本润色
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        Task TeachingPlanCreateRecordOptimize(TeachingPlanCreateRecordOptimizeInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 教案创建记录详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<TeachingPlanCreateRecordDetailsOutput> TeachingPlanCreateRecordDetails(TeachingPlanCreateRecordDetailsInput input);

        /// <summary>
        /// 教案创建记录文本转word
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<TeachingPlanCreateRecordTextToWordOutput> TeachingPlanCreateRecordTextToWord(TeachingPlanCreateRecordTextToWordInput input);

        /// <summary>
        /// 教案生成记录修改文件名称
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task TeachingPlanCreateRecordFileUpdateName(TeachingPlanCreateRecordFileUpdateNameInput input);

        /// <summary>
        /// 获取教案生成记录文件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<TeachingPlanCreateRecordFileOutput>> TeachingPlanCreateRecordFile(TeachingPlanCreateRecordFileInput input);

        /// <summary>
        /// 保存教案
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task SaveTeachingPlan(SaveTeachingPlanInput input);

        /// <summary>
        /// 获取教案列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PageReturn<GetTeachingPlanOutput>> GetTeachingPlan(GetTeachingPlanInput input);

        /// <summary>
        /// 获取教案详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetTeachingPlanDetailsOutput> GetTeachingPlanDetails(GetTeachingPlanDetailsInput input);

        /// <summary>
        /// 教案文本类型编辑
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task TeachingPlanTextUpdate(TeachingPlanTextUpdateInput input);

        /// <summary>
        /// 教案重命名
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task TeachingPlanUpdateName(TeachingPlanUpdateNameInput input);

        /// <summary>
        /// 删除教案
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task DelTeachingPlan(DelTeachingPlanInput input);
    }
}
