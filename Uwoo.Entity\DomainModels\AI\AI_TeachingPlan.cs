﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// 智能体_教案信息
	/// </summary>
	[Table("AI_TeachingPlan")]
    public class AI_TeachingPlan : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 教案名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 类型（1:文本、2:Word、3PPT）
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 教案文本内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// 学科ID
        /// </summary>
        public string SubjectId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string GradeId { get; set; }

        /// <summary>
        /// 学年
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// 学期（1:上、2:下）
        /// </summary>
        public int? Term { get; set; }
    }
}
