﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Util.Helper
{
	public class HttpRequestClient
    {
        #region //字段
        private Encoding encoding = Encoding.UTF8;
        private string boundary = String.Empty;
        #endregion

        #region //构造方法
        public HttpRequestClient()
        {
            string flag = DateTime.Now.Ticks.ToString("x");
            boundary = "---------------------------" + flag;
        }
        #endregion

        #region //方法
        /// <summary>
        /// 合并请求数据
        /// </summary>
        /// <returns></returns>
        private byte[] MergeContent(List<byte[]> bytesArray)
        {
            int length = 0;
            int readLength = 0;
            string endBoundary = "--" + boundary + "--\r\n";
            byte[] endBoundaryBytes = encoding.GetBytes(endBoundary);

            if (endBoundaryBytes != null)
                bytesArray.Add(endBoundaryBytes);

            foreach (byte[] b in bytesArray)
            {
                length += b.Length;
            }

            byte[] bytes = new byte[length];

            foreach (byte[] b in bytesArray)
            {
                b.CopyTo(bytes, readLength);
                readLength += b.Length;
            }

            return bytes;
        }

        /// <summary>
        /// 上传
        /// </summary>
        /// <param name="requestUrl">请求url</param>
        /// <param name="responseText">响应</param>
        /// <param name="bytesArray"></param>
        /// <returns></returns>
        public bool Upload(string requestUrl, out string responseText,List<byte[]> bytesArray)
        {
            HttpClient client = new HttpClient();
          
            byte[] responseBytes;
            var bytes = MergeContent(bytesArray);
            var httpContent = new ByteArrayContent(bytes);
            var request = new HttpRequestMessage(System.Net.Http.HttpMethod.Post, requestUrl);
            httpContent.Headers.Add("Content-Type", $"multipart/form-data; boundary={boundary}"); 
            request.Content = httpContent;

            _SetFieldValueF?.Invoke(this);

            try
            {
                var res = client.Send(request);
                responseBytes = res.Content.ReadAsByteArrayAsync().Result;
                responseText = System.Text.Encoding.UTF8.GetString(responseBytes);
                return true;
            }
            catch (WebException ex)
            {
                //服务器500错误
                if (((System.Net.HttpWebResponse)ex.Response).StatusCode != HttpStatusCode.InternalServerError)
                {
                    using (var responseStream = ex.Response.GetResponseStream())
                    {
                        responseBytes = new byte[ex.Response.ContentLength];
                        responseStream?.Read(responseBytes, 0, responseBytes.Length);
                    }
                }
                else
                {
                    responseText = string.Empty;
                    return false;
                }  
            }
            finally
            {
                client.Dispose();
            }
            responseText = System.Text.Encoding.UTF8.GetString(responseBytes);

            return false;
        }

        /// <summary>
        /// 上传
        /// </summary>
        /// <param name="requestUrl">请求url</param>
        /// <param name="bites"></param>
        /// <returns></returns>
        public async Task<string> UploadObjectAsync(string requestUrl,List<byte[]> bites)
        {
	        byte[] response;
	        using var client = new HttpClient();
	        var result = "";
	        var bytes = MergeContent(bites);
	        var content = new ByteArrayContent(bytes);
	        var request = new HttpRequestMessage(HttpMethod.Post, requestUrl);
	        content.Headers.Add("Content-Type", $"multipart/form-data; boundary={boundary}");
	        request.Content = content;

	        _SetFieldValueF?.Invoke(this);

	        try
	        {
		        using var res = await client.SendAsync(request);
		        response = await res.Content.ReadAsByteArrayAsync();
		        result = Encoding.UTF8.GetString(response);
		        return result;
	        }
	        catch (WebException ex)
	        {
		        if (((HttpWebResponse) ex.Response)!.StatusCode != HttpStatusCode.InternalServerError)
		        {
			        await using var response_stream = ex.Response.GetResponseStream();
			        response = new byte[ex.Response.ContentLength];
			        _ = await response_stream?.ReadAsync(response, 0, response.Length)!;
		        }
		        else
		        {
			        result = string.Empty;
			        return result;
		        }
	        }

	        result = Encoding.UTF8.GetString(response);
	        return result;
        }

        /// <summary>
		/// 上传
		/// </summary>
		/// <param name="requestUrl">请求url</param>
		/// <param name="responseText">响应</param>
		/// <param name="bytesArray"></param>
		/// <returns></returns>
		public bool UploadOld(String requestUrl, out String responseText, List<byte[]> bytesArray)
		{
			using var client = new HttpClient();
			byte[] responseBytes;
			var bytes = MergeContent(bytesArray);
			var httpContent = new ByteArrayContent(bytes);
			var request = new HttpRequestMessage(HttpMethod.Post, requestUrl);
			httpContent.Headers.Add("Content-Type", $"multipart/form-data; boundary={boundary}");
			request.Content = httpContent;

			_SetFieldValueF?.Invoke(this);

			try
			{
				var res = client.Send(request);
				responseBytes = res.Content.ReadAsByteArrayAsync().Result;
				responseText = System.Text.Encoding.UTF8.GetString(responseBytes);
				return true;
			}
			catch (WebException ex)
			{
				//服务器500错误
				if (((System.Net.HttpWebResponse)ex.Response).StatusCode != HttpStatusCode.InternalServerError)
				{
					using (var responseStream = ex.Response.GetResponseStream())
					{
						responseBytes = new byte[ex.Response.ContentLength];
						responseStream?.Read(responseBytes, 0, responseBytes.Length);
					}
				}
				else
				{
					responseText = string.Empty;
					return false;
				}
			}
			responseText = System.Text.Encoding.UTF8.GetString(responseBytes);
			return false;
		}

		public bool UploadTime(String requestUrl, out String responseText, List<byte[]> bytesArray)
        {
            HttpClient client = new HttpClient();
            client.Timeout = new TimeSpan(0,2,30);
            byte[] responseBytes;
            var bytes = MergeContent(bytesArray);
            var httpContent = new ByteArrayContent(bytes);
            httpContent.Headers.Add("Content-Type", $"multipart/form-data; boundary={boundary}");

            var request = new HttpRequestMessage(System.Net.Http.HttpMethod.Post, requestUrl);
            request.Content = httpContent;

            _SetFieldValueF?.Invoke(this);

            try
            {
                var res = client.Send(request);
                responseBytes = res.Content.ReadAsByteArrayAsync().Result;
                responseText = System.Text.Encoding.UTF8.GetString(responseBytes);
                return true;
            }
            catch (WebException ex)
            {
                //服务器500错误
                if (((System.Net.HttpWebResponse)ex.Response).StatusCode != HttpStatusCode.InternalServerError)
                {
                    using (var responseStream = ex.Response.GetResponseStream())
                    {
                        responseBytes = new byte[ex.Response.ContentLength];
                        responseStream?.Read(responseBytes, 0, responseBytes.Length);
                    }
                }
                else
                {
                    responseText = string.Empty;
                    return false;
                }
            }
            finally
            {
                client.Dispose();
            }
            responseText = System.Text.Encoding.UTF8.GetString(responseBytes);

            return false;
        }

		public bool UploadTime2(String requestUrl, out String responseText, List<byte[]> bytesArray)
		{
			using var client = new HttpClient();
			client.Timeout = TimeSpan.FromMinutes(33.33);
			byte[] responseBytes;
			var bytes = MergeContent(bytesArray);
			var httpContent = new ByteArrayContent(bytes);
			var request = new HttpRequestMessage(HttpMethod.Post, requestUrl);
			httpContent.Headers.Add("Content-Type", $"multipart/form-data; boundary={boundary}");
			request.Content = httpContent;

			_SetFieldValueF?.Invoke(this);

			try
			{
				var res = client.Send(request);
				responseBytes = res.Content.ReadAsByteArrayAsync().Result;
				responseText = System.Text.Encoding.UTF8.GetString(responseBytes);
				return true;
			}
			catch (WebException ex)
			{
				//服务器500错误
				if (((System.Net.HttpWebResponse)ex.Response).StatusCode != HttpStatusCode.InternalServerError)
				{
					using (var responseStream = ex.Response.GetResponseStream())
					{
						responseBytes = new byte[ex.Response.ContentLength];
						responseStream?.Read(responseBytes, 0, responseBytes.Length);
					}
				}
				else
				{
					responseText = string.Empty;
					return false;
				}
			}
			finally
			{
				client.Dispose();
			}
			responseText = System.Text.Encoding.UTF8.GetString(responseBytes);

			return false;
		}

		public bool UploadAsync(String requestUrl, List<byte[]> bytesArray)
        {
            byte[] bytes = MergeContent(bytesArray);
            try
            {
                using (HttpClient client = new())
                {
                    var httpContent = new ByteArrayContent(bytes);
                    var request = new HttpRequestMessage(HttpMethod.Post, requestUrl);
                    httpContent.Headers.Add("Content-Type", $"multipart/form-data; boundary={boundary}");
                    request.Content = httpContent;
                    client.Send(request);
                }
                return true;
            }
            catch (WebException)
            {
                throw;
            }
        }


		/// <summary>
        /// 设置表单数据字段
        /// </summary>
        /// <param name="fieldName">字段名</param>
        /// <param name="fieldValue">字段值</param>
        /// <param name="bytesArray"></param>
        /// <returns></returns>
        public void SetFieldValue(String fieldName, String fieldValue, List<byte[]> bytesArray)
        {
            string httpRow = "--" + boundary + "\r\nContent-Disposition: form-data; name=\"{0}\"\r\n\r\n{1}\r\n";
            string httpRowData = String.Format(httpRow, fieldName, fieldValue);

            bytesArray.Add(encoding.GetBytes(httpRowData));
        }

        /// <summary>
        /// 设置表单文件数据
        /// </summary>
        /// <param name="fieldName">字段名</param>
        /// <param name="filename">字段值</param>
        /// <param name="contentType">内容内型</param>
        /// <param name="fileBytes">文件字节流</param>
        /// <param name="bytesArray"></param>
        /// <returns></returns>
        public void SetFieldValue(String fieldName, String filename, String contentType, Byte[] fileBytes, List<byte[]> bytesArray)
        {
            string end = "\r\n";
            string httpRow = "--" + boundary + "\r\nContent-Disposition: form-data; name=\"{0}\"; filename=\"{1}\"\r\nContent-Type: {2}\r\n\r\n";
            string httpRowData = String.Format(httpRow, fieldName, filename, contentType);

            byte[] headerBytes = encoding.GetBytes(httpRowData);
            byte[] endBytes = encoding.GetBytes(end);
            byte[] fileDataBytes = new byte[headerBytes.Length + fileBytes.Length + endBytes.Length];

            headerBytes.CopyTo(fileDataBytes, 0);
            fileBytes.CopyTo(fileDataBytes, headerBytes.Length);
            endBytes.CopyTo(fileDataBytes, headerBytes.Length + fileBytes.Length);

            bytesArray.Add(fileDataBytes);
        }


        Action<HttpRequestClient> _SetFieldValueF { get; set; }
        public void SetFieldValueF(Action<HttpRequestClient> action)
        {
            _SetFieldValueF = action;
        }

        #endregion
    }
}
