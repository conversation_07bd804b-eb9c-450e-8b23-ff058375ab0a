﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.System.IRepositories.AI;

namespace UwooAgent.System.Repositories.AI
{
    /// <summary>
    /// 智能体_通用功能
    /// </summary>
    public class AgentCommonRepository : RepositoryBase<AI_AgentBaseInfo>, IAgentCommonRepository
    {
        public AgentCommonRepository(VOLContext dbContext) : base(dbContext)
        {

        }

        public static IAgentCommonRepository Instance
        {
            get
            {
                return AutofacContainerModule.GetService<IAgentCommonRepository>();
            }
        }
    }
}
