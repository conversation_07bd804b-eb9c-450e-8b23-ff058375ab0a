﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 文本转语音入参
    /// </summary>
    public class TextChangeVoiceInputDto
    {
        /// <summary>
        /// 用户信息（包含 uid）
        /// </summary>
        [JsonPropertyName("user")]
        public UserDto User { get; set; } = new UserDto();

        /// <summary>
        /// 命名空间（固定为 BidirectionalTTS）
        /// </summary>
        [JsonPropertyName("namespace")]
        public string Namespace { get; set; } = "BidirectionalTTS";

        /// <summary>
        /// TTS 核心请求参数（音色、文本、音频配置）
        /// </summary>
        [JsonPropertyName("req_params")]
        public ReqParamsDto ReqParams { get; set; } = new ReqParamsDto();

        /// <summary>
        /// 附加参数（序列化为 JSON 字符串传递）
        /// </summary>
        [JsonPropertyName("additions")]
        public string Additions { get; set; } = string.Empty;

        /// <summary>
        /// 事件
        /// </summary>
        [JsonPropertyName("event")]
        public int Event { get; set; }
    }

    /// <summary>
    /// 用户信息类（对应原 ["user"] 层级）
    /// </summary>
    public class UserDto
    {
        /// <summary>
        /// 用户唯一标识（自动生成 GUID）
        /// </summary>
        [JsonPropertyName("uid")]
        public string Uid { get; set; } = Guid.NewGuid().ToString();
    }

    /// <summary>
    /// TTS 核心请求参数类（对应原 ["req_params"] 层级）
    /// </summary>
    public class ReqParamsDto
    {
        /// <summary>
        /// 音色类型（如 zh_female_tianmeixiaoyuan_moon_bigtts）
        /// </summary>
        [JsonPropertyName("speaker")]
        public string Speaker { get; set; } = string.Empty;

        /// <summary>
        /// 待合成的文本内容
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// 音频参数配置（格式、采样率等）
        /// </summary>
        [JsonPropertyName("audio_params")]
        public AudioParamsDto AudioParams { get; set; } = new AudioParamsDto();
    }

    /// <summary>
    /// 音频参数配置类（对应原 ["audio_params"] 层级）
    /// </summary>
    public class AudioParamsDto
    {
        /// <summary>
        /// 音频编码格式（如 pcm、mp3）
        /// </summary>
        [JsonPropertyName("format")]
        public string Format { get; set; } = string.Empty;

        /// <summary>
        /// 采样率（固定为 24000）
        /// </summary>
        [JsonPropertyName("sample_rate")]
        public int SampleRate { get; set; } = 24000;

        /// <summary>
        /// 是否启用时间戳
        /// </summary>
        [JsonPropertyName("enable_timestamp")]
        public bool EnableTimestamp { get; set; } = true;

        /// <summary>
        /// 设置音色的情感
        /// </summary>
        [JsonPropertyName("emotion")]
        public string emotion { get; set; } = string.Empty;
    }

    /// <summary>
    /// 附加参数类（对应原 ["additions"] 序列化前的 Dictionary）
    /// </summary>
    public class AdditionsDto
    {
        /// <summary>
        /// 是否禁用 Markdown 过滤
        /// </summary>
        public bool disable_markdown_filter { get; set; }

        /// <summary>
        /// 是否自动识别语种
        /// </summary>
        public bool enable_language_detector { get; set; }

        /// <summary>
        /// 是否可以播报latex公式，需将disable_markdown_filter设为true
        /// </summary>
        public bool enable_latex_tn { get; set; }
    }
}
