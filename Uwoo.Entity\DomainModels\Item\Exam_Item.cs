﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Item
{
    /// <summary>
    /// 试题信息表
    /// </summary>
    [Table("Exam_Item")]
    public class Exam_Item
    {
        [SugarColumn(IsPrimaryKey = true)]
        /// <summary>
        /// Id
        /// </summary>
        public String Id { get; set; }

        public string ScoreMark { get; set; }

        /// <summary>
        /// 出卷人
        /// </summary>
        public String CreatorId { get; set; }

        /// <summary>
        /// 试卷名称
        /// </summary>
        public String Title { get; set; }

        /// <summary>
        /// 题型Id，关联Exam_ItemType 表的主键
        /// </summary>
        public String TypeId { get; set; }

        /// <summary>
        /// 题型类型
        /// </summary>
        public int? ItemType { get; set; }

        /// <summary>
        /// 难度等级
        /// </summary>
        public Decimal? Level { get; set; }

        /// <summary>
        /// 学习水平目标
        /// </summary>
        public String Examine { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string GradeId { get; set; }

        /// <summary>
        /// 学期 1上学期 2下学期
        /// </summary>
        public string Term { get; set; }

        /// <summary>
        /// 年级_学期 : 1_1
        /// </summary>
        public string GradeTerm { get; set; }

        /// <summary>
        /// 章节
        /// </summary>
        public String Chapter { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public Int32? Status { get; set; }

        /// <summary>
        /// 做题时间
        /// </summary>
        public Int32? StandardMinutes { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// Md5
        /// </summary>
        public String Md5 { get; set; }

        /// <summary> 
        /// 题型板块   内容领域
        /// </summary>
        public String PlateTypeId { get; set; }

        /// <summary>
        /// ShareType
        /// </summary>
        public Int32? ShareType { get; set; }

        /// <summary>
        /// Deleted
        /// </summary>
        public Boolean? Deleted { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string ChapterTarget { get; set; }
        /// <summary>
        /// 试题编码
        /// </summary>
        public string ItemCode { get; set; }


        /// <summary>
        /// 0 不上传  1 必须上传 2可选上传 3核心素养上传
        /// </summary>
        public int? IsUpload { get; set; }

        /// <summary>
        /// 是否必答,0:否  1:是
        /// </summary>
        public int IsFacing { get; set; } = 1;

        /// <summary>
        /// 批阅内容类型 1:图片  2:音频  3:视频  4:文本
        /// </summary>
        public int? ReviewContentType { get; set; }
        /// <summary>
        /// 来源 0原创 1配套练习册 2引用 3改编
        /// </summary>
        public int? Source { get; set; }

        /// <summary>
        /// 题目提示
        /// </summary>
        public string Tips { get; set; }

        /// <summary>
        /// 情景属性
        /// </summary>
        public int? Scene { get; set; }

        /// <summary>
        /// 作业类型
        /// </summary>
        public string JobType { get; set; }

        /// <summary>
        /// 是否是核心素养编码
        /// </summary>
        public bool? IsKernelCode { get; set; }

        /// <summary>
        /// 是否为语音类型题目
        /// </summary>
        /// <remarks>题干是否关联语音</remarks>
        public bool? IsVoiceType { get; set; }

        /// <summary>
        /// 是否开启语音作答
        /// </summary>
        public bool? IsVoiceAnswer { get; set; }

        /// <summary>
        /// 是否开启语音评价
        /// </summary>
        public bool? IsVoiceEval { get; set; }

        /// <summary>
        /// 知识点
        /// </summary>
        public string Knowledge { get; set; }

        /// <summary>
        /// 检测目标
        /// </summary>
        public string DetectionTarget { get; set; }

        /// <summary>
        /// 是否上传解题思路（默认false）
        /// </summary>
        public bool? IsSolutions { get; set; } = false;

        /// <summary>
        ///  是否为整行作答
        /// </summary>
        public bool? IsWholeLineAnswer { get; set; } = false;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 平台来源（1专课专练、2三个助手）
        /// </summary>
        public int? PlatformSource { get; set; }

        /// <summary>
        /// 评测体系
        /// </summary>
        public int? EvaluationType { get; set; }

        /// <summary>
        /// 字数（作文题）
        /// </summary>
        public int? Words { get; set; }
    }
}
