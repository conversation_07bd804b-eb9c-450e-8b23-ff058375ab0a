﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Entity.DomainModels
{
    /// <summary>
    /// 用户表
    /// </summary>
    [Entity(TableCnName = "用户表", TableName = "Base_User")]
    public class Base_User : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key, Column(Order = 1)]
        [SugarColumn(IsPrimaryKey = true)]
        public String Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public String CreatorId { get; set; }

        /// <summary>
        /// 否已删除
        /// </summary>
        public Boolean Deleted { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public String UserName { get; set; }

        /// <summary>
        /// 昵称
        /// </summary>
        public String NickName { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public String Password { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public String RealName { get; set; }

        /// <summary>
        /// 性别(1为男，0为女)
        /// </summary>
        public Int32 Sex { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        public DateTime? Birthday { get; set; }

        /// <summary>
        /// 所属部门Id
        /// </summary>
        public String DepartmentId { get; set; }

        /// <summary>
        /// 所属学校
        /// </summary>
        public String SchoolId { get; set; }

        /// <summary>
        /// 统一身份认证Id
        /// </summary>
        public String UnionId { get; set; }

        /// <summary>
        /// 微校网校用户认证 Id
        /// </summary>
        public string WxId { get; set; }


        /// <summary>
        /// 统一用户认证Id
        /// </summary>
        public string XhId { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string PhoneNum { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        public string Photo { get; set; }

        /// <summary>
        /// 审核状态 0或者null 未审核（不通过） 1 审核通过 2 审核不通过
        /// </summary>
        public int? State { get; set; }

        /// <summary>
        /// 区域ID
        /// </summary>
        public int AreaId { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? AuditTime { get; set; }

        /// <summary>
        /// 注销时间
        /// </summary>
        public string LogoutTime { get; set; }

        /// <summary>
        /// 点阵笔是否有编辑权限
        /// </summary>
        public bool? PenState { get; set; } = false;

        /// <summary>
        /// 点阵笔编辑权限开通时间
        /// </summary>
        public DateTime? PenDate { get; set; }

        /// <summary>
        /// 父级Id
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 是否是真实用户账号
        /// </summary>
        public bool? IsRealUser { get; set; }
    }
}
