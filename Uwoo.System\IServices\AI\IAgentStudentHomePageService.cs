﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_学生端首页
    /// </summary>
    public interface IAgentStudentHomePageService : IService<AI_AgentBaseInfo>
    {
        /// <summary>
        /// 智能体_学生端首页智能体任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task<AgentStudentHomePageAgentTaskOuput> GetStudentHomePageAgentTaskList(AgentStudentHomePageAgentTaskInput input);
    }
}
