using System.ComponentModel;

namespace Uwoo.Core.Enums
{
    public enum HomeworkType
    {
        #region HomeworkBaseType -> DZB

        /// <summary>
        /// A4答题卷（原点阵笔）
        /// </summary>
        [Description("A4答题卷")]
        AnswerSheet_A4 = 1,

        /// <summary>
        /// B3答题卷
        /// </summary>
        [Description("B3答题卷")]
        AnswerSheet_B3 = 2,

        /// <summary>
        /// 客观答题卡
        /// </summary>
        [Description("客观答题卡")]
        ObjectiveAnswerCard = 3,

        /// <summary>
        /// 互动作业簿
        /// </summary>
        [Description("互动作业簿")]
        InteractiveWorkbook = 4,

        /// <summary>
        /// 自主答题卡(位育)
        /// </summary>
        [Description("自主答题卡(位育)")]
        AutonomousAnswerCardOne = 5,

        #endregion

        /// <summary>
        /// 随堂即兴题，不限答题纸张
        /// </summary>
        [Description("不限")]
        Unlimited = 9,
        #region HomeworkBaseType -> Online

        /// <summary>
        /// 电子作业
        /// </summary>
        [Description("电子作业")]
        ElectronicHomework = 21,

        /// <summary>
        /// 拍照分割
        /// </summary>
        [Description("拍照分割")]
        PhotoSegmentation = 22

        #endregion
    }

	public enum HomeworkBaseType
	{
		/// <summary>
		/// 点阵笔
		/// </summary>
		[Description("点阵笔")]
		DZB = 1,

		/// <summary>
		/// 在线作业
		/// </summary>
		[Description("在线作业")]
		Online = 2
	}
}