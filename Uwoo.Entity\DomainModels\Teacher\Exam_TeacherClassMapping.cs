﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace UwooAgent.Entity.DomainModels.Teacher
{
    /// <summary>
    /// Exam_TeacherClassMapping
    /// </summary>
    [Table("Exam_TeacherClassMapping")]
    public class Exam_TeacherClassMapping
    {

        /// <summary>
        /// Id
        /// </summary>
        [Key, Column(Order = 1)]
        [SugarColumn(IsPrimaryKey = true)]
        public String Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人Id
        /// </summary>
        public String CreatorId { get; set; }

        /// <summary>
        /// 否已删除
        /// </summary>
        public Boolean Deleted { get; set; }

        /// <summary>
        /// 老师用户Id
        /// </summary>
        public String TeacherUserId { get; set; }

        /// <summary>
        /// 班级Id
        /// </summary>
        public String ClassId { get; set; }

        /// <summary>
        /// 学年
        /// </summary>
        public int Year { get; set; }
    }
}
