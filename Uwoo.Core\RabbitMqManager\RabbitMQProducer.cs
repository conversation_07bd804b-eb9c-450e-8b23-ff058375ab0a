using System;
using System.Text;
using Newtonsoft.Json;
using RabbitMQ.Client;
using System.Threading.Tasks;

namespace Uwoo.Core.RabbitMQ
{
    public class RabbitMQProducer
    {
        private readonly RabbitMQConnectionFactory _connectionFactory;

        public RabbitMQProducer(RabbitMQConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public void PublishMessage(string exchangeName, string queueName, object message)
        {
            if (string.IsNullOrEmpty(exchangeName))
            {
                throw new ArgumentException("Exchange name cannot be null or empty.", nameof(exchangeName));
            }

            if (string.IsNullOrEmpty(queueName))
            {
                throw new ArgumentException("Routing key cannot be null or empty.", nameof(queueName));
            }

            if (message == null)
            {
                throw new ArgumentNullException(nameof(message), "Message cannot be null.");
            }
            
            using (var connection = _connectionFactory.CreateConnection())
            using (var channel =  connection.CreateModel())
            {
                // 声明交换机
                channel.ExchangeDeclare(exchange: exchangeName, type: ExchangeType.Fanout, durable: true, autoDelete: false);

				// 声明队列
				channel.QueueDeclare(queue: queueName, durable: true, exclusive: false, autoDelete: false, arguments: null);

				// 将队列绑定到 Fanout 交换机，无需指定路由键
				channel.QueueBind(queue: queueName, exchange: exchangeName, routingKey: "");

				// 将消息序列化为 JSON 字符串
				var jsonMessage = JsonConvert.SerializeObject(message);
                var body = Encoding.UTF8.GetBytes(jsonMessage);

                // 设置消息持久化
                var properties = channel.CreateBasicProperties();
                properties.Persistent = true;
                properties.DeliveryMode = 2;

				// 发布消息
				channel.BasicPublish(exchange: exchangeName,
                                     routingKey: "",
                                     basicProperties: properties,
                                     body: body);
            }
        }
    }
}