﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取学生做建模未达标列表输出
    /// </summary>
    public class GetStudentDoModelingNoStandardListOutput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 建模Id
        /// </summary>
        public string? ModelingId { get; set; }

        /// <summary>
        /// 建模名称
        /// </summary>
        public string? ModelingName { get; set; }

        /// <summary>
        /// 建模Logo
        /// </summary>
        public string? ModelingLogo { get; set; }

        /// <summary>
        /// 建模背景
        /// </summary>
        public string? ModelingIntroduce { get; set; }

        /// <summary>
        /// 建模阶段
        /// </summary>
        public List<GetStudentDoModelingNoStandardListStageOutput> ModelingStageInfos { get; set; } = new List<GetStudentDoModelingNoStandardListStageOutput>();
    }

    /// <summary>
    /// 获取学生做建模未达标列表阶段信息输出
    /// </summary>
    public class GetStudentDoModelingNoStandardListStageOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 建模阶段任务
        /// </summary>
        public List<GetStudentDoModelingNoStandardListStageTaskOutput> ModelingStageTaskInfos { get; set; } = new List<GetStudentDoModelingNoStandardListStageTaskOutput>();
    }

    /// <summary>
    /// 获取学生做建模未达标列表阶段任务信息输出
    /// </summary>
    public class GetStudentDoModelingNoStandardListStageTaskOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 建模阶段Id
        /// </summary>
        public string? ModelingStageId { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:问题理解、7:模型构建、8:模型假设、9:模型评价）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 任务提交记录Id
        /// </summary>
        public List<string> TaskSubmitId { get; set; } = new List<string>();

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }
    }
}
