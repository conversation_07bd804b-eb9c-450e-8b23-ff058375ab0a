﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 智能体_口语交际任务信息
    /// </summary>
    [Table("AI_OralCommunicationTask")]
    public class AI_OralCommunicationTask : BaseEntity
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string AgentTaskId { get; set; }

        /// <summary>
        /// 章节Id
        /// </summary>
        public string ChapterId { get; set; }

        /// <summary>
        /// 教学目标
        /// </summary>
        public string Target { get; set; }

        /// <summary>
        /// 评价体系Id
        /// </summary>
        public string EvaluateId { get; set; }

        /// <summary>
        /// 开场白
        /// </summary>
        public string Prologue { get; set; }

        /// <summary>
        /// 场景
        /// </summary>
        public string Scene { get; set; }

        /// <summary>
        /// 场景细节
        /// </summary>
        public string SceneDetail { get; set; }

        /// <summary>
        /// 互动模式(1指令式、2对话式、3辩论式)
        /// </summary>
        public int? InteractiveMode { get; set; }

        /// <summary>
        /// 音色Id
        /// </summary>
        public string ToneId { get; set; }

        /// <summary>
        /// 角色名称（用于AI展示名称）
        /// </summary>
        public string RoleName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// 是否是案例
        /// </summary>
        public bool IsCase { get; set; }
    }
}
