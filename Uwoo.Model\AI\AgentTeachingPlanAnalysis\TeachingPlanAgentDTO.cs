﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI.AgentTeachingPlanAnalysis
{
    public class TeachingPlanAgentDTO
    {
        public string workflow_id { get; set; }
        public StartPointParameters parameters { get; set; }

        /// <summary>
        /// 是否异步执行，默认为false
        /// </summary>
        public bool is_async { get; set; } = false;
    }
    /// <summary>
    /// 工作流的开始节点
    /// </summary>
    public class StartPointParameters
    {
        /// <summary>
        /// Prompt 输入文本
        /// </summary>
        public string userPrompt { get; set; }

    }
}
