﻿// -- Function：CurrentPaperId.cs
// --- Project：X.PenServer.Contracts
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/31 17:48

namespace Uwoo.Contracts.Redis
{
    /// <summary>
    /// 当前正在作答试卷
    /// </summary>
    /// <remarks>卷码纸专用</remarks>
    public class CurrentPaperId
    {
        /// <summary>
        /// 试卷id
        /// </summary>
        public string PaperId { get; set; }

        /// <summary>
        /// 班级id
        /// </summary>
        public string ClassId { get; set; }

        /// <summary>
        /// 教师id
        /// </summary>
        public string TeacherId { get; set; }
    }

	/// <inheritdoc />
	public class CurrentItem : CurrentPaperId
    {
		/// <inheritdoc />
		public string ItemId { get; set; }

		/// <inheritdoc />
		public bool IsFullLine { get; set; }
	}
}