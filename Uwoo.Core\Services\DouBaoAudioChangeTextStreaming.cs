﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Services
{
	/// <summary>
	/// 大模型流式语音识别API
	/// </summary>
	public class DouBaoAudioChangeTextStreaming
	{
		public const int PROTOCOL_VERSION = 0b0001;
		public const int DEFAULT_HEADER_SIZE = 0b0001;

		// Message Type
		public const int FULL_CLIENT_REQUEST = 0b0001;
		public const int AUDIO_ONLY_REQUEST = 0b0010;
		public const int FULL_SERVER_RESPONSE = 0b1001;
		public const int SERVER_ACK = 0b1011;
		public const int SERVER_ERROR_RESPONSE = 0b1111;

		// Message Type Specific Flags
		public const int NO_SEQUENCE = 0b0000;
		public const int POS_SEQUENCE = 0b0001;
		public const int NEG_SEQUENCE = 0b0010;
		public const int NEG_WITH_SEQUENCE = 0b0011;
		public const int NEG_SEQUENCE_1 = 0b0011;

		// Message Serialization
		public const int NO_SERIALIZATION = 0b0000;
		public const int JSON = 0b0001;

		// Message Compression
		public const int NO_COMPRESSION = 0b0000;
		public const int GZIP = 0b0001;

		// 生成头部
		public static byte[] GenerateHeader(int messageType = FULL_CLIENT_REQUEST, int messageTypeSpecificFlags = NO_SEQUENCE, int serialMethod = JSON, int compressionType = GZIP, byte reservedData = 0x00)
		{
			byte[] header = new byte[4];
			header[0] = (byte)((PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE);
			header[1] = (byte)((messageType << 4) | messageTypeSpecificFlags);
			header[2] = (byte)((serialMethod << 4) | compressionType);
			header[3] = reservedData;
			return header;
		}

		// 生成负载前的数据
		public static byte[] GenerateBeforePayload(int sequence)
		{
			return BitConverter.GetBytes(sequence).Reverse().ToArray();
		}

		// 解析响应
		public static Dictionary<string, object> ParseResponse(byte[] res)
		{
			int protocolVersion = res[0] >> 4;
			int headerSize = res[0] & 0x0f;
			int messageType = res[1] >> 4;
			int messageTypeSpecificFlags = res[1] & 0x0f;
			int serializationMethod = res[2] >> 4;
			int messageCompression = res[2] & 0x0f;
			byte reserved = res[3];
			byte[] headerExtensions = res.Skip(4).Take(headerSize * 4 - 4).ToArray();
			byte[] payload = res.Skip(headerSize * 4).ToArray();

			var result = new Dictionary<string, object>
			{
				["is_last_package"] = false
			};

			byte[] payloadMsg = null;
			int payloadSize = 0;

			if ((messageTypeSpecificFlags & 0x01) != 0)
			{
				int seq = BitConverter.ToInt32(payload.Take(4).Reverse().ToArray(), 0);
				result["payload_sequence"] = seq;
				payload = payload.Skip(4).ToArray();
			}

			if ((messageTypeSpecificFlags & 0x02) != 0)
			{
				result["is_last_package"] = true;
			}

			if (messageType == FULL_SERVER_RESPONSE)
			{
				payloadSize = BitConverter.ToInt32(payload.Take(4).Reverse().ToArray(), 0);
				payloadMsg = payload.Skip(4).ToArray();
			}
			else if (messageType == SERVER_ACK)
			{
				int seq = BitConverter.ToInt32(payload.Take(4).Reverse().ToArray(), 0);
				result["seq"] = seq;
				if (payload.Length >= 8)
				{
					payloadSize = BitConverter.ToInt32(payload.Skip(4).Take(4).Reverse().ToArray(), 0);
					payloadMsg = payload.Skip(8).ToArray();
				}
			}
			else if (messageType == SERVER_ERROR_RESPONSE)
			{
				int code = BitConverter.ToInt32(payload.Take(4).Reverse().ToArray(), 0);
				result["code"] = code;
				payloadSize = BitConverter.ToInt32(payload.Skip(4).Take(4).Reverse().ToArray(), 0);
				payloadMsg = payload.Skip(8).ToArray();
			}

			if (payloadMsg != null)
			{
				if (messageCompression == GZIP)
				{
					using (var input = new MemoryStream(payloadMsg))
					using (var gzipStream = new GZipStream(input, CompressionMode.Decompress))
					using (var output = new MemoryStream())
					{
						gzipStream.CopyTo(output);
						payloadMsg = output.ToArray();
					}
				}

				if (serializationMethod == JSON)
				{
					result["payload_msg"] = System.Text.Json.JsonSerializer.Deserialize<object>(Encoding.UTF8.GetString(payloadMsg));
				}
				else if (serializationMethod != NO_SERIALIZATION)
				{
					result["payload_msg"] = Encoding.UTF8.GetString(payloadMsg);
				}

				result["payload_size"] = payloadSize;
			}

			return result;
		}

		// 读取WAV文件信息
		public static (int nchannels, int sampwidth, int framerate, int nframes, byte[] waveBytes) ReadWavInfo(byte[] data)
		{
			using (var ms = new MemoryStream(data))
			using (var reader = new BinaryReader(ms))
			{
				string chunkId = new string(reader.ReadChars(4));
				int chunkSize = reader.ReadInt32();
				string format = new string(reader.ReadChars(4));

				string subchunk1Id = new string(reader.ReadChars(4));
				int subchunk1Size = reader.ReadInt32();
				short audioFormat = reader.ReadInt16();
				short numChannels = reader.ReadInt16();
				int sampleRate = reader.ReadInt32();
				int byteRate = reader.ReadInt32();
				short blockAlign = reader.ReadInt16();
				short bitsPerSample = reader.ReadInt16();

				string subchunk2Id = new string(reader.ReadChars(4));
				int subchunk2Size = reader.ReadInt32();

				byte[] waveBytes = reader.ReadBytes(subchunk2Size);

				return (numChannels, bitsPerSample / 8, sampleRate, subchunk2Size / (numChannels * (bitsPerSample / 8)), waveBytes);
			}
		}

		// 构造请求
		public static string ConstructRequest(string reqid)
		{
			var req = new
			{
				user = new
				{
					uid = "test"
				},
				audio = new
				{
					format = "wav",
					sample_rate = 16000,
					bits = 16,
					channel = 1,
					codec = "raw"
				},
				request = new
				{
					model_name = "bigmodel",
					enable_punc = true
				}
			};

			return System.Text.Json.JsonSerializer.Serialize(req);
		}

		// 切片数据
		public static IEnumerable<(byte[] chunk, bool isLast)> SliceData(byte[] data, int chunkSize)
		{
			int offset = 0;
			while (offset + chunkSize < data.Length)
			{
				yield return (data.Skip(offset).Take(chunkSize).ToArray(), false);
				offset += chunkSize;
			}
			yield return (data.Skip(offset).ToArray(), true);
		}
	}
}
