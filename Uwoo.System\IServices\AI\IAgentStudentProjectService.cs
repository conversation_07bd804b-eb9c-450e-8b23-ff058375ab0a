﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_学生端项目化实践
    /// </summary>
    public interface IAgentStudentProjectService : IService<AI_AgentTask>
    {
        /// <summary>
        /// 获取学生端项目化实践信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentProjectInfoOutput> GetStudentProjectInfo(GetStudentProjectInfoInput input);

        /// <summary>
        /// 学生端作品评估
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentAssessOutput> StudentAssess(StudentAssessInput input);

        /// <summary>
        /// 学生端情景对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task StudentDialogue(StudentDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 学生端情景对话提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentDialogueSubmitOutput> StudentDialogueSubmit(StudentDialogueSubmitInput input);

        /// <summary>
        /// 学生端知识问答
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task StudentKnowledge(StudentKnowledgeInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 学生端项目化实践阶段任务提交未达标对话记录备份
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task StudentSubmitNoStandardBackups(StudentSubmitNoStandardBackupsInput input);

        /// <summary>
        /// 获取学生做项目化实践阶段任务结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentDoTaskResultOutput> GetStudentDoTaskResult(GetStudentDoTaskResultInput input);

        /// <summary>
        /// 获取学生端未达标列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentNoStandardListOutput> GetStudentNoStandardList(GetStudentNoStandardListInput input);

        #region 阅读理解智能体新增接口





        #endregion
    }
}
