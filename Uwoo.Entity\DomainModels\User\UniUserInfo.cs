﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.User
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("UniUserInfos")]
    public class UniUserInfo : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Oid { get; set; }

        /// <summary>
        /// 登录用户名，3 <= 字符长度 <= 20
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户所属组 ID 号，3 <= 字符长度 <= 20
        /// </summary>
        public int GroupId { get; set; }

        /// <summary>
        /// 用户真实姓名，字符长度 <= 20
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 昵称，字符长度 <= 20
        /// </summary>
        public string NickName { get; set; }

        /// <summary>
        /// 生日，格式为: yyyy-mm-dd，如: 1977-01-01
        /// </summary>
        public string Birthday { get; set; }

        /// <summary>
        /// 性别，值为“男”或“女”
        /// </summary>
        public string Gender { get; set; }

        /// <summary>
        /// 电子邮件，字符长度<=50
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 用户状态标志。0: 禁用，1: 正常，2: 未通过验证。注意: 第三方应用如果想要继承UAC中的“启用”“禁用”状态，必须自行编码实现。
        /// </summary>
        public int ActiveFlag { get; set; }

        /// <summary>
        /// 帐号标志。-1: 公共帐号，0: 一般成员，1: 所属组组长，2: 超级管理员
        /// </summary>
        public int AdminFlag { get; set; }

        /// <summary>
        /// 用户身份代码: 有 5 位，从左往右，每一位代表一种身份，分别表示“一般注册用户”、“行政管理人员”、“教职员工”、“学生”、“家长”，对应位值为 1 时表示用户具有该身份
        /// </summary>
        public string UserType { get; set; }

        /// <summary>
        /// 学生用户在徐汇教务系统中的自增用户ID
        /// </summary>
        public int RiseId { get; set; }
    }
}
