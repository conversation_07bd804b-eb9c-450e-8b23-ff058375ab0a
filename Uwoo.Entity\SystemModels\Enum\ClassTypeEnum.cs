﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Entity.SystemModels.Enum
{
    /// <summary>
    /// 班级类型
    /// </summary>
    public enum ClassTypeEnum
    {
        /// <summary>
        /// 行政班
        /// </summary>
        [Description("行政班")]
        Administrative = 1,

        /// <summary>
        /// 课程班
        /// </summary>
        [Description("课程班")]
        Course = 2,

        /// <summary>
        /// 教学班
        /// </summary>
        [Description("教学班")]
        Teaching = 3,

        /// <summary>
        /// 课题班
        /// </summary>
        [Description("课题班")]
        Project = 4,

        /// <summary>
        /// 其他
        /// </summary>
        [Description("其他")]
        Other = 9,
    }
}
