﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.System.IRepositories.AI;

namespace UwooAgent.System.Repositories.AI
{
    /// <summary>
    /// 智能体_教师端建模
    /// </summary>
    public class AgentTeacherModelingRepository : RepositoryBase<AI_AgentTask>, IAgentTeacherModelingRepository
    {
        public AgentTeacherModelingRepository(VOLContext dbContext) : base(dbContext)
        {

        }

        public static IAgentTeacherModelingRepository Instance
        {
            get
            {
                return AutofacContainerModule.GetService<IAgentTeacherModelingRepository>();
            }
        }
    }
}
