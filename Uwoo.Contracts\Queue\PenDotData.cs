﻿//  -- Function：PenDotData.cs
//  --- Project：PenServer
//  ---- Remark：
//  ---- Author：Lucifer
//  ------ Date：2023/05/22 13:52

// ReSharper disable once CheckNamespace
namespace X.PenServer.Contracts.Queue
{
	using System.Text.Json.Serialization;

	/// <inheritdoc />
	public class PenDotData
	{
		/// <summary>
		/// 请求id
		/// </summary>
		[JsonPropertyName(nameof(RequestId))]
		[JsonInclude]
		public string RequestId { get; set; } = "";

		/// <summary>
		/// 实际采集的页码id
		/// </summary>
		[JsonPropertyName(nameof(Page))]
		[JsonInclude]
		public int Page { get; set; }

		/// <summary>
		/// 对应平台业务页码id
		/// </summary>
		[JsonPropertyName("Page")]
		[JsonInclude]
		public int PageId { get; set; }

		/// <summary>
		/// Mac
		/// </summary>
		[JsonPropertyName(nameof(Mac))]
		[JsonInclude]
		public string Mac { get; set; } = "";

		/// <summary>
		/// 时间
		/// </summary>
		[JsonPropertyName(nameof(Time))]
		[JsonInclude]
		public DateTime Time { get; set; }

		/// <summary>
		/// 点位数据
		/// </summary>
		[JsonPropertyName(nameof(Dots))]
		[JsonInclude]
		public List<PenDot> Dots { get; set; } = new();

		/// <summary>
		/// 用户id
		/// </summary>
		[JsonPropertyName(nameof(UserId))]
		[JsonInclude]
		public string UserId { get; set; }

		/// <summary>
		/// 数据类型: 1.在线 2.离线
		/// </summary>
		[JsonPropertyName(nameof(DataType))]
		[JsonInclude]
		public int DataType { get; set; }
	}
}