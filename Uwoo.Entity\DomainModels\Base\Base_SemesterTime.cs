﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Base
{
    /// <summary>
    /// 学年-学期时间
    /// </summary>
    [Entity(TableCnName = "学年-学期时间", TableName = "Base_SemesterTime")]
    public partial class Base_SemesterTime : BaseEntity
    {
        /// <summary>
        /// Id
        /// </summary>
        [Key, Column(Order = 1)]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 学届（学年）
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// 学届（学年）开始时间
        /// </summary>
        public DateTime? YearStartDate { get; set; }

        /// <summary>
        /// 学届（学年）结束时间
        /// </summary>
        public DateTime? YearEndDate { get; set; }

        /// <summary>
        /// 上学期开始时间
        /// </summary>
        public DateTime? FallStartDate { get; set; }

        /// <summary>
        /// 上学期结束时间
        /// </summary>
        public DateTime? FallEndDate { get; set; }

        /// <summary>
        /// 下学期开始时间
        /// </summary>
        public DateTime? SpringStartDate { get; set; }

        /// <summary>
        /// 下学期结束时间
        /// </summary>
        public DateTime? SpringEndDate { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public string CreatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 是否已删除
        /// </summary>
        public bool? Deleted { get; set; }
    }
}
