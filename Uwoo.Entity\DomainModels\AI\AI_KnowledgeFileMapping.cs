﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 知识库文件映射
    /// </summary>
    public class AI_KnowledgeFileMapping
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        public string KnowledgeId { get; set; } // 知识库ID

        public string FileName { get; set; } // 文件名称

        public string FileUrl { get; set; }// 文件存储地址

        public string FileType { get; set; } // 文件类型

        public DateTime CreateTime { get; set; } = DateTime.Now;// 创建时间
    }
}
