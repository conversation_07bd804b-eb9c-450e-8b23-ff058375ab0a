﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 智能体音色基础信息
    /// </summary>
    [Table("AI_ToneBaseInfo")]
    public class AI_ToneBaseInfo
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 音色配置名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 第三方平台标识（对接外部音色服务的Key）
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// 语种（如：中文、英文等）
        /// </summary>
        public string Language { get; set; }

        /// <summary>
        /// 是否支持MIX混音功能
        /// </summary>
        public bool? IsMIX { get; set; }

        /// <summary>
        /// 来源（1：火山引擎）
        /// </summary>
        public int? Source { get; set; }

        /// <summary>
        /// 音色效果预览地址
        /// </summary>
        public string PreviewUrl { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// 排序序号
        /// </summary>
        public int? OrderId { get; set; }
    }
}
