﻿// -- Function：DapperService.cs
// --- Project：X.PenServer.Models
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/24 17:38

namespace Uwoo.Mongo.Services.Dapper;

using System.Data;
using System.Threading;
using global::Dapper;
using Uwoo.Mongo.Interfaces.Dapper;

/// <summary>
/// Dapper
/// </summary>
public class DapperService : IDapperService
{
    private readonly IDapperFactoryService _factory;

    /// <summary>
    /// Init
    /// </summary>
    /// <param name="factory"></param>
    public DapperService(IDapperFactoryService factory)
    {
        _factory = factory;
    }

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public int Execute(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }

            using var db = _factory.CreateConnection();
            return db.Execute(sql, parameters, commandTimeout: timeout);
        }
        catch (Exception)
        {
            return -1;
        }
    }

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<int> ExecuteAsync(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            return await db.ExecuteAsync(sql, parameters, commandTimeout: timeout);
        }
        catch (Exception)
        {
            return -1;
        }
    }

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public DataTable ExecuteTable(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }

            var table = new DataTable("X");
            using var db = _factory.CreateConnection();
            var reader = db.ExecuteReader(sql, parameters, commandTimeout: timeout);
            table.Load(reader);
            return table;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<DataTable> ExecuteTableAsync(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }

            var table = new DataTable("X");
            using var db = _factory.CreateConnection();
            var reader = await db.ExecuteReaderAsync(sql, parameters, commandTimeout: timeout);
            table.Load(reader);
            return table;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// reader转daset
    /// </summary>
    /// <param name="reader"></param>
    /// <returns></returns>
    private static DataSet ConvertReaderToSet(IDataReader reader)
    {
        var set = new DataSet();
        var i = 0;
        while (!reader.IsClosed)
        {
            set.Tables.Add($"X_{i}");
            set.EnforceConstraints = false;
            set.Tables[i].Load(reader);
            i++;
        }

        return set;
    }

    /// <summary>
    /// 参数转换
    /// </summary>
    /// <param name="parameters"></param>
    /// <returns></returns>
    private static object ConvertParams(object parameters)
    {
        try
        {
            if (parameters == null)
            {
                return null;
            }

            var xparam = parameters;
            var zparam = new DynamicParameters();
            switch (parameters)
            {
                case IDataParameter xzzz:
                    zparam.Add(xzzz.ParameterName, xzzz.Value, xzzz.DbType, xzzz.Direction);
                    xparam = zparam;
                    break;
                case IList<IDataParameter> xiii:
                {
                    foreach (var item in xiii)
                    {
                        zparam.Add(item.ParameterName, item.Value, item.DbType, item.Direction);
                    }

                    xparam = zparam;
                    break;
                }
            }

            return xparam;
        }
        catch (Exception)
        {
            return null;
        }
    }

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public DataSet ExecuteDataSet(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var reader = db.ExecuteReader(sql, parameters, commandTimeout: timeout);
            var result = ConvertReaderToSet(reader);
            return result;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<DataSet> ExecuteDataSetAsync(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var reader = await db.ExecuteReaderAsync(sql, parameters, commandTimeout: timeout);
            var result = ConvertReaderToSet(reader);
            return result;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public dynamic ExecuteScalar(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            return db.ExecuteScalar(sql, parameters, commandTimeout: timeout);
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<dynamic> ExecuteScalarAsync(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            return await db.ExecuteScalarAsync(sql, parameters, commandTimeout: timeout);
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public T ExecuteScalar<T>(string sql, object parameters = null, int? timeout = null) where T : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            return db.ExecuteScalar<T>(sql, parameters, commandTimeout: timeout);
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行SQL
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<T> ExecuteScalarAsync<T>(string sql, object parameters = null, int? timeout = null)
        where T : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            return await db.ExecuteScalarAsync<T>(sql, parameters, commandTimeout: timeout);
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 批量插入
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sql"></param>
    /// <param name="newObjects"></param>
    /// <returns></returns>
    public bool BulkInsert<T>(string sql, List<T> newObjects)
    {
        using var db = _factory.CreateConnection();
        var iResult = db.Execute(sql, newObjects);
        if (iResult > 0)
            return true;
        return false;
    }

	/// <summary>
	/// 查询首条数据
	/// </summary>
	/// <param name="sql">sql</param>
	/// <param name="parameters">参数</param>
	/// <param name="timeout">超时时间,以秒为单位</param>
	/// <returns></returns>
	public dynamic QueryFirst(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            return db.QueryFirstOrDefault(sql, parameters, commandTimeout: timeout);
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询首条数据
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<dynamic> QueryFirstAsync(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            return await db.QueryFirstOrDefaultAsync(sql, parameters, commandTimeout: timeout);
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询首条数据
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public T QueryFirst<T>(string sql, object parameters = null, int? timeout = null) where T : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            return db.QueryFirstOrDefault<T>(sql, parameters, commandTimeout: timeout);
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询首条数据
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<T> QueryFirstAsync<T>(string sql, object parameters = null, int? timeout = null)
        where T : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            return await db.QueryFirstOrDefaultAsync<T>(sql, parameters, commandTimeout: timeout);
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询数据列表
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public IQueryable<dynamic> QueryList(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            return db.Query(sql, parameters, commandTimeout: timeout).AsQueryable();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询数据列表
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<IQueryable<dynamic>> QueryListAsync(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = await db.QueryAsync(sql, parameters, commandTimeout: timeout);
            return result.AsQueryable();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询数据列表
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public IQueryable<T> QueryList<T>(string sql, object parameters = null, int? timeout = null)
        where T : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = db.Query<T>(sql, parameters, commandTimeout: timeout);
            return result.AsQueryable();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询数据列表
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<IQueryable<T>> QueryListAsync<T>(string sql, object parameters = null, int? timeout = null)
        where T : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = await db.QueryAsync<T>(sql, parameters, commandTimeout: timeout);
            return result.AsQueryable();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public IDataReader ExecuteReader(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = db.ExecuteReader(sql, parameters, commandTimeout: timeout);
            return result;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<IDataReader> ExecuteReaderAsync(string sql, object parameters = null, int? timeout = null)
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = await db.ExecuteReaderAsync(sql, parameters, commandTimeout: timeout);
            return result;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public T ExecuteReader<T>(string sql, object parameters = null, int? timeout = null) where T : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = db.ExecuteReader(sql, parameters, commandTimeout: timeout);
            return result.Parse<T>().FirstOrDefault();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<T> ExecuteReaderAsync<T>(string sql, object parameters = null, int? timeout = null)
        where T : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = await db.ExecuteReaderAsync(sql, parameters, commandTimeout: timeout);
            return result.Parse<T>().FirstOrDefault();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public List<T> ExecuteReaderList<T>(string sql, object parameters = null, int? timeout = null)
        where T : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = db.ExecuteReader(sql, parameters, commandTimeout: timeout);
            return result.Parse<T>().ToList();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间,以秒为单位</param>
    /// <returns></returns>
    public async Task<List<T>> ExecuteReaderListAsync<T>(string sql, object parameters = null, int? timeout = null)
        where T : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = await db.ExecuteReaderAsync(sql, parameters, commandTimeout: timeout);
            return result.Parse<T>().ToList();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    /// <returns></returns>
    public bool Transaction(string sql, object parameters = null, int? timeout = null)
    {
        using var db = _factory.CreateConnection();
        if (db.State != ConnectionState.Open)
        {
            db.Open();
        }
        using var trans = db.BeginTransaction();
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            db.Execute(sql, parameters, trans, timeout);
            trans.Commit();
            return true;
        }
        catch (Exception)
        {
            trans.Rollback();
            return false;
        }
    }

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    /// <returns></returns>
    public async Task<bool> TransactionAsync(string sql, object parameters = null, int? timeout = null)
    {
        using var db = _factory.CreateConnection();
        if (db.State != ConnectionState.Open)
        {
            db.Open();
        }
        using var trans = db.BeginTransaction();
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            await db.ExecuteAsync(sql, parameters, trans, timeout);
            trans.Commit();
            return true;
        }
        catch (Exception)
        {
            trans.Rollback();
            return false;
        }
    }

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sqls">sql列表</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    public bool Transaction(Dictionary<string, object> sqls, int? timeout = null)
    {
        using var db = _factory.CreateConnection();
        if (db.State != ConnectionState.Open)
        {
            db.Open();
        }
        using var trans = db.BeginTransaction();
        try
        {
            foreach (var item in sqls)
            {
                db.Execute(item.Key, item.Value, trans, timeout);
            }
            trans.Commit();
            return true;
        }
        catch (Exception)
        {
            trans.Rollback();
            return false;
        }
    }

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sqls">sql列表</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    public async Task<bool> TransactionAsync(Dictionary<string, object> sqls, int? timeout = null)
    {
        using var db = _factory.CreateConnection();
        if (db.State != ConnectionState.Open)
        {
            db.Open();
        }
        using var trans = db.BeginTransaction();
        try
        {
            foreach (var item in sqls)
            {
                await db.ExecuteAsync(item.Key, item.Value, trans, timeout);
            }

            trans.Commit();
            return true;
        }
        catch (Exception)
        {
            trans.Rollback();
            return false;
        }
    }

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sqls">sql列表</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    /// <returns></returns>
    public bool Transaction(List<string> sqls, int? timeout = null)
    {
        using var db = _factory.CreateConnection();
        if (db.State != ConnectionState.Open)
        {
            db.Open();
        }
        using var trans = db.BeginTransaction();
        try
        {
            foreach (var item in sqls)
            {
                db.Execute(item, trans, commandTimeout: timeout);
            }

            trans.Commit();
            return true;
        }
        catch (Exception)
        {
            trans.Rollback();
            return false;
        }
    }

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="sqls">sql列表</param>
    /// <param name="timeout">超时时间, 以秒为单位</param>
    public async Task<bool> TransactionAsync(List<string> sqls, int? timeout = null)
    {
        using var db = _factory.CreateConnection();
        if (db.State != ConnectionState.Open)
        {
            db.Open();
        }
        using var trans = db.BeginTransaction();
        try
        {
            foreach (var item in sqls)
            {
                await db.ExecuteAsync(item, trans, commandTimeout: timeout);
            }

            trans.Commit();
            return true;
        }
        catch (Exception)
        {
            trans.Rollback();
            return false;
        }
    }

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <param name="sql"></param>
    /// <param name="parameters"></param>
    /// <param name="ta"></param>
    /// <param name="tb"></param>
    public void ExecuteMultip<TA, TB>(out List<TA> ta, out List<TB> tb, string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new()
    {
        try
        {
            using var db = _factory.CreateConnection();
            var result = db.QueryMultiple(sql, parameters);
            ta = result.Read<TA>().ToList();
            tb = result.Read<TB>().ToList();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message);
        }
    }

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <param name="sql"></param>
    /// <param name="parameters"></param>
    /// <param name="ta"></param>
    /// <param name="tb"></param>
    /// <param name="tc"></param>
    public void ExecuteMultip<TA, TB, TC>(out List<TA> ta, out List<TB> tb, out List<TC> tc, string sql,
        object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new()
    {
        try
        {
            using var db = _factory.CreateConnection();
            var result = db.QueryMultiple(sql, parameters);
            ta = result.Read<TA>().ToList();
            tb = result.Read<TB>().ToList();
            tc = result.Read<TC>().ToList();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message);
        }
    }

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <typeparam name="TD"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    /// <param name="ta"></param>
    /// <param name="tb"></param>
    /// <param name="tc"></param>
    /// <param name="td"></param>
    public void ExecuteMultip<TA, TB, TC, TD>(out List<TA> ta, out List<TB> tb, out List<TC> tc, out List<TD> td,
        string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new()
        where TD : class, new()
    {
        try
        {
            using var db = _factory.CreateConnection();
            var result = db.QueryMultiple(sql, parameters);
            ta = result.Read<TA>().ToList();
            tb = result.Read<TB>().ToList();
            tc = result.Read<TC>().ToList();
            td = result.Read<TD>().ToList();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message);
        }
    }

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <param name="sql"></param>
    /// <param name="parameters"></param>
    public Tuple<List<TA>, List<TB>> ExecuteMultip<TA, TB>(string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = db.QueryMultiple(sql, parameters);
            var ta = result.Read<TA>().ToList();
            var tb = result.Read<TB>().ToList();
            var tuple = Tuple.Create(ta, tb);
            return tuple;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <param name="sql"></param>
    /// <param name="parameters"></param>
    public async Task<Tuple<List<TA>, List<TB>>> ExecuteMultipAsync<TA, TB>(string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = await db.QueryMultipleAsync(sql, parameters);
            var ta = await result.ReadAsync<TA>();
            var tb = await result.ReadAsync<TB>();
            var tuple = Tuple.Create(ta.ToList(), tb.ToList());
            return tuple;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <param name="sql"></param>
    /// <param name="parameters"></param>
    public Tuple<List<TA>, List<TB>, List<TC>> ExecuteMultip<TA, TB, TC>(string sql, object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = db.QueryMultiple(sql, parameters);
            var ta = result.Read<TA>().ToList();
            var tb = result.Read<TB>().ToList();
            var tc = result.Read<TC>().ToList();
            var tuple = Tuple.Create(ta, tb, tc);
            return tuple;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <param name="sql"></param>
    /// <param name="parameters"></param>
    public async Task<Tuple<List<TA>, List<TB>, List<TC>>> ExecuteMultipAsync<TA, TB, TC>(string sql,
        object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new()
    {
        try
        {

            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = await db.QueryMultipleAsync(sql, parameters);
            var ta = await result.ReadAsync<TA>();
            var tb = await result.ReadAsync<TB>();
            var tc = await result.ReadAsync<TC>();
            var tuple = Tuple.Create(ta.ToList(), tb.ToList(), tc.ToList());
            return tuple;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <typeparam name="TD"></typeparam>
    /// <param name="sql">sql</param>
    /// <param name="parameters">参数</param>
    public Tuple<List<TA>, List<TB>, List<TC>, List<TD>> ExecuteMultip<TA, TB, TC, TD>(string sql,
        object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new()
        where TD : class, new()
    {
        try
        {

            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = db.QueryMultiple(sql, parameters);
            var ta = result.Read<TA>();
            var tb = result.Read<TB>();
            var tc = result.Read<TC>();
            var td = result.Read<TD>();
            var tuple = Tuple.Create(ta.ToList(), tb.ToList(), tc.ToList(), td.ToList());
            return tuple;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }

    /// <summary>
    /// 查询多结果集
    /// </summary>
    /// <typeparam name="TA"></typeparam>
    /// <typeparam name="TB"></typeparam>
    /// <typeparam name="TC"></typeparam>
    /// <typeparam name="TD"></typeparam>
    /// <param name="sql"></param>
    /// <param name="parameters"></param>
    public async Task<Tuple<List<TA>, List<TB>, List<TC>, List<TD>>> ExecuteMultipAsync<TA, TB, TC, TD>(string sql,
        object parameters = null)
        where TA : class, new()
        where TB : class, new()
        where TC : class, new()
        where TD : class, new()
    {
        try
        {
            if (parameters != null)
            {
                parameters = ConvertParams(parameters);
            }
            using var db = _factory.CreateConnection();
            var result = await db.QueryMultipleAsync(sql, parameters);
            var ta = await result.ReadAsync<TA>();
            var tb = await result.ReadAsync<TB>();
            var tc = await result.ReadAsync<TC>();
            var td = await result.ReadAsync<TD>();
            var tuple = Tuple.Create(ta.ToList(), tb.ToList(), tc.ToList(), td.ToList());
            return tuple;
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e);
        }
    }
}