using SqlSugar;
using System;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 阅读理解项目班级关联表
    /// </summary>
    [SugarTable("RC_ReadingProjectClassRelation")]
    public class RC_ReadingProjectClassRelation : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 阅读理解项目Id
        /// </summary>
        public string ReadingProjectId { get; set; }

        /// <summary>
        /// 班级Id
        /// </summary>
        public string ClassId { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 发布开始时间
        /// </summary>
        public DateTime? PublishStartTime { get; set; }

        /// <summary>
        /// 发布结束时间
        /// </summary>
        public DateTime? PublishEndTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }
    }
}
