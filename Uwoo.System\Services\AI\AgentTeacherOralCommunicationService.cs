﻿using Coldairarrow.Util;
using NetTaste;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Model.CustomException;
using Uwoo.System.IServices;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.School;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.Model.SemesterTime;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_教师端口语交际
    /// </summary>
    public class AgentTeacherOralCommunicationService : ServiceBase<AI_OralCommunicationTask, IAgentTeacherOralCommunicationRepository>, IAgentTeacherOralCommunicationService, IDependency
    {
        #region DI

        private IBase_SemesterTimeService _semesterTimeService;
        private IAgentCommonService _agentCommonService;
        public AgentTeacherOralCommunicationService(IBase_SemesterTimeService semesterTimeService,
            IAgentCommonService agentCommonService)
        {
            _semesterTimeService = semesterTimeService;
            _agentCommonService = agentCommonService;
        }

        #endregion

        /// <summary>
        /// 教师保存/编辑口语交际
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<string> SaveTeacherOralCommunication(SaveTeacherOralCommunicationInput input)
        {
            try
            {
                //指令式任务图片和html文件处理
                List<AIFileInfoDto> aIFileInfoDtos = new List<AIFileInfoDto>();
                foreach (var instructTask in input.InstructTasks)
                {
                    //AI生成的图片转换为华为云OBS
                    if (instructTask.ImgUrls.Count > 0)
                    {
                        instructTask.ImgUrls = await _agentCommonService.AIUrlReplaceHWYOBS(instructTask.ImgUrls);
                    }

                    //html文件消息
                    if (instructTask.HtmlFileInfo != null && !string.IsNullOrEmpty(instructTask.HtmlFileInfo.Url) && !string.IsNullOrEmpty(instructTask.HtmlFileInfo.Name))
                    {
                        aIFileInfoDtos.Add(instructTask.HtmlFileInfo);
                    }
                }

                //html文件信息保存
                if (aIFileInfoDtos.Count > 0)
                {
                    //文件地址
                    List<string> htmlUrls = aIFileInfoDtos.Select(p => p.Url).ToList();

                    //获取文件信息
                    List<AI_FileInfo> fileInfos = await DBSqlSugar.Queryable<AI_FileInfo>().Where(p => htmlUrls.Contains(p.FileUrl) && p.Type == 1 && p.IsDeleted == false).ToListAsync();

                    //新增文件消息
                    List<AI_FileInfo> addFileInfos = new List<AI_FileInfo>();

                    foreach (var aIFileInfoDto in aIFileInfoDtos)
                    {
                        if (fileInfos.Where(p => p.FileUrl == aIFileInfoDto.Url).FirstOrDefault() == null)
                        {
                            addFileInfos.Add(new AI_FileInfo()
                            {
                                Id = IdHelper.GetId(),
                                Type = 1,
                                FileName = aIFileInfoDto.Name,
                                FileUrl = aIFileInfoDto.Url,
                                CreateTime = aIFileInfoDto.CreateTime.HasValue ? aIFileInfoDto.CreateTime : DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = aIFileInfoDto.CreateTime.HasValue ? aIFileInfoDto.CreateTime : DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            });
                        }
                    }

                    if (addFileInfos.Count > 0)
                    {
                        await DBSqlSugar.Insertable(addFileInfos).ExecuteCommandAsync();
                    }
                }

                if (string.IsNullOrEmpty(input.Id))
                {
                    //获取学校信息
                    Exam_School schoolInfo = await DBSqlSugar.Queryable<Exam_School>().Where(p => p.Id == input.SchoolId).FirstAsync();
                    if (schoolInfo == null)
                    {
                        throw new BusException("学校Id异常!");
                    }

                    //获取当前学年学期
                    NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                    if (nowSemesterTime == null)
                    {
                        throw new BusException("无法获取当前学年!", 801);
                    }

                    //智能体任务
                    AI_AgentTask agentTask = new AI_AgentTask()
                    {
                        Id = IdHelper.GetId(),
                        AgentId = input.AgentId,
                        Name = input.Name,
                        Introduce = input.Introduce,
                        Term = nowSemesterTime.NowTerm,
                        Year = nowSemesterTime.Year,
                        CreateTime = DateTime.Now,
                        Creator = input.TeacherId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId,
                        IsDeleted = false,
                        SubjectId = input.SubjectId,
                        AgentTaskType = 1,
                        GradeId = input.GradeId
                    };
                    await DBSqlSugar.Insertable(agentTask).ExecuteCommandAsync();

                    //口语交际任务
                    AI_OralCommunicationTask oralCommunicationTask = new AI_OralCommunicationTask()
                    {
                        Id = IdHelper.GetId(),
                        AgentTaskId = agentTask.Id,
                        ChapterId = input.ChapterId,
                        EvaluateId = input.EvaluateId,
                        InteractiveMode = input.InteractiveMode,
                        Prologue = input.Prologue,
                        Scene = input.Scene,
                        SceneDetail = input.SceneDetail,
                        Target = input.Target,
                        ToneId = input.ToneId,
                        RoleName = input.RoleName,
                        CreateTime = DateTime.Now,
                        Creator = input.TeacherId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId,
                        IsDeleted = false,
                        IsCase = input.TeacherId == "1952916450991222784" ? true : false
                    };
                    await DBSqlSugar.Insertable(oralCommunicationTask).ExecuteCommandAsync();

                    if (oralCommunicationTask.InteractiveMode == 1)
                    {
                        //指令式任务
                        if (input.InstructTasks.Count > 0)
                        {
                            List<AI_OralCommunicationInstructTask> oralCommunicationInstructTasks = new List<AI_OralCommunicationInstructTask>();
                            foreach (var instructTask in input.InstructTasks)
                            {
                                oralCommunicationInstructTasks.Add(new AI_OralCommunicationInstructTask()
                                {
                                    Id = IdHelper.GetId(),
                                    OralCommunicationId = oralCommunicationTask.Id,
                                    Name = instructTask.Name,
                                    InstructContent = instructTask.InstructContent,
                                    VerificationMode = instructTask.VerificationMode,
                                    Result = instructTask.Result,
                                    HtmlFileUrl = instructTask.HtmlFileInfo?.Url,
                                    ImgUrl = instructTask.ImgUrls.ToJsonString(),
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false,
                                    OrderId = input.InstructTasks.IndexOf(instructTask) + 1
                                });
                            }
                            await DBSqlSugar.Insertable(oralCommunicationInstructTasks).ExecuteCommandAsync();
                        }
                    }
                    if (oralCommunicationTask.InteractiveMode == 2)
                    {
                        //对话式任务
                        if (input.DialogueTasks.Count > 0)
                        {
                            List<AI_OralCommunicationDialogueTask> oralCommunicationDialogueTasks = new List<AI_OralCommunicationDialogueTask>();
                            foreach (var dialogueTask in input.DialogueTasks)
                            {
                                oralCommunicationDialogueTasks.Add(new AI_OralCommunicationDialogueTask()
                                {
                                    Id = IdHelper.GetId(),
                                    OralCommunicationId = oralCommunicationTask.Id,
                                    Asked = dialogueTask.Asked,
                                    DialogueTarget = dialogueTask.DialogueTarget,
                                    Name = dialogueTask.Name,
                                    ValidRespond = dialogueTask.ValidRespond,
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false,
                                    OrderId = input.DialogueTasks.IndexOf(dialogueTask) + 1
                                });
                            }
                            await DBSqlSugar.Insertable(oralCommunicationDialogueTasks).ExecuteCommandAsync();
                        }
                    }

                    return agentTask.Id;
                }
                else
                {
                    //获取智能体任务
                    AI_AgentTask agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.Id && p.IsDeleted == false).FirstAsync();
                    if (agentTask == null)
                    {
                        throw new BusException("智能体任务Id异常!");
                    }

                    //获取口语交际信息
                    AI_OralCommunicationTask oralCommunicationTask = await DBSqlSugar.Queryable<AI_OralCommunicationTask>().Where(p => p.AgentTaskId == agentTask.Id && p.IsDeleted == false).FirstAsync();
                    if (oralCommunicationTask == null)
                    {
                        throw new BusException("口语交际任务Id异常!");
                    }

                    //获取口语交际发布班级验证时间
                    List<AI_AgentTaskPublish> agentTaskPublisheTimes = await DBSqlSugar.Queryable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == agentTask.Id && p.IsDeleted == false).ToListAsync();
                    if (agentTaskPublisheTimes.Count > 0)
                    {
                        if (agentTaskPublisheTimes[0].EndTime <= DateTime.Now)
                        {
                            return agentTask.Id;
                        }
                    }

                    //验证是否存在提交数据
                    bool isExists = await DBSqlSugar.Queryable<AI_StudentDoOralCommunicationTask>().AnyAsync(p => p.AgentTaskId == agentTask.Id);

                    //更新（1.没有任何提交数据时所有内容都可以编辑。2.有提交数据时只支持修改基础数据）
                    if (!isExists)
                    {
                        //更新口语交际任务基础信息
                        oralCommunicationTask.ChapterId = input.ChapterId;
                        oralCommunicationTask.Target = input.Target;
                        oralCommunicationTask.EvaluateId = input.EvaluateId;
                        oralCommunicationTask.Prologue = input.Prologue;
                        oralCommunicationTask.Scene = input.Scene;
                        oralCommunicationTask.SceneDetail = input.SceneDetail;
                        oralCommunicationTask.InteractiveMode = input.InteractiveMode;
                        oralCommunicationTask.ToneId = input.ToneId;
                        oralCommunicationTask.RoleName = input.RoleName;
                        oralCommunicationTask.ModifyTime = DateTime.Now;
                        oralCommunicationTask.Modifier = input.TeacherId;
                        await DBSqlSugar.Updateable(oralCommunicationTask).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                        //指令式任务
                        await DBSqlSugar.Deleteable<AI_OralCommunicationInstructTask>().Where(it => it.OralCommunicationId == oralCommunicationTask.Id).ExecuteCommandAsync();
                        if (oralCommunicationTask.InteractiveMode == 1)
                        {
                            if (input.InstructTasks.Count > 0)
                            {
                                List<AI_OralCommunicationInstructTask> oralCommunicationInstructTasks = new List<AI_OralCommunicationInstructTask>();
                                foreach (var instructTask in input.InstructTasks)
                                {
                                    oralCommunicationInstructTasks.Add(new AI_OralCommunicationInstructTask()
                                    {
                                        Id = IdHelper.GetId(),
                                        OralCommunicationId = oralCommunicationTask.Id,
                                        Name = instructTask.Name,
                                        InstructContent = instructTask.InstructContent,
                                        VerificationMode = instructTask.VerificationMode,
                                        Result = instructTask.Result,
                                        HtmlFileUrl = instructTask.HtmlFileInfo?.Url,
                                        ImgUrl = instructTask.ImgUrls.ToJsonString(),
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false,
                                        OrderId = input.InstructTasks.IndexOf(instructTask) + 1
                                    });
                                }
                                await DBSqlSugar.Insertable(oralCommunicationInstructTasks).ExecuteCommandAsync();
                            }
                        }

                        //对话式任务
                        await DBSqlSugar.Deleteable<AI_OralCommunicationDialogueTask>().Where(it => it.OralCommunicationId == oralCommunicationTask.Id).ExecuteCommandAsync();
                        if (oralCommunicationTask.InteractiveMode == 2)
                        {
                            if (input.DialogueTasks.Count > 0)
                            {
                                List<AI_OralCommunicationDialogueTask> oralCommunicationDialogueTasks = new List<AI_OralCommunicationDialogueTask>();
                                foreach (var dialogueTask in input.DialogueTasks)
                                {
                                    oralCommunicationDialogueTasks.Add(new AI_OralCommunicationDialogueTask()
                                    {
                                        Id = IdHelper.GetId(),
                                        OralCommunicationId = oralCommunicationTask.Id,
                                        Asked = dialogueTask.Asked,
                                        DialogueTarget = dialogueTask.DialogueTarget,
                                        Name = dialogueTask.Name,
                                        ValidRespond = dialogueTask.ValidRespond,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false,
                                        OrderId = input.DialogueTasks.IndexOf(dialogueTask) + 1
                                    });
                                }
                                await DBSqlSugar.Insertable(oralCommunicationDialogueTasks).ExecuteCommandAsync();
                            }
                        }

                        await DBSqlSugar.Deleteable<AI_ContextCacheKey>().Where(p => p.BusinessId == agentTask.Id).ExecuteCommandAsync();
                        await DBSqlSugar.Deleteable<AI_DialogueContentRecord>().Where(p => p.BusinessId == agentTask.Id).ExecuteCommandAsync();
                    }
                    else
                    {
                        ////是否需要清空问答数据
                        //if (oralCommunicationTask.ChapterId != input.ChapterId ||
                        //    oralCommunicationTask.Target != input.Target ||
                        //    oralCommunicationTask.Scene != input.Scene ||
                        //    oralCommunicationTask.SceneDetail != input.SceneDetail)
                        //{
                        //    //获取已经提交的学生Id
                        //    List<string> studentIds = await DBSqlSugar.Queryable<AI_StudentDoOralCommunicationTask>()
                        //        .Where(p => p.AgentTaskId == agentTask.Id && p.IsDeleted == false)
                        //        .Select(p => p.StudentId).ToListAsync();

                        //    //学生口语交际key
                        //    List<string> studentKeys = new List<string>();
                        //    foreach (var studentId in studentIds)
                        //    {
                        //        //获取学生端口语交际Key
                        //        string oralCommunicationKey = AIAgentKeys.GetStudentOralCommunicationKey(agentTask.AgentId, agentTask.Id, studentId);
                        //        if (!string.IsNullOrEmpty(oralCommunicationKey))
                        //        {
                        //            studentKeys.Add(oralCommunicationKey);
                        //        }
                        //    }

                        //    if (studentKeys.Count > 0)
                        //    {
                        //        await DBSqlSugar.Deleteable<AI_ContextCacheKey>().Where(p => !studentKeys.Contains(p.CacheKey) && p.BusinessId == agentTask.Id).ExecuteCommandAsync();
                        //        await DBSqlSugar.Deleteable<AI_DialogueContentRecord>().Where(p => !studentKeys.Contains(p.Key) && p.BusinessId == agentTask.Id).ExecuteCommandAsync();
                        //    }
                        //    else
                        //    {
                        //        await DBSqlSugar.Deleteable<AI_ContextCacheKey>().Where(p => p.BusinessId == agentTask.Id).ExecuteCommandAsync();
                        //        await DBSqlSugar.Deleteable<AI_DialogueContentRecord>().Where(p => p.BusinessId == agentTask.Id).ExecuteCommandAsync();
                        //    }
                        //}

                        //更新口语交际任务基础信息
                        oralCommunicationTask.ChapterId = input.ChapterId;
                        oralCommunicationTask.Target = input.Target;
                        oralCommunicationTask.EvaluateId = input.EvaluateId;
                        oralCommunicationTask.Prologue = input.Prologue;
                        oralCommunicationTask.Scene = input.Scene;
                        oralCommunicationTask.SceneDetail = input.SceneDetail;
                        oralCommunicationTask.ToneId = input.ToneId;
                        oralCommunicationTask.RoleName = input.RoleName;
                        oralCommunicationTask.ModifyTime = DateTime.Now;
                        oralCommunicationTask.Modifier = input.TeacherId;
                        await DBSqlSugar.Updateable(oralCommunicationTask).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }

                    //更新智能体任务基础信息
                    agentTask.Name = input.Name;
                    agentTask.Introduce = input.Introduce;
                    agentTask.ModifyTime = DateTime.Now;
                    agentTask.Modifier = input.TeacherId;
                    await DBSqlSugar.Updateable(agentTask).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                    return agentTask.Id;
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取智能体口语交际详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AgentOralCommunicationDetailOutput> GetOralCommunicationDetail(AgentOralCommunicationDetailInput input)
        {
            try
            {
                //获取智能体任务
                AI_AgentTask agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.Id && p.IsDeleted == false).FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("智能体任务Id异常!", 801);
                }

                //获取智能体logo
                string agentLogo = await DBSqlSugar.Queryable<AI_AgentBaseInfo>().Where(p => p.Id == agentTask.AgentId && p.IsDeleted == false).Select(p => p.Logo).FirstAsync();

                //获取口语交际信息
                SaveTeacherOralCommunicationInput oralCommunicationTask = await DBSqlSugar.Queryable<AI_OralCommunicationTask>()
                    .Where(p => p.AgentTaskId == agentTask.Id && p.IsDeleted == false)
                    .Select(p => new SaveTeacherOralCommunicationInput()
                    {
                        Id = p.Id,
                        ChapterId = p.ChapterId,
                        EvaluateId = p.EvaluateId,
                        Scene = p.Scene,
                        SceneDetail = p.SceneDetail,
                        InteractiveMode = p.InteractiveMode,
                        Target = p.Target,
                        Prologue = p.Prologue,
                        RoleName = p.RoleName,
                        ToneId = p.ToneId
                    }).FirstAsync();
                if (oralCommunicationTask == null)
                {
                    throw new BusException("口语交际任务Id异常!", 801);
                }

                if (oralCommunicationTask.InteractiveMode == 1)
                {
                    //指令式任务
                    List<AI_OralCommunicationInstructTask> aI_OralCommunicationInstructTasks = await DBSqlSugar.Queryable<AI_OralCommunicationInstructTask>()
                        .Where(p => p.OralCommunicationId == oralCommunicationTask.Id && p.IsDeleted == false)
                        .OrderBy(p => p.OrderId)
                        .ToListAsync();

                    //html文件信息
                    List<AIFileInfoDto> aIFileInfoDtos = new List<AIFileInfoDto>();
                    if (aI_OralCommunicationInstructTasks.Count > 0)
                    {
                        List<string> htmlUrls = aI_OralCommunicationInstructTasks.Select(p => p.HtmlFileUrl).ToList();
                        if (htmlUrls.Count > 0)
                        {
                            //获取文件信息
                            aIFileInfoDtos = await DBSqlSugar.Queryable<AI_FileInfo>()
                                .Where(p => htmlUrls.Contains(p.FileUrl) && p.Type == 1 && p.IsDeleted == false)
                                .Select(p => new AIFileInfoDto()
                                {
                                    Name = p.FileName,
                                    Url = p.FileUrl,
                                    CreateTime = p.CreateTime
                                }).ToListAsync();
                        }
                    }

                    foreach (var item in aI_OralCommunicationInstructTasks)
                    {
                        oralCommunicationTask.InstructTasks.Add(new OralCommunicationInstructTaskInput()
                        {
                            Id = item.Id,
                            HtmlFileInfo = aIFileInfoDtos.Where(p => p.Url == item.HtmlFileUrl).FirstOrDefault(),
                            ImgUrls = JsonConvert.DeserializeObject<List<ImgInput>>(item.ImgUrl),
                            InstructContent = item.InstructContent,
                            Name = item.Name,
                            Result = item.Result,
                            VerificationMode = item.VerificationMode
                        });
                    }
                }
                else if (oralCommunicationTask.InteractiveMode == 2)
                {
                    //对话式任务
                    oralCommunicationTask.DialogueTasks = await DBSqlSugar.Queryable<AI_OralCommunicationDialogueTask>()
                        .Where(p => p.OralCommunicationId == oralCommunicationTask.Id && p.IsDeleted == false)
                        .OrderBy(p => p.OrderId)
                        .Select(p => new OralCommunicationDialogueTaskInput()
                        {
                            Id = p.Id,
                            Asked = p.Asked,
                            DialogueTarget = p.DialogueTarget,
                            Name = p.Name,
                            ValidRespond = p.ValidRespond,
                        }).ToListAsync();
                }

                //获取口语交际发布班级
                List<AI_AgentTaskPublish> agentTaskPublishes = await DBSqlSugar.Queryable<AI_AgentTaskPublish>()
                    .Where(p => p.AgentTaskId == agentTask.Id && p.IsDeleted == false && p.PublishType == 1).ToListAsync();
                if (agentTaskPublishes.Count > 0)
                {
                    oralCommunicationTask.TimeRange = new List<DateTime>() { agentTaskPublishes[0].BeginTime.Value, agentTaskPublishes[0].EndTime.Value };
                    oralCommunicationTask.ClassId = agentTaskPublishes.Select(p => p.PublishBusinessId).ToList();
                }

                //验证是否存在问答数据
                bool isExists = await DBSqlSugar.Queryable<AI_DialogueContentRecord>().AnyAsync(p => p.BusinessId == agentTask.Id);

                oralCommunicationTask.Id = agentTask.Id;
                oralCommunicationTask.Name = agentTask.Name;
                oralCommunicationTask.AgentId = agentTask.AgentId;
                oralCommunicationTask.Introduce = agentTask.Introduce;
                oralCommunicationTask.GradeId = agentTask.GradeId;

                return new AgentOralCommunicationDetailOutput()
                {
                    IsData = isExists,
                    Logo = agentLogo,
                    OralCommunicationInfo = oralCommunicationTask,
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 删除口语交际任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task DelOralCommunication(DelOralCommunicationInput input)
        {
            try
            {
                //删除口语交际任务
                await DBSqlSugar.Updateable<AI_AgentTask>()
                    .SetColumns(it => new AI_AgentTask() { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.Id == input.AgentTaskId && p.Creator == input.TeacherId)
                    .ExecuteCommandAsync();

                //获取口语交际任务
                AI_OralCommunicationTask oralCommunicationTask = await DBSqlSugar.Queryable<AI_OralCommunicationTask>().Where(p => p.AgentTaskId == input.AgentTaskId && p.Creator == input.TeacherId).FirstAsync();
                if (oralCommunicationTask != null)
                {
                    //删除口语交际任务
                    await DBSqlSugar.Updateable<AI_OralCommunicationTask>()
                        .SetColumns(it => new AI_OralCommunicationTask() { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                        .Where(p => p.AgentTaskId == input.AgentTaskId && p.Creator == input.TeacherId)
                        .ExecuteCommandAsync();

                    //删除口语交际任务（指令式）
                    await DBSqlSugar.Updateable<AI_OralCommunicationInstructTask>()
                        .SetColumns(it => new AI_OralCommunicationInstructTask() { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                        .Where(p => p.OralCommunicationId == oralCommunicationTask.Id && p.Creator == input.TeacherId)
                        .ExecuteCommandAsync();

                    //删除口语交际任务（对话式）
                    await DBSqlSugar.Updateable<AI_OralCommunicationDialogueTask>()
                        .SetColumns(it => new AI_OralCommunicationDialogueTask() { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                        .Where(p => p.OralCommunicationId == oralCommunicationTask.Id && p.Creator == input.TeacherId)
                        .ExecuteCommandAsync();
                }

                //删除口语交际任务发布的班级
                await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                    .SetColumns(it => new AI_AgentTaskPublish() { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.AgentTaskId == input.AgentTaskId && p.Creator == input.TeacherId)
                    .ExecuteCommandAsync();

                //删除学生做任务记录
                await DBSqlSugar.Updateable<AI_StudentDoOralCommunicationTask>()
                    .SetColumns(it => new AI_StudentDoOralCommunicationTask() { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.AgentTaskId == input.AgentTaskId)
                    .ExecuteCommandAsync();

                //删除学生对话记录
                await DBSqlSugar.Updateable<AI_DialogueContentRecord>()
                    .SetColumns(it => new AI_DialogueContentRecord() { IsDeleted = true })
                    .Where(p => p.BusinessId == input.AgentTaskId)
                    .ExecuteCommandAsync();

                //删除学生对话缓存key
                await DBSqlSugar.Updateable<AI_ContextCacheKey>()
                    .SetColumns(it => new AI_ContextCacheKey() { IsDeleted = true })
                    .Where(p => p.BusinessId == input.AgentTaskId)
                    .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 智能体_教师端口语交际列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageReturn<AgentTeacherOralCommunicationListOutput>> GetOralCommunicationList(AgentTeacherOralCommunicationListInput input)
        {
            try
            {
                //获取学校信息
                Exam_School schoolInfo = await DBSqlSugar.Queryable<Exam_School>().Where(p => p.Id == input.SchoolId).FirstAsync();
                if (schoolInfo == null)
                {
                    throw new BusException("学校Id异常!");
                }

                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!", 801);
                }

                //特殊处理
                if (input.TeacherId == "1952916450991222784")
                {
                    input.IsCase = true;
                }

                //参数
                List<SugarParameter> parameters = new List<SugarParameter>();
                parameters.Add(new SugarParameter("@year", nowSemesterTime.Year));
                parameters.Add(new SugarParameter("@term", nowSemesterTime.NowTerm));
                parameters.Add(new SugarParameter("@agentId", input.AgentId));
                parameters.Add(new SugarParameter("@subjectId", input.SubjectId));
                parameters.Add(new SugarParameter("@gradeId", input.GradeId));
                parameters.Add(new SugarParameter("@isCase", input.IsCase));
                string where = string.Empty;
                if (!string.IsNullOrEmpty(input.Name))
                {
                    where += $" AND task.Name LIKE '%{input.Name}%' ";
                }
                if (input.InteractiveMode != 0)
                {
                    where += $" AND oct.InteractiveMode=@interactiveMode ";
                    parameters.Add(new SugarParameter("@interactiveMode", input.InteractiveMode));
                }
                if (!string.IsNullOrEmpty(input.ClassId))
                {
                    where += $" AND atp.PublishBusinessId=@classId  ";
                    parameters.Add(new SugarParameter("@classId", input.ClassId));
                }
                if (!input.IsCase)
                {
                    where += $" AND task.Creator= @teacherId  ";
                    parameters.Add(new SugarParameter("@teacherId", input.TeacherId));
                }

                //获取口语交际列表
                string sql = $@"SELECT
                                	task.Id,
                                	task.Name,
                                	oct.InteractiveMode,
                                	task.Introduce,
                                	oct.ChapterId,
                                	task.CreateTime,
                                	cha.ChapterName,
                                	atp.BeginTime,
                                	atp.EndTime,
                                	agent.Logo,
                                    oct.Target,
                                    CASE WHEN atp.Id IS NULL THEN 2 ELSE 1 END AS PublishState
                                FROM
                                	AI_AgentTask task WITH ( NOLOCK )
                                	INNER JOIN AI_OralCommunicationTask oct WITH ( NOLOCK ) ON task.Id= oct.AgentTaskId AND oct.IsDeleted= 0 AND oct.IsCase=@isCase
                                	INNER JOIN Exam_SubjectChapter cha WITH ( NOLOCK ) ON oct.ChapterId= cha.Id
                                	LEFT JOIN AI_AgentTaskPublish atp WITH ( NOLOCK ) ON task.Id= atp.AgentTaskId 
                                	AND atp.IsDeleted= 0
                                	LEFT JOIN Exam_Class class WITH ( NOLOCK ) ON atp.PublishBusinessId= class.Id
                                	INNER JOIN AI_AgentBaseInfo agent WITH ( NOLOCK ) ON task.AgentId= agent.Id
                                WHERE
                                	task.IsDeleted= 0 
                                	AND task.[Year] = @year 
                                	AND task.Term= @term 
                                    AND task.SubjectId=@subjectId
                                	AND task.AgentId= @agentId
                                    AND task.GradeId=@gradeId {where}
                                GROUP BY
                                	task.Id,
                                	task.Name,
                                	oct.InteractiveMode,
                                	task.Introduce,
                                	oct.ChapterId,
                                	task.CreateTime,
                                	cha.ChapterName,
                                	atp.BeginTime,
                                	atp.EndTime,
                                	agent.Logo,
                                    oct.Target,
                                    atp.Id";
                RefAsync<int> totalNumber = 0;
                List<AgentTeacherOralCommunicationListOutput> communicationListOutputs = await DBSqlSugar.SqlQueryable<AgentTeacherOralCommunicationListOutput>(sql)
                    .AddParameters(parameters)
                    .OrderBy("CreateTime DESC")
                    .ToPageListAsync(input.PageIndex, input.PageSize, totalNumber);

                if (communicationListOutputs.Count > 0)
                {
                    List<string> ids = communicationListOutputs.Select(p => p.Id).ToList();

                    //获取智能体任务发布的班级
                    string classSql = $@"SELECT
                                         	atp.AgentTaskId,
                                         	class.ClassName 
                                         FROM
                                         	AI_AgentTaskPublish atp WITH ( NOLOCK )
                                         	INNER JOIN Exam_Class class WITH ( NOLOCK ) ON atp.PublishBusinessId= class.Id 
                                         WHERE
                                         	atp.IsDeleted= 0 
                                         	AND atp.Creator=@teacherId 
                                            AND atp.AgentTaskId in (@agentTaskIdIds) ";
                    List<AgentTeacherOralCommunicationListClass> classNames = await DBSqlSugar.SqlQueryable<AgentTeacherOralCommunicationListClass>(classSql)
                        .AddParameters(new { teacherId = input.TeacherId, agentTaskIdIds = ids }).ToListAsync();
                    foreach (var communication in communicationListOutputs)
                    {
                        List<string> className = classNames.Where(p => p.AgentTaskId == communication.Id).Select(p => p.ClassName).ToList();
                        if (className.Count > 0)
                        {
                            communication.ClassName = string.Join('、', className);
                        }
                    }
                }

                return new PageReturn<AgentTeacherOralCommunicationListOutput>()
                {
                    Datas = communicationListOutputs,
                    TotalCount = totalNumber
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 智能体_教师端获取智能体任务分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task<GetOralCommunicationAnalyseOutput> GetOralCommunicationAnalyse(GetOralCommunicationAnalyseInput input)
        {
            try
            {
                //获取智能体任务信息
                string agenTaskSql = @"SELECT
                                	task.AgentId,
                                	task.Id AS AgentTaskId,
                                	task.Name AS AgentTaskName,
                                	task.Introduce AS AgentTaskIntroduce,
                                	( CASE ora.InteractiveMode WHEN 1 THEN '指令式' WHEN 2 THEN '对话式' WHEN 3 THEN '辩论式' ELSE '其他' END ) InteractiveMode,
                                    agent.Logo
                                FROM
                                	AI_AgentTask task WITH ( NOLOCK )
                                	INNER JOIN AI_OralCommunicationTask ora WITH ( NOLOCK ) ON task.Id= ora.AgentTaskId 
                                	AND ora.IsDeleted= 0 
                                    INNER JOIN AI_AgentBaseInfo agent WITH ( NOLOCK ) ON task.AgentId= agent.Id 
	                                AND agent.IsDeleted= 0 
                                WHERE
                                	task.IsDeleted= 0 
                                	AND task.Id= @taskId";
                GetOralCommunicationAnalyseOutput analyseOutput = await DBSqlSugar.SqlQueryable<GetOralCommunicationAnalyseOutput>(agenTaskSql)
                    .AddParameters(new { taskId = input.AgentTaskId }).FirstAsync();

                //获取学生做智能体口语交际任务信息
                string doAgentTaskSql = @"SELECT
                                            	stu.id AS StudentId,
                                            	stu.StudentNo AS StudentNum,
                                            	stu.RealName AS StudentName,
                                            	doAgentTask.Score AS StudentScore,
                                            	( CASE WHEN doAgentTask.Id IS NULL THEN '未提交' ELSE doAgentTask.[Level] END ) StudentLevel,
                                            	( CASE WHEN doAgentTask.Id IS NULL THEN 0 ELSE 1 END ) IsSubmit 
                                            FROM
                                            	Exam_Student stu WITH ( NOLOCK )
                                            	LEFT JOIN AI_StudentDoOralCommunicationTask doAgentTask WITH ( NOLOCK ) ON stu.Id= doAgentTask.StudentId 
                                            	AND doAgentTask.AgentTaskId= @agentTaskId 
                                            	AND doAgentTask.IsDeleted= 0 
                                            WHERE
                                            	stu.Deleted= 0 
                                            	AND stu.ClassId= @classId
                                            ORDER BY
                                            	doAgentTask.CreateTime DESC,
                                            	stu.RealName";
                List<GetStudentDoAgentTaskInfoDto> studentDoAgentTasks = await DBSqlSugar.SqlQueryable<GetStudentDoAgentTaskInfoDto>(doAgentTaskSql)
                    .AddParameters(new { agentTaskId = input.AgentTaskId, classId = input.ClassId }).ToListAsync();

                //等第分布处理
                List<GetOralCommunicationAnalyseLevelOutput> levelList = studentDoAgentTasks.Select(p => new GetOralCommunicationAnalyseLevelOutput()
                {
                    LevelName = p.StudentLevel
                }).DistinctBy(p => p.LevelName).OrderByDescending(p => p.LevelName).ToList();
                foreach (var level in levelList)
                {
                    level.LevelCount = studentDoAgentTasks.Count(p => p.StudentLevel == level.LevelName);
                }

                analyseOutput.LevelList = levelList;
                analyseOutput.ScoreList = studentDoAgentTasks.Select(p => new GetOralCommunicationAnalyseScoreOutput()
                {
                    StudentId = p.StudentId,
                    StudentName = p.StudentName,
                    StudentNum = p.StudentNum,
                    StudentLevel = p.StudentLevel
                }).ToList();
                analyseOutput.SubmitCount = studentDoAgentTasks.Count(p => p.IsSubmit);

                decimal? avgScore = studentDoAgentTasks.Average(p => p.StudentScore);

                analyseOutput.AvgLevel = avgScore.HasValue ? BusinessUtil.GetAvgLevel(avgScore.Value) : "D";

                return analyseOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 教师获取学生口语交际评估结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetOralCommunicationStudentResultOutput> GetOralCommunicationStudentResult(GetOralCommunicationStudentResultInput input)
        {
            try
            {
                string sql = @"SELECT
                                	task.Name AS AgentTaskName,
                                	task.Introduce AS AgentTaskIntroduce,
                                	stu.AssessmentResult,
                                	agent.Logo,
                                	student.Id AS StudentId,
                                	student.RealName AS StudentName,
                                    ora.RoleName,
                                	( CASE WHEN student.Photo IS NULL THEN 'https://userphoto.obs.cn-east-2.myhuaweicloud.com/uploadFile131e561d405a4f6a834a6a79d27ded7e.20210305132909.png' ELSE student.Photo END ) AS StudentLogo,
                                	( CASE ora.InteractiveMode WHEN 1 THEN '指令式' WHEN 2 THEN '对话式' WHEN 3 THEN '辩论式' ELSE '其他' END ) InteractiveMode 
                                FROM
                                	AI_AgentTask task WITH ( NOLOCK )
                                	INNER JOIN AI_StudentDoOralCommunicationTask stu WITH ( NOLOCK ) ON task.Id= stu.AgentTaskId 
                                	AND stu.StudentId= @studentId 
                                	AND stu.IsDeleted= 0 
                                	AND stu.AgentTaskId= @agentTaskId
                                	INNER JOIN Exam_Student student WITH ( NOLOCK ) ON student.Id= stu.StudentId
                                	INNER JOIN AI_AgentBaseInfo agent WITH ( NOLOCK ) ON task.AgentId= agent.Id 
                                	AND agent.IsDeleted= 0
                                	INNER JOIN AI_OralCommunicationTask ora WITH ( NOLOCK ) ON task.Id= ora.AgentTaskId 
                                	AND ora.IsDeleted= 0 
                                WHERE
                                	task.IsDeleted= 0 ";
                GetOralCommunicationStudentResultOutput studentResultOutput = await DBSqlSugar.SqlQueryable<GetOralCommunicationStudentResultOutput>(sql)
                    .AddParameters(new { studentId = input.StudentId, agentTaskId = input.AgentTaskId }).FirstAsync();

                return studentResultOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 发布口语交际
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task PublishOralCommunication(PublishOralCommunicationInput input)
        {
            try
            {
                //获取口语交际信息
                AI_OralCommunicationTask oralCommunicationTask = await DBSqlSugar.Queryable<AI_OralCommunicationTask>().Where(p => p.AgentTaskId == input.Id && p.IsDeleted == false).FirstAsync();
                if (oralCommunicationTask == null)
                {
                    throw new BusException("口语交际任务Id异常!");
                }

                if (!oralCommunicationTask.IsCase)
                {
                    //发布班级
                    await DBSqlSugar.Deleteable<AI_AgentTaskPublish>().Where(it => it.AgentTaskId == input.Id).ExecuteCommandAsync();
                    List<AI_AgentTaskPublish> agentTaskPublishes = new List<AI_AgentTaskPublish>();
                    foreach (var item in input.ClassId)
                    {
                        agentTaskPublishes.Add(new AI_AgentTaskPublish()
                        {
                            Id = IdHelper.GetId(),
                            AgentTaskId = input.Id,
                            PublishType = 1,
                            PublishBusinessId = item,
                            BeginTime = input.TimeRange[0],
                            EndTime = input.TimeRange[1],
                            CreateTime = DateTime.Now,
                            Creator = input.TeacherId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.TeacherId,
                            IsDeleted = false
                        });
                    }
                    await DBSqlSugar.Insertable(agentTaskPublishes).ExecuteCommandAsync();
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 撤销发布口语交际
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task DelPublishOralCommunication(DelPublishOralCommunicationInput input)
        {
            try
            {
                await DBSqlSugar.Deleteable<AI_AgentTaskPublish>().Where(it => it.AgentTaskId == input.Id && it.Creator == input.TeacherId).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }
    }
}
