﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.Word
{
    /// <summary>
    /// Markdown文本转word生成压缩包接口入参
    /// </summary>
    public class MarkdownTextToWordZipInput
    {
        /// <summary>
        /// word信息
        /// </summary>
        public List<MarkdownTextToWordZipInfo> WordInfos { get; set; } = new List<MarkdownTextToWordZipInfo>();
    }

    /// <summary>
    /// Markdown文本转word生成压缩包接口入参
    /// </summary>
    public class MarkdownTextToWordZipInfo
    {
        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// Markdown文本
        /// </summary>
        public string? Text { get; set; }
    }
}
