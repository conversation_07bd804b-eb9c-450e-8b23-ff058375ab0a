﻿using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.Core.Utilities;
using Uwoo.Model.JWT;

namespace Uwoo.Core.Attributes
{
	public class CheckJWTAttribute : BaseActionFilter,IActionFilter
	{
		private static readonly int _errorCode = 401;

		/// <summary>
		/// Action执行之前执行
		/// </summary>
		/// <param name="context">过滤器上下文</param>
		public void OnActionExecuting(ActionExecutingContext context)
		{
			try
			{
				if (context.ContainsFilter<NoCheckJWTAttribute>())
					return;

				var req = context.HttpContext.Request;

				string token = req.GetToken();
				if (!UwooJwtHelper.CheckToken(token, AppSetting.Secret.JWT))
				{
					
					context.Result = Error("token校验失败!", _errorCode);
					return;
				}

				var payload = UwooJwtHelper.GetPayload<JWTPayload>(token);

				if (payload.Expire < DateTime.Now)
				{
					context.Result = Error("登录过期，请重新登录", _errorCode);
					return;
				}
			}
			catch (Exception ex)
			{
				context.Result = Error(ex.Message, _errorCode);
			}
		}

		/// <summary>
		/// Action执行完毕之后执行
		/// </summary>
		/// <param name="context"></param>
		public void OnActionExecuted(ActionExecutedContext context)
		{

		}
	}
}
