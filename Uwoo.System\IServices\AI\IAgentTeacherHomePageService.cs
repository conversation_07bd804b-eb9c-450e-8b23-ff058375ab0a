﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_教师端首页
    /// </summary>
    public interface IAgentTeacherHomePageService : IService<AI_AgentBaseInfo>
    {
        /// <summary>
        /// 获取智能体列表信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<AgentTeacherHomePageListOutPut> GetAgentListInfo(AgentTeacherHomePageListInput input);

        /// <summary>
        /// 智能体收藏
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task AgentCollection(AgentTeacherCollectionInput input);
    }
}
