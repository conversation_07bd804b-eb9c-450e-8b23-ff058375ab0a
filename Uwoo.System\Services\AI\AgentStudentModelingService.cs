﻿using Coldairarrow.Util;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Model.CustomException;
using UwooAgent.Core.CacheManager.IBusinessCacheService;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.Student;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_学生端建模
    /// </summary>
    public class AgentStudentModelingService : ServiceBase<AI_AgentTask, IAgentStudentModelingRepository>, IAgentStudentModelingService, IDependency
    {
        #region DI

        private IAgentCommonService _agentCommonService;
        private IAgentCommonCacheService _agentCommonCacheService;
        public AgentStudentModelingService(IAgentCommonService agentCommonService,
            IAgentCommonCacheService agentCommonCacheService)
        {
            _agentCommonService = agentCommonService;
            _agentCommonCacheService = agentCommonCacheService;
        }

        #endregion

        /// <summary>
        /// 获取学生端建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentModelingInfoOutput> GetStudentModelingInfo(GetStudentModelingInfoInput input)
        {
            try
            {
                //获取建模基础信息
                GetStudentModelingInfoOutput modelingInfoOutput = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ModelingId && p.IsDeleted == false)
                    .Select(p => new GetStudentModelingInfoOutput()
                    {
                        AgentId = p.AgentId,
                        ModelingId = p.Id,
                        ModelingName = p.Name,
                        ModelingIntroduce = p.Introduce,
                        ModelingLogo = p.TaskLogo,
                        ScorePublishType = p.ScorePublishType,
                        ScorePublishTime = p.ScorePublishTime
                    })
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (modelingInfoOutput == null)
                {
                    throw new BusException("建模Id异常!");
                }

                //获取建模发布的记录
                AI_AgentTaskPublish agentTaskPublish = await DBSqlSugar.Queryable<AI_AgentTaskPublish>()
                    .Where(p => p.AgentTaskId == input.ModelingId && p.IsDeleted == false && (p.PublishBusinessId == input.ClassId || p.PublishBusinessId == input.StudentId))
                    .FirstAsync();
                if (agentTaskPublish == null || !agentTaskPublish.EndTime.HasValue)
                {
                    throw new BusException("建模发布异常!");
                }
                if (agentTaskPublish.EndTime.Value <= DateTime.Now)
                {
                    modelingInfoOutput.ModelingTaskState = 2;
                }
                else
                {
                    modelingInfoOutput.ModelingTaskState = 1;
                }

                //评分公布状态
                if (modelingInfoOutput.ScorePublishType == 1)
                {
                    modelingInfoOutput.IsScorePublish = true;
                }
                else if (modelingInfoOutput.ScorePublishType == 2)
                {
                    if (modelingInfoOutput.ModelingTaskState == 2)
                    {
                        modelingInfoOutput.IsScorePublish = true;
                    }
                    else
                    {
                        modelingInfoOutput.IsScorePublish = false;
                    }
                }
                else if (modelingInfoOutput.ScorePublishType == 3)
                {
                    if (modelingInfoOutput.ScorePublishTime.HasValue && modelingInfoOutput.ScorePublishTime.Value >= DateTime.Now)
                    {
                        modelingInfoOutput.IsScorePublish = true;
                    }
                    else
                    {
                        modelingInfoOutput.IsScorePublish = false;
                    }
                }
                else
                {
                    modelingInfoOutput.IsScorePublish = true;
                }

                //建模阶段
                List<GetStudentModelingStageInfoOutput> modelingStages = await DBSqlSugar.Queryable<AI_ModelingStage>()
                    .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.IsDeleted == false)
                    .Select(p => new GetStudentModelingStageInfoOutput()
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Describe = p.Describe,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //建模阶段任务
                List<GetStudentModelingStageTaskInfoOutput> modelingStageTasks = await DBSqlSugar.Queryable<AI_ModelingStageTask>()
                    .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.IsDeleted == false)
                    .Select(p => new GetStudentModelingStageTaskInfoOutput()
                    {
                        Id = p.Id,
                        ModelingStageId = p.ModelingStageId,
                        Name = p.Name,
                        Target = p.Target,
                        TaskType = p.TaskType,
                        ToneId = p.ToneId,
                        RoleName = p.RoleName,
                        Prologue = p.Prologue,
                        GroupAssessmentScore = p.GroupAssessmentScore,
                        GroupIsAssessment = p.GroupIsAssessment,
                        GroupIsSubmit = p.GroupIsSubmit,
                        GroupIsReadAllDocuments = p.GroupIsReadAllDocuments,
                        GroupIsWatchVideo = p.GroupIsWatchVideo,
                        GroupVideoWatchDuration = p.GroupVideoWatchDuration,
                        GroupIsVideoWatchDuration = p.GroupIsVideoWatchDuration,
                        TaskAssessmentScore = p.TaskAssessmentScore,
                        TaskIsAssessment = p.TaskIsAssessment,
                        TaskIsSubmit = p.TaskIsSubmit,
                        TaskIsReadAllDocuments = p.TaskIsReadAllDocuments,
                        TaskVideoWatchDuration = p.TaskVideoWatchDuration,
                        TaskIsVideoWatchDuration = p.TaskIsVideoWatchDuration,
                        TaskIsWatchVideo = p.TaskIsWatchVideo,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //获取建模阶段任务视频
                string taskVideoSql = @"SELECT
                                    	video.Id,
                                    	video.ModelingStageTaskId,
                                    	video.Name,
                                    	video.Url,
                                    	video.[Size],
                                    	video.Duration,
                                    	video.[Order],
                                    	( CASE WHEN sw.Id IS NOT NULL THEN 1 ELSE 0 END ) AS IsWatch,
                                    	sw.TotalWatchDuration 
                                    FROM
                                    	AI_ModelingStageTaskVideo video WITH ( NOLOCK )
                                    	LEFT JOIN AI_StudentWatchModelingVideo sw WITH ( NOLOCK ) ON video.Id= sw.ModelingStageTaskVideoId 
                                    	AND sw.IsDeleted= 0 
                                    	AND sw.StudentId= @studentId 
                                        AND sw.ModelingId= @modelingId
                                    WHERE
                                    	video.IsDeleted= 0 
                                    	AND video.ModelingId= @modelingId";
                List<GetStudentModelingStageTaskVideoInfoOutput> taskVideoInfos = await DBSqlSugar.SqlQueryable<GetStudentModelingStageTaskVideoInfoOutput>(taskVideoSql)
                    .AddParameters(new { studentId = input.StudentId, modelingId = input.ModelingId }).ToListAsync();

                //获取建模阶段任务文档
                string taskDocSql = @"SELECT
                                        	doc.Id,
                                        	doc.ModelingStageTaskId,
                                        	doc.Name,
                                        	doc.Url,
                                        	doc.[Size],
                                        	doc.[Order],
                                        	( CASE WHEN sr.Id IS NOT NULL THEN 1 ELSE 0 END ) AS IsRead 
                                        FROM
                                        	AI_ModelingStageTaskDocument doc WITH ( NOLOCK )
                                        	LEFT JOIN AI_StudentReadModelingDocument sr WITH ( NOLOCK ) ON doc.Id= sr.ModelingStageTaskDocumentId 
                                        	AND sr.IsDeleted= 0 
                                        	AND sr.StudentId= @studentId 
                                            AND sr.ModelingId= @modelingId
                                        WHERE
                                        	doc.IsDeleted= 0 
                                        	AND doc.ModelingId= @modelingId";
                List<GetStudentModelingStageTaskDocumentInfoOutput> taskDocInfos = await DBSqlSugar.SqlQueryable<GetStudentModelingStageTaskDocumentInfoOutput>(taskDocSql)
                    .AddParameters(new { studentId = input.StudentId, modelingId = input.ModelingId }).ToListAsync();

                //获取学生做建模阶段任务信息
                List<AI_StudentDoModelingTask> studentDoModelingTasks = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>()
                    .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false)
                    .Select(p => new AI_StudentDoModelingTask()
                    {
                        Id = p.Id,
                        ModelingStageId = p.ModelingStageId,
                        ModelingStageTaskId = p.ModelingStageTaskId,
                        Score = p.Score,
                        IsStandard = p.IsStandard,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //获取学生问答记录
                List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                    .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false)
                    .Select(p => new AI_ModelingDialogueRecord()
                    {
                        ModelingStageTaskId = p.ModelingStageTaskId,
                        StudentDoModelingTaskId = p.StudentDoModelingTaskId,
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //下一个阶段是否锁
                bool isStages = false;
                //处理阶段信息
                foreach (var stage in modelingStages)
                {
                    //获取当前阶段的任务
                    List<GetStudentModelingStageTaskInfoOutput> modelingStageTask = modelingStageTasks.Where(p => p.ModelingStageId == stage.Id).OrderBy(p => p.Order).ToList();
                    if (modelingStageTask.Count <= 0)
                    {
                        stage.IsLock = false;
                        continue;
                    }
                    foreach (var stageTaskInfo in modelingStageTask)
                    {
                        //视频信息
                        if (stageTaskInfo.TaskType == 4)
                        {
                            stageTaskInfo.TaskVideos = taskVideoInfos.Where(p => p.ModelingStageTaskId == stageTaskInfo.Id).OrderBy(p => p.Order).ToList();
                        }
                        //文档信息
                        if (stageTaskInfo.TaskType == 5)
                        {
                            stageTaskInfo.TaskDocuments = taskDocInfos.Where(p => p.ModelingStageTaskId == stageTaskInfo.Id).OrderBy(p => p.Order).ToList();
                        }
                    }

                    //设置当前阶段锁
                    stage.IsLock = isStages;

                    if (stage.IsLock)
                    {
                        //当前阶段下面的所有任务都锁
                        foreach (var taskLock in modelingStageTask)
                        {
                            taskLock.IsLock = true;
                        }
                    }
                    else
                    {
                        //任务是否锁
                        bool isTask = false;
                        foreach (var taskIsLock in modelingStageTask)
                        {
                            //知识问答类型不锁
                            if (taskIsLock.TaskType == 3)
                            {
                                //不锁
                                taskIsLock.IsLock = false;
                                continue;
                            }

                            //设置当前任务锁
                            taskIsLock.IsLock = isTask;

                            //视频类型
                            if (taskIsLock.TaskType == 4)
                            {
                                //视频观看条件
                                bool videoWatch = taskIsLock.TaskIsWatchVideo ? taskIsLock.TaskVideos.Count == taskIsLock.TaskVideos.Count(p => p.IsWatch) : true;
                                //视频观看时长条件
                                bool videoWatchDuration = taskIsLock.TaskIsVideoWatchDuration ? taskIsLock.TaskVideos.Count > 0 && taskIsLock.TaskVideos.Sum(p => p.TotalWatchDuration) >= taskIsLock.TaskVideoWatchDuration * 60 : true;
                                //设置下一个任务是否锁
                                if (videoWatch && videoWatchDuration)
                                {
                                    isTask = false;
                                }
                                else
                                {
                                    isTask = true;
                                }
                                continue;
                            }

                            //文档类型
                            if (taskIsLock.TaskType == 5)
                            {
                                //阅读文档条件
                                bool readDocuments = taskIsLock.TaskIsReadAllDocuments ? taskIsLock.TaskDocuments.Count == taskIsLock.TaskDocuments.Count(p => p.IsRead) : true;
                                //设置下一个任务是否锁
                                if (readDocuments)
                                {
                                    isTask = false;
                                }
                                else
                                {
                                    isTask = true;
                                }
                                continue;
                            }

                            //获取学生做任务信息
                            List<AI_StudentDoModelingTask> studentDoModelingTask = studentDoModelingTasks.Where(p => p.ModelingStageTaskId == taskIsLock.Id).OrderBy(p => p.Order).ToList();

                            //提交
                            bool submitPass = taskIsLock.TaskIsSubmit ? studentDoModelingTask.Count > 0 : true;
                            //评估分数
                            bool assessmentPass = taskIsLock.TaskIsAssessment ? studentDoModelingTask.Count > 0 && studentDoModelingTask.Max(p => p.Score) >= taskIsLock.TaskAssessmentScore : true;
                            //设置下一个任务是否锁
                            if (submitPass && assessmentPass)
                            {
                                isTask = false;
                            }
                            else
                            {
                                isTask = true;
                            }
                        }
                    }

                    //验证下一个阶段是否锁
                    if (modelingStageTask.Count == modelingStageTask.Count(p => p.TaskType == 3))
                    {
                        isStages = false;
                    }
                    else
                    {
                        foreach (var taskStageIsLock in modelingStageTask)
                        {
                            //知识问答类型不做条件限制
                            if (taskStageIsLock.TaskType == 3)
                            {
                                continue;
                            }

                            //视频类型
                            if (taskStageIsLock.TaskType == 4)
                            {
                                //视频观看条件
                                bool videoWatch = taskStageIsLock.GroupIsWatchVideo ? taskStageIsLock.TaskVideos.Count == taskStageIsLock.TaskVideos.Count(p => p.IsWatch) : true;
                                //视频观看时长条件
                                bool videoWatchDuration = taskStageIsLock.GroupIsVideoWatchDuration ? taskStageIsLock.TaskVideos.Count > 0 && taskStageIsLock.TaskVideos.Sum(p => p.TotalWatchDuration) >= taskStageIsLock.GroupVideoWatchDuration * 60 : true;
                                if (videoWatch && videoWatchDuration)
                                {
                                    //已完成当前条件不锁
                                    isStages = false;
                                }
                                else
                                {
                                    //未完成当前条件锁
                                    isStages = true;
                                    break;
                                }
                                continue;
                            }

                            //文档类型
                            if (taskStageIsLock.TaskType == 5)
                            {
                                //阅读文档条件
                                bool readDocuments = taskStageIsLock.GroupIsReadAllDocuments ? taskStageIsLock.TaskDocuments.Count == taskStageIsLock.TaskDocuments.Count(p => p.IsRead) : true;
                                if (readDocuments)
                                {
                                    //已完成当前条件不锁
                                    isStages = false;
                                }
                                else
                                {
                                    //未完成当前条件锁
                                    isStages = true;
                                    break;
                                }
                                continue;
                            }

                            //获取学生做任务信息
                            List<AI_StudentDoModelingTask> studentDoModelingTask = studentDoModelingTasks.Where(p => p.ModelingStageTaskId == taskStageIsLock.Id).OrderBy(p => p.Order).ToList();
                            if (taskStageIsLock.GroupIsSubmit)
                            {
                                if (studentDoModelingTask.Count > 0)
                                {
                                    //已完成当前条件不锁
                                    isStages = false;
                                }
                                else
                                {
                                    //未完成当前条件锁
                                    isStages = true;
                                    break;
                                }
                            }
                            else
                            {
                                //未设置条件不锁
                                isStages = false;
                            }

                            if (taskStageIsLock.GroupIsAssessment)
                            {
                                if (studentDoModelingTask.Count > 0 && studentDoModelingTask.Max(p => p.Score) >= taskStageIsLock.GroupAssessmentScore)
                                {
                                    //已完成当前条件不锁
                                    isStages = false;
                                }
                                else
                                {
                                    //未完成当前条件锁
                                    isStages = true;
                                    break;
                                }
                            }
                            else
                            {
                                //未设置条件不锁
                                isStages = false;
                            }
                        }
                    }

                    //学生做任务状态
                    foreach (var taskState in modelingStageTask)
                    {
                        //获取学生做任务信息
                        List<AI_StudentDoModelingTask> studentDoModelingTask = studentDoModelingTasks.Where(p => p.ModelingStageTaskId == taskState.Id).OrderBy(p => p.Order).ToList();

                        //验证学生当前任务是否达标
                        if (studentDoModelingTask.Where(p => p.IsStandard == true).FirstOrDefault() != null)
                        {
                            //任务达标已完成
                            taskState.State = 3;
                            //获取提交记录Id
                            taskState.TaskSubmitId = studentDoModelingTask.Where(p => p.IsStandard == true).OrderByDescending(p => p.Order).FirstOrDefault()?.Id;
                        }
                        else
                        {
                            //视频
                            if (taskState.TaskType == 4)
                            {
                                //是否存在观看记录
                                if (taskState.TaskVideos.Count(p => p.IsWatch) > 0)
                                {
                                    //存在观看记录进行中
                                    taskState.State = 2;
                                }
                                else
                                {
                                    //不存在观看记录未开始
                                    taskState.State = 1;
                                }
                                continue;
                            }

                            //文档
                            if (taskState.TaskType == 5)
                            {
                                //是否存在阅读记录
                                if (taskState.TaskDocuments.Count(p => p.IsRead) > 0)
                                {
                                    //存在阅读记录进行中
                                    taskState.State = 2;
                                }
                                else
                                {
                                    //不存在阅读记录未开始
                                    taskState.State = 1;
                                }
                                continue;
                            }

                            //是否存在问答记录
                            if (dialogueContentRecords.Count(p => p.ModelingStageTaskId == taskState.Id) > 0)
                            {
                                //存在问答记录进行中
                                taskState.State = 2;
                            }
                            else
                            {
                                //不存在问答记录未开始
                                taskState.State = 1;
                            }

                            //获取未达标提交记录Id
                            List<string> noStandardId = studentDoModelingTask.Where(p => p.IsStandard == false).Select(p => p.Id).ToList();
                            //获取未达标对话备份梳理
                            int noStandardDialogueCount = dialogueContentRecords.Where(p => noStandardId.Contains(p.StudentDoModelingTaskId) && p.ModelingStageTaskId == taskState.Id)
                                .DistinctBy(p => p.StudentDoModelingTaskId).Count();

                            if (noStandardId.Count > noStandardDialogueCount)
                            {
                                //已提交但未达标
                                taskState.IsSubmitNoStandard = true;
                                //获取提交记录Id
                                taskState.TaskSubmitId = studentDoModelingTask.Where(p => p.IsStandard == false).OrderByDescending(p => p.Order).FirstOrDefault()?.Id;
                            }
                            else
                            {
                                //已提交但未达标
                                taskState.IsSubmitNoStandard = false;
                            }
                        }
                    }

                    stage.ModelingStageTaskInfos = modelingStageTask;
                }

                modelingInfoOutput.ModelingStageInfos = modelingStages;
                modelingInfoOutput.ProgressBar = BusinessUtil.GetAccuracy(studentDoModelingTasks.Where(p => p.IsStandard).DistinctBy(p => p.ModelingStageTaskId).Count(), modelingStageTasks.Count(p => p.TaskType != 3), 1);
                return modelingInfoOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 学生阅读建模文档
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task StudentReadModelingDocument(StudentReadModelingDocumentInput input)
        {
            //缓存锁
            string key = input.StudentId + "|" + input.ModelingStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    //获取当前任务信息
                    AI_ModelingStageTask modelingStageTask = await DBSqlSugar.Queryable<AI_ModelingStageTask>()
                        .Where(p => p.Id == input.ModelingStageTaskId && p.IsDeleted == false && p.TaskType == 5)
                        .Select(p => new AI_ModelingStageTask()
                        {
                            Id = p.Id
                        })
                        .FirstAsync();
                    if (modelingStageTask == null)
                    {
                        throw new BusException("建模阶段任务Id异常!");
                    }

                    //保存阅读记录
                    AI_StudentReadModelingDocument readModelingDocument = await DBSqlSugar.Queryable<AI_StudentReadModelingDocument>()
                    .Where(p => p.ModelingStageTaskDocumentId == input.DocumentId
                    && p.StudentId == input.StudentId
                    && p.ModelingId == input.ModelingId
                    && p.ModelingStageId == input.ModelingStageId
                    && p.ModelingStageTaskId == input.ModelingStageTaskId
                    && p.IsDeleted == false
                    ).FirstAsync();
                    if (readModelingDocument == null)
                    {
                        //保存阅读记录
                        readModelingDocument = new AI_StudentReadModelingDocument()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = input.ModelingId,
                            ModelingStageId = input.ModelingStageId,
                            ModelingStageTaskId = input.ModelingStageTaskId,
                            ModelingStageTaskDocumentId = input.DocumentId,
                            StudentId = input.StudentId,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false
                        };
                        await DBSqlSugar.Insertable(readModelingDocument).ExecuteCommandAsync();
                    }

                    //获取任务提交记录
                    AI_StudentDoModelingTask studentDoModelingTask = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>()
                            .Where(p => p.StudentId == input.StudentId &&
                            p.ModelingId == input.ModelingId &&
                            p.ModelingStageId == input.ModelingStageId &&
                            p.ModelingStageTaskId == input.ModelingStageTaskId &&
                            p.IsDeleted == false)
                            .FirstAsync();
                    if (studentDoModelingTask == null)
                    {
                        //是否保存任务提交记录
                        bool isSaveDoTask = false;

                        //获取建模阶段任务文档
                        string taskDocSql = @"SELECT
                                                	doc.Id,
                                                	( CASE WHEN sr.Id IS NOT NULL THEN 1 ELSE 0 END ) AS IsRead 
                                                FROM
                                                	AI_ModelingStageTaskDocument doc WITH ( NOLOCK )
                                                	LEFT JOIN AI_StudentReadModelingDocument sr WITH ( NOLOCK ) ON doc.Id= sr.ModelingStageTaskDocumentId 
                                                	AND sr.IsDeleted= 0 
                                                	AND sr.StudentId= @studentId 
                                                    AND sr.ModelingId= @modelingId 
                                                    AND sr.ModelingStageId=@modelingStageId 
                                                    AND sr.ModelingStageTaskId=@modelingStageTaskId
                                                WHERE
                                                	doc.IsDeleted= 0 
                                                	AND doc.ModelingId= @modelingId 
                                                    AND doc.ModelingStageId=@modelingStageId 
                                                    AND doc.ModelingStageTaskId=@modelingStageTaskId";
                        List<GetStudentModelingStageTaskDocumentInfoOutput> taskDocInfos = await DBSqlSugar.SqlQueryable<GetStudentModelingStageTaskDocumentInfoOutput>(taskDocSql)
                            .AddParameters(new
                            {
                                studentId = input.StudentId,
                                modelingId = input.ModelingId,
                                modelingStageId = input.ModelingStageId,
                                modelingStageTaskId = input.ModelingStageTaskId
                            }).ToListAsync();

                        if (taskDocInfos.Count == taskDocInfos.Count(p => p.IsRead))
                        {
                            isSaveDoTask = true;
                        }

                        //保存任务提交记录
                        if (isSaveDoTask)
                        {
                            studentDoModelingTask = new AI_StudentDoModelingTask()
                            {
                                Id = IdHelper.GetId(),
                                ModelingId = input.ModelingId,
                                ModelingStageId = input.ModelingStageId,
                                ModelingStageTaskId = input.ModelingStageTaskId,
                                StudentId = input.StudentId,
                                IsStandard = true,
                                Score = 100,
                                AssessmentResult = "--",
                                Level = "A",
                                CreateTime = DateTime.Now,
                                Creator = input.StudentId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.StudentId,
                                IsDeleted = false,
                                Order = 1
                            };
                            await DBSqlSugar.Insertable(studentDoModelingTask).ExecuteCommandAsync();
                        }
                    }

                    //释放锁
                    _agentCommonCacheService.DelRedisLock(key);
                }
                catch (Exception ex)
                {
                    //释放锁
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
        }

        /// <summary>
        /// 学生观看建模视频
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task StudentWatchModelingVideo(StudentWatchModelingVideoInput input)
        {
            //缓存锁
            string key = input.StudentId + "|" + input.ModelingStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    //获取当前任务信息
                    AI_ModelingStageTask modelingStageTask = await DBSqlSugar.Queryable<AI_ModelingStageTask>()
                        .Where(p => p.Id == input.ModelingStageTaskId && p.IsDeleted == false && p.TaskType == 4)
                        .Select(p => new AI_ModelingStageTask()
                        {
                            GroupIsVideoWatchDuration = p.GroupIsVideoWatchDuration,
                            GroupVideoWatchDuration = p.GroupVideoWatchDuration,
                            TaskIsVideoWatchDuration = p.TaskIsVideoWatchDuration,
                            TaskVideoWatchDuration = p.TaskVideoWatchDuration
                        })
                        .FirstAsync();
                    if (modelingStageTask == null)
                    {
                        throw new BusException("建模阶段任务Id异常!");
                    }

                    //保存观看记录
                    AI_StudentWatchModelingVideo watchModelingVideo = await DBSqlSugar.Queryable<AI_StudentWatchModelingVideo>()
                    .Where(p => p.ModelingStageTaskVideoId == input.VideoId
                    && p.StudentId == input.StudentId
                    && p.ModelingId == input.ModelingId
                    && p.ModelingStageId == input.ModelingStageId
                    && p.ModelingStageTaskId == input.ModelingStageTaskId
                    && p.IsDeleted == false
                    ).FirstAsync();
                    if (watchModelingVideo == null)
                    {
                        //保存观看记录
                        watchModelingVideo = new AI_StudentWatchModelingVideo()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = input.ModelingId,
                            ModelingStageId = input.ModelingStageId,
                            ModelingStageTaskId = input.ModelingStageTaskId,
                            ModelingStageTaskVideoId = input.VideoId,
                            TotalWatchDuration = input.Duration,
                            StudentId = input.StudentId,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false
                        };
                        await DBSqlSugar.Insertable(watchModelingVideo).ExecuteCommandAsync();
                    }
                    else
                    {
                        watchModelingVideo.TotalWatchDuration = watchModelingVideo.TotalWatchDuration + input.Duration;
                        await DBSqlSugar.Updateable(watchModelingVideo).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }

                    //获取任务提交记录
                    AI_StudentDoModelingTask studentDoModelingTask = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>()
                            .Where(p => p.StudentId == input.StudentId &&
                            p.ModelingId == input.ModelingId &&
                            p.ModelingStageId == input.ModelingStageId &&
                            p.ModelingStageTaskId == input.ModelingStageTaskId &&
                            p.IsDeleted == false)
                            .FirstAsync();
                    if (studentDoModelingTask == null)
                    {
                        //是否保存任务提交记录
                        bool isSaveDoTask = false;

                        //获取建模阶段任务视频
                        string taskVideoSql = @"SELECT
                                                	video.Id,
                                                	( CASE WHEN sw.Id IS NOT NULL THEN 1 ELSE 0 END ) AS IsWatch,
                                                	sw.TotalWatchDuration 
                                                FROM
                                                	AI_ModelingStageTaskVideo video WITH ( NOLOCK )
                                                	LEFT JOIN AI_StudentWatchModelingVideo sw WITH ( NOLOCK ) ON video.Id= sw.ModelingStageTaskVideoId 
                                                	AND sw.IsDeleted= 0 
                                                	AND sw.StudentId= @studentId 
                                                    AND sw.ModelingId= @modelingId
                                                    AND sw.ModelingStageId=@modelingStageId 
                                                    AND sw.ModelingStageTaskId=@modelingStageTaskId
                                                WHERE
                                                	video.IsDeleted= 0 
                                                	AND video.ModelingId= @modelingId
                                                    AND video.ModelingStageId=@modelingStageId 
                                                    AND video.ModelingStageTaskId=@modelingStageTaskId";
                        List<GetStudentModelingStageTaskVideoInfoOutput> taskVideoInfos = await DBSqlSugar.SqlQueryable<GetStudentModelingStageTaskVideoInfoOutput>(taskVideoSql)
                            .AddParameters(new
                            {
                                studentId = input.StudentId,
                                modelingId = input.ModelingId,
                                modelingStageId = input.ModelingStageId,
                                modelingStageTaskId = input.ModelingStageTaskId
                            }).ToListAsync();

                        //视频观看时长条件
                        bool videoWatchDuration = modelingStageTask.TaskIsVideoWatchDuration ? taskVideoInfos.Sum(p => p.TotalWatchDuration) >= modelingStageTask.TaskVideoWatchDuration * 60 : true;

                        //视频观看时长条件
                        bool videoWatchDurationGroup = modelingStageTask.GroupIsVideoWatchDuration ? taskVideoInfos.Sum(p => p.TotalWatchDuration) >= modelingStageTask.GroupVideoWatchDuration * 60 : true;

                        if (videoWatchDuration && videoWatchDurationGroup && taskVideoInfos.Count == taskVideoInfos.Count(p => p.IsWatch))
                        {
                            isSaveDoTask = true;
                        }

                        //保存任务提交记录
                        if (isSaveDoTask)
                        {
                            studentDoModelingTask = new AI_StudentDoModelingTask()
                            {
                                Id = IdHelper.GetId(),
                                ModelingId = input.ModelingId,
                                ModelingStageId = input.ModelingStageId,
                                ModelingStageTaskId = input.ModelingStageTaskId,
                                StudentId = input.StudentId,
                                IsStandard = true,
                                Score = 100,
                                AssessmentResult = "--",
                                Level = "A",
                                CreateTime = DateTime.Now,
                                Creator = input.StudentId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.StudentId,
                                IsDeleted = false,
                                Order = 1
                            };
                            await DBSqlSugar.Insertable(studentDoModelingTask).ExecuteCommandAsync();
                        }
                    }

                    //释放锁
                    _agentCommonCacheService.DelRedisLock(key);
                }
                catch (Exception ex)
                {
                    //释放锁
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
        }

        /// <summary>
        /// 学生端建模作品评估
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentModelingAssessOutput> StudentModelingAssess(StudentModelingAssessInput input)
        {
            //缓存锁
            string key = input.StudentId + "|" + input.ModelingStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    //是否存在达标提交记录
                    AI_StudentDoModelingTask isDo = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == input.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsStandard == true && p.IsDeleted == false).FirstAsync();
                    if (isDo != null)
                    {
                        throw new BusException("已完成，无法重复提交!", 801);
                    }

                    //获取建模阶段任务基础信息
                    string modelingStageTaskSql = @"SELECT
                                                    	task.Id AS ModelingStageTaskId,
                                                    	task.ModelingStageId,
                                                    	task.ModelingId,
                                                    	task.RoleSetting,
                                                    	task.ScoreStandard,
                                                    	task.TaskIsSubmit,
                                                    	task.TaskIsAssessment,
                                                    	task.TaskAssessmentScore,
                                                    	task.GroupIsSubmit,
                                                    	task.GroupIsAssessment,
                                                    	task.GroupAssessmentScore 
                                                    FROM
                                                    	AI_ModelingStageTask task WITH ( NOLOCK )
                                                    WHERE
                                                    	task.IsDeleted= 0 
                                                    	AND task.TaskType= 1 
                                                    	AND task.Id= @modelingStageTaskId";
                    ModelingStageTaskBaseInfoDto modelingStageTask = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                        .AddParameters(new { modelingStageTaskId = input.ModelingStageTaskId }).FirstAsync();
                    if (modelingStageTask == null)
                    {
                        throw new BusException("建模阶段任务Id异常!");
                    }

                    //获取建模阶段任务的高频问题
                    List<AI_ModelingStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ModelingStageTaskQuestion>()
                        .Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.IsDeleted == false)
                        .Select(p => new AI_ModelingStageTaskQuestion()
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Describe = p.Describe
                        }).ToListAsync();
                    string taskQuestionsText = string.Empty;
                    foreach (var taskQuestion in taskQuestions)
                    {
                        taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                    }
                    if (string.IsNullOrEmpty(taskQuestionsText))
                    {
                        taskQuestionsText = "暂无";
                    }

                    //获取配置
                    string apiKey = AppSetting.DouBaoAI.APIKey;
                    string model = await DBSqlSugar.Queryable<AI_ModelConfig>().Where(p => p.SceneType == 6).Select(p => p.Model).FirstAsync();
                    string url = AppSetting.DouBaoAI.MyAppUrl;
                    if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(model) || string.IsNullOrEmpty(url))
                    {
                        throw new Exception("请求配置异常!");
                    }

                    //参数处理
                    string fileUrlStr = string.Empty;
                    foreach (var file in input.Files)
                    {
                        fileUrlStr += $"第{input.Files.IndexOf(file) + 1}个作品地址:{file.FileUrl}。\n";
                    }
                    DouBaoMyAppDto fileAnalysisDouBaoDto = new DouBaoMyAppDto()
                    {
                        model = model,
                        stream = true,
                        messages = new List<DouBaoMyAppMessages>()
                        {
                            new DouBaoMyAppMessages()
                            {
                                role="user",
                                content=$"作品地址:{fileUrlStr}、评分标准:{modelingStageTask.ScoreStandard}、用户提示词:{modelingStageTask.RoleSetting}、主题:{taskQuestionsText}"
                            }
                        }
                    };
                    string jsonData = JsonConvert.SerializeObject(fileAnalysisDouBaoDto);

                    string resDataJson = string.Empty;
                    //http请求
                    using (var httpClient = new HttpClient())
                    {
                        var request = new HttpRequestMessage(HttpMethod.Post, url);
                        request.Headers.Add("Authorization", apiKey);
                        request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                        using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead))
                        {
                            if (response.IsSuccessStatusCode)
                            {
                                using (var stream = await response.Content.ReadAsStreamAsync())
                                {
                                    using (var reader = new StreamReader(stream, Encoding.UTF8))
                                    {
                                        string line;
                                        while ((line = await reader.ReadLineAsync()) != "data:[DONE]")
                                        {
                                            // 处理SSE数据行
                                            if (line.StartsWith("data:"))
                                            {
                                                //去除(data: )进行解析数据
                                                var data = line.Substring("data:".Length).Trim();
                                                if (!string.IsNullOrEmpty(data))
                                                {
                                                    //解析请求结果
                                                    DouBaoMyAppStreamOutput chatResponse = JsonConvert.DeserializeObject<DouBaoMyAppStreamOutput>(data);
                                                    if (chatResponse != null && chatResponse.choices.Count > 0 && chatResponse.choices[0].delta != null)
                                                    {
                                                        resDataJson += chatResponse.choices[0].delta.content;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                throw new BusException("提交异常!");
                            }
                        }
                    }

                    //豆包评估输出
                    StudentModelingSubmitDouBaoOutput studentAssessDouBaoOutput = StudentModelingSubmitAnalysisJson(resDataJson);
                    if (studentAssessDouBaoOutput != null)
                    {
                        //保存提交记录
                        AI_StudentDoModelingTask studentDoModelingTask = new AI_StudentDoModelingTask()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            Score = studentAssessDouBaoOutput.Score,
                            Level = BusinessUtil.GetAvgLevel(studentAssessDouBaoOutput.Score),
                            AssessmentResult = !string.IsNullOrEmpty(studentAssessDouBaoOutput.AssessmentResult) ? studentAssessDouBaoOutput.AssessmentResult : "暂无评估结果!",
                            StudentId = input.StudentId,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsStandard = true
                        };
                        studentDoModelingTask.Order = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsDeleted == false).CountAsync() + 1;

                        //验证是否达标
                        // 1.任务评估
                        bool taskPass = modelingStageTask.TaskIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.TaskAssessmentScore : true;
                        // 2.组件评估
                        bool groupPass = modelingStageTask.GroupIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.GroupAssessmentScore : true;
                        // 3. 两者同时满足才算达标
                        studentDoModelingTask.IsStandard = taskPass == true && groupPass == true;
                        await DBSqlSugar.Insertable(studentDoModelingTask).ExecuteCommandAsync();

                        //高频主题保存
                        if (studentAssessDouBaoOutput.QuestionIds != null && studentAssessDouBaoOutput.QuestionIds.Count > 0)
                        {
                            List<AI_StudentDoModelingTaskQuestion> studentDoModelingTaskQuestions = new List<AI_StudentDoModelingTaskQuestion>();
                            foreach (var questionId in studentAssessDouBaoOutput.QuestionIds)
                            {
                                studentDoModelingTaskQuestions.Add(new AI_StudentDoModelingTaskQuestion()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingId = modelingStageTask.ModelingId,
                                    ModelingStageId = modelingStageTask.ModelingStageId,
                                    ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                                    StudentId = input.StudentId,
                                    QuestionId = questionId,
                                    StudentDoModelingTaskId = studentDoModelingTask.Id,
                                    CreateTime = DateTime.Now,
                                    Creator = input.StudentId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.StudentId,
                                    IsDeleted = false
                                });
                            }
                            await DBSqlSugar.Insertable(studentDoModelingTaskQuestions).ExecuteCommandAsync();
                        }

                        //保存会话记录
                        List<AIDialogueASKFileInfo> fileInfos = new List<AIDialogueASKFileInfo>();
                        foreach (var file in input.Files)
                        {
                            fileInfos.Add(new AIDialogueASKFileInfo()
                            {
                                FileName = file.FileName,
                                FileUrl = file.FileUrl
                            });
                        }
                        AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                        {
                            Files = fileInfos
                        };
                        AI_ModelingDialogueRecord modelingDialogueRecord = new AI_ModelingDialogueRecord()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            StudentId = input.StudentId,
                            Ask = aIDialogueASKDto.ToJsonString(),
                            Answer = "---",
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsBackups = false
                        };
                        await DBSqlSugar.Insertable(modelingDialogueRecord).ExecuteCommandAsync();

                        _agentCommonCacheService.DelRedisLock(key);
                        return new StudentModelingAssessOutput()
                        {
                            IsStandard = studentDoModelingTask.IsStandard,
                            AssessmentResult = studentDoModelingTask.AssessmentResult
                        };
                    }
                    else
                    {
                        throw new Exception("提交异常!");
                    }
                }
                catch (Exception ex)
                {
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
            else
            {
                throw new BusException("数据处理中!", 801);
            }
        }

        /// <summary>
        /// 学生端建模情景对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task StudentModelingDialogue(StudentModelingDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取建模阶段任务基础信息
                string modelingStageTaskSql = @"SELECT
                                                    	task.Id AS ModelingStageTaskId,
                                                    	task.ModelingStageId,
                                                    	task.ModelingId,
                                                    	task.Demand,
                                                        task.Name,
                                                        task.Target,
                                                        model.Modelkey
                                                    FROM
                                                    	AI_ModelingStageTask task WITH ( NOLOCK )
                                                    	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON task.ModelingId= agent.Id 
                                                    	AND agent.IsDeleted= 0
                                                    	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                    	AND agentBase.IsDeleted= 0 
                                                        INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	    AND model.IsDeleted= 0
                                                    WHERE
                                                    	task.IsDeleted= 0 
                                                    	AND task.TaskType= 2 
                                                    	AND task.Id= @modelingStageTaskId";
                ModelingStageTaskBaseInfoDto modelingStageTask = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                    .AddParameters(new { modelingStageTaskId = input.ModelingStageTaskId }).FirstAsync();
                if (modelingStageTask == null)
                {
                    throw new BusException("建模阶段任务Id异常!");
                }
                if (string.IsNullOrEmpty(modelingStageTask.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                //获取豆包上下文缓存Id
                bool isCreate = false;
                bool isTimeOut = false;
                AI_ModelingContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ModelingContextCacheKey>()
                    .Where(p => p.ModelingId == modelingStageTask.ModelingId
                    && p.ModelingStageId == modelingStageTask.ModelingStageId
                    && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                    && p.StudentId == input.StudentId
                    && p.IsBackups == false
                    && p.IsDeleted == false)
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (contextCacheKey != null)
                {
                    //验证是否过期
                    if (contextCacheKey.ExpirationTime <= DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                        isTimeOut = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                //创建上下文缓存
                if (isCreate)
                {
                    //获取建模阶段任务的高频问题
                    List<AI_ModelingStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ModelingStageTaskQuestion>()
                        .Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.IsDeleted == false)
                        .Select(p => new AI_ModelingStageTaskQuestion()
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Describe = p.Describe
                        }).ToListAsync();
                    string taskQuestionsText = string.Empty;
                    foreach (var taskQuestion in taskQuestions)
                    {
                        taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                    }
                    if (string.IsNullOrEmpty(taskQuestionsText))
                    {
                        taskQuestionsText = "暂无";
                    }

                    //获取建模情景对话系统指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentModelingDialogue_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null || string.IsNullOrEmpty(directive.Directive))
                    {
                        throw new BusException("无法获取建模情景对话系统指令,请联系管理员!");
                    }

                    string directiveStr = directive.Directive.Replace("{任务名称}", modelingStageTask.Name)
                        .Replace("{任务目标}", modelingStageTask.Target)
                        .Replace("{主题}", taskQuestionsText);
                    if (string.IsNullOrEmpty(modelingStageTask.Demand))
                    {
                        directiveStr = directiveStr.Replace("{情景对话要求}", "暂无");
                    }
                    else
                    {
                        directiveStr = directiveStr.Replace("{情景对话要求}", modelingStageTask.Demand);
                    }

                    //创建上下文缓存
                    string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                    {
                        Msg = directiveStr,
                        TimeOut = 604800,
                        modelId = modelingStageTask.Modelkey
                    });
                    if (string.IsNullOrEmpty(contextId))
                    {
                        throw new Exception("创建上下文缓存异常!");
                    }

                    contextCacheKey = new AI_ModelingContextCacheKey()
                    {
                        Id = IdHelper.GetId(),
                        ModelingId = modelingStageTask.ModelingId,
                        ModelingStageId = modelingStageTask.ModelingStageId,
                        ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                        StudentId = input.StudentId,
                        CacheId = contextId,
                        TimeOut = 604800,
                        ExpirationTime = DateTime.Now.AddSeconds(604800),
                        CreateTime = DateTime.Now,
                        Creator = input.StudentId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.StudentId,
                        IsDeleted = false,
                        IsBackups = false
                    };
                    await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                }

                //缓存过期逻辑
                if (isTimeOut)
                {
                    //获取历史对话记录
                    List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId
                        && p.ModelingStageId == modelingStageTask.ModelingStageId
                        && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                        && p.StudentId == input.StudentId
                        && p.IsBackups == false
                        && p.IsDeleted == false)
                        .OrderBy(p => p.CreateTime)
                        .With(SqlWith.NoLock)
                        .ToListAsync();
                    if (dialogueContentRecords.Count > 0)
                    {
                        //历史对话记录
                        string dialogueData = "学生历史问答记录如下:\n";
                        foreach (var dialogueContentRecord in dialogueContentRecords)
                        {
                            AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                            dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                             + $"学生问:{ask.AskText}。\n"
                                             + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                        }
                        dialogueData += "要求：后续学生问答结合历史记录产出你的回答。（注意:当前对话你只需要输出'收到'）";

                        //上下文对话（历史对话记录）
                        Func<string, Task> emptyDataHandler = async (data) =>
                        {
                            await Task.CompletedTask;
                        };
                        await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                        {
                            context_id = contextCacheKey.CacheId,
                            content = dialogueData,
                            role = "user",
                            modelId = modelingStageTask.Modelkey
                        }, async (data) =>
                        {
                            await emptyDataHandler(data);
                        });

                        //更新过期时间
                        contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }
                }

                //上下文对话
                string msg = string.Empty;
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = input.Msg,
                    role = "user",
                    modelId = modelingStageTask.Modelkey
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);

                    string json = data;
                    int startIndex = json.IndexOf('{');
                    json = json.Substring(startIndex);
                    JObject jsonObject = JObject.Parse(json);
                    string content = jsonObject["Content"]?.Value<string>();
                    if (content != null)
                    {
                        msg += content;
                    }
                }, cancellationToken);

                //更新过期时间
                contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                //保存会话记录
                AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                {
                    AskText = input.Msg,
                    AudioFile = new AIDialogueASKAudioFileInfo()
                    {
                        AudioUrl = input.AudioUrl,
                        Duration = input.Duration,
                    }
                };
                AI_ModelingDialogueRecord modelingDialogueRecord = new AI_ModelingDialogueRecord()
                {
                    Id = IdHelper.GetId(),
                    ModelingId = modelingStageTask.ModelingId,
                    ModelingStageId = modelingStageTask.ModelingStageId,
                    ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                    StudentId = input.StudentId,
                    Ask = aIDialogueASKDto.ToJsonString(),
                    Answer = msg,
                    CreateTime = DateTime.Now,
                    Creator = input.StudentId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.StudentId,
                    IsDeleted = false,
                    IsBackups = false
                };
                await DBSqlSugar.Insertable(modelingDialogueRecord).ExecuteCommandAsync();

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
        }

        /// <summary>
        /// 学生端建模情景对话提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentModelingDialogueSubmitOutput> StudentModelingDialogueSubmit(StudentModelingDialogueSubmitInput input)
        {
            //缓存锁
            string key = input.StudentId + "|" + input.ModelingStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    //是否存在达标提交记录
                    AI_StudentDoModelingTask isDo = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == input.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsStandard == true && p.IsDeleted == false).FirstAsync();
                    if (isDo != null)
                    {
                        throw new BusException("已完成，无法重复提交!", 801);
                    }

                    //获取建模阶段任务基础信息
                    string modelingStageTaskSql = @"SELECT
                                                    	task.Id AS ModelingStageTaskId,
                                                    	task.ModelingStageId,
                                                    	task.ModelingId,
                                                    	task.RoleSetting,
                                                    	task.Demand,
                                                        task.ScoreStandard,
                                                	    model.Modelkey,
                                                        task.TaskIsSubmit,
                                                        task.TaskIsAssessment,
                                                        task.TaskAssessmentScore,
                                                        task.GroupIsSubmit,
                                                        task.GroupIsAssessment,
                                                        task.GroupAssessmentScore,
                                                        task.Name,
                                                        task.Target
                                                    FROM
                                                    	AI_ModelingStageTask task WITH ( NOLOCK )
                                                    	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON task.ModelingId= agent.Id 
                                                    	AND agent.IsDeleted= 0
                                                    	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                    	AND agentBase.IsDeleted= 0 
                                                        INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	    AND model.IsDeleted= 0
                                                    WHERE
                                                    	task.IsDeleted= 0 
                                                    	AND task.TaskType= 2 
                                                    	AND task.Id= @modelingStageTaskId";
                    ModelingStageTaskBaseInfoDto modelingStageTask = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                        .AddParameters(new { modelingStageTaskId = input.ModelingStageTaskId }).FirstAsync();
                    if (modelingStageTask == null)
                    {
                        throw new BusException("建模阶段任务Id异常!");
                    }
                    if (string.IsNullOrEmpty(modelingStageTask.Modelkey))
                    {
                        throw new BusException("智能体配置的模型异常!");
                    }

                    //获取豆包上下文缓存Id
                    bool isCreate = false;
                    bool isTimeOut = false;
                    AI_ModelingContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ModelingContextCacheKey>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId
                        && p.ModelingStageId == modelingStageTask.ModelingStageId
                        && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                        && p.StudentId == input.StudentId
                        && p.IsBackups == false
                        && p.IsDeleted == false)
                        .With(SqlWith.NoLock)
                        .FirstAsync();
                    if (contextCacheKey != null)
                    {
                        //验证是否过期
                        if (contextCacheKey.ExpirationTime <= DateTime.Now)
                        {
                            contextCacheKey.IsDeleted = true;
                            await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                            isCreate = true;
                            isTimeOut = true;
                        }
                    }
                    else
                    {
                        isCreate = true;
                    }

                    //创建上下文缓存
                    if (isCreate)
                    {
                        //获取建模阶段任务的高频问题
                        List<AI_ModelingStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ModelingStageTaskQuestion>()
                            .Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.IsDeleted == false)
                            .Select(p => new AI_ModelingStageTaskQuestion()
                            {
                                Id = p.Id,
                                Name = p.Name,
                                Describe = p.Describe
                            }).ToListAsync();
                        string taskQuestionsText = string.Empty;
                        foreach (var taskQuestion in taskQuestions)
                        {
                            taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                        }
                        if (string.IsNullOrEmpty(taskQuestionsText))
                        {
                            taskQuestionsText = "暂无";
                        }

                        //获取建模情景对话系统指令
                        AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentModelingDialogue_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                        if (directive == null || string.IsNullOrEmpty(directive.Directive))
                        {
                            throw new BusException("无法获取建模情景对话系统指令,请联系管理员!");
                        }

                        string directiveStr = directive.Directive.Replace("{任务名称}", modelingStageTask.Name)
                            .Replace("{任务目标}", modelingStageTask.Target)
                            .Replace("{主题}", taskQuestionsText);
                        if (string.IsNullOrEmpty(modelingStageTask.Demand))
                        {
                            directiveStr = directiveStr.Replace("{情景对话要求}", "暂无");
                        }
                        else
                        {
                            directiveStr = directiveStr.Replace("{情景对话要求}", modelingStageTask.Demand);
                        }

                        //创建上下文缓存
                        string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                        {
                            Msg = directiveStr,
                            TimeOut = 604800,
                            modelId = modelingStageTask.Modelkey
                        });
                        if (string.IsNullOrEmpty(contextId))
                        {
                            throw new Exception("创建上下文缓存异常!");
                        }

                        contextCacheKey = new AI_ModelingContextCacheKey()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            StudentId = input.StudentId,
                            CacheId = contextId,
                            TimeOut = 604800,
                            ExpirationTime = DateTime.Now.AddSeconds(604800),
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsBackups = false
                        };
                        await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                    }

                    //获取提交指令
                    AI_Directive submitDirective = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentModelingDialogueSubmit_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (submitDirective == null)
                    {
                        throw new BusException("无法获取提交系统提示词,请联系管理员!");
                    }

                    //提交信息
                    string submitMsg = submitDirective.Directive;
                    submitMsg = submitMsg.Replace("{评分标准}", modelingStageTask.ScoreStandard)
                        .Replace("{评估角色设定}", modelingStageTask.RoleSetting);

                    //缓存过期逻辑
                    if (isTimeOut)
                    {
                        //获取历史对话记录
                        List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                            .Where(p => p.ModelingId == modelingStageTask.ModelingId
                            && p.ModelingStageId == modelingStageTask.ModelingStageId
                            && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                            && p.StudentId == input.StudentId
                            && p.IsBackups == false
                            && p.IsDeleted == false)
                            .OrderBy(p => p.CreateTime)
                            .With(SqlWith.NoLock)
                            .ToListAsync();
                        if (dialogueContentRecords.Count > 0)
                        {
                            //历史对话记录
                            string dialogueData = "学生历史问答记录如下:\n";
                            foreach (var dialogueContentRecord in dialogueContentRecords)
                            {
                                AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                                dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                                 + $"学生问:{ask.AskText}。\n"
                                                 + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                            }
                            submitMsg = submitMsg + dialogueData;
                        }
                    }

                    //上下文对话
                    string resultData = string.Empty;
                    Func<string, Task> emptyDataHandler = async (data) =>
                    {
                        await Task.CompletedTask;
                    };
                    await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                    {
                        context_id = contextCacheKey.CacheId,
                        content = submitMsg,
                        role = "user",
                        modelId = modelingStageTask.Modelkey
                    }, async (data) =>
                    {
                        await emptyDataHandler(data);

                        string json = data;
                        int startIndex = json.IndexOf('{');
                        json = json.Substring(startIndex);
                        JObject jsonObject = JObject.Parse(json);
                        string content = jsonObject["Content"]?.Value<string>();
                        if (content != null)
                        {
                            resultData += content;
                        }
                    });

                    //豆包输出
                    StudentModelingSubmitDouBaoOutput dialogueSubmitDouBaoOutput = StudentModelingSubmitAnalysisJson(resultData);
                    if (dialogueSubmitDouBaoOutput != null)
                    {
                        //保存提交记录
                        AI_StudentDoModelingTask studentDoModelingTask = new AI_StudentDoModelingTask()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            Score = dialogueSubmitDouBaoOutput.Score,
                            Level = BusinessUtil.GetAvgLevel(dialogueSubmitDouBaoOutput.Score),
                            AssessmentResult = !string.IsNullOrEmpty(dialogueSubmitDouBaoOutput.AssessmentResult) ? dialogueSubmitDouBaoOutput.AssessmentResult : "暂无评估结果!",
                            StudentId = input.StudentId,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsStandard = true
                        };
                        studentDoModelingTask.Order = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsDeleted == false).CountAsync() + 1;

                        //验证是否达标
                        // 1.任务评估
                        bool taskPass = modelingStageTask.TaskIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.TaskAssessmentScore : true;
                        // 2.组件评估
                        bool groupPass = modelingStageTask.GroupIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.GroupAssessmentScore : true;
                        // 3. 两者同时满足才算达标
                        studentDoModelingTask.IsStandard = taskPass == true && groupPass == true;
                        await DBSqlSugar.Insertable(studentDoModelingTask).ExecuteCommandAsync();

                        //高频主题保存
                        if (dialogueSubmitDouBaoOutput.QuestionIds != null && dialogueSubmitDouBaoOutput.QuestionIds.Count > 0)
                        {
                            List<AI_StudentDoModelingTaskQuestion> studentDoModelingTaskQuestions = new List<AI_StudentDoModelingTaskQuestion>();
                            foreach (var questionId in dialogueSubmitDouBaoOutput.QuestionIds)
                            {
                                studentDoModelingTaskQuestions.Add(new AI_StudentDoModelingTaskQuestion()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingId = modelingStageTask.ModelingId,
                                    ModelingStageId = modelingStageTask.ModelingStageId,
                                    ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                                    StudentId = input.StudentId,
                                    QuestionId = questionId,
                                    StudentDoModelingTaskId = studentDoModelingTask.Id,
                                    CreateTime = DateTime.Now,
                                    Creator = input.StudentId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.StudentId,
                                    IsDeleted = false
                                });
                            }
                            await DBSqlSugar.Insertable(studentDoModelingTaskQuestions).ExecuteCommandAsync();
                        }

                        _agentCommonCacheService.DelRedisLock(key);
                        return new StudentModelingDialogueSubmitOutput()
                        {
                            IsStandard = studentDoModelingTask.IsStandard,
                            AssessmentResult = studentDoModelingTask.AssessmentResult
                        };
                    }
                    else
                    {
                        throw new Exception("提交异常!");
                    }
                }
                catch (Exception ex)
                {
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
            else
            {
                throw new BusException("数据处理中!", 801);
            }
        }

        /// <summary>
        /// 学生端建模知识问答
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task StudentModelingKnowledge(StudentModelingKnowledgeInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取建模阶段任务基础信息
                string modelingStageTaskSql = @"SELECT
                                                    	task.Id AS ModelingStageTaskId,
                                                    	task.ModelingStageId,
                                                    	task.ModelingId,
                                                    	task.RoleSetting,
                                                    	task.Scope,
                                                        model.Modelkey
                                                    FROM
                                                    	AI_ModelingStageTask task WITH ( NOLOCK )
                                                    	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON task.ModelingId= agent.Id 
                                                    	AND agent.IsDeleted= 0
                                                    	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                    	AND agentBase.IsDeleted= 0 
                                                        INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	    AND model.IsDeleted= 0
                                                    WHERE
                                                    	task.IsDeleted= 0 
                                                    	AND task.TaskType= 3 
                                                    	AND task.Id= @modelingStageTaskId";
                ModelingStageTaskBaseInfoDto modelingStageTask = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                    .AddParameters(new { modelingStageTaskId = input.ModelingStageTaskId }).FirstAsync();
                if (modelingStageTask == null)
                {
                    throw new BusException("建模阶段任务Id异常!");
                }
                if (string.IsNullOrEmpty(modelingStageTask.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                //获取豆包上下文缓存Id
                bool isCreate = false;
                bool isTimeOut = false;
                AI_ModelingContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ModelingContextCacheKey>()
                    .Where(p => p.ModelingId == modelingStageTask.ModelingId
                    && p.ModelingStageId == modelingStageTask.ModelingStageId
                    && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                    && p.StudentId == input.StudentId
                    && p.IsBackups == false
                    && p.IsDeleted == false)
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (contextCacheKey != null)
                {
                    //验证是否过期
                    if (contextCacheKey.ExpirationTime <= DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                        isTimeOut = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                //创建上下文缓存
                if (isCreate)
                {
                    string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                    {
                        Msg = $"一、问答范围:{modelingStageTask.Scope}。",
                        TimeOut = 604800,
                        modelId = modelingStageTask.Modelkey
                    });
                    if (string.IsNullOrEmpty(contextId))
                    {
                        throw new Exception("创建上下文缓存异常!");
                    }

                    contextCacheKey = new AI_ModelingContextCacheKey()
                    {
                        Id = IdHelper.GetId(),
                        ModelingId = modelingStageTask.ModelingId,
                        ModelingStageId = modelingStageTask.ModelingStageId,
                        ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                        StudentId = input.StudentId,
                        CacheId = contextId,
                        TimeOut = 604800,
                        ExpirationTime = DateTime.Now.AddSeconds(604800),
                        CreateTime = DateTime.Now,
                        Creator = input.StudentId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.StudentId,
                        IsDeleted = false,
                        IsBackups = false
                    };
                    await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                }

                //缓存过期逻辑
                if (isTimeOut)
                {
                    //获取历史对话记录
                    List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId
                        && p.ModelingStageId == modelingStageTask.ModelingStageId
                        && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                        && p.StudentId == input.StudentId
                        && p.IsBackups == false
                        && p.IsDeleted == false)
                        .OrderBy(p => p.CreateTime)
                        .With(SqlWith.NoLock)
                        .ToListAsync();
                    if (dialogueContentRecords.Count > 0)
                    {
                        //历史对话记录
                        string dialogueData = "学生历史问答记录如下:\n";
                        foreach (var dialogueContentRecord in dialogueContentRecords)
                        {
                            AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                            dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                             + $"学生问:{ask.AskText}。\n"
                                             + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                        }
                        dialogueData += "要求：后续学生问答结合历史记录产出你的回答。（注意:当前对话你只需要输出'收到'）";

                        //上下文对话（历史对话记录）
                        Func<string, Task> emptyDataHandler = async (data) =>
                        {
                            await Task.CompletedTask;
                        };
                        await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                        {
                            context_id = contextCacheKey.CacheId,
                            content = dialogueData,
                            role = "user",
                            modelId = modelingStageTask.Modelkey
                        }, async (data) =>
                        {
                            await emptyDataHandler(data);
                        });

                        //更新过期时间
                        contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }
                }

                //上下文对话
                string msg = string.Empty;
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = input.Msg,
                    role = "user",
                    modelId = modelingStageTask.Modelkey
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);

                    string json = data;
                    int startIndex = json.IndexOf('{');
                    json = json.Substring(startIndex);
                    JObject jsonObject = JObject.Parse(json);
                    string content = jsonObject["Content"]?.Value<string>();
                    if (content != null)
                    {
                        msg += content;
                    }
                }, cancellationToken);

                //更新过期时间
                contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                //保存会话记录
                AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                {
                    AskText = input.Msg,
                    AudioFile = new AIDialogueASKAudioFileInfo()
                    {
                        AudioUrl = input.AudioUrl,
                        Duration = input.Duration,
                    }
                };
                AI_ModelingDialogueRecord modelingDialogueRecord = new AI_ModelingDialogueRecord()
                {
                    Id = IdHelper.GetId(),
                    ModelingId = modelingStageTask.ModelingId,
                    ModelingStageId = modelingStageTask.ModelingStageId,
                    ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                    StudentId = input.StudentId,
                    Ask = aIDialogueASKDto.ToJsonString(),
                    Answer = msg,
                    CreateTime = DateTime.Now,
                    Creator = input.StudentId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.StudentId,
                    IsDeleted = false,
                    IsBackups = false
                };
                await DBSqlSugar.Insertable(modelingDialogueRecord).ExecuteCommandAsync();

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
        }

        /// <summary>
        /// 学生端建模问题理解
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task StudentModelingComprehend(StudentModelingComprehendInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取建模阶段任务基础信息
                string modelingStageTaskSql = @"SELECT
                                                    	task.Id AS ModelingStageTaskId,
                                                    	task.ModelingStageId,
                                                    	task.ModelingId,
                                                    	task.GuideRoleSetting,
                                                        task.Prologue,
                                                    	task.Demand,
                                                        model.Modelkey
                                                    FROM
                                                    	AI_ModelingStageTask task WITH ( NOLOCK )
                                                    	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON task.ModelingId= agent.Id 
                                                    	AND agent.IsDeleted= 0
                                                    	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                    	AND agentBase.IsDeleted= 0 
                                                        INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	    AND model.IsDeleted= 0
                                                    WHERE
                                                    	task.IsDeleted= 0 
                                                    	AND task.TaskType= 6 
                                                    	AND task.Id= @modelingStageTaskId";
                ModelingStageTaskBaseInfoDto modelingStageTask = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                    .AddParameters(new { modelingStageTaskId = input.ModelingStageTaskId }).FirstAsync();
                if (modelingStageTask == null)
                {
                    throw new BusException("建模阶段任务Id异常!");
                }
                if (string.IsNullOrEmpty(modelingStageTask.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                //获取豆包上下文缓存Id
                bool isCreate = false;
                bool isTimeOut = false;
                AI_ModelingContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ModelingContextCacheKey>()
                    .Where(p => p.ModelingId == modelingStageTask.ModelingId
                    && p.ModelingStageId == modelingStageTask.ModelingStageId
                    && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                    && p.StudentId == input.StudentId
                    && p.IsBackups == false
                    && p.IsDeleted == false)
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (contextCacheKey != null)
                {
                    //验证是否过期
                    if (contextCacheKey.ExpirationTime <= DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                        isTimeOut = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                //创建上下文缓存
                if (isCreate)
                {
                    //获取建模阶段任务的高频问题
                    List<AI_ModelingStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ModelingStageTaskQuestion>()
                        .Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.IsDeleted == false)
                        .Select(p => new AI_ModelingStageTaskQuestion()
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Describe = p.Describe
                        }).ToListAsync();
                    string taskQuestionsText = string.Empty;
                    foreach (var taskQuestion in taskQuestions)
                    {
                        taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                    }
                    if (string.IsNullOrEmpty(taskQuestionsText))
                    {
                        taskQuestionsText = "暂无";
                    }

                    string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                    {
                        Msg = $"一、引导角色设定:{modelingStageTask.GuideRoleSetting}。\n二、对话引导要求:{modelingStageTask.Demand}。\n三、开场白:{modelingStageTask.Prologue}。\n四、主题:{taskQuestionsText}。",
                        TimeOut = 604800,
                        modelId = modelingStageTask.Modelkey
                    });
                    if (string.IsNullOrEmpty(contextId))
                    {
                        throw new Exception("创建上下文缓存异常!");
                    }

                    contextCacheKey = new AI_ModelingContextCacheKey()
                    {
                        Id = IdHelper.GetId(),
                        ModelingId = modelingStageTask.ModelingId,
                        ModelingStageId = modelingStageTask.ModelingStageId,
                        ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                        StudentId = input.StudentId,
                        CacheId = contextId,
                        TimeOut = 604800,
                        ExpirationTime = DateTime.Now.AddSeconds(604800),
                        CreateTime = DateTime.Now,
                        Creator = input.StudentId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.StudentId,
                        IsDeleted = false,
                        IsBackups = false
                    };
                    await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                }

                //缓存过期逻辑
                if (isTimeOut)
                {
                    //获取历史对话记录
                    List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId
                        && p.ModelingStageId == modelingStageTask.ModelingStageId
                        && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                        && p.StudentId == input.StudentId
                        && p.IsBackups == false
                        && p.IsDeleted == false)
                        .OrderBy(p => p.CreateTime)
                        .With(SqlWith.NoLock)
                        .ToListAsync();
                    if (dialogueContentRecords.Count > 0)
                    {
                        //历史对话记录
                        string dialogueData = "学生历史问答记录如下:\n";
                        foreach (var dialogueContentRecord in dialogueContentRecords)
                        {
                            AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                            dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                             + $"学生问:{ask.AskText}。\n"
                                             + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                        }
                        dialogueData += "要求：后续学生问答结合历史记录产出你的回答。（注意:当前对话你只需要输出'收到'）";

                        //上下文对话（历史对话记录）
                        Func<string, Task> emptyDataHandler = async (data) =>
                        {
                            await Task.CompletedTask;
                        };
                        await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                        {
                            context_id = contextCacheKey.CacheId,
                            content = dialogueData,
                            role = "user",
                            modelId = modelingStageTask.Modelkey
                        }, async (data) =>
                        {
                            await emptyDataHandler(data);
                        });

                        //更新过期时间
                        contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }
                }

                //上下文对话
                string msg = string.Empty;
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = input.Msg,
                    role = "user",
                    modelId = modelingStageTask.Modelkey
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);

                    string json = data;
                    int startIndex = json.IndexOf('{');
                    json = json.Substring(startIndex);
                    JObject jsonObject = JObject.Parse(json);
                    string content = jsonObject["Content"]?.Value<string>();
                    if (content != null)
                    {
                        msg += content;
                    }
                }, cancellationToken);

                //更新过期时间
                contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                //保存会话记录
                AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                {
                    AskText = input.Msg,
                    AudioFile = new AIDialogueASKAudioFileInfo()
                    {
                        AudioUrl = input.AudioUrl,
                        Duration = input.Duration,
                    }
                };
                AI_ModelingDialogueRecord modelingDialogueRecord = new AI_ModelingDialogueRecord()
                {
                    Id = IdHelper.GetId(),
                    ModelingId = modelingStageTask.ModelingId,
                    ModelingStageId = modelingStageTask.ModelingStageId,
                    ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                    StudentId = input.StudentId,
                    Ask = aIDialogueASKDto.ToJsonString(),
                    Answer = msg,
                    CreateTime = DateTime.Now,
                    Creator = input.StudentId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.StudentId,
                    IsDeleted = false,
                    IsBackups = false
                };
                await DBSqlSugar.Insertable(modelingDialogueRecord).ExecuteCommandAsync();

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
        }

        /// <summary>
        /// 学生端建模问题理解提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentModelingComprehendSubmitOutput> StudentModelingComprehendSubmit(StudentModelingComprehendSubmitInput input)
        {
            //缓存锁
            string key = input.StudentId + "|" + input.ModelingStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    //是否存在达标提交记录
                    AI_StudentDoModelingTask isDo = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == input.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsStandard == true && p.IsDeleted == false).FirstAsync();
                    if (isDo != null)
                    {
                        throw new BusException("已完成，无法重复提交!", 801);
                    }

                    //获取建模阶段任务基础信息
                    string modelingStageTaskSql = @"SELECT
                                                    	task.Id AS ModelingStageTaskId,
                                                    	task.ModelingStageId,
                                                    	task.ModelingId,
                                                    	task.RoleSetting,
                                                        task.ScoreStandard,
                                                        task.GuideRoleSetting,
                                                        task.Prologue,
                                                    	task.Demand,
                                                	    model.Modelkey,
                                                        task.TaskIsSubmit,
                                                        task.TaskIsAssessment,
                                                        task.TaskAssessmentScore,
                                                        task.GroupIsSubmit,
                                                        task.GroupIsAssessment,
                                                        task.GroupAssessmentScore
                                                    FROM
                                                    	AI_ModelingStageTask task WITH ( NOLOCK )
                                                    	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON task.ModelingId= agent.Id 
                                                    	AND agent.IsDeleted= 0
                                                    	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                    	AND agentBase.IsDeleted= 0 
                                                        INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	    AND model.IsDeleted= 0
                                                    WHERE
                                                    	task.IsDeleted= 0 
                                                    	AND task.TaskType= 6 
                                                    	AND task.Id= @modelingStageTaskId";
                    ModelingStageTaskBaseInfoDto modelingStageTask = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                        .AddParameters(new { modelingStageTaskId = input.ModelingStageTaskId }).FirstAsync();
                    if (modelingStageTask == null)
                    {
                        throw new BusException("建模阶段任务Id异常!");
                    }
                    if (string.IsNullOrEmpty(modelingStageTask.Modelkey))
                    {
                        throw new BusException("智能体配置的模型异常!");
                    }

                    //获取豆包上下文缓存Id
                    bool isCreate = false;
                    bool isTimeOut = false;
                    AI_ModelingContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ModelingContextCacheKey>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId
                        && p.ModelingStageId == modelingStageTask.ModelingStageId
                        && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                        && p.StudentId == input.StudentId
                        && p.IsBackups == false
                        && p.IsDeleted == false)
                        .With(SqlWith.NoLock)
                        .FirstAsync();
                    if (contextCacheKey != null)
                    {
                        //验证是否过期
                        if (contextCacheKey.ExpirationTime <= DateTime.Now)
                        {
                            contextCacheKey.IsDeleted = true;
                            await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                            isCreate = true;
                            isTimeOut = true;
                        }
                    }
                    else
                    {
                        isCreate = true;
                    }

                    //创建上下文缓存
                    if (isCreate)
                    {
                        //获取建模阶段任务的高频问题
                        List<AI_ModelingStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ModelingStageTaskQuestion>()
                            .Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.IsDeleted == false)
                            .Select(p => new AI_ModelingStageTaskQuestion()
                            {
                                Id = p.Id,
                                Name = p.Name,
                                Describe = p.Describe
                            }).ToListAsync();
                        string taskQuestionsText = string.Empty;
                        foreach (var taskQuestion in taskQuestions)
                        {
                            taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                        }
                        if (string.IsNullOrEmpty(taskQuestionsText))
                        {
                            taskQuestionsText = "暂无";
                        }

                        //创建上下文缓存
                        string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                        {
                            Msg = $"一、引导角色设定:{modelingStageTask.GuideRoleSetting}。\n二、对话引导要求:{modelingStageTask.Demand}。\n三、开场白:{modelingStageTask.Prologue}。\n四、主题:{taskQuestionsText}。",
                            TimeOut = 604800,
                            modelId = modelingStageTask.Modelkey
                        });
                        if (string.IsNullOrEmpty(contextId))
                        {
                            throw new Exception("创建上下文缓存异常!");
                        }

                        contextCacheKey = new AI_ModelingContextCacheKey()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            StudentId = input.StudentId,
                            CacheId = contextId,
                            TimeOut = 604800,
                            ExpirationTime = DateTime.Now.AddSeconds(604800),
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsBackups = false
                        };
                        await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                    }

                    //获取提交指令
                    AI_Directive submitDirective = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentModelingComprehendSubmit_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (submitDirective == null)
                    {
                        throw new BusException("无法获取提交系统提示词,请联系管理员!");
                    }

                    //提交信息
                    string submitMsg = submitDirective.Directive;
                    submitMsg = submitMsg.Replace("{评分标准}", modelingStageTask.ScoreStandard).Replace("{评估角色设定}", modelingStageTask.RoleSetting);

                    //缓存过期逻辑
                    if (isTimeOut)
                    {
                        //获取历史对话记录
                        List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                            .Where(p => p.ModelingId == modelingStageTask.ModelingId
                            && p.ModelingStageId == modelingStageTask.ModelingStageId
                            && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                            && p.StudentId == input.StudentId
                            && p.IsBackups == false
                            && p.IsDeleted == false)
                            .OrderBy(p => p.CreateTime)
                            .With(SqlWith.NoLock)
                            .ToListAsync();
                        if (dialogueContentRecords.Count > 0)
                        {
                            //历史对话记录
                            string dialogueData = "学生历史问答记录如下:\n";
                            foreach (var dialogueContentRecord in dialogueContentRecords)
                            {
                                AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                                dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                                 + $"学生问:{ask.AskText}。\n"
                                                 + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                            }
                            submitMsg = submitMsg + dialogueData;
                        }
                    }

                    //上下文对话
                    string resultData = string.Empty;
                    Func<string, Task> emptyDataHandler = async (data) =>
                    {
                        await Task.CompletedTask;
                    };
                    await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                    {
                        context_id = contextCacheKey.CacheId,
                        content = submitMsg,
                        role = "user",
                        modelId = modelingStageTask.Modelkey
                    }, async (data) =>
                    {
                        await emptyDataHandler(data);

                        string json = data;
                        int startIndex = json.IndexOf('{');
                        json = json.Substring(startIndex);
                        JObject jsonObject = JObject.Parse(json);
                        string content = jsonObject["Content"]?.Value<string>();
                        if (content != null)
                        {
                            resultData += content;
                        }
                    });

                    //豆包输出
                    StudentModelingSubmitDouBaoOutput dialogueSubmitDouBaoOutput = StudentModelingSubmitAnalysisJson(resultData);
                    if (dialogueSubmitDouBaoOutput != null)
                    {
                        //保存提交记录
                        AI_StudentDoModelingTask studentDoModelingTask = new AI_StudentDoModelingTask()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            Score = dialogueSubmitDouBaoOutput.Score,
                            Level = BusinessUtil.GetAvgLevel(dialogueSubmitDouBaoOutput.Score),
                            AssessmentResult = !string.IsNullOrEmpty(dialogueSubmitDouBaoOutput.AssessmentResult) ? dialogueSubmitDouBaoOutput.AssessmentResult : "暂无评估结果!",
                            StudentId = input.StudentId,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsStandard = true
                        };
                        studentDoModelingTask.Order = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsDeleted == false).CountAsync() + 1;

                        //验证是否达标
                        // 1.任务评估
                        bool taskPass = modelingStageTask.TaskIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.TaskAssessmentScore : true;
                        // 2.组件评估
                        bool groupPass = modelingStageTask.GroupIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.GroupAssessmentScore : true;
                        // 3. 两者同时满足才算达标
                        studentDoModelingTask.IsStandard = taskPass == true && groupPass == true;
                        await DBSqlSugar.Insertable(studentDoModelingTask).ExecuteCommandAsync();

                        //高频主题保存
                        if (dialogueSubmitDouBaoOutput.QuestionIds != null && dialogueSubmitDouBaoOutput.QuestionIds.Count > 0)
                        {
                            List<AI_StudentDoModelingTaskQuestion> studentDoModelingTaskQuestions = new List<AI_StudentDoModelingTaskQuestion>();
                            foreach (var questionId in dialogueSubmitDouBaoOutput.QuestionIds)
                            {
                                studentDoModelingTaskQuestions.Add(new AI_StudentDoModelingTaskQuestion()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingId = modelingStageTask.ModelingId,
                                    ModelingStageId = modelingStageTask.ModelingStageId,
                                    ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                                    StudentId = input.StudentId,
                                    QuestionId = questionId,
                                    StudentDoModelingTaskId = studentDoModelingTask.Id,
                                    CreateTime = DateTime.Now,
                                    Creator = input.StudentId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.StudentId,
                                    IsDeleted = false
                                });
                            }
                            await DBSqlSugar.Insertable(studentDoModelingTaskQuestions).ExecuteCommandAsync();
                        }

                        _agentCommonCacheService.DelRedisLock(key);
                        return new StudentModelingComprehendSubmitOutput()
                        {
                            IsStandard = studentDoModelingTask.IsStandard,
                            AssessmentResult = studentDoModelingTask.AssessmentResult
                        };
                    }
                    else
                    {
                        throw new Exception("提交异常!");
                    }
                }
                catch (Exception ex)
                {
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
            else
            {
                throw new BusException("数据处理中!", 801);
            }
        }

        /// <summary>
        /// 学生端建模构建图片识别
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<StudentModelingStructureImgOcrOutput> StudentModelingStructureImgOcr(StudentModelingStructureImgOcrInput input)
        {
            try
            {
                //获取识别指令
                AI_Directive ocrDirective = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentModelingStructureImgOcr_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                if (ocrDirective == null)
                {
                    throw new BusException("无法获取系统提示词,请联系管理员!");
                }

                //获取视觉理解模型Key
                string model = await DBSqlSugar.Queryable<AI_ModelConfig>().Where(p => p.SceneType == 7).Select(p => p.Model).FirstAsync();
                if (string.IsNullOrEmpty(model))
                {
                    throw new BusException("模型配置异常，请联系管理员!");
                }

                //对话
                Func<string, Task> emptyDataHandler = async (data) =>
                {
                    await Task.CompletedTask;
                };
                string resData = string.Empty;
                await _agentCommonService.AIDialogue(new AIDialogueInput()
                {
                    ModelKey = model,
                    content = new List<AIDialogueDouBaoMessageContent>()
                    {
                          new AIDialogueDouBaoMessageContent()
                          {
                              type="text",
                              text=ocrDirective.Directive
                          },
                          new AIDialogueDouBaoMessageContent()
                          {
                              type="image_url",
                              image_url=new AIDialogueDouBaoMessageContentImageUrl()
                              {
                                  url=input.ImgUrl
                              }
                          }
                     },
                }, async (data) =>
                {
                    await emptyDataHandler(data);
                    string json = data;
                    int startIndex = json.IndexOf('{');
                    json = json.Substring(startIndex);
                    JObject jsonObject = JObject.Parse(json);
                    string content = jsonObject["Content"]?.Value<string>();
                    if (content != null)
                    {
                        resData += content;
                    }
                });

                return StudentModelingStructureImgOcrAnalysisJson(resData);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 学生端建模模型构建提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentModelingStructureSubmitOutput> StudentModelingStructureSubmit(StudentModelingStructureSubmitInput input)
        {
            //缓存锁
            string key = input.StudentId + "|" + input.ModelingStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    //是否存在达标提交记录
                    AI_StudentDoModelingTask isDo = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == input.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsStandard == true && p.IsDeleted == false).FirstAsync();
                    if (isDo != null)
                    {
                        throw new BusException("已完成，无法重复提交!", 801);
                    }

                    //获取建模阶段任务基础信息
                    string modelingStageTaskSql = @"SELECT
                                                    	task.Id AS ModelingStageTaskId,
                                                    	task.ModelingStageId,
                                                    	task.ModelingId,
                                                    	task.RoleSetting,
                                                        task.ScoreStandard,
                                                	    model.Modelkey,
                                                        task.TaskIsSubmit,
                                                        task.TaskIsAssessment,
                                                        task.TaskAssessmentScore,
                                                        task.GroupIsSubmit,
                                                        task.GroupIsAssessment,
                                                        task.GroupAssessmentScore
                                                    FROM
                                                    	AI_ModelingStageTask task WITH ( NOLOCK )
                                                    	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON task.ModelingId= agent.Id 
                                                    	AND agent.IsDeleted= 0
                                                    	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                    	AND agentBase.IsDeleted= 0 
                                                        INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	    AND model.IsDeleted= 0
                                                    WHERE
                                                    	task.IsDeleted= 0 
                                                    	AND task.TaskType= 7 
                                                    	AND task.Id= @modelingStageTaskId";
                    ModelingStageTaskBaseInfoDto modelingStageTask = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                        .AddParameters(new { modelingStageTaskId = input.ModelingStageTaskId }).FirstAsync();
                    if (modelingStageTask == null)
                    {
                        throw new BusException("建模阶段任务Id异常!");
                    }
                    if (string.IsNullOrEmpty(modelingStageTask.Modelkey))
                    {
                        throw new BusException("智能体配置的模型异常!");
                    }

                    //获取提交指令
                    AI_Directive submitDirective = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentModelingStructureSubmit_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (submitDirective == null)
                    {
                        throw new BusException("无法获取提交系统提示词,请联系管理员!");
                    }

                    //提交信息
                    string submitMsg = submitDirective.Directive;
                    submitMsg = submitMsg.Replace("{评分标准}", modelingStageTask.ScoreStandard)
                        .Replace("{评估角色设定}", modelingStageTask.RoleSetting)
                        .Replace("{变量定义}", input.Variable)
                        .Replace("{关系表达式}", input.Expression);

                    //提交
                    string resultData = string.Empty;
                    Func<string, Task> emptyDataHandler = async (data) =>
                    {
                        await Task.CompletedTask;
                    };
                    await _agentCommonService.AIDialogue(new AIDialogueInput()
                    {
                        ModelKey = modelingStageTask.Modelkey,
                        content = new List<AIDialogueDouBaoMessageContent>()
                        {
                          new AIDialogueDouBaoMessageContent()
                          {
                              type="text",
                              text=submitMsg
                          }
                        }
                    }, async (data) =>
                    {
                        await emptyDataHandler(data);

                        string json = data;
                        int startIndex = json.IndexOf('{');
                        json = json.Substring(startIndex);
                        JObject jsonObject = JObject.Parse(json);
                        string content = jsonObject["Content"]?.Value<string>();
                        if (content != null)
                        {
                            resultData += content;
                        }
                    });

                    //豆包输出
                    StudentModelingSubmitDouBaoOutput dialogueSubmitDouBaoOutput = StudentModelingSubmitAnalysisJson(resultData);
                    if (dialogueSubmitDouBaoOutput != null)
                    {
                        //保存提交记录
                        AI_StudentDoModelingTask studentDoModelingTask = new AI_StudentDoModelingTask()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            Score = dialogueSubmitDouBaoOutput.Score,
                            Level = BusinessUtil.GetAvgLevel(dialogueSubmitDouBaoOutput.Score),
                            AssessmentResult = !string.IsNullOrEmpty(dialogueSubmitDouBaoOutput.AssessmentResult) ? dialogueSubmitDouBaoOutput.AssessmentResult : "暂无评估结果!",
                            StudentId = input.StudentId,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsStandard = true
                        };
                        studentDoModelingTask.Order = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsDeleted == false).CountAsync() + 1;

                        //验证是否达标
                        // 1.任务评估
                        bool taskPass = modelingStageTask.TaskIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.TaskAssessmentScore : true;
                        // 2.组件评估
                        bool groupPass = modelingStageTask.GroupIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.GroupAssessmentScore : true;
                        // 3. 两者同时满足才算达标
                        studentDoModelingTask.IsStandard = taskPass == true && groupPass == true;
                        await DBSqlSugar.Insertable(studentDoModelingTask).ExecuteCommandAsync();

                        //高频主题保存
                        if (dialogueSubmitDouBaoOutput.QuestionIds != null && dialogueSubmitDouBaoOutput.QuestionIds.Count > 0)
                        {
                            List<AI_StudentDoModelingTaskQuestion> studentDoModelingTaskQuestions = new List<AI_StudentDoModelingTaskQuestion>();
                            foreach (var questionId in dialogueSubmitDouBaoOutput.QuestionIds)
                            {
                                studentDoModelingTaskQuestions.Add(new AI_StudentDoModelingTaskQuestion()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingId = modelingStageTask.ModelingId,
                                    ModelingStageId = modelingStageTask.ModelingStageId,
                                    ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                                    StudentId = input.StudentId,
                                    QuestionId = questionId,
                                    StudentDoModelingTaskId = studentDoModelingTask.Id,
                                    CreateTime = DateTime.Now,
                                    Creator = input.StudentId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.StudentId,
                                    IsDeleted = false
                                });
                            }
                            await DBSqlSugar.Insertable(studentDoModelingTaskQuestions).ExecuteCommandAsync();
                        }

                        //保存模型构建
                        AI_StudentModelingStructure studentModelingStructure = new AI_StudentModelingStructure()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            StudentDoModelingTaskId = studentDoModelingTask.Id,
                            StudentId = input.StudentId,
                            Variable = input.Variable,
                            Expression = input.Expression,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsOptimize = false
                        };
                        studentModelingStructure.Order = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                            .Where(p => p.ModelingId == modelingStageTask.ModelingId
                            && p.ModelingStageId == modelingStageTask.ModelingStageId
                            && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                            && p.StudentId == input.StudentId
                            && p.IsDeleted == false
                            && p.IsOptimize == false).CountAsync() + 1;
                        await DBSqlSugar.Insertable(studentModelingStructure).ExecuteCommandAsync();

                        //保存对话记录（用于验证）
                        AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                        {
                            AskText = "学生端建模模型构建提交记录（用于验证）"
                        };
                        AI_ModelingDialogueRecord modelingDialogueRecord = new AI_ModelingDialogueRecord()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            StudentId = input.StudentId,
                            Ask = aIDialogueASKDto.ToJsonString(),
                            Answer = "学生端建模模型构建提交记录（用于验证）",
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsBackups = false
                        };
                        await DBSqlSugar.Insertable(modelingDialogueRecord).ExecuteCommandAsync();

                        _agentCommonCacheService.DelRedisLock(key);
                        return new StudentModelingStructureSubmitOutput()
                        {
                            IsStandard = studentDoModelingTask.IsStandard,
                            AssessmentResult = studentDoModelingTask.AssessmentResult
                        };
                    }
                    else
                    {
                        throw new Exception("提交异常!");
                    }
                }
                catch (Exception ex)
                {
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
            else
            {
                throw new BusException("数据处理中!", 801);
            }
        }

        /// <summary>
        /// 学生端建模模型假设
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task StudentModelingHypothesis(StudentModelingHypothesisInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取建模阶段任务基础信息
                string modelingStageTaskSql = @"SELECT
                                                    	task.Id AS ModelingStageTaskId,
                                                    	task.ModelingStageId,
                                                    	task.ModelingId,
                                                    	task.GuideRoleSetting,
                                                        task.Prologue,
                                                    	task.Demand,
                                                        model.Modelkey
                                                    FROM
                                                    	AI_ModelingStageTask task WITH ( NOLOCK )
                                                    	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON task.ModelingId= agent.Id 
                                                    	AND agent.IsDeleted= 0
                                                    	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                    	AND agentBase.IsDeleted= 0 
                                                        INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	    AND model.IsDeleted= 0
                                                    WHERE
                                                    	task.IsDeleted= 0 
                                                    	AND task.TaskType= 8 
                                                    	AND task.Id= @modelingStageTaskId";
                ModelingStageTaskBaseInfoDto modelingStageTask = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                    .AddParameters(new { modelingStageTaskId = input.ModelingStageTaskId }).FirstAsync();
                if (modelingStageTask == null)
                {
                    throw new BusException("建模阶段任务Id异常!");
                }
                if (string.IsNullOrEmpty(modelingStageTask.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                //获取豆包上下文缓存Id
                bool isCreate = false;
                bool isTimeOut = false;
                AI_ModelingContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ModelingContextCacheKey>()
                    .Where(p => p.ModelingId == modelingStageTask.ModelingId
                    && p.ModelingStageId == modelingStageTask.ModelingStageId
                    && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                    && p.StudentId == input.StudentId
                    && p.IsBackups == false
                    && p.IsDeleted == false)
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (contextCacheKey != null)
                {
                    //验证是否过期
                    if (contextCacheKey.ExpirationTime <= DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                        isTimeOut = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                //创建上下文缓存
                if (isCreate)
                {
                    //获取建模阶段任务的高频问题
                    List<AI_ModelingStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ModelingStageTaskQuestion>()
                        .Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.IsDeleted == false)
                        .Select(p => new AI_ModelingStageTaskQuestion()
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Describe = p.Describe
                        }).ToListAsync();
                    string taskQuestionsText = string.Empty;
                    foreach (var taskQuestion in taskQuestions)
                    {
                        taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                    }
                    if (string.IsNullOrEmpty(taskQuestionsText))
                    {
                        taskQuestionsText = "暂无";
                    }

                    //获取模型构建
                    List<AI_StudentModelingStructure> modelingStructures = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsOptimize == true)
                        .OrderBy(p => p.Order)
                        .Select(p => new AI_StudentModelingStructure()
                        {
                            Versions = p.Versions,
                            Variable = p.Variable,
                            Expression = p.Expression
                        })
                        .ToListAsync();
                    if (modelingStructures.Count <= 0)
                    {
                        //获取模型构建最新记录并保存
                        AI_StudentModelingStructure modelingStructure = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsOptimize == false)
                        .OrderByDescending(p => p.Order)
                        .FirstAsync();
                        if (modelingStructure != null)
                        {
                            AI_StudentModelingStructure addModelingStructure = new AI_StudentModelingStructure()
                            {
                                Id = IdHelper.GetId(),
                                ModelingId = modelingStructure.ModelingId,
                                ModelingStageId = modelingStructure.ModelingStageId,
                                ModelingStageTaskId = modelingStructure.ModelingStageTaskId,
                                StudentId = modelingStructure.StudentId,
                                Variable = modelingStructure.Variable,
                                Expression = modelingStructure.Expression,
                                CreateTime = DateTime.Now,
                                Creator = modelingStructure.StudentId,
                                ModifyTime = DateTime.Now,
                                Modifier = modelingStructure.StudentId,
                                IsDeleted = false,
                                IsOptimize = true,
                                Order = 1,
                                Versions = "V1"
                            };
                            await DBSqlSugar.Insertable(addModelingStructure).ExecuteCommandAsync();
                            modelingStructures.Add(addModelingStructure);
                        }
                    }
                    string modelingStructureStr = string.Empty;
                    if (modelingStructures.Count > 0)
                    {
                        foreach (var modelingStructure in modelingStructures)
                        {
                            modelingStructureStr += $"{modelingStructures.IndexOf(modelingStructure) + 1}模型版本:{modelingStructure.Versions}。\n变量定义:{modelingStructure.Variable}。\n关系表达式:{modelingStructure.Expression}。\n";
                        }
                    }
                    if (string.IsNullOrEmpty(modelingStructureStr))
                    {
                        modelingStructureStr = "暂无";
                    }
                    modelingStructureStr += "(注意:后续可能会有新版本)";

                    string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                    {
                        Msg = $"一、引导角色设定:{modelingStageTask.GuideRoleSetting}。\n二、对话引导要求:{modelingStageTask.Demand}。\n三、开场白:{modelingStageTask.Prologue}。\n四、主题:{taskQuestionsText}。\n五、模型:{modelingStructureStr}。\n六、统计图表输出格式:统计图表请按照echarts的json格式来输出（注意：格式开头必须是```echarts_json结尾必须是```，只需要输出这个格式的Json不需要输出其它文本）。\n",
                        TimeOut = 604800,
                        modelId = modelingStageTask.Modelkey
                    });
                    if (string.IsNullOrEmpty(contextId))
                    {
                        throw new Exception("创建上下文缓存异常!");
                    }

                    contextCacheKey = new AI_ModelingContextCacheKey()
                    {
                        Id = IdHelper.GetId(),
                        ModelingId = modelingStageTask.ModelingId,
                        ModelingStageId = modelingStageTask.ModelingStageId,
                        ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                        StudentId = input.StudentId,
                        CacheId = contextId,
                        TimeOut = 604800,
                        ExpirationTime = DateTime.Now.AddSeconds(604800),
                        CreateTime = DateTime.Now,
                        Creator = input.StudentId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.StudentId,
                        IsDeleted = false,
                        IsBackups = false
                    };
                    await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                }

                //缓存过期逻辑
                if (isTimeOut)
                {
                    //获取历史对话记录
                    List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId
                        && p.ModelingStageId == modelingStageTask.ModelingStageId
                        && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                        && p.StudentId == input.StudentId
                        && p.IsBackups == false
                        && p.IsDeleted == false)
                        .OrderBy(p => p.CreateTime)
                        .With(SqlWith.NoLock)
                        .ToListAsync();
                    if (dialogueContentRecords.Count > 0)
                    {
                        //历史对话记录
                        string dialogueData = "学生历史问答记录如下:\n";
                        foreach (var dialogueContentRecord in dialogueContentRecords)
                        {
                            AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                            dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                             + $"学生问:{ask.AskText}。\n"
                                             + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                        }
                        dialogueData += "要求：后续学生问答结合历史记录产出你的回答。（注意:当前对话你只需要输出'收到'）";

                        //上下文对话（历史对话记录）
                        Func<string, Task> emptyDataHandler = async (data) =>
                        {
                            await Task.CompletedTask;
                        };
                        await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                        {
                            context_id = contextCacheKey.CacheId,
                            content = dialogueData,
                            role = "user",
                            modelId = modelingStageTask.Modelkey
                        }, async (data) =>
                        {
                            await emptyDataHandler(data);
                        });

                        //更新过期时间
                        contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }
                }

                //上下文对话
                string msg = string.Empty;
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = input.Msg,
                    role = "user",
                    modelId = modelingStageTask.Modelkey
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);

                    string json = data;
                    int startIndex = json.IndexOf('{');
                    json = json.Substring(startIndex);
                    JObject jsonObject = JObject.Parse(json);
                    string content = jsonObject["Content"]?.Value<string>();
                    if (content != null)
                    {
                        msg += content;
                    }
                }, cancellationToken);

                //更新过期时间
                contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                //保存会话记录
                AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                {
                    AskText = input.Msg,
                    AudioFile = new AIDialogueASKAudioFileInfo()
                    {
                        AudioUrl = input.AudioUrl,
                        Duration = input.Duration,
                    }
                };
                AI_ModelingDialogueRecord modelingDialogueRecord = new AI_ModelingDialogueRecord()
                {
                    Id = IdHelper.GetId(),
                    ModelingId = modelingStageTask.ModelingId,
                    ModelingStageId = modelingStageTask.ModelingStageId,
                    ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                    StudentId = input.StudentId,
                    Ask = aIDialogueASKDto.ToJsonString(),
                    Answer = msg,
                    CreateTime = DateTime.Now,
                    Creator = input.StudentId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.StudentId,
                    IsDeleted = false,
                    IsBackups = false
                };
                await DBSqlSugar.Insertable(modelingDialogueRecord).ExecuteCommandAsync();

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
        }

        /// <summary>
        /// 学生端建模模型假设提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentModelingHypothesisSubmitOutput> StudentModelingHypothesisSubmit(StudentModelingHypothesisSubmitInput input)
        {
            //缓存锁
            string key = input.StudentId + "|" + input.ModelingStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    //是否存在达标提交记录
                    AI_StudentDoModelingTask isDo = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == input.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsStandard == true && p.IsDeleted == false).FirstAsync();
                    if (isDo != null)
                    {
                        throw new BusException("已完成，无法重复提交!", 801);
                    }

                    //获取建模阶段任务基础信息
                    string modelingStageTaskSql = @"SELECT
                                                    	task.Id AS ModelingStageTaskId,
                                                    	task.ModelingStageId,
                                                    	task.ModelingId,
                                                    	task.RoleSetting,
                                                        task.ScoreStandard,
                                                        task.GuideRoleSetting,
                                                        task.Prologue,
                                                    	task.Demand,
                                                	    model.Modelkey,
                                                        task.TaskIsSubmit,
                                                        task.TaskIsAssessment,
                                                        task.TaskAssessmentScore,
                                                        task.GroupIsSubmit,
                                                        task.GroupIsAssessment,
                                                        task.GroupAssessmentScore
                                                    FROM
                                                    	AI_ModelingStageTask task WITH ( NOLOCK )
                                                    	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON task.ModelingId= agent.Id 
                                                    	AND agent.IsDeleted= 0
                                                    	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                    	AND agentBase.IsDeleted= 0 
                                                        INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	    AND model.IsDeleted= 0
                                                    WHERE
                                                    	task.IsDeleted= 0 
                                                    	AND task.TaskType= 8 
                                                    	AND task.Id= @modelingStageTaskId";
                    ModelingStageTaskBaseInfoDto modelingStageTask = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                        .AddParameters(new { modelingStageTaskId = input.ModelingStageTaskId }).FirstAsync();
                    if (modelingStageTask == null)
                    {
                        throw new BusException("建模阶段任务Id异常!");
                    }
                    if (string.IsNullOrEmpty(modelingStageTask.Modelkey))
                    {
                        throw new BusException("智能体配置的模型异常!");
                    }

                    //获取豆包上下文缓存Id
                    bool isCreate = false;
                    bool isTimeOut = false;
                    AI_ModelingContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ModelingContextCacheKey>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId
                        && p.ModelingStageId == modelingStageTask.ModelingStageId
                        && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                        && p.StudentId == input.StudentId
                        && p.IsBackups == false
                        && p.IsDeleted == false)
                        .With(SqlWith.NoLock)
                        .FirstAsync();
                    if (contextCacheKey != null)
                    {
                        //验证是否过期
                        if (contextCacheKey.ExpirationTime <= DateTime.Now)
                        {
                            contextCacheKey.IsDeleted = true;
                            await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                            isCreate = true;
                            isTimeOut = true;
                        }
                    }
                    else
                    {
                        isCreate = true;
                    }

                    //创建上下文缓存
                    if (isCreate)
                    {
                        //获取建模阶段任务的高频问题
                        List<AI_ModelingStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ModelingStageTaskQuestion>()
                            .Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.IsDeleted == false)
                            .Select(p => new AI_ModelingStageTaskQuestion()
                            {
                                Id = p.Id,
                                Name = p.Name,
                                Describe = p.Describe
                            }).ToListAsync();
                        string taskQuestionsText = string.Empty;
                        foreach (var taskQuestion in taskQuestions)
                        {
                            taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                        }
                        if (string.IsNullOrEmpty(taskQuestionsText))
                        {
                            taskQuestionsText = "暂无";
                        }

                        //获取模型构建
                        List<AI_StudentModelingStructure> modelingStructures = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                            .Where(p => p.ModelingId == modelingStageTask.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsOptimize == true)
                            .OrderBy(p => p.Order)
                            .Select(p => new AI_StudentModelingStructure()
                            {
                                Versions = p.Versions,
                                Variable = p.Variable,
                                Expression = p.Expression
                            })
                            .ToListAsync();
                        if (modelingStructures.Count <= 0)
                        {
                            //获取模型构建最新记录并保存
                            AI_StudentModelingStructure modelingStructure = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                            .Where(p => p.ModelingId == modelingStageTask.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsOptimize == false)
                            .OrderByDescending(p => p.Order)
                            .FirstAsync();
                            if (modelingStructure != null)
                            {
                                AI_StudentModelingStructure addModelingStructure = new AI_StudentModelingStructure()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingId = modelingStructure.ModelingId,
                                    ModelingStageId = modelingStructure.ModelingStageId,
                                    ModelingStageTaskId = modelingStructure.ModelingStageTaskId,
                                    StudentId = modelingStructure.StudentId,
                                    Variable = modelingStructure.Variable,
                                    Expression = modelingStructure.Expression,
                                    CreateTime = DateTime.Now,
                                    Creator = modelingStructure.StudentId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = modelingStructure.StudentId,
                                    IsDeleted = false,
                                    IsOptimize = true,
                                    Order = 1,
                                    Versions = "V1"
                                };
                                await DBSqlSugar.Insertable(addModelingStructure).ExecuteCommandAsync();
                                modelingStructures.Add(addModelingStructure);
                            }
                        }
                        string modelingStructureStr = string.Empty;
                        if (modelingStructures.Count > 0)
                        {
                            foreach (var modelingStructure in modelingStructures)
                            {
                                modelingStructureStr += $"{modelingStructures.IndexOf(modelingStructure) + 1}模型版本:{modelingStructure.Versions}。\n变量定义:{modelingStructure.Variable}。\n关系表达式:{modelingStructure.Expression}。\n";
                            }
                        }
                        if (string.IsNullOrEmpty(modelingStructureStr))
                        {
                            modelingStructureStr = "暂无";
                        }
                        modelingStructureStr += "(注意:后续可能会有新版本)";

                        string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                        {
                            Msg = $"一、引导角色设定:{modelingStageTask.GuideRoleSetting}。\n二、对话引导要求:{modelingStageTask.Demand}。\n三、开场白:{modelingStageTask.Prologue}。\n四、主题:{taskQuestionsText}。\n五、模型:{modelingStructureStr}。\n六、统计图表输出格式:统计图表请按照echarts的json格式来输出（注意：格式开头必须是```echarts_json结尾必须是```，只需要输出这个格式的Json不需要输出其它文本）。\n",
                            TimeOut = 604800,
                            modelId = modelingStageTask.Modelkey
                        });
                        if (string.IsNullOrEmpty(contextId))
                        {
                            throw new Exception("创建上下文缓存异常!");
                        }

                        contextCacheKey = new AI_ModelingContextCacheKey()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            StudentId = input.StudentId,
                            CacheId = contextId,
                            TimeOut = 604800,
                            ExpirationTime = DateTime.Now.AddSeconds(604800),
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsBackups = false
                        };
                        await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                    }

                    //获取提交指令
                    AI_Directive submitDirective = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentModelingHypothesisSubmit_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (submitDirective == null)
                    {
                        throw new BusException("无法获取提交系统提示词,请联系管理员!");
                    }

                    //提交信息
                    string submitMsg = submitDirective.Directive;
                    submitMsg = submitMsg.Replace("{评分标准}", modelingStageTask.ScoreStandard).Replace("{评估角色设定}", modelingStageTask.RoleSetting);

                    //缓存过期逻辑
                    if (isTimeOut)
                    {
                        //获取历史对话记录
                        List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                            .Where(p => p.ModelingId == modelingStageTask.ModelingId
                            && p.ModelingStageId == modelingStageTask.ModelingStageId
                            && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                            && p.StudentId == input.StudentId
                            && p.IsBackups == false
                            && p.IsDeleted == false)
                            .OrderBy(p => p.CreateTime)
                            .With(SqlWith.NoLock)
                            .ToListAsync();
                        if (dialogueContentRecords.Count > 0)
                        {
                            //历史对话记录
                            string dialogueData = "学生历史问答记录如下:\n";
                            foreach (var dialogueContentRecord in dialogueContentRecords)
                            {
                                AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                                dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                                 + $"学生问:{ask.AskText}。\n"
                                                 + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                            }
                            submitMsg = submitMsg + dialogueData;
                        }
                    }

                    //上下文对话
                    string resultData = string.Empty;
                    Func<string, Task> emptyDataHandler = async (data) =>
                    {
                        await Task.CompletedTask;
                    };
                    await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                    {
                        context_id = contextCacheKey.CacheId,
                        content = submitMsg,
                        role = "user",
                        modelId = modelingStageTask.Modelkey
                    }, async (data) =>
                    {
                        await emptyDataHandler(data);

                        string json = data;
                        int startIndex = json.IndexOf('{');
                        json = json.Substring(startIndex);
                        JObject jsonObject = JObject.Parse(json);
                        string content = jsonObject["Content"]?.Value<string>();
                        if (content != null)
                        {
                            resultData += content;
                        }
                    });

                    //豆包输出
                    StudentModelingSubmitDouBaoOutput dialogueSubmitDouBaoOutput = StudentModelingSubmitAnalysisJson(resultData);
                    if (dialogueSubmitDouBaoOutput != null)
                    {
                        //保存提交记录
                        AI_StudentDoModelingTask studentDoModelingTask = new AI_StudentDoModelingTask()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            Score = dialogueSubmitDouBaoOutput.Score,
                            Level = BusinessUtil.GetAvgLevel(dialogueSubmitDouBaoOutput.Score),
                            AssessmentResult = !string.IsNullOrEmpty(dialogueSubmitDouBaoOutput.AssessmentResult) ? dialogueSubmitDouBaoOutput.AssessmentResult : "暂无评估结果!",
                            StudentId = input.StudentId,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsStandard = true
                        };
                        studentDoModelingTask.Order = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsDeleted == false).CountAsync() + 1;

                        //验证是否达标
                        // 1.任务评估
                        bool taskPass = modelingStageTask.TaskIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.TaskAssessmentScore : true;
                        // 2.组件评估
                        bool groupPass = modelingStageTask.GroupIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.GroupAssessmentScore : true;
                        // 3. 两者同时满足才算达标
                        studentDoModelingTask.IsStandard = taskPass == true && groupPass == true;
                        await DBSqlSugar.Insertable(studentDoModelingTask).ExecuteCommandAsync();

                        //高频主题保存
                        if (dialogueSubmitDouBaoOutput.QuestionIds != null && dialogueSubmitDouBaoOutput.QuestionIds.Count > 0)
                        {
                            List<AI_StudentDoModelingTaskQuestion> studentDoModelingTaskQuestions = new List<AI_StudentDoModelingTaskQuestion>();
                            foreach (var questionId in dialogueSubmitDouBaoOutput.QuestionIds)
                            {
                                studentDoModelingTaskQuestions.Add(new AI_StudentDoModelingTaskQuestion()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingId = modelingStageTask.ModelingId,
                                    ModelingStageId = modelingStageTask.ModelingStageId,
                                    ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                                    StudentId = input.StudentId,
                                    QuestionId = questionId,
                                    StudentDoModelingTaskId = studentDoModelingTask.Id,
                                    CreateTime = DateTime.Now,
                                    Creator = input.StudentId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.StudentId,
                                    IsDeleted = false
                                });
                            }
                            await DBSqlSugar.Insertable(studentDoModelingTaskQuestions).ExecuteCommandAsync();
                        }

                        _agentCommonCacheService.DelRedisLock(key);
                        return new StudentModelingHypothesisSubmitOutput()
                        {
                            IsStandard = studentDoModelingTask.IsStandard,
                            AssessmentResult = studentDoModelingTask.AssessmentResult
                        };
                    }
                    else
                    {
                        throw new Exception("提交异常!");
                    }
                }
                catch (Exception ex)
                {
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
            else
            {
                throw new BusException("数据处理中!", 801);
            }
        }

        /// <summary>
        /// 学生端建模模型评价
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task StudentModelingEvaluate(StudentModelingEvaluateInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取建模阶段任务基础信息
                string modelingStageTaskSql = @"SELECT
                                                    	task.Id AS ModelingStageTaskId,
                                                    	task.ModelingStageId,
                                                    	task.ModelingId,
                                                    	task.GuideRoleSetting,
                                                        task.Prologue,
                                                    	task.Demand,
                                                        model.Modelkey
                                                    FROM
                                                    	AI_ModelingStageTask task WITH ( NOLOCK )
                                                    	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON task.ModelingId= agent.Id 
                                                    	AND agent.IsDeleted= 0
                                                    	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                    	AND agentBase.IsDeleted= 0 
                                                        INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	    AND model.IsDeleted= 0
                                                    WHERE
                                                    	task.IsDeleted= 0 
                                                    	AND task.TaskType= 9 
                                                    	AND task.Id= @modelingStageTaskId";
                ModelingStageTaskBaseInfoDto modelingStageTask = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                    .AddParameters(new { modelingStageTaskId = input.ModelingStageTaskId }).FirstAsync();
                if (modelingStageTask == null)
                {
                    throw new BusException("建模阶段任务Id异常!");
                }
                if (string.IsNullOrEmpty(modelingStageTask.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                //获取豆包上下文缓存Id
                bool isCreate = false;
                bool isTimeOut = false;
                AI_ModelingContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ModelingContextCacheKey>()
                    .Where(p => p.ModelingId == modelingStageTask.ModelingId
                    && p.ModelingStageId == modelingStageTask.ModelingStageId
                    && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                    && p.StudentId == input.StudentId
                    && p.IsBackups == false
                    && p.IsDeleted == false)
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (contextCacheKey != null)
                {
                    //验证是否过期
                    if (contextCacheKey.ExpirationTime <= DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                        isTimeOut = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                //创建上下文缓存
                if (isCreate)
                {
                    //获取建模阶段任务的高频问题
                    List<AI_ModelingStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ModelingStageTaskQuestion>()
                        .Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.IsDeleted == false)
                        .Select(p => new AI_ModelingStageTaskQuestion()
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Describe = p.Describe
                        }).ToListAsync();
                    string taskQuestionsText = string.Empty;
                    foreach (var taskQuestion in taskQuestions)
                    {
                        taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                    }
                    if (string.IsNullOrEmpty(taskQuestionsText))
                    {
                        taskQuestionsText = "暂无";
                    }

                    //获取模型构建
                    List<AI_StudentModelingStructure> modelingStructures = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsOptimize == true)
                        .OrderBy(p => p.Order)
                        .Select(p => new AI_StudentModelingStructure()
                        {
                            Versions = p.Versions,
                            Variable = p.Variable,
                            Expression = p.Expression
                        })
                        .ToListAsync();
                    if (modelingStructures.Count <= 0)
                    {
                        //获取模型构建最新记录并保存
                        AI_StudentModelingStructure modelingStructure = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsOptimize == false)
                        .OrderByDescending(p => p.Order)
                        .FirstAsync();
                        if (modelingStructure != null)
                        {
                            AI_StudentModelingStructure addModelingStructure = new AI_StudentModelingStructure()
                            {
                                Id = IdHelper.GetId(),
                                ModelingId = modelingStructure.ModelingId,
                                ModelingStageId = modelingStructure.ModelingStageId,
                                ModelingStageTaskId = modelingStructure.ModelingStageTaskId,
                                StudentId = modelingStructure.StudentId,
                                Variable = modelingStructure.Variable,
                                Expression = modelingStructure.Expression,
                                CreateTime = DateTime.Now,
                                Creator = modelingStructure.StudentId,
                                ModifyTime = DateTime.Now,
                                Modifier = modelingStructure.StudentId,
                                IsDeleted = false,
                                IsOptimize = true,
                                Order = 1,
                                Versions = "V1"
                            };
                            await DBSqlSugar.Insertable(addModelingStructure).ExecuteCommandAsync();
                            modelingStructures.Add(addModelingStructure);
                        }
                    }
                    string modelingStructureStr = string.Empty;
                    if (modelingStructures.Count > 0)
                    {
                        foreach (var modelingStructure in modelingStructures)
                        {
                            modelingStructureStr += $"{modelingStructures.IndexOf(modelingStructure) + 1}模型版本:{modelingStructure.Versions}。\n变量定义:{modelingStructure.Variable}。\n关系表达式:{modelingStructure.Expression}。\n";
                        }
                    }
                    if (string.IsNullOrEmpty(modelingStructureStr))
                    {
                        modelingStructureStr = "暂无";
                    }
                    modelingStructureStr += "(注意:后续可能会有新版本)。";

                    string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                    {
                        Msg = $"一、引导角色设定:{modelingStageTask.GuideRoleSetting}。\n二、对话引导要求:{modelingStageTask.Demand}。\n三、开场白:{modelingStageTask.Prologue}。\n四、主题:{taskQuestionsText}。\n五、模型:{modelingStructureStr}",
                        TimeOut = 604800,
                        modelId = modelingStageTask.Modelkey
                    });
                    if (string.IsNullOrEmpty(contextId))
                    {
                        throw new Exception("创建上下文缓存异常!");
                    }

                    contextCacheKey = new AI_ModelingContextCacheKey()
                    {
                        Id = IdHelper.GetId(),
                        ModelingId = modelingStageTask.ModelingId,
                        ModelingStageId = modelingStageTask.ModelingStageId,
                        ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                        StudentId = input.StudentId,
                        CacheId = contextId,
                        TimeOut = 604800,
                        ExpirationTime = DateTime.Now.AddSeconds(604800),
                        CreateTime = DateTime.Now,
                        Creator = input.StudentId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.StudentId,
                        IsDeleted = false,
                        IsBackups = false
                    };
                    await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                }

                //缓存过期逻辑
                if (isTimeOut)
                {
                    //获取历史对话记录
                    List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId
                        && p.ModelingStageId == modelingStageTask.ModelingStageId
                        && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                        && p.StudentId == input.StudentId
                        && p.IsBackups == false
                        && p.IsDeleted == false)
                        .OrderBy(p => p.CreateTime)
                        .With(SqlWith.NoLock)
                        .ToListAsync();
                    if (dialogueContentRecords.Count > 0)
                    {
                        //历史对话记录
                        string dialogueData = "学生历史问答记录如下:\n";
                        foreach (var dialogueContentRecord in dialogueContentRecords)
                        {
                            AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                            dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                             + $"学生问:{ask.AskText}。\n"
                                             + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                        }
                        dialogueData += "要求：后续学生问答结合历史记录产出你的回答。（注意:当前对话你只需要输出'收到'）";

                        //上下文对话（历史对话记录）
                        Func<string, Task> emptyDataHandler = async (data) =>
                        {
                            await Task.CompletedTask;
                        };
                        await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                        {
                            context_id = contextCacheKey.CacheId,
                            content = dialogueData,
                            role = "user",
                            modelId = modelingStageTask.Modelkey
                        }, async (data) =>
                        {
                            await emptyDataHandler(data);
                        });

                        //更新过期时间
                        contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                    }
                }

                //上下文对话
                string msg = string.Empty;
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = input.Msg,
                    role = "user",
                    modelId = modelingStageTask.Modelkey
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);

                    string json = data;
                    int startIndex = json.IndexOf('{');
                    json = json.Substring(startIndex);
                    JObject jsonObject = JObject.Parse(json);
                    string content = jsonObject["Content"]?.Value<string>();
                    if (content != null)
                    {
                        msg += content;
                    }
                }, cancellationToken);

                //更新过期时间
                contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                //保存会话记录
                AIDialogueASKDto aIDialogueASKDto = new AIDialogueASKDto()
                {
                    AskText = input.Msg,
                    AudioFile = new AIDialogueASKAudioFileInfo()
                    {
                        AudioUrl = input.AudioUrl,
                        Duration = input.Duration,
                    }
                };
                AI_ModelingDialogueRecord modelingDialogueRecord = new AI_ModelingDialogueRecord()
                {
                    Id = IdHelper.GetId(),
                    ModelingId = modelingStageTask.ModelingId,
                    ModelingStageId = modelingStageTask.ModelingStageId,
                    ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                    StudentId = input.StudentId,
                    Ask = aIDialogueASKDto.ToJsonString(),
                    Answer = msg,
                    CreateTime = DateTime.Now,
                    Creator = input.StudentId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.StudentId,
                    IsDeleted = false,
                    IsBackups = false
                };
                await DBSqlSugar.Insertable(modelingDialogueRecord).ExecuteCommandAsync();

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
        }

        /// <summary>
        /// 学生端建模模型评价提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentModelingEvaluateSubmitOutput> StudentModelingEvaluateSubmit(StudentModelingEvaluateSubmitInput input)
        {
            //缓存锁
            string key = input.StudentId + "|" + input.ModelingStageTaskId;
            bool getLock = _agentCommonCacheService.SetRedisLock(key);
            if (getLock)
            {
                try
                {
                    //是否存在达标提交记录
                    AI_StudentDoModelingTask isDo = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == input.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsStandard == true && p.IsDeleted == false).FirstAsync();
                    if (isDo != null)
                    {
                        throw new BusException("已完成，无法重复提交!", 801);
                    }

                    //获取建模阶段任务基础信息
                    string modelingStageTaskSql = @"SELECT
                                                    	task.Id AS ModelingStageTaskId,
                                                    	task.ModelingStageId,
                                                    	task.ModelingId,
                                                    	task.RoleSetting,
                                                        task.ScoreStandard,
                                                        task.GuideRoleSetting,
                                                        task.Prologue,
                                                    	task.Demand,
                                                	    model.Modelkey,
                                                        task.TaskIsSubmit,
                                                        task.TaskIsAssessment,
                                                        task.TaskAssessmentScore,
                                                        task.GroupIsSubmit,
                                                        task.GroupIsAssessment,
                                                        task.GroupAssessmentScore
                                                    FROM
                                                    	AI_ModelingStageTask task WITH ( NOLOCK )
                                                    	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON task.ModelingId= agent.Id 
                                                    	AND agent.IsDeleted= 0
                                                    	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                    	AND agentBase.IsDeleted= 0 
                                                        INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	    AND model.IsDeleted= 0
                                                    WHERE
                                                    	task.IsDeleted= 0 
                                                    	AND task.TaskType= 9 
                                                    	AND task.Id= @modelingStageTaskId";
                    ModelingStageTaskBaseInfoDto modelingStageTask = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                        .AddParameters(new { modelingStageTaskId = input.ModelingStageTaskId }).FirstAsync();
                    if (modelingStageTask == null)
                    {
                        throw new BusException("建模阶段任务Id异常!");
                    }
                    if (string.IsNullOrEmpty(modelingStageTask.Modelkey))
                    {
                        throw new BusException("智能体配置的模型异常!");
                    }

                    //获取豆包上下文缓存Id
                    bool isCreate = false;
                    bool isTimeOut = false;
                    AI_ModelingContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ModelingContextCacheKey>()
                        .Where(p => p.ModelingId == modelingStageTask.ModelingId
                        && p.ModelingStageId == modelingStageTask.ModelingStageId
                        && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                        && p.StudentId == input.StudentId
                        && p.IsBackups == false
                        && p.IsDeleted == false)
                        .With(SqlWith.NoLock)
                        .FirstAsync();
                    if (contextCacheKey != null)
                    {
                        //验证是否过期
                        if (contextCacheKey.ExpirationTime <= DateTime.Now)
                        {
                            contextCacheKey.IsDeleted = true;
                            await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                            isCreate = true;
                            isTimeOut = true;
                        }
                    }
                    else
                    {
                        isCreate = true;
                    }

                    //创建上下文缓存
                    if (isCreate)
                    {
                        //获取建模阶段任务的高频问题
                        List<AI_ModelingStageTaskQuestion> taskQuestions = await DBSqlSugar.Queryable<AI_ModelingStageTaskQuestion>()
                            .Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.IsDeleted == false)
                            .Select(p => new AI_ModelingStageTaskQuestion()
                            {
                                Id = p.Id,
                                Name = p.Name,
                                Describe = p.Describe
                            }).ToListAsync();
                        string taskQuestionsText = string.Empty;
                        foreach (var taskQuestion in taskQuestions)
                        {
                            taskQuestionsText += $"第{taskQuestions.IndexOf(taskQuestion) + 1}个主题:Id:{taskQuestion.Id}、标题名称:{taskQuestion.Name}、描述:{taskQuestion.Describe}。\n";
                        }
                        if (string.IsNullOrEmpty(taskQuestionsText))
                        {
                            taskQuestionsText = "暂无";
                        }

                        //获取模型构建
                        List<AI_StudentModelingStructure> modelingStructures = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                            .Where(p => p.ModelingId == modelingStageTask.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsOptimize == true)
                            .OrderBy(p => p.Order)
                            .Select(p => new AI_StudentModelingStructure()
                            {
                                Versions = p.Versions,
                                Variable = p.Variable,
                                Expression = p.Expression
                            })
                            .ToListAsync();
                        if (modelingStructures.Count <= 0)
                        {
                            //获取模型构建最新记录并保存
                            AI_StudentModelingStructure modelingStructure = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                            .Where(p => p.ModelingId == modelingStageTask.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsOptimize == false)
                            .OrderByDescending(p => p.Order)
                            .FirstAsync();
                            if (modelingStructure != null)
                            {
                                AI_StudentModelingStructure addModelingStructure = new AI_StudentModelingStructure()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingId = modelingStructure.ModelingId,
                                    ModelingStageId = modelingStructure.ModelingStageId,
                                    ModelingStageTaskId = modelingStructure.ModelingStageTaskId,
                                    StudentId = modelingStructure.StudentId,
                                    Variable = modelingStructure.Variable,
                                    Expression = modelingStructure.Expression,
                                    CreateTime = DateTime.Now,
                                    Creator = modelingStructure.StudentId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = modelingStructure.StudentId,
                                    IsDeleted = false,
                                    IsOptimize = true,
                                    Order = 1,
                                    Versions = "V1"
                                };
                                await DBSqlSugar.Insertable(addModelingStructure).ExecuteCommandAsync();
                                modelingStructures.Add(addModelingStructure);
                            }
                        }
                        string modelingStructureStr = string.Empty;
                        if (modelingStructures.Count > 0)
                        {
                            foreach (var modelingStructure in modelingStructures)
                            {
                                modelingStructureStr += $"{modelingStructures.IndexOf(modelingStructure) + 1}模型版本:{modelingStructure.Versions}。\n变量定义:{modelingStructure.Variable}。\n关系表达式:{modelingStructure.Expression}。\n";
                            }
                        }
                        if (string.IsNullOrEmpty(modelingStructureStr))
                        {
                            modelingStructureStr = "暂无";
                        }
                        modelingStructureStr += "(注意:后续可能会有新版本)。";

                        string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                        {
                            Msg = $"一、引导角色设定:{modelingStageTask.GuideRoleSetting}。\n二、对话引导要求:{modelingStageTask.Demand}。\n三、开场白:{modelingStageTask.Prologue}。\n四、主题:{taskQuestionsText}。\n五、模型:{modelingStructureStr}",
                            TimeOut = 604800,
                            modelId = modelingStageTask.Modelkey
                        });
                        if (string.IsNullOrEmpty(contextId))
                        {
                            throw new Exception("创建上下文缓存异常!");
                        }

                        contextCacheKey = new AI_ModelingContextCacheKey()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            StudentId = input.StudentId,
                            CacheId = contextId,
                            TimeOut = 604800,
                            ExpirationTime = DateTime.Now.AddSeconds(604800),
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsBackups = false
                        };
                        await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                    }

                    //获取提交指令
                    AI_Directive submitDirective = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.StudentModelingEvaluateSubmit_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (submitDirective == null)
                    {
                        throw new BusException("无法获取提交系统提示词,请联系管理员!");
                    }

                    //提交信息
                    string submitMsg = submitDirective.Directive;
                    submitMsg = submitMsg.Replace("{评分标准}", modelingStageTask.ScoreStandard).Replace("{评估角色设定}", modelingStageTask.RoleSetting);

                    //缓存过期逻辑
                    if (isTimeOut)
                    {
                        //获取历史对话记录
                        List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                            .Where(p => p.ModelingId == modelingStageTask.ModelingId
                            && p.ModelingStageId == modelingStageTask.ModelingStageId
                            && p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId
                            && p.StudentId == input.StudentId
                            && p.IsBackups == false
                            && p.IsDeleted == false)
                            .OrderBy(p => p.CreateTime)
                            .With(SqlWith.NoLock)
                            .ToListAsync();
                        if (dialogueContentRecords.Count > 0)
                        {
                            //历史对话记录
                            string dialogueData = "学生历史问答记录如下:\n";
                            foreach (var dialogueContentRecord in dialogueContentRecords)
                            {
                                AIDialogueASKDto ask = JsonConvert.DeserializeObject<AIDialogueASKDto>(dialogueContentRecord.Ask);
                                dialogueData += $"第{dialogueContentRecords.IndexOf(dialogueContentRecord) + 1}次对话:\n"
                                                 + $"学生问:{ask.AskText}。\n"
                                                 + $"AI答:{dialogueContentRecord.Answer}。\n\n";
                            }
                            submitMsg = submitMsg + dialogueData;
                        }
                    }

                    //上下文对话
                    string resultData = string.Empty;
                    Func<string, Task> emptyDataHandler = async (data) =>
                    {
                        await Task.CompletedTask;
                    };
                    await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                    {
                        context_id = contextCacheKey.CacheId,
                        content = submitMsg,
                        role = "user",
                        modelId = modelingStageTask.Modelkey
                    }, async (data) =>
                    {
                        await emptyDataHandler(data);

                        string json = data;
                        int startIndex = json.IndexOf('{');
                        json = json.Substring(startIndex);
                        JObject jsonObject = JObject.Parse(json);
                        string content = jsonObject["Content"]?.Value<string>();
                        if (content != null)
                        {
                            resultData += content;
                        }
                    });

                    //豆包输出
                    StudentModelingSubmitDouBaoOutput dialogueSubmitDouBaoOutput = StudentModelingSubmitAnalysisJson(resultData);
                    if (dialogueSubmitDouBaoOutput != null)
                    {
                        //保存提交记录
                        AI_StudentDoModelingTask studentDoModelingTask = new AI_StudentDoModelingTask()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStageTask.ModelingId,
                            ModelingStageId = modelingStageTask.ModelingStageId,
                            ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                            Score = dialogueSubmitDouBaoOutput.Score,
                            Level = BusinessUtil.GetAvgLevel(dialogueSubmitDouBaoOutput.Score),
                            AssessmentResult = !string.IsNullOrEmpty(dialogueSubmitDouBaoOutput.AssessmentResult) ? dialogueSubmitDouBaoOutput.AssessmentResult : "暂无评估结果!",
                            StudentId = input.StudentId,
                            CreateTime = DateTime.Now,
                            Creator = input.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.StudentId,
                            IsDeleted = false,
                            IsStandard = true
                        };
                        studentDoModelingTask.Order = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>().Where(p => p.ModelingStageTaskId == modelingStageTask.ModelingStageTaskId && p.StudentId == input.StudentId && p.IsDeleted == false).CountAsync() + 1;

                        //验证是否达标
                        // 1.任务评估
                        bool taskPass = modelingStageTask.TaskIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.TaskAssessmentScore : true;
                        // 2.组件评估
                        bool groupPass = modelingStageTask.GroupIsAssessment ? studentDoModelingTask.Score >= modelingStageTask.GroupAssessmentScore : true;
                        // 3. 两者同时满足才算达标
                        studentDoModelingTask.IsStandard = taskPass == true && groupPass == true;
                        await DBSqlSugar.Insertable(studentDoModelingTask).ExecuteCommandAsync();

                        //高频主题保存
                        if (dialogueSubmitDouBaoOutput.QuestionIds != null && dialogueSubmitDouBaoOutput.QuestionIds.Count > 0)
                        {
                            List<AI_StudentDoModelingTaskQuestion> studentDoModelingTaskQuestions = new List<AI_StudentDoModelingTaskQuestion>();
                            foreach (var questionId in dialogueSubmitDouBaoOutput.QuestionIds)
                            {
                                studentDoModelingTaskQuestions.Add(new AI_StudentDoModelingTaskQuestion()
                                {
                                    Id = IdHelper.GetId(),
                                    ModelingId = modelingStageTask.ModelingId,
                                    ModelingStageId = modelingStageTask.ModelingStageId,
                                    ModelingStageTaskId = modelingStageTask.ModelingStageTaskId,
                                    StudentId = input.StudentId,
                                    QuestionId = questionId,
                                    StudentDoModelingTaskId = studentDoModelingTask.Id,
                                    CreateTime = DateTime.Now,
                                    Creator = input.StudentId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.StudentId,
                                    IsDeleted = false
                                });
                            }
                            await DBSqlSugar.Insertable(studentDoModelingTaskQuestions).ExecuteCommandAsync();
                        }

                        _agentCommonCacheService.DelRedisLock(key);
                        return new StudentModelingEvaluateSubmitOutput()
                        {
                            IsStandard = studentDoModelingTask.IsStandard,
                            AssessmentResult = studentDoModelingTask.AssessmentResult
                        };
                    }
                    else
                    {
                        throw new Exception("提交异常!");
                    }
                }
                catch (Exception ex)
                {
                    _agentCommonCacheService.DelRedisLock(key);
                    throw new BusException(ex.Message);
                }
            }
            else
            {
                throw new BusException("数据处理中!", 801);
            }
        }

        /// <summary>
        /// 获取学生模型构建最新版本模型构建信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentModelingStructureNewestOutput> StudentModelingStructureNewest(StudentModelingStructureNewestInput input)
        {
            try
            {
                //获取最新版本模型构建
                StudentModelingStructureNewestOutput modelingStructureNewestOutput = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                    .Where(p => p.ModelingId == input.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsOptimize == true)
                    .OrderByDescending(p => p.Order)
                    .Select(p => new StudentModelingStructureNewestOutput()
                    {
                        Variable = p.Variable,
                        Expression = p.Expression
                    })
                    .FirstAsync();
                if (modelingStructureNewestOutput == null)
                {
                    //获取模型构建最新记录并保存
                    AI_StudentModelingStructure modelingStructure = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                    .Where(p => p.ModelingId == input.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsOptimize == false)
                    .OrderByDescending(p => p.Order)
                    .FirstAsync();
                    if (modelingStructure != null)
                    {
                        AI_StudentModelingStructure addModelingStructure = new AI_StudentModelingStructure()
                        {
                            Id = IdHelper.GetId(),
                            ModelingId = modelingStructure.ModelingId,
                            ModelingStageId = modelingStructure.ModelingStageId,
                            ModelingStageTaskId = modelingStructure.ModelingStageTaskId,
                            StudentId = modelingStructure.StudentId,
                            Variable = modelingStructure.Variable,
                            Expression = modelingStructure.Expression,
                            CreateTime = DateTime.Now,
                            Creator = modelingStructure.StudentId,
                            ModifyTime = DateTime.Now,
                            Modifier = modelingStructure.StudentId,
                            IsDeleted = false,
                            IsOptimize = true,
                            Order = 1,
                            Versions = "V1"
                        };
                        await DBSqlSugar.Insertable(addModelingStructure).ExecuteCommandAsync();

                        modelingStructureNewestOutput = new StudentModelingStructureNewestOutput()
                        {
                            Expression = addModelingStructure.Expression,
                            Variable = addModelingStructure.Variable
                        };
                    }
                }

                return modelingStructureNewestOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 学生端建模模型构建优化
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task StudentModelingStructureOptimize(StudentModelingStructureOptimizeInput input)
        {
            try
            {
                //保存最新版本模型构建优化
                AI_StudentModelingStructure addModelingStructure = new AI_StudentModelingStructure()
                {
                    Id = IdHelper.GetId(),
                    ModelingId = input.ModelingId,
                    StudentId = input.StudentId,
                    Variable = input.Variable,
                    Expression = input.Expression,
                    CreateTime = DateTime.Now,
                    Creator = input.StudentId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.StudentId,
                    IsDeleted = false,
                    IsOptimize = true
                };
                addModelingStructure.Order = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                            .Where(p => p.ModelingId == input.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsOptimize == true)
                            .CountAsync() + 1;
                addModelingStructure.Versions = $"V{addModelingStructure.Order}";
                await DBSqlSugar.Insertable(addModelingStructure).ExecuteCommandAsync();

                //获取建模阶段任务基础信息
                string modelingStageTaskSql = @"SELECT
                                                	task.Id AS ModelingStageTaskId,
                                                	task.ModelingStageId,
                                                	task.ModelingId,
                                                	task.TaskType,
                                                	model.Modelkey 
                                                FROM
                                                	AI_ModelingStageTask task WITH ( NOLOCK )
                                                	INNER JOIN AI_AgentTask agent WITH ( NOLOCK ) ON task.ModelingId= agent.Id 
                                                	AND agent.IsDeleted= 0
                                                	INNER JOIN AI_AgentBaseInfo agentBase WITH ( NOLOCK ) ON agent.AgentId= agentBase.Id 
                                                	AND agentBase.IsDeleted= 0
                                                	INNER JOIN AI_ModelBaseInfo model WITH ( NOLOCK ) ON agentBase.ModelId= model.Id 
                                                	AND model.IsDeleted= 0 
                                                WHERE
                                                	task.IsDeleted= 0 
                                                	AND task.TaskType IN ( 8, 9 ) 
                                                	AND task.ModelingId= @modelingId";
                List<ModelingStageTaskBaseInfoDto> modelingStageTasks = await DBSqlSugar.SqlQueryable<ModelingStageTaskBaseInfoDto>(modelingStageTaskSql)
                    .AddParameters(new { modelingId = input.ModelingId }).ToListAsync();

                //模型假设
                ModelingStageTaskBaseInfoDto hypothesisTask = modelingStageTasks.Where(p => p.TaskType == 8).FirstOrDefault();
                if (hypothesisTask != null && !string.IsNullOrEmpty(hypothesisTask.ModelingId) && !string.IsNullOrEmpty(hypothesisTask.ModelingStageId) && !string.IsNullOrEmpty(hypothesisTask.ModelingStageTaskId) && !string.IsNullOrEmpty(hypothesisTask.Modelkey))
                {
                    //获取豆包上下文缓存Id（模型假设）
                    AI_ModelingContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ModelingContextCacheKey>()
                        .Where(p => p.ModelingId == hypothesisTask.ModelingId
                        && p.ModelingStageId == hypothesisTask.ModelingStageId
                        && p.ModelingStageTaskId == hypothesisTask.ModelingStageTaskId
                        && p.StudentId == input.StudentId
                        && p.IsBackups == false
                        && p.IsDeleted == false)
                        .With(SqlWith.NoLock)
                        .FirstAsync();
                    if (contextCacheKey != null)
                    {
                        //验证是否过期
                        if (contextCacheKey.ExpirationTime <= DateTime.Now)
                        {
                            contextCacheKey.IsDeleted = true;
                            await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        }
                        else
                        {
                            //录入最新版本信息
                            string modelingStructureStr = $"最新模型版本:{addModelingStructure.Versions}。\n变量定义:{addModelingStructure.Variable}。\n关系表达式:{addModelingStructure.Expression}。\n要求：后续学生问答结合历史版本产出你的回答。（注意:当前对话你只需要输出'收到'）";

                            //上下文对话（录入最新版本信息）
                            Func<string, Task> emptyDataHandler = async (data) =>
                            {
                                await Task.CompletedTask;
                            };
                            await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                            {
                                context_id = contextCacheKey.CacheId,
                                content = modelingStructureStr,
                                role = "user",
                                modelId = hypothesisTask.Modelkey
                            }, async (data) =>
                            {
                                await emptyDataHandler(data);
                            });
                        }
                    }
                }

                //模型评价
                ModelingStageTaskBaseInfoDto evaluateTask = modelingStageTasks.Where(p => p.TaskType == 9).FirstOrDefault();
                if (evaluateTask != null && !string.IsNullOrEmpty(evaluateTask.ModelingId) && !string.IsNullOrEmpty(evaluateTask.ModelingStageId) && !string.IsNullOrEmpty(evaluateTask.ModelingStageTaskId) && !string.IsNullOrEmpty(evaluateTask.Modelkey))
                {
                    //获取豆包上下文缓存Id（模型评价）
                    AI_ModelingContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ModelingContextCacheKey>()
                        .Where(p => p.ModelingId == evaluateTask.ModelingId
                        && p.ModelingStageId == evaluateTask.ModelingStageId
                        && p.ModelingStageTaskId == evaluateTask.ModelingStageTaskId
                        && p.StudentId == input.StudentId
                        && p.IsBackups == false
                        && p.IsDeleted == false)
                        .With(SqlWith.NoLock)
                        .FirstAsync();
                    if (contextCacheKey != null)
                    {
                        //验证是否过期
                        if (contextCacheKey.ExpirationTime <= DateTime.Now)
                        {
                            contextCacheKey.IsDeleted = true;
                            await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        }
                        else
                        {
                            //录入最新版本信息
                            string modelingStructureStr = $"最新模型版本:{addModelingStructure.Versions}。\n变量定义:{addModelingStructure.Variable}。\n关系表达式:{addModelingStructure.Expression}。\n要求：后续学生问答结合历史版本产出你的回答。（注意:当前对话你只需要输出'收到'）";

                            //上下文对话（录入最新版本信息）
                            Func<string, Task> emptyDataHandler = async (data) =>
                            {
                                await Task.CompletedTask;
                            };
                            await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                            {
                                context_id = contextCacheKey.CacheId,
                                content = modelingStructureStr,
                                role = "user",
                                modelId = evaluateTask.Modelkey
                            }, async (data) =>
                            {
                                await emptyDataHandler(data);
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取学生建模相关任务对话内容记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageReturn<StudentModelingDialogueRecordOutput>> StudentModelingDialogueRecord(StudentModelingDialogueRecordInput input)
        {
            try
            {
                //获取学生信息
                Exam_Student studentInfo = await DBSqlSugar.Queryable<Exam_Student>().Where(p => p.Id == input.StudentId && p.Deleted == false).FirstAsync();
                if (studentInfo == null)
                {
                    throw new Exception("学生Id异常!");
                }

                //参数
                List<SugarParameter> parameters = new List<SugarParameter>();
                parameters.Add(new SugarParameter("@modelingId", input.ModelingId));
                parameters.Add(new SugarParameter("@modelingStageId", input.ModelingStageId));
                parameters.Add(new SugarParameter("@modelingStageTaskId", input.ModelingStageTaskId));
                parameters.Add(new SugarParameter("@studentId", input.StudentId));
                parameters.Add(new SugarParameter("@isBackups", input.IsBackups));

                string where = string.Empty;
                if (!string.IsNullOrEmpty(input.StudentDoModelingTaskId) && input.IsBackups)
                {
                    where += " AND dcr.StudentDoModelingTaskId=@studentDoModelingTaskId";
                    parameters.Add(new SugarParameter("@studentDoModelingTaskId", input.StudentDoModelingTaskId));
                }

                //获取数据
                string sql = $@"SELECT
                                	dcr.Id,
                                	dcr.Ask,
                                	dcr.Answer,
                                	dcr.CreateTime 
                                FROM
                                	AI_ModelingDialogueRecord dcr WITH ( NOLOCK ) 
                                WHERE
                                	dcr.IsDeleted= 0 
                                	AND dcr.ModelingId= @modelingId 
                                	AND dcr.ModelingStageId= @modelingStageId 
                                	AND dcr.ModelingStageTaskId= @modelingStageTaskId 
                                	AND dcr.StudentId= @studentId
                                    AND dcr.IsBackups=@isBackups
                                   {where}";
                RefAsync<int> totalNumber = 0;
                List<StudentModelingDialogueRecordOutput> datas = await DBSqlSugar.SqlQueryable<StudentModelingDialogueRecordOutput>(sql)
                    .AddParameters(parameters)
                    .OrderBy("CreateTime desc")
                    .ToPageListAsync(input.PageIndex, input.PageSize, totalNumber);

                foreach (var data in datas)
                {
                    data.StudentLogo = string.IsNullOrEmpty(studentInfo.Photo) ? "https://userphoto.obs.cn-east-2.myhuaweicloud.com/uploadFile131e561d405a4f6a834a6a79d27ded7e.20210305132909.png" : studentInfo.Photo;
                    data.AskInfo = JsonConvert.DeserializeObject<AIDialogueASKDto>(data.Ask);
                }

                return new PageReturn<StudentModelingDialogueRecordOutput>()
                {
                    Datas = datas.OrderBy(p => p.CreateTime).ToList(),
                    TotalCount = totalNumber
                };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 获取学生端建模模型构建记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StudentModelingStructureRecordOutput> StudentModelingStructureRecord(StudentModelingStructureRecordInput input)
        {
            try
            {
                StudentModelingStructureRecordOutput studentModelingStructure = await DBSqlSugar.Queryable<AI_StudentModelingStructure>()
                    .Where(p => p.ModelingId == input.ModelingId
                    && p.ModelingStageId == input.ModelingStageId
                    && p.ModelingStageTaskId == input.ModelingStageTaskId
                    && p.StudentDoModelingTaskId == input.StudentDoModelingTaskId
                    && p.StudentId == input.StudentId
                    && p.IsDeleted == false
                    && p.IsOptimize == false)
                    .Select(p => new StudentModelingStructureRecordOutput()
                    {
                        Variable = p.Variable,
                        Expression = p.Expression
                    }).FirstAsync();

                return studentModelingStructure;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 学生端建模提交未达标对话记录备份
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task StudentModelingSubmitNoStandardBackups(StudentModelingSubmitNoStandardBackupsInput input)
        {
            try
            {

                //对话记录备份
                await DBSqlSugar.Updateable<AI_ModelingDialogueRecord>()
                    .SetColumns(it => new AI_ModelingDialogueRecord() { StudentDoModelingTaskId = input.TaskSubmitId, IsBackups = true, ModifyTime = DateTime.Now, Modifier = input.StudentId })
                    .Where(p => p.ModelingId == input.ModelingId
                    && p.ModelingStageId == input.ModelingStageId
                    && p.ModelingStageTaskId == input.ModelingStageTaskId
                    && p.StudentId == input.StudentId
                    && p.IsBackups == false)
                    .ExecuteCommandAsync();

                //缓存备份
                await DBSqlSugar.Updateable<AI_ModelingContextCacheKey>()
                    .SetColumns(it => new AI_ModelingContextCacheKey() { StudentDoModelingTaskId = input.TaskSubmitId, IsBackups = true, ModifyTime = DateTime.Now, Modifier = input.StudentId })
                    .Where(p => p.ModelingId == input.ModelingId
                    && p.ModelingStageId == input.ModelingStageId
                    && p.ModelingStageTaskId == input.ModelingStageTaskId
                    && p.StudentId == input.StudentId
                    && p.IsBackups == false)
                    .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取学生做建模阶段任务结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentDoModelingTaskResultOutput> GetStudentDoModelingTaskResult(GetStudentDoModelingTaskResultInput input)
        {
            try
            {
                GetStudentDoModelingTaskResultOutput resultOutput = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>()
                    .Where(p => p.Id == input.TaskSubmitId && p.StudentId == input.StudentId && p.IsDeleted == false)
                    .Select(p => new GetStudentDoModelingTaskResultOutput()
                    {
                        AssessmentResult = p.AssessmentResult
                    }).FirstAsync();

                return resultOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取学生做建模未达标列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentDoModelingNoStandardListOutput> GetStudentDoModelingNoStandardList(GetStudentDoModelingNoStandardListInput input)
        {
            try
            {
                //获取建模基础信息
                GetStudentDoModelingNoStandardListOutput modelingInfoOutput = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ModelingId && p.IsDeleted == false)
                    .Select(p => new GetStudentDoModelingNoStandardListOutput()
                    {
                        AgentId = p.AgentId,
                        ModelingId = p.Id,
                        ModelingName = p.Name,
                        ModelingIntroduce = p.Introduce,
                        ModelingLogo = p.TaskLogo
                    })
                    .With(SqlWith.NoLock)
                    .FirstAsync();
                if (modelingInfoOutput == null)
                {
                    throw new BusException("建模Id异常!");
                }

                //获取学生做建模阶段任务未达标信息
                List<AI_StudentDoModelingTask> studentDoModelingTasks = await DBSqlSugar.Queryable<AI_StudentDoModelingTask>()
                    .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsStandard == false)
                    .Select(p => new AI_StudentDoModelingTask()
                    {
                        Id = p.Id,
                        ModelingStageId = p.ModelingStageId,
                        ModelingStageTaskId = p.ModelingStageTaskId,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //获取学生未达标问答记录
                List<AI_ModelingDialogueRecord> dialogueContentRecords = await DBSqlSugar.Queryable<AI_ModelingDialogueRecord>()
                    .Where(p => p.ModelingId == modelingInfoOutput.ModelingId && p.StudentId == input.StudentId && p.IsDeleted == false && p.IsBackups == true)
                    .Select(p => new AI_ModelingDialogueRecord()
                    {
                        StudentDoModelingTaskId = p.StudentDoModelingTaskId,
                    })
                    .With(SqlWith.NoLock)
                    .ToListAsync();

                //删除未备份的未达标提交记录
                List<string> delStudentDoProjectTaskIds = new List<string>();
                foreach (var item in studentDoModelingTasks)
                {
                    //验证是否存在
                    if (dialogueContentRecords.Where(p => p.StudentDoModelingTaskId == item.Id).FirstOrDefault() == null)
                    {
                        delStudentDoProjectTaskIds.Add(item.Id);
                    }
                }
                if (delStudentDoProjectTaskIds.Count > 0)
                {
                    studentDoModelingTasks = studentDoModelingTasks.Where(p => !delStudentDoProjectTaskIds.Contains(p.Id)).ToList();
                }

                if (studentDoModelingTasks.Count > 0)
                {
                    //建模阶段
                    List<string> stageIds = studentDoModelingTasks.Select(p => p.ModelingStageId).Distinct().ToList();
                    List<GetStudentDoModelingNoStandardListStageOutput> modelingStages = await DBSqlSugar.Queryable<AI_ModelingStage>()
                        .Where(p => stageIds.Contains(p.Id) && p.IsDeleted == false)
                        .Select(p => new GetStudentDoModelingNoStandardListStageOutput()
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Order = p.Order
                        })
                        .OrderBy(p => p.Order)
                        .With(SqlWith.NoLock)
                        .ToListAsync();

                    //建模阶段任务
                    List<string> stageTaskIds = studentDoModelingTasks.Select(p => p.ModelingStageTaskId).Distinct().ToList();
                    List<GetStudentDoModelingNoStandardListStageTaskOutput> modelingStageTasks = await DBSqlSugar.Queryable<AI_ModelingStageTask>()
                        .Where(p => stageTaskIds.Contains(p.Id) && p.IsDeleted == false)
                        .Select(p => new GetStudentDoModelingNoStandardListStageTaskOutput()
                        {
                            Id = p.Id,
                            ModelingStageId = p.ModelingStageId,
                            Name = p.Name,
                            Target = p.Target,
                            TaskType = p.TaskType,
                            Order = p.Order
                        })
                        .OrderBy(p => p.Order)
                        .With(SqlWith.NoLock)
                        .ToListAsync();

                    //处理阶段信息
                    foreach (var stage in modelingStages)
                    {
                        //获取当前阶段的任务
                        List<GetStudentDoModelingNoStandardListStageTaskOutput> stageTask = modelingStageTasks.Where(p => p.ModelingStageId == stage.Id).OrderBy(p => p.Order).ToList();
                        foreach (var task in stageTask)
                        {
                            task.TaskSubmitId = studentDoModelingTasks.Where(p => p.ModelingStageTaskId == task.Id).OrderBy(p => p.Order).Select(p => p.Id).ToList();
                        }
                        stage.ModelingStageTaskInfos = stageTask;
                    }

                    modelingInfoOutput.ModelingStageInfos = modelingStages;
                    return modelingInfoOutput;
                }
                else
                {
                    return modelingInfoOutput;
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 解析建模阶段任务提交后返回的Json
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="JsonException"></exception>
        /// <exception cref="Exception"></exception>
        public static StudentModelingSubmitDouBaoOutput StudentModelingSubmitAnalysisJson(string input)
        {
            try
            {
                var result = new StudentModelingSubmitDouBaoOutput();

                if (string.IsNullOrWhiteSpace(input))
                {
                    return result;
                }

                //先尝试直接解析
                try
                {
                    var jsonResult = JsonConvert.DeserializeObject<StudentModelingSubmitDouBaoOutput>(input);
                    if (jsonResult != null)
                    {
                        return jsonResult;
                    }
                }
                catch
                {

                }

                //尝试JSON解析（优先处理标准格式）
                try
                {
                    int startIndex = input.IndexOf('{');
                    int endIndex = input.LastIndexOf('}');
                    if (startIndex != -1 && endIndex != -1 && startIndex < endIndex)
                    {
                        string jsonPart = input.Substring(startIndex, endIndex - startIndex + 1);
                        // 清理JSON中的特殊引号
                        jsonPart = jsonPart.Replace('‘', '\'')
                                           .Replace('’', '\'')
                                           .Replace('“', '"')
                                           .Replace('”', '"');

                        // 尝试 deserialization
                        var jsonResult = JsonConvert.DeserializeObject<StudentModelingSubmitDouBaoOutput>(jsonPart);
                        if (jsonResult != null)
                        {
                            // 验证是否有效解析（至少有一个字段有值）
                            if (jsonResult.Score > 0 || !string.IsNullOrEmpty(jsonResult.Level) ||
                                !string.IsNullOrEmpty(jsonResult.AssessmentResult) ||
                                jsonResult.QuestionIds.Count > 0)
                            {
                                return jsonResult;
                            }
                        }
                    }
                }
                catch (Exception)
                {

                }

                // 正则提取 - 适配带双引号的字段名
                var inputCopy = input;

                // 1. 提取Score（匹配"Score": 0.0格式）
                var scoreMatch = Regex.Match(inputCopy, @"""Score""\s*:\s*(\d+\.?\d*)", RegexOptions.IgnoreCase);
                if (!scoreMatch.Success)
                {
                    // 兼容不带引号的字段名
                    scoreMatch = Regex.Match(inputCopy, @"Score\s*:\s*(\d+\.?\d*)", RegexOptions.IgnoreCase);
                }
                if (scoreMatch.Success && decimal.TryParse(scoreMatch.Groups[1].Value, out decimal score))
                {
                    result.Score = score;
                    inputCopy = inputCopy.Remove(scoreMatch.Index, scoreMatch.Length);
                }

                // 2. 提取Level（匹配"Level":"D"格式）
                var levelMatch = Regex.Match(inputCopy, @"""Level""\s*:\s*""([A-Za-z])""", RegexOptions.IgnoreCase);
                if (!levelMatch.Success)
                {
                    // 兼容不带引号的字段名
                    levelMatch = Regex.Match(inputCopy, @"Level\s*:\s*""?([A-Za-z])""?", RegexOptions.IgnoreCase);
                }
                if (levelMatch.Success)
                {
                    result.Level = levelMatch.Groups[1].Value;
                    inputCopy = inputCopy.Remove(levelMatch.Index, levelMatch.Length);
                }

                // 3. 提取QuestionIds（支持多个ID）
                var questionIdsMatch = Regex.Match(inputCopy, @"""QuestionIds""\s*:\s*\[\s*(.*?)\s*\]", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                if (!questionIdsMatch.Success)
                {
                    // 兼容不带引号的字段名
                    questionIdsMatch = Regex.Match(inputCopy, @"QuestionIds\s*:\s*\[\s*(.*?)\s*\]", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
                if (questionIdsMatch.Success)
                {
                    string idsContent = questionIdsMatch.Groups[1].Value;
                    // 提取所有引号中的ID
                    var idMatches = Regex.Matches(idsContent, @"""([^""]+)""");
                    foreach (Match idMatch in idMatches)
                    {
                        result.QuestionIds.Add(idMatch.Groups[1].Value);
                    }
                    inputCopy = inputCopy.Remove(questionIdsMatch.Index, questionIdsMatch.Length);
                }

                // 4. 提取AssessmentResult（处理长文本）
                var assessmentMatch = Regex.Match(inputCopy, @"""AssessmentResult""\s*:\s*""([^""]+)""", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                if (!assessmentMatch.Success)
                {
                    // 兼容不带引号的字段名
                    assessmentMatch = Regex.Match(inputCopy, @"AssessmentResult\s*:\s*""([^""]+)""", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
                if (assessmentMatch.Success)
                {
                    result.AssessmentResult = assessmentMatch.Groups[1].Value
                        .Replace('‘', '\'')
                        .Replace('’', '\'')
                        .Trim();
                }
                // 处理没有双引号包裹的情况
                else if (Regex.IsMatch(inputCopy, @"AssessmentResult", RegexOptions.IgnoreCase))
                {
                    var fallbackMatch = Regex.Match(inputCopy, @"(AssessmentResult)\s*[:=]\s*(.+?)(?=""Level""|""QuestionIds""|""Score""|Level|QuestionIds|Score|$)",
                        RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    if (fallbackMatch.Success)
                    {
                        result.AssessmentResult = fallbackMatch.Groups[2].Value
                            .Trim('"', '\'', ',', ';', ' ', '}')
                            .Replace('‘', '\'')
                            .Replace('’', '\'');
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new BusException($"解析失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析建模构建图片OCR结果返回的Json
        /// </summary>
        /// <param name="input">输入的JSON字符串</param>
        /// <returns>解析后的StudentModelingStructureImgOcrOutput对象</returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="JsonException"></exception>
        /// <exception cref="Exception"></exception>
        public static StudentModelingStructureImgOcrOutput StudentModelingStructureImgOcrAnalysisJson(string input)
        {
            try
            {
                var result = new StudentModelingStructureImgOcrOutput();

                if (string.IsNullOrWhiteSpace(input))
                {
                    return result;
                }

                // 先尝试直接解析标准JSON
                try
                {
                    var jsonResult = JsonConvert.DeserializeObject<StudentModelingStructureImgOcrOutput>(input);
                    if (jsonResult != null)
                    {
                        return jsonResult;
                    }
                }
                catch
                {
                    // 解析失败时继续尝试其他方法
                }

                // 尝试提取JSON部分并清理特殊字符后解析
                try
                {
                    int startIndex = input.IndexOf('{');
                    int endIndex = input.LastIndexOf('}');
                    if (startIndex != -1 && endIndex != -1 && startIndex < endIndex)
                    {
                        string jsonPart = input.Substring(startIndex, endIndex - startIndex + 1);
                        // 清理JSON中的特殊引号
                        jsonPart = jsonPart.Replace('‘', '\'')
                                           .Replace('’', '\'')
                                           .Replace('“', '"')
                                           .Replace('”', '"');

                        // 尝试反序列化
                        var jsonResult = JsonConvert.DeserializeObject<StudentModelingStructureImgOcrOutput>(jsonPart);
                        if (jsonResult != null)
                        {
                            // 验证是否有效解析（至少有一个字段有值）
                            if (!string.IsNullOrEmpty(jsonResult.Variable) || !string.IsNullOrEmpty(jsonResult.Expression))
                            {
                                return jsonResult;
                            }
                        }
                    }
                }
                catch (Exception)
                {
                    // 解析失败时继续尝试其他方法
                }

                // 正则提取 - 处理各种非标准格式
                var inputCopy = input;

                // 1. 提取Variable（变量定义）
                var variableMatch = Regex.Match(inputCopy, @"""Variable""\s*:\s*""([^""]+)""", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                if (!variableMatch.Success)
                {
                    // 兼容不带引号的字段名
                    variableMatch = Regex.Match(inputCopy, @"Variable\s*:\s*""([^""]+)""", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
                // 处理没有双引号包裹值的情况
                if (!variableMatch.Success)
                {
                    variableMatch = Regex.Match(inputCopy, @"""Variable""\s*:\s*([^,""}]+)", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
                if (!variableMatch.Success)
                {
                    variableMatch = Regex.Match(inputCopy, @"Variable\s*:\s*([^,""}]+)", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }

                if (variableMatch.Success)
                {
                    result.Variable = variableMatch.Groups[1].Value
                        .Trim('"', '\'', ' ', ',', ';')
                        .Replace('‘', '\'')
                        .Replace('’', '\'');
                    inputCopy = inputCopy.Remove(variableMatch.Index, variableMatch.Length);
                }

                // 2. 提取Expression（表达式）
                var expressionMatch = Regex.Match(inputCopy, @"""Expression""\s*:\s*""([^""]+)""", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                if (!expressionMatch.Success)
                {
                    // 兼容不带引号的字段名
                    expressionMatch = Regex.Match(inputCopy, @"Expression\s*:\s*""([^""]+)""", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
                // 处理没有双引号包裹值的情况
                if (!expressionMatch.Success)
                {
                    expressionMatch = Regex.Match(inputCopy, @"""Expression""\s*:\s*([^,""}]+)", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
                if (!expressionMatch.Success)
                {
                    expressionMatch = Regex.Match(inputCopy, @"Expression\s*:\s*([^,""}]+)", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }

                // 处理长表达式可能包含特殊字符的情况
                if (!expressionMatch.Success)
                {
                    expressionMatch = Regex.Match(inputCopy, @"(Expression)\s*[:=]\s*(.+?)(?=""Variable""|Variable|$)",
                        RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }

                if (expressionMatch.Success)
                {
                    result.Expression = expressionMatch.Groups[expressionMatch.Groups.Count > 1 ? 1 : 0].Value
                        .Trim('"', '\'', ' ', ',', ';', '}')
                        .Replace('‘', '\'')
                        .Replace('’', '\'');
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new BusException($"解析结果失败: {ex.Message}");
            }
        }
    }
}
