﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.CustomException;
using Uwoo.System.IServices;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_学生端首页控制器
    /// </summary>
    [Route("/AgentStudentHomePage/[controller]/[action]")]
    [ApiController]
    public class AgentStudentHomePageController : ApiBaseController<IAgentStudentHomePageService>
    {
        #region DI

        private IAgentStudentHomePageService _agentStudentHomePageService;
        public AgentStudentHomePageController(IAgentStudentHomePageService agentStudentHomePageService)
        {
            _agentStudentHomePageService = agentStudentHomePageService;
        }

        #endregion

        /// <summary>
        /// 智能体_学生端首页智能体任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task<AgentStudentHomePageAgentTaskOuput> GetStudentHomePageAgentTaskList(AgentStudentHomePageAgentTaskInput input)
        {
            if (string.IsNullOrEmpty(input.StudentId) || string.IsNullOrEmpty(input.SchoolId) || string.IsNullOrEmpty(input.ClassId))
            {
                throw new BusException("参数异常!");
            }
            if (input.PageIndex <= 0)
            {
                input.PageIndex = 1;
            }
            if (input.PageSize <= 0)
            {
                input.PageSize = 10;
            }
            if (input.AgentTaskState != 1 && input.AgentTaskState != 2)
            {
                input.AgentTaskState = 1;
            }
            return await _agentStudentHomePageService.GetStudentHomePageAgentTaskList(input);
        }
    }
}
