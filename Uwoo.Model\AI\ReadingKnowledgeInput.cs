using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 阅读理解知识问答接口入参
    /// </summary>
    public class ReadingKnowledgeInput
    {
        /// <summary>
        /// 阅读理解项目阶段任务ID
        /// </summary>
        public string? ProjectStageTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 对话消息
        /// </summary>
        public string? Msg { get; set; }

        /// <summary>
        /// 语音文件
        /// </summary>
        public string? AudioUrl { get; set; }

        /// <summary>
        /// 语音时长（单位:秒）
        /// </summary>
        public int Duration { get; set; }
    }
}
