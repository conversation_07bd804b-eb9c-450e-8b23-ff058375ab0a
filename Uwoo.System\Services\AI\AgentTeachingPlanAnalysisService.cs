﻿using Amazon.Runtime.Internal.Util;
using Aspose.Words.Drawing;
using Aspose.Words;
using Autofac.Core;
using Azure;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Client;
using MongoDB.Bson;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.DBManager;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Core.Utilities;
using Uwoo.Core.Utilities.Response;
using UwooAgent.Core.Enums;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.Item;
using UwooAgent.Entity.DomainModels.Office;
using UwooAgent.Entity.DomainModels.Question;
using UwooAgent.Entity.DomainModels.Student;
using UwooAgent.Model.AI.AgentTeachingPlanAnalysis;
using UwooAgent.System.IServices.AI;
using Novacode;
using SharpCompress.Common;
using UwooAgent.Entity.DomainModels.Paper;
using System.Xml.Schema;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Presentation;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// AI 教学计划分析服务
    /// </summary>
    public class AgentTeachingPlanAnalysisService : IAgentTeachingPlanAnalysisService, IDependency
    {
        private SqlSugar.SqlSugarScope _DBSqlSugar = null;

        public SqlSugar.SqlSugarScope DBSqlSugar
        {
            get
            {
                if (_DBSqlSugar != null)
                    return _DBSqlSugar;
                else
                {
                    _DBSqlSugar = DbManger.SqlSugarClient;
                    return _DBSqlSugar;
                }
            }
        }

        /// <summary>
        /// 获取教学计划库列表
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<TeachingPlanDTO>> GetTeachingPlanLibrary(string userId,string SubjectId, string GradeId)
        {
            var year=BusinessUtil.GetCurrentYear(DateTime.Now);
            var teachingPlanList =await DBSqlSugar.Queryable<AI_TeachingPlan>()
                .LeftJoin<AI_TeachingPlanDZBWordImportHistoryMapping>((t, tp) => t.Id == tp.TeachingPlanId)
                .LeftJoin<Office_File>((t, tp, o) => tp.DZBWordImportHistoryId == o.FileId)
                .Where(t => t.Creator == userId && t.SubjectId==SubjectId &&t.GradeId==GradeId&& t.Year == year)
            .Select((t, tp, o) => new TeachingPlanDTO
             {
                 TeachingPlanId = t.Id,
                 DZBWordImportHistoryId = tp.DZBWordImportHistoryId,
                 Version = o.Version,
                 FileName = o.FileName,
                 FileUrl = o.Url,
                TeachingPlanName=t.Name
            })
            .ToListAsync();

            //var teachingPlanList = await DBSqlSugar.Queryable<AI_TeachingPlanDZBWordImportHistoryMapping>()
            //      .LeftJoin<Office_File>((a, o) => a.DZBWordImportHistoryId == o.FileId)
            //      .Where(a => a.Creator == userId)
            //      .Select((a, o) => new TeachingPlanDTO
            //      {
            //          TeachingPlanId = a.TeachingPlanId,
            //          DZBWordImportHistoryId = a.DZBWordImportHistoryId,
            //          Version = o.Version,
            //          FileName = o.FileName,
            //          FileUrl = o.Url
            //      })
            //      .ToListAsync();
            var nullIdPlans = teachingPlanList.Where(p => p.DZBWordImportHistoryId == null);


            // 3. 将两部分数据合并，并返回一个新列表

            var lastTeachingPlanList = teachingPlanList.Where(x=>x.DZBWordImportHistoryId!=null).GroupBy(x => x.DZBWordImportHistoryId).Select(g => g.OrderByDescending(x => x.Version).First()).ToList();
            var finalResult = nullIdPlans.Concat(lastTeachingPlanList).ToList();
            return finalResult;
        }
        /// <summary>
        /// 获取学生试卷列表
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="subjectId"></param>
        /// <returns></returns>
        public async Task<List<PaperDTO>> GetStudentPaperList(string userId, int subjectId, string classId)
        {

            string sqlStr = @"select Title, PaperId ,
        CASE 
        WHEN IsDZB = 1 THEN 1
        ELSE 2
        END AS PaperType from v_StudentPaper ";
            List<SugarParameter> paras = new List<SugarParameter>();
            if (!string.IsNullOrEmpty(userId))
            {
                sqlStr += $" where UserId=@userId";
                paras.Add(new SugarParameter("@userId", userId));
            }
            if (subjectId > 0)
            {
                sqlStr += $" and SubjectId=@subjectId";
                paras.Add(new SugarParameter("@subjectId", subjectId));
            }
            if (!string.IsNullOrEmpty(classId))
            {
                sqlStr += $" and ClassId=@classId";
                paras.Add(new SugarParameter("@classId", classId));
            }
            var version = BusinessUtil.GetCurrentYear(DateTime.Now);
            if (version != 0)
            {
                sqlStr += $" and Version=@version";
                paras.Add(new SugarParameter("@version", version));
            }
            sqlStr += " and (IsStop=0 or IsStop is null) and HomeworkType!=9 and CreateTime is not null";
            var result = await DBSqlSugar.Ado.SqlQueryAsync<PaperDTO>(sqlStr, paras);
            return result;
        }
        /// <summary>
        /// 学生成绩分布对比
        /// </summary>
        /// <returns></returns>
        public List<StudentsGradeDistributionModel> StudentsGradeDistribution(StudentGradeDistributionInput input)
        {

            List<StudentsGradeDistributionModel> models = new List<StudentsGradeDistributionModel>();
            var studentIds = DBSqlSugar.Queryable<Exam_Student>().Where(x => x.ClassId == input.ClassId).Select(x => x.Id).ToList();
            if (studentIds != null && studentIds.Count > 0)
            {
                var pre = input.GradeModels.Where(x => x.DataType == 1)?.FirstOrDefault(); //前侧
                var back = input.GradeModels.Where(x => x.DataType == 2)?.FirstOrDefault(); //后侧
                //前侧做题数据
                var preDopaper = DBSqlSugar.Queryable<Exam_UserDoPaperInfo>().Where(x => studentIds.Contains(x.UserId) && pre.PaperIds.Contains(x.PaperId)).ToList();
                //后撤做题数据
                var backDoPaper = DBSqlSugar.Queryable<Exam_UserDoPaperInfo>().Where(x => studentIds.Contains(x.UserId) && back.PaperIds.Contains(x.PaperId)).ToList();

                /// 前后侧分数段人数，例如：0-20,21-40,41-60,61-80,81-100
                int preNumber20 = preDopaper.Where(x => x.Score >= 0 && x.Score <= 20).Count();
                int preNumber40 = preDopaper.Where(x => x.Score > 21 && x.Score <= 40).Count();
                int preNumber60 = preDopaper.Where(x => x.Score > 41 && x.Score <= 60).Count();
                int preNumber80 = preDopaper.Where(x => x.Score > 61 && x.Score <= 80).Count();
                int preNumber100 = preDopaper.Where(x => x.Score > 81 && x.Score <= 100).Count();
                int backNumber20 = backDoPaper.Where(x => x.Score >= 0 && x.Score <= 20).Count();
                int backNumber40 = backDoPaper.Where(x => x.Score > 21 && x.Score <= 40).Count();
                int backNumber60 = backDoPaper.Where(x => x.Score > 41 && x.Score <= 60).Count();
                int backNumber80 = backDoPaper.Where(x => x.Score > 61 && x.Score <= 80).Count();
                int backNumber100 = backDoPaper.Where(x => x.Score > 81 && x.Score <= 100).Count();
                List<int> DataTypes = new List<int> { 1, 2 };
                foreach (var dataType in DataTypes)
                {
                    StudentsGradeDistributionModel model = new StudentsGradeDistributionModel()
                    {
                        DataType = dataType,
                        StudentsGradeData = new List<StudentGradeDataModel>(){
                            new StudentGradeDataModel{
                                FractionalSegments="0-20",
                                StudentCount=pre.DataType==1? preNumber20:backNumber20
                            },
                            new StudentGradeDataModel{
                                FractionalSegments="21-40",
                                StudentCount=pre.DataType==1? preNumber40:backNumber40
                            },
                            new StudentGradeDataModel{
                                FractionalSegments="41-60",
                                StudentCount=pre.DataType==1? preNumber60:backNumber60
                            },
                            new StudentGradeDataModel{
                                FractionalSegments="61-80",
                                StudentCount=pre.DataType==1? preNumber80:backNumber80
                            },
                            new StudentGradeDataModel{
                                FractionalSegments="81-100",
                                StudentCount=pre.DataType==1? preNumber100:backNumber100
                            }
                        }
                    };
                    models.Add(model);
                }
            }
            return models;
        }
        /// <summary>
        /// 生成教学计划分析报告
        /// </summary>
        /// <returns></returns>
        public async Task<string> GenerateTeachingPlanAnalysis(GenerateTeachingPlanAnalysisInput input)
        {
            string token = "Bearer pat_9e6BCB9bodp6WLSmyfpGaMK8Ofd7TRp28uMcNXZIkO2jgsXGmUJsYbmO5ftTcw8l";
            string workflowId = "7538278065873846322";
            string workflowUrl = "https://api.coze.cn/v1/workflow/run";

            var studentIds = DBSqlSugar.Queryable<Exam_Student>().Where(x => x.ClassId == input.ClassId && x.Deleted==false).Select(x => x.Id).ToList();
            if (studentIds.Count == 0)
            {
                return "班级学生数为0，无法生成分析报告。";
            }
            // 前侧做题数据
            var preDopaper = DBSqlSugar.Queryable<Exam_UserDoPaperInfo>().Where(x => studentIds.Contains(x.UserId) && input.PrePaperIds.Contains(x.PaperId)).ToList();
            //后撤做题数据
            var backDoPaper = DBSqlSugar.Queryable<Exam_UserDoPaperInfo>().Where(x => studentIds.Contains(x.UserId) && input.BackPaperIds.Contains(x.PaperId)).ToList();

            var prePapersTotalScore = DBSqlSugar.Queryable<Exam_Paper>().Where(x => input.PrePaperIds.Contains(x.Id)).Select(x => new PaperTotalScoreDTO()
            {
                PaperId = x.Id,
                TotalScore = x.TotalScore ?? 1
            }).ToList();
            var backPaperTotalScore = DBSqlSugar.Queryable<Exam_Paper>().Where(x => input.BackPaperIds.Contains(x.Id)).Select(x => new PaperTotalScoreDTO()
            {
                PaperId = x.Id,
                TotalScore = x.TotalScore ?? 1
            }).ToList();


            var preDoPaperInfo = preDopaper
                .Join(prePapersTotalScore,
                    answer => answer.PaperId,
                    total => total.PaperId,
                    (answer, total) => new { answer.PaperId, answer.Score, total.TotalScore }).ToList();

            var backDoPaperInfo = backDoPaper
                .Join(prePapersTotalScore,
                    answer => answer.PaperId,
                    total => total.PaperId,
                    (answer, total) => new { answer.PaperId, answer.Score, total.TotalScore }).ToList();


            var preTestPassCount = preDoPaperInfo.Count(x => x.Score >= x.TotalScore * 0.6m);//前测及格人数
            var preTestExcellentCount = preDoPaperInfo.Count(x => x.Score >= x.TotalScore * 0.9m); //前测优秀人数

            var backTestPassCount = backDoPaperInfo.Count(x => x.Score >= x.TotalScore * 0.6m);//后测及格人数
            var backTestExcellentCount = backDoPaperInfo.Count(x => x.Score >= x.TotalScore * 0.9m);//后测优秀人数



            var subject = Enum.Parse(typeof(SubjectType), input.SubjectId.ToString()).ToString();
            var grade = BusinessUtil.GradeName(input.GradeId);
            var preTestAveScore = preDopaper.Average(x => x.Score) ?? 0;
            var backTestAveScore = backDoPaper.Average(x => x.Score) ?? 0;
            var aveScoreIncrease = backTestAveScore - preTestAveScore;
            var preTestPassRate = preDopaper.Any() ? (preTestPassCount / preDopaper.Count) * 100 : 0;
            var backTestPassRate = backDoPaper.Any()?(backTestPassCount / backDoPaper.Count) * 100:0;
            var testPassRateIncrease = backTestPassRate - preTestPassRate;
            var preTestGreatRate =  preDopaper.Any() ? (preTestExcellentCount / preDopaper.Count) * 100 : 0;
            var backTestGreatRate = backDoPaper.Any() ? (backTestExcellentCount / backDoPaper.Count) * 100 : 0;
            var testGreatRateIncrease = backTestGreatRate - preTestGreatRate;
            string teachingPlanContent = "";
            string fileType = "text";
            if (!string.IsNullOrEmpty(input.TeachingPlanUrl))
            {
                fileType = GetFileTypeFromUrl(input.TeachingPlanUrl);
            }
            //var fileType= GetFileTypeFromUrl(input.TeachingPlanUrl);
            if (fileType == "Word" || fileType == "PDF")
            {
                teachingPlanContent= await GetWordContent(input.TeachingPlanUrl);
            }
            else if (fileType == "PPTX")
            {
                teachingPlanContent=await GetXmlDocumentContent(input.TeachingPlanUrl);
            }
            else
            {
                teachingPlanContent=DBSqlSugar.Queryable<AI_TeachingPlan>().Where(x=>x.Id==input.TeachingPlanId).Select(x=>x.Content).FirstOrDefault();
            }
            var preTeachingPlanList = await DBSqlSugar.Queryable<Exam_PaperUserAnswer>()
      .LeftJoin<Exam_Paper>((p, ep) => p.DoPaperId == ep.Id)
      .LeftJoin<Exam_Item>((p, ep, i) => p.ItemId == i.Id)
      .LeftJoin<Exam_Student>((p, ep, i, s) => p.UserId == s.Id && s.ClassId == input.ClassId)
      .Where((p, ep, i, s) => input.BackPaperIds.Contains(p.DoPaperId))
                .Select((p, ep, i, s) => new ItemDTO()
                {
                    PaperId = p.DoPaperId,
                    PaperTitle = ep.Title,
                    ItemId = p.ItemId,
                    ItemTitle = i.Title,
                    UserId = p.UserId,
                    UserName = s.RealName,
                    UserAnswer = p.UserAnswer,
                    Score = p.Score,
                    Result = p.Result,
                    CreateTime = p.CreateTime,
                    ATime = p.ATime,
                    SubType = p.SubType
                })
    .ToListAsync(); // 前侧做题数

            var backTeachingPlanList = await DBSqlSugar.Queryable<Exam_PaperUserAnswer>()
                .LeftJoin<Exam_Paper>((p, ep) => p.DoPaperId == ep.Id)
                .LeftJoin<Exam_Item>((p, ep, i) => p.ItemId == i.Id)
                .LeftJoin<Exam_Student>((p, ep, i, s) => p.UserId == s.Id && s.ClassId == input.ClassId)
                .Where((p, ep, i, s) => input.BackPaperIds.Contains(p.DoPaperId))
                          .Select((p, ep, i, s) => new ItemDTO()
                          {
                              PaperId = p.DoPaperId,
                              PaperTitle = ep.Title,
                              ItemId = p.ItemId,
                              ItemTitle = i.Title,
                              UserId = p.UserId,
                              UserName = s.RealName,
                              UserAnswer = p.UserAnswer,
                              Score = p.Score,
                              Result = p.Result,
                              CreateTime = p.CreateTime,
                              ATime = p.ATime,
                              SubType = p.SubType
                          })
              .ToListAsync(); // 后侧做题数
            var preTeachingPlanStr = JsonConvert.SerializeObject(preTeachingPlanList);
            var backTeachingPlanStr = JsonConvert.SerializeObject(backTeachingPlanList);
            //根据分析类型生成不同的报告
            if (input.GenerateAnalysisType == 1)
            {
                //                string prompt = $@"  - 提示词：
                //  你是上海市{subject}{grade}一个教学分析AI助手，请根据以下信息，分析学生成绩前测与后测的差异，并结合教案判断教学设计哪些部分有效，哪些部分还有改进空间。
                //  【教案内容】
                //  {teachingPlanContent}
                //  【成绩统计】
                //  前测平均分：{preTestAveScore}，后测平均分：{backTestAveScore}（提升{aveScoreIncrease}分）
                //  前测及格率：{preTestPassRate}%，后测及格率：{backTestPassRate}%（提升{testPassRateIncrease}%）
                //  前测优秀率：{preTestGreatRate}%，后测优秀率：{backTestGreatRate}%（提升{testGreatRateIncrease}%）
                //  请按如下结构输出：
                //  1. 成绩变化诊断（指出提升或下降）
                //  2. 教学设计成效分析（哪些内容帮助提升）
                //  3. 改进建议（哪些学生仍然未掌握，可能需要优化的教学环节）
                //- 差距原因分析：如果提分就展示提分原因，如果降分就展示显著降分原因；
                //  - 提示词：
                //请根据以下教案内容和前后测成绩，分析导致学生成绩提升/未提升的可能教学原因。
                //【教案内容】
                //{teachingPlanContent}
                //【成绩表现】
                //请从教学设计角度分析原因，列出3-5条积极因素与3-5条待改进因素，每条以简洁条目列出。";
                var analysis = DBSqlSugar.Queryable<AI_TeachingPlanAnalysisAgent>().Where(x => x.GenerateAnalysisType == "1").OrderBy(x => x.Sort).ToList();
                string combinedPrompt = string.Join(Environment.NewLine, analysis.Select(x => x.Prompt));
                string prompt = combinedPrompt.Replace("{subject}", subject)
                                    .Replace("{grade}", grade)
                                    .Replace("{teachingPlanContent}", teachingPlanContent)
                                    .Replace("{preTestAveScore}", preTestAveScore.ToString())
                                    .Replace("{backTestAveScore}", backTestAveScore.ToString())
                                    .Replace("{aveScoreIncrease}", aveScoreIncrease.ToString())
                                    .Replace("{preTestPassRate}", preTestPassRate.ToString())
                                    .Replace("{backTestPassRate}", backTestPassRate.ToString())
                                    .Replace("{testPassRateIncrease}", testPassRateIncrease.ToString())
                                    .Replace("{preTestGreatRate}", preTestGreatRate.ToString())
                                    .Replace("{backTestGreatRate}", backTestGreatRate.ToString())
                                    .Replace("{testGreatRateIncrease}", testGreatRateIncrease.ToString());

                StartPointParameters agentInput = new StartPointParameters()
                {
                    userPrompt = prompt
                };
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(agentInput);
                TeachingPlanAgentDTO agentDTO = new TeachingPlanAgentDTO()
                {
                    workflow_id = workflowId,
                    parameters = agentInput,
                    is_async = false
                };
                string jsonStr = Newtonsoft.Json.JsonConvert.SerializeObject(agentDTO);
                var dataJson = PsotRequest(jsonStr, token, workflowUrl);
                if (dataJson != null)
                {
                    var result = Newtonsoft.Json.JsonConvert.DeserializeObject<ApiResponse>(dataJson);
                    if (result != null && result.code == 0)
                    {
                        var url = JsonConvert.DeserializeObject<DataContent>(result.data).url;
                        AI_TeachingPlanEvaluation evaluation = new AI_TeachingPlanEvaluation()
                        {
                            Id = Guid.NewGuid().ToString(),
                            CreateTime = DateTime.Now,
                            TeacherId = input.TeacherId,
                            ClassId = input.ClassId,
                            TeachingPlanUrl = input.TeachingPlanUrl,
                            PrePaperIds = string.Join(",", input.PrePaperIds),
                            BackPaperIds = string.Join(",", input.BackPaperIds),
                            EvaluationHtml = url
                        };
                        await DBSqlSugar.Insertable(evaluation).ExecuteCommandAsync();
                        return url;
                    }
                }
            }
            if (input.GenerateAnalysisType == 2)
            {
                #region 提示词
                var analysis = DBSqlSugar.Queryable<AI_TeachingPlanAnalysisAgent>().Where(x => x.GenerateAnalysisType == "2").OrderBy(x => x.Sort).ToList();
                string combinedPrompt = string.Join(Environment.NewLine, analysis.Select(x => x.Prompt));
                string prompt = combinedPrompt.Replace("{subject}", subject)
                                    .Replace("{grade}", grade)
                                    .Replace("{teachingPlanContent}", teachingPlanContent)
                                    .Replace("{preTestAveScore}", preTestAveScore.ToString())
                                    .Replace("{backTestAveScore}", backTestAveScore.ToString())
                                    .Replace("{aveScoreIncrease}", aveScoreIncrease.ToString())
                                    .Replace("{preTestPassRate}", preTestPassRate.ToString())
                                    .Replace("{backTestPassRate}", backTestPassRate.ToString())
                                    .Replace("{testPassRateIncrease}", testPassRateIncrease.ToString())
                                    .Replace("{preTestGreatRate}", preTestGreatRate.ToString())
                                    .Replace("{backTestGreatRate}", backTestGreatRate.ToString())
                                    .Replace("{testGreatRateIncrease}", testGreatRateIncrease.ToString())
                                    .Replace("{preTeachingPlanStr}", preTeachingPlanStr)
                                    .Replace("{backTeachingPlanStr}", backTeachingPlanStr);
                #endregion


                StartPointParameters agentInput = new StartPointParameters()
                {
                    userPrompt = prompt
                };
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(agentInput);
                TeachingPlanAgentDTO agentDTO = new TeachingPlanAgentDTO()
                {
                    workflow_id = workflowId,
                    parameters = agentInput,
                    is_async = false
                };
                string jsonStr = Newtonsoft.Json.JsonConvert.SerializeObject(agentDTO);
                var dataJson = PsotRequest(jsonStr, token, workflowUrl);
                if (dataJson != null)
                {
                    var result = Newtonsoft.Json.JsonConvert.DeserializeObject<ApiResponse>(dataJson);
                    if (result != null && result.code == 0)
                    {
                        var url = JsonConvert.DeserializeObject<DataContent>(result.data).url;
                        AI_TeachingPlanEvaluation evaluation = new AI_TeachingPlanEvaluation()
                        {
                            Id = Guid.NewGuid().ToString(),
                            CreateTime = DateTime.Now,
                            TeacherId = input.TeacherId,
                            ClassId = input.ClassId,
                            TeachingPlanUrl = input.TeachingPlanUrl,
                            PrePaperIds = string.Join(",", input.PrePaperIds),
                            BackPaperIds = string.Join(",", input.BackPaperIds),
                            EvaluationHtml = url
                        };
                        await DBSqlSugar.Insertable(evaluation).ExecuteCommandAsync();
                        return url;
                    }
                }
            }
            if (input.GenerateAnalysisType == 3)
            {
                var analysis = DBSqlSugar.Queryable<AI_TeachingPlanAnalysisAgent>().Where(x => x.GenerateAnalysisType == "3").OrderBy(x => x.Sort).ToList();
                string combinedPrompt = string.Join(Environment.NewLine, analysis.Select(x => x.Prompt));
                string resultPrompt = combinedPrompt.Replace("{teachingPlanContent}", teachingPlanContent)
                                                   .Replace("{preTeachingPlanStr}", preTeachingPlanStr)
                                                   .Replace("{backTeachingPlanStr}", backTeachingPlanStr);
                StartPointParameters agentInput = new StartPointParameters()
                {
                    userPrompt = resultPrompt
                };
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(agentInput);
                TeachingPlanAgentDTO agentDTO = new TeachingPlanAgentDTO()
                {
                    workflow_id = workflowId,
                    parameters = agentInput,
                    is_async = false
                };
                string jsonStr = Newtonsoft.Json.JsonConvert.SerializeObject(agentDTO);
                var dataJson = PsotRequest(jsonStr, token, workflowUrl);
                if (dataJson != null)
                {
                    var result = Newtonsoft.Json.JsonConvert.DeserializeObject<ApiResponse>(dataJson);
                    if (result != null && result.code == 0)
                    {
                        var url = JsonConvert.DeserializeObject<DataContent>(result.data).url;
                        AI_TeachingPlanEvaluation evaluation = new AI_TeachingPlanEvaluation()
                        {
                            Id = Guid.NewGuid().ToString(),
                            CreateTime = DateTime.Now,
                            TeacherId = input.TeacherId,
                            ClassId = input.ClassId,
                            TeachingPlanUrl = input.TeachingPlanUrl,
                            PrePaperIds = string.Join(",", input.PrePaperIds),
                            BackPaperIds = string.Join(",", input.BackPaperIds),
                            EvaluationHtml = url
                        };
                        await DBSqlSugar.Insertable(evaluation).ExecuteCommandAsync();
                        return url;
                    }
                }
            }
            return null;
        }
        public string PsotRequest(string jsonStr, string token, string workflowUrl)
        {
            Dictionary<string, string> headers = new Dictionary<string, string>();
            headers.Add("Authorization", token);
            //headers.Add("Content-Type", "application/json");
            string dataJson = HttpHelper.RequestData(HttpMethod.Post, workflowUrl, jsonStr, "application/json", headers);
            return dataJson;
        }
        public async Task<string> GetWordContent(string wordFileUrl)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                // 发送 GET 请求获取文件流
                HttpResponseMessage response = await httpClient.GetAsync(wordFileUrl);
                response.EnsureSuccessStatusCode();
                // 2. 将文件流加载到 Aspose.Words
                using (Stream wordStream = await response.Content.ReadAsStreamAsync())
                {
                    Document doc = new Document(wordStream);
                    string text = doc.GetText();
                    return text;
                }
            }
        }
        public async Task<string> GetXmlDocumentContent(string pptxFileUrl)
        {
            string str = "";
            // 使用 HttpClient 下载文件到 MemoryStream
            using (var client = new HttpClient())
            using (var stream = await client.GetStreamAsync(pptxFileUrl))
            using (var memoryStream = new MemoryStream())
            {
                await stream.CopyToAsync(memoryStream);

                // 将流的当前位置重置为0，以便 PresentationDocument.Open() 可以读取
                memoryStream.Position = 0;

                // 使用 Open XML SDK 从流中打开演示文稿
                using (PresentationDocument presentationDocument = PresentationDocument.Open(memoryStream, false))
                {
                    PresentationPart presentationPart = presentationDocument.PresentationPart;
                    if (presentationPart != null && presentationPart.Presentation != null)
                    {
                        // 获取所有幻灯片ID
                        var slideIds = presentationPart.Presentation.SlideIdList.Elements<SlideId>();

                        foreach (var slideId in slideIds)
                        {
                            // 获取每个幻灯片
                            SlidePart slidePart = (SlidePart)presentationPart.GetPartById(slideId.RelationshipId);
                            if (slidePart != null && slidePart.Slide != null)
                            {
                                str += slidePart.Slide.InnerText + Environment.NewLine;
                            }
                        }
                    }
                }
                return str;
            }
            //public async Task<string> GetString(string wordFileUrl)
            //{
            //    // 发送 GET 请求获取文件流
            //    HttpResponseMessage response = await httpClient.GetAsync(wordFileUrl);
            //    response.EnsureSuccessStatusCode();
            //    using (Stream wordStream = await response.Content.ReadAsStreamAsync())
            //    {
            //        DocX document = DocX.Load(wordStream);
            //        string text = document.Text;
            //        return text;
            //    }
            //}

        }
        // 你提供的文件类型判断方法
        private string GetFileTypeFromUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                return "Other";
            }

            string extension = Path.GetExtension(url)?.ToLowerInvariant();

            switch (extension)
            {
                case ".doc":
                case ".docx":
                    return "Word";
                case ".pdf":
                    return "PDF";
                case ".ppt":
                case ".pptx":
                    return "PPTX";
                default:
                    return "Other";
            }
        }
    }
}
