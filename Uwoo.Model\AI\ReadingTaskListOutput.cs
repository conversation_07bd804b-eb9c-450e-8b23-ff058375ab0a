using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 阅读理解项目列表输出
    /// </summary>
    public class ReadingTaskListOutput
    {
        /// <summary>
        /// 项目列表
        /// </summary>
        public List<ReadingTaskListItemOutput> TaskList { get; set; } = new List<ReadingTaskListItemOutput>();

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }
    }

    /// <summary>
    /// 阅读理解项目列表项输出
    /// </summary>
    public class ReadingTaskListItemOutput
    {
        /// <summary>
        /// 项目Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 项目图标
        /// </summary>
        public string? TaskLogo { get; set; }

        /// <summary>
        /// 主要任务类型（显示第一个任务类型）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 包含的任务类型名称（显示所有任务类型）
        /// </summary>
        public string? TaskTypeName { get; set; }

        /// <summary>
        /// 是否发布
        /// </summary>
        public bool IsPublished { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 已发布班级数量
        /// </summary>
        public int PublishedClassCount { get; set; }

        /// <summary>
        /// 学生完成数量
        /// </summary>
        public int CompletedStudentCount { get; set; }

        /// <summary>
        /// 学生总数量
        /// </summary>
        public int TotalStudentCount { get; set; }

        /// <summary>
        /// 项目阶段数量
        /// </summary>
        public int StageCount { get; set; }

        /// <summary>
        /// 项目任务总数量
        /// </summary>
        public int TaskCount { get; set; }

        /// <summary>
        /// 发布的班级信息列表
        /// </summary>
        public List<PublishedClassInfo> PublishedClasses { get; set; } = new List<PublishedClassInfo>();
    }

    /// <summary>
    /// 发布班级信息
    /// </summary>
    public class PublishedClassInfo
    {
        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 班级名称
        /// </summary>
        public string? ClassName { get; set; }

        /// <summary>
        /// 发布开始时间
        /// </summary>
        public DateTime? BeginTime { get; set; }

        /// <summary>
        /// 发布结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 阅读理解项目学生完成情况统计输出
    /// </summary>
    public class ReadingTaskStudentStatisticsOutput
    {
        /// <summary>
        /// 项目Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目阶段数量
        /// </summary>
        public int StageCount { get; set; }

        /// <summary>
        /// 项目任务总数量
        /// </summary>
        public int TaskCount { get; set; }

        /// <summary>
        /// 学生总数量
        /// </summary>
        public int TotalStudentCount { get; set; }

        /// <summary>
        /// 已完成学生数量（完成所有任务）
        /// </summary>
        public int CompletedStudentCount { get; set; }

        /// <summary>
        /// 进行中学生数量（部分完成）
        /// </summary>
        public int InProgressStudentCount { get; set; }

        /// <summary>
        /// 未开始学生数量
        /// </summary>
        public int NotStartedStudentCount { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal CompletionRate { get; set; }

        /// <summary>
        /// 平均分数
        /// </summary>
        public decimal AverageScore { get; set; }

        /// <summary>
        /// 学生详细列表
        /// </summary>
        public List<StudentProjectStatisticsItemOutput> StudentList { get; set; } = new List<StudentProjectStatisticsItemOutput>();
    }

    /// <summary>
    /// 学生项目统计项输出
    /// </summary>
    public class StudentProjectStatisticsItemOutput
    {
        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生姓名
        /// </summary>
        public string? StudentName { get; set; }

        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 班级名称
        /// </summary>
        public string? ClassName { get; set; }

        /// <summary>
        /// 项目状态（1:未开始，2:进行中，3:已完成）
        /// </summary>
        public int ProjectStatus { get; set; }

        /// <summary>
        /// 项目状态名称
        /// </summary>
        public string? ProjectStatusName { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompleteTime { get; set; }

        /// <summary>
        /// 已完成任务数量
        /// </summary>
        public int CompletedTaskCount { get; set; }

        /// <summary>
        /// 任务总数量
        /// </summary>
        public int TotalTaskCount { get; set; }

        /// <summary>
        /// 项目完成进度（百分比）
        /// </summary>
        public decimal Progress { get; set; }

        /// <summary>
        /// 平均评估分数
        /// </summary>
        public decimal AverageScore { get; set; }

        /// <summary>
        /// 是否达标
        /// </summary>
        public bool IsStandard { get; set; }

        /// <summary>
        /// AI综合评价等第
        /// </summary>
        public string? AIGrade { get; set; }
    }
}
