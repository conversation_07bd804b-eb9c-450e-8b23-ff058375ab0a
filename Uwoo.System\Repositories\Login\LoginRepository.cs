﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Entity.DomainModels;
using Uwoo.System.IRepositories;
using Uwoo.System.IRepositories.Login;

namespace Uwoo.System.Repositories.Login
{
    public class LoginRepository : RepositoryBase<Base_User>, ILoginRepository
    {
        public LoginRepository(VOLContext dbContext)
    : base(dbContext)
        {

        }
    }
}
