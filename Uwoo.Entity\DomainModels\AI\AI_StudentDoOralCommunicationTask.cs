﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 学生做口语交际任务信息
    /// </summary>
    [Table("AI_StudentDoOralCommunicationTask")]
    public class AI_StudentDoOralCommunicationTask
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string AgentTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string StudentId { get; set; }

        /// <summary>
        /// 分数
        /// </summary>
        public decimal? Score { get; set; }

        /// <summary>
        /// 评估结果
        /// </summary>
        public string AssessmentResult { get; set; }

        /// <summary>
        /// 等第
        /// </summary>
        public string Level { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
