using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 学生阅读理解任务列表输入
    /// </summary>
    public class StudentReadingTaskListInput
    {
        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 任务类型（可选筛选）
        /// </summary>
        public int? TaskType { get; set; }

        /// <summary>
        /// 任务状态（可选筛选）
        /// </summary>
        public int? TaskStatus { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 学生阅读理解任务详情输入
    /// </summary>
    public class StudentReadingTaskDetailsInput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }
    }

    /// <summary>
    /// 视频观看状态输入
    /// </summary>
    public class VideoWatchStatusInput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 视频Id
        /// </summary>
        public string? VideoId { get; set; }
    }

    /// <summary>
    /// 视频观看时长输入
    /// </summary>
    public class VideoWatchDurationInput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 视频Id
        /// </summary>
        public string? VideoId { get; set; }

        /// <summary>
        /// 本次观看时长（秒）
        /// </summary>
        public int WatchDuration { get; set; }
    }

    /// <summary>
    /// 文档阅读状态输入
    /// </summary>
    public class DocumentReadStatusInput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 已读文档Id列表
        /// </summary>
        public List<string> ReadDocumentIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// 思维导图提交输入
    /// </summary>
    public class MindMapSubmitInput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 思维导图内容（JSON格式）
        /// </summary>
        public string? MindMapContent { get; set; }
    }

    /// <summary>
    /// 选词填空提交输入
    /// </summary>
    public class WordFillSubmitInput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生答案列表
        /// </summary>
        public List<WordFillAnswerInput> Answers { get; set; } = new List<WordFillAnswerInput>();

        /// <summary>
        /// 答题用时（秒）
        /// </summary>
        public int AnswerDuration { get; set; }

        /// <summary>
        /// 操作记录列表（记录学生的答题过程）
        /// </summary>
        public List<WordFillOperationInput> OperationRecords { get; set; } = new List<WordFillOperationInput>();
    }

    /// <summary>
    /// 选词填空答案输入
    /// </summary>
    public class WordFillAnswerInput
    {
        /// <summary>
        /// 题目序号（从1开始）
        /// </summary>
        public int QuestionIndex { get; set; }

        /// <summary>
        /// 选择的词汇
        /// </summary>
        public string? SelectedWord { get; set; }
    }

    /// <summary>
    /// 选词填空操作记录输入
    /// </summary>
    public class WordFillOperationInput
    {
        /// <summary>
        /// 操作序号（按时间顺序递增）
        /// </summary>
        public int OperationOrder { get; set; }

        /// <summary>
        /// 题目序号（第几个空）
        /// </summary>
        public int QuestionIndex { get; set; }

        /// <summary>
        /// 选择的词汇
        /// </summary>
        public string? SelectedWord { get; set; }

        /// <summary>
        /// 填空位置标识
        /// </summary>
        public string? BlankId { get; set; }

        /// <summary>
        /// 操作类型（1:选择答案, 2:修改答案, 3:清空答案）
        /// </summary>
        public int OperationType { get; set; }

        /// <summary>
        /// 操作时间戳（前端传递）
        /// </summary>
        public long OperationTimestamp { get; set; }
    }

    /// <summary>
    /// 学生任务历史输入
    /// </summary>
    public class StudentTaskHistoryInput
    {
        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 任务类型（可选筛选）
        /// </summary>
        public int? TaskType { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 阅读理解情景对话输入
    /// </summary>
    public class ReadingDialogueInput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? ProjectStageTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 对话消息
        /// </summary>
        public string? Msg { get; set; }

        /// <summary>
        /// 语音文件
        /// </summary>
        public string? AudioUrl { get; set; }

        /// <summary>
        /// 语音时长（单位:秒）
        /// </summary>
        public int Duration { get; set; }
    }

    /// <summary>
    /// 阅读理解情景对话提交输入
    /// </summary>
    public class ReadingDialogueSubmitInput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? ProjectStageTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }
    }

    /// <summary>
    /// 阅读理解成果评估输入
    /// </summary>
    public class ReadingAssessmentInput
    {
        /// <summary>
        /// 阅读理解项目阶段任务ID
        /// </summary>
        public string? ProjectStageTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 文件信息
        /// </summary>
        public List<StudentAssessFileInfoInput> Files { get; set; } = new List<StudentAssessFileInfoInput>();
    }

    /// <summary>
    /// 查询任务提交记录入参
    /// </summary>
    public class GetTaskSubmitRecordsInput
    {
        /// <summary>
        /// 学生Id（必需）
        /// </summary>
        public string StudentId { get; set; }

        /// <summary>
        /// 阅读理解项目Id（可选，不传则查询所有项目）
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 阅读理解项目阶段Id（可选，不传则查询所有阶段）
        /// </summary>
        public string? StageId { get; set; }

        /// <summary>
        /// 阅读理解项目阶段任务Id（可选，不传则查询所有任务）
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 提交状态筛选（可选）
        /// 1:未开始、2:进行中、3:已完成
        /// </summary>
        public List<int>? TaskStatuses { get; set; }

        /// <summary>
        /// 是否达标筛选（可选）
        /// true:只查达标记录、false:只查未达标记录、null:查询所有
        /// </summary>
        public bool? IsStandard { get; set; }

        /// <summary>
        /// 提交时间范围-开始时间（可选）
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 提交时间范围-结束时间（可选）
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 排序字段（可选，默认按提交时间倒序）
        /// CreateTime:提交时间、Score:评估分数、TaskOrder:任务顺序
        /// </summary>
        public string? OrderBy { get; set; } = "CreateTime";

        /// <summary>
        /// 排序方向（可选，默认倒序）
        /// ASC:升序、DESC:倒序
        /// </summary>
        public string? OrderDirection { get; set; } = "DESC";

        /// <summary>
        /// 页码（默认1）
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 每页数量（默认20，最大100）
        /// </summary>
        public int PageSize { get; set; } = 20;
    }
}
