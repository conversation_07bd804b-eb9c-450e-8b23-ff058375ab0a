﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Uwoo.Model.Login
{
    /// <summary>
    /// 登录入参
    /// </summary>
    public class SubmitLoginInput
    {
        /// <summary>
        /// 手机号/用户名
        /// </summary>
        public string? PhoneNo { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// 0 学生 1 教师 2 管理
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 平台来源（1专课专练）默认专课专练
        /// </summary>
        public int Source { get; set; } = 1;

        /// <summary>
        /// 登录方式，网页还是app
        /// </summary>
        public int? RequestAgent { get; set; }

	}
}
