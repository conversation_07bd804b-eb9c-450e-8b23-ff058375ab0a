using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 重新生成单题请求参数
    /// </summary>
    public class RegenerateQuestionInput
    {
        /// <summary>
        /// AI模型ID
        /// </summary>
        public string? AIModeId { get; set; }

        /// <summary>
        /// 原题目信息
        /// </summary>
        public AIGeneratedQuestion OriginalQuestion { get; set; } = new AIGeneratedQuestion();

        /// <summary>
        /// 用户要求（可选）
        /// </summary>
        public string? UserRequirement { get; set; }

        /// <summary>
        /// 难度等级名称（前端不要传，只传DifficultyId）
        /// </summary>
        public string DifficultyLevelName { get; set; } = string.Empty;

        /// <summary>
        /// 出题方向名称（也就是学习水平）（前端不要传，只传LearningLevelId）
        /// </summary>
        public string QuestionDirectionName { get; set; } = string.Empty;

        /// <summary>
        /// 难度等级id
        /// </summary>
        public string? DifficultyId { get; set; }

        /// <summary>
        /// 学习水平Id
        /// </summary>
        public string? LearningLevelId { get; set; }
    }

    /// <summary>
    /// 重新生成单题输出
    /// </summary>
    public class RegenerateQuestionOutput
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 重新生成的题目
        /// </summary>
        public AIGeneratedQuestion? Question { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
}
