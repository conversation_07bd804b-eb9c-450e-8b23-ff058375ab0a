﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 智能体音色情感关联
    /// </summary>
    [Table("AI_ToneEmotionMapping")]
    public class AI_ToneEmotionMapping
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 情感名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 第三方平台标识
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// 音色ID
        /// </summary>
        public string ToneId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// 排序序号
        /// </summary>
        public int? OrderId { get; set; }
    }
}
