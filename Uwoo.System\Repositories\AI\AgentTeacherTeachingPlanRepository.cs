﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.System.IRepositories.AI;

namespace UwooAgent.System.Repositories.AI
{
    /// <summary>
    /// 智能体_教师端教案
    /// </summary>
    public class AgentTeacherTeachingPlanRepository : RepositoryBase<AI_TeachingPlan>, IAgentTeacherTeachingPlanRepository
    {
        public AgentTeacherTeachingPlanRepository(VOLContext dbContext) : base(dbContext)
        {

        }

        public static IAgentTeacherTeachingPlanRepository Instance
        {
            get
            {
                return AutofacContainerModule.GetService<IAgentTeacherTeachingPlanRepository>();
            }
        }
    }
}
