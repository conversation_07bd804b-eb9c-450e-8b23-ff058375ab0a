<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .loading {
            display: none;
        }
        .data-type-badge {
            font-size: 0.75em;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="bi bi-database"></i> 知识库管理
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- 操作栏 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createModal">
                                    <i class="bi bi-plus-circle"></i> 创建知识库
                                </button>
                                <button type="button" class="btn btn-info ms-2" onclick="loadStatistics()">
                                    <i class="bi bi-bar-chart"></i> 统计信息
                                </button>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchKeyword" placeholder="搜索知识库名称或描述...">
                                    <button class="btn btn-outline-secondary" type="button" onclick="searchKnowledge()">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 筛选栏 -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select class="form-select" id="dataTypeFilter">
                                    <option value="">全部数据类型</option>
                                    <option value="unstructured_data">非结构化数据</option>
                                    <option value="structured_data">结构化数据</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="sortField">
                                    <option value="CreateTime">按创建时间排序</option>
                                    <option value="Name">按名称排序</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="sortDirection">
                                    <option value="desc">降序</option>
                                    <option value="asc">升序</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-secondary w-100" onclick="loadKnowledgeList()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>

                        <!-- 加载状态 -->
                        <div class="text-center loading" id="loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载数据...</p>
                        </div>

                        <!-- 知识库列表 -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>知识库名称</th>
                                        <th>豆包名称</th>
                                        <th>描述</th>
                                        <th>数据类型</th>
                                        <th>创建人</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="knowledgeTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="分页导航">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建知识库模态框 -->
    <div class="modal fade" id="createModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建知识库</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createForm">
                        <div class="mb-3">
                            <label for="createName" class="form-label">知识库名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="createName" required>
                            <div class="form-text">长度1-64个字符，只能使用英文字母、数字、下划线，并以英文字母开头</div>
                        </div>
                        <div class="mb-3">
                            <label for="createDescription" class="form-label">描述信息</label>
                            <textarea class="form-control" id="createDescription" rows="3" maxlength="65535"
                                      placeholder="请输入知识库的描述信息"></textarea>
                            <div class="form-text">最多65535个字符</div>
                        </div>
                        <div class="mb-3">
                            <label for="createDataType" class="form-label">数据类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="createDataType" required>
                                <option value="unstructured_data">非结构化数据（支持pdf、doc、csv等）</option>
                                <option value="structured_data">结构化数据（仅支持csv、xlsx、jsonl）</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showAdvanced">
                                <label class="form-check-label" for="showAdvanced">
                                    显示高级配置
                                </label>
                            </div>
                        </div>
                        <div id="advancedConfig" style="display: none;">
                            <div class="mb-3">
                                <label for="vectorModel" class="form-label">向量化模型</label>
                                <input type="text" class="form-control" id="vectorModel" placeholder="例如：ve_model_xxx">
                            </div>
                            <div class="mb-3">
                                <label for="chunkLength" class="form-label">分块长度</label>
                                <input type="number" class="form-control" id="chunkLength" placeholder="例如：512" min="1">
                            </div>
                            <div class="mb-3">
                                <label for="indexType" class="form-label">索引类型</label>
                                <select class="form-select" id="indexType">
                                    <option value="HNSW">HNSW</option>
                                    <option value="IVF_FLAT">IVF_FLAT</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="dimension" class="form-label">维度数量</label>
                                <input type="number" class="form-control" id="dimension" value="768" min="1">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createKnowledge()">
                        <span class="spinner-border spinner-border-sm d-none" id="createSpinner"></span>
                        创建
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑知识库模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑知识库</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <input type="hidden" id="editId">
                        <div class="mb-3">
                            <label for="editName" class="form-label">知识库名称</label>
                            <input type="text" class="form-control" id="editName" 
                                   pattern="^[a-zA-Z][a-zA-Z0-9_]*$" maxlength="64">
                        </div>
                        <div class="mb-3">
                            <label for="editDescription" class="form-label">描述信息</label>
                            <textarea class="form-control" id="editDescription" rows="3" maxlength="65535"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateKnowledge()">
                        <span class="spinner-border spinner-border-sm d-none" id="editSpinner"></span>
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息模态框 -->
    <div class="modal fade" id="statisticsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">知识库统计信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h3 id="totalCount">-</h3>
                                    <p class="mb-0">总数量</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h3 id="unstructuredCount">-</h3>
                                    <p class="mb-0">非结构化</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h3 id="structuredCount">-</h3>
                                    <p class="mb-0">结构化</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 0;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadKnowledgeList();
            
            // 高级配置显示/隐藏
            document.getElementById('showAdvanced').addEventListener('change', function() {
                const advancedConfig = document.getElementById('advancedConfig');
                advancedConfig.style.display = this.checked ? 'block' : 'none';
            });

            // 搜索框回车事件
            document.getElementById('searchKeyword').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchKnowledge();
                }
            });
        });

        // 加载知识库列表
        async function loadKnowledgeList(page = 1) {
            try {
                showLoading(true);

                const params = {
                    page: page,
                    pageSize: pageSize,
                    keyword: document.getElementById('searchKeyword').value.trim(),
                    dataType: document.getElementById('dataTypeFilter').value,
                    sortField: document.getElementById('sortField').value,
                    sortDirection: document.getElementById('sortDirection').value
                };

                const response = await fetch('http://localhost:9910/api/Knowledge/GetList', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(params)
                });

                const result = await response.json();

                if (result.success) {
                    renderKnowledgeTable(result.data.items);
                    renderPagination(result.data.page, result.data.totalPages, result.data.total);
                    currentPage = result.data.page;
                    totalPages = result.data.totalPages;
                } else {
                    showAlert('加载失败：' + result.msg, 'danger');
                }
            } catch (error) {
                console.error('加载知识库列表失败:', error);
                showAlert('加载失败：网络错误', 'danger');
            } finally {
                showLoading(false);
            }
        }

        // 渲染知识库表格
        function renderKnowledgeTable(items) {
            const tbody = document.getElementById('knowledgeTableBody');

            if (!items || items.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted py-4">
                            <i class="bi bi-inbox fs-1"></i>
                            <p class="mt-2">暂无数据</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = items.map(item => `
                <tr>
                    <td>
                        <strong>${escapeHtml(item.name)}</strong>
                    </td>
                    <td>
                        <code class="text-muted">${escapeHtml(item.doubaoName)}</code>
                    </td>
                    <td>
                        <span title="${escapeHtml(item.description)}">
                            ${truncateText(escapeHtml(item.description), 50)}
                        </span>
                    </td>
                    <td>
                        <span class="badge ${item.dataType === 'unstructured_data' ? 'bg-primary' : 'bg-success'} data-type-badge">
                            ${item.dataTypeDisplayName}
                        </span>
                    </td>
                    <td>${escapeHtml(item.creator)}</td>
                    <td>
                        <small class="text-muted">${item.createTimeText}</small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary" onclick="editKnowledge('${item.id}')" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteKnowledge('${item.id}', '${escapeHtml(item.name)}')" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染分页
        function renderPagination(currentPage, totalPages, totalCount) {
            const pagination = document.getElementById('pagination');

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHtml = '';

            // 上一页
            paginationHtml += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadKnowledgeList(${currentPage - 1}); return false;">上一页</a>
                </li>
            `;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadKnowledgeList(1); return false;">1</a></li>`;
                if (startPage > 2) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationHtml += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadKnowledgeList(${i}); return false;">${i}</a>
                    </li>
                `;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadKnowledgeList(${totalPages}); return false;">${totalPages}</a></li>`;
            }

            // 下一页
            paginationHtml += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadKnowledgeList(${currentPage + 1}); return false;">下一页</a>
                </li>
            `;

            pagination.innerHTML = paginationHtml;
        }

        // 搜索知识库
        function searchKnowledge() {
            currentPage = 1;
            loadKnowledgeList(1);
        }

        // 创建知识库
        async function createKnowledge() {
            const form = document.getElementById('createForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const name = document.getElementById('createName').value.trim();
            const description = document.getElementById('createDescription').value.trim();
            const dataType = document.getElementById('createDataType').value;

            // 检查名称是否已存在
            if (!(await checkNameExists(name))) {
                return;
            }

            const createData = {
                name: name,
                description: description || null,
                dataType: dataType
            };

            // 高级配置
            if (document.getElementById('showAdvanced').checked) {
                const vectorModel = document.getElementById('vectorModel').value.trim();
                const chunkLength = document.getElementById('chunkLength').value;
                const indexType = document.getElementById('indexType').value;
                const dimension = document.getElementById('dimension').value;

                if (vectorModel) {
                    createData.vectorModel = vectorModel;
                }

                if (chunkLength) {
                    createData.preprocessing = {
                        chunkingStrategy: "custom_balance",
                        chunkLength: parseInt(chunkLength)
                    };
                }

                createData.indexConfig = {
                    type: indexType,
                    dimension: parseInt(dimension) || 768
                };
            }

            try {
                showSpinner('createSpinner', true);

                const response = await fetch('http://localhost:9910/api/Knowledge/Create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(createData)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('知识库创建成功！', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('createModal')).hide();
                    form.reset();
                    loadKnowledgeList(currentPage);
                } else {
                    showAlert('创建失败：' + result.msg, 'danger');
                }
            } catch (error) {
                console.error('创建知识库失败:', error);
                showAlert('创建失败：网络错误', 'danger');
            } finally {
                showSpinner('createSpinner', false);
            }
        }

        // 检查名称是否存在
        async function checkNameExists(name, excludeId = null) {
            try {
                const url = `http://localhost:9910/api/Knowledge/CheckName?name=${encodeURIComponent(name)}${excludeId ? `&excludeId=${excludeId}` : ''}`;
                const response = await fetch(url);
                const result = await response.json();

                if (result.success && result.data) {
                    showAlert('知识库名称已存在，请使用其他名称', 'warning');
                    return false;
                }
                return true;
            } catch (error) {
                console.error('检查名称失败:', error);
                return true; // 检查失败时允许继续
            }
        }

        // 编辑知识库
        async function editKnowledge(id) {
            try {
                const response = await fetch(`http://localhost:9910/api/Knowledge/GetDetail/${id}`);
                const result = await response.json();

                if (result.success) {
                    document.getElementById('editId').value = result.data.id;
                    document.getElementById('editName').value = result.data.name;
                    document.getElementById('editDescription').value = result.data.description;

                    const editModal = new bootstrap.Modal(document.getElementById('editModal'));
                    editModal.show();
                } else {
                    showAlert('获取知识库详情失败：' + result.msg, 'danger');
                }
            } catch (error) {
                console.error('获取知识库详情失败:', error);
                showAlert('获取详情失败：网络错误', 'danger');
            }
        }

        // 更新知识库
        async function updateKnowledge() {
            const form = document.getElementById('editForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const id = document.getElementById('editId').value;
            const name = document.getElementById('editName').value.trim();
            const description = document.getElementById('editDescription').value.trim();

            // 检查名称是否已存在（排除当前记录）
            if (name && !(await checkNameExists(name, id))) {
                return;
            }

            const updateData = {
                id: id,
                name: name || null,
                description: description || null
            };

            try {
                showSpinner('editSpinner', true);

                const response = await fetch('http://localhost:9910/api/Knowledge/Update', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('知识库更新成功！', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                    loadKnowledgeList(currentPage);
                } else {
                    showAlert('更新失败：' + result.msg, 'danger');
                }
            } catch (error) {
                console.error('更新知识库失败:', error);
                showAlert('更新失败：网络错误', 'danger');
            } finally {
                showSpinner('editSpinner', false);
            }
        }

        // 删除知识库
        async function deleteKnowledge(id, name) {
            if (!confirm(`确定要删除知识库"${name}"吗？此操作不可恢复！`)) {
                return;
            }

            try {
                const response = await fetch(`http://localhost:9910/api/Knowledge/Delete/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('知识库删除成功！', 'success');
                    loadKnowledgeList(currentPage);
                } else {
                    showAlert('删除失败：' + result.msg, 'danger');
                }
            } catch (error) {
                console.error('删除知识库失败:', error);
                showAlert('删除失败：网络错误', 'danger');
            }
        }

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch('http://localhost:9910/api/Knowledge/GetStatistics');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('totalCount').textContent = result.data.total || 0;
                    document.getElementById('unstructuredCount').textContent = result.data.unstructured_data || 0;
                    document.getElementById('structuredCount').textContent = result.data.structured_data || 0;

                    const statisticsModal = new bootstrap.Modal(document.getElementById('statisticsModal'));
                    statisticsModal.show();
                } else {
                    showAlert('获取统计信息失败：' + result.msg, 'danger');
                }
            } catch (error) {
                console.error('获取统计信息失败:', error);
                showAlert('获取统计信息失败：网络错误', 'danger');
            }
        }

        // 工具函数
        function showLoading(show) {
            const loading = document.getElementById('loading');
            loading.style.display = show ? 'block' : 'none';
        }

        function showSpinner(spinnerId, show) {
            const spinner = document.getElementById(spinnerId);
            if (show) {
                spinner.classList.remove('d-none');
            } else {
                spinner.classList.add('d-none');
            }
        }

        function showAlert(message, type = 'info') {
            // 创建alert元素
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function truncateText(text, maxLength) {
            if (!text || text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }
    </script>
</body>
</html>
