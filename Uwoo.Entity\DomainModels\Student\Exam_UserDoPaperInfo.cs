﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Student
{
    /// <summary>
    /// 做卷记录
    /// </summary>
    [Table("Exam_UserDoPaperInfo")]
    public class Exam_UserDoPaperInfo
    {
        /// <summary>
        /// Id
        /// </summary>
        [Key, Column(Order = 1)]
        [SugarColumn(IsPrimaryKey = true)]
        public String Id { get; set; }

        /// <summary>
        /// PaperId
        /// </summary>
        public String PaperId { get; set; }

        /// <summary>
        /// UserId
        /// </summary>
        public String UserId { get; set; }


        /// <summary>
        /// CreateTime
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// ErrorCount
        /// </summary>
        public Int32? ErrorCount { get; set; }

        /// <summary>
        /// 答题数量
        /// </summary>
        public Int32? PaperItemCount { get; set; }

        /// <summary>
        /// Score
        /// </summary>
        public Decimal? Score { get; set; }

        /// <summary>
        /// SpendSecondTime
        /// </summary>
        public Int32? SpendSecondTime { get; set; }

        /// <summary>
        /// 试卷提交类型  0:在线练习提交   1:点阵笔作答提交
        /// </summary>
        public int SubType { get; set; } = 0;

        /// <summary>
        /// 平台来源（1专课专练、2三个助手）
        /// </summary>
        public int Source { get; set; }
    }
}
