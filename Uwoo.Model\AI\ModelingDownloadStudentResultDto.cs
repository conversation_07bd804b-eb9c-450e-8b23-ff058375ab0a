﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 教师端建模_下载学生评估结果Dto
    /// </summary>
    public class ModelingDownloadStudentResultDto
    {
        /// <summary>
        /// 建模阶段任务Id
        /// </summary>
        public string? ModelingStageTaskId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生名称
        /// </summary>
        public string? RealName { get; set; }

        /// <summary>
        /// 评估结果
        /// </summary>
        public string? AssessmentResult { get; set; }

        /// <summary>
        /// 提交顺序
        /// </summary>
        public int Order { get; set; }
    }
}
