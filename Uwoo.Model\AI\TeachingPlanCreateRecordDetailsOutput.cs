﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 教案创建记录详情输出
    /// </summary>
    public class TeachingPlanCreateRecordDetailsOutput
    {
        /// <summary>
        /// 教案生成记录Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 教案名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 创建类型（1：标题创建、2：文本创建、3：章节创建、4：文档创建）
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 年级
        /// </summary>
        public int Grade { get; set; }

        /// <summary>
        /// 教案标题（用于标题创建逻辑处理AI指令）
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 其他要求
        /// </summary>
        public string? Demand { get; set; }

        /// <summary>
        /// 教案内容
        /// </summary>
        public string? Content { get; set; }

        /// <summary>
        /// 章节ID
        /// </summary>
        public string? ChapterId { get; set; }

        /// <summary>
        /// 文件地址
        /// </summary>
        public string? FileUrl { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 模型ID
        /// </summary>
        public string? ModelId { get; set; }

        /// <summary>
        /// 学科ID
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 教案文本
        /// </summary>
        public string? TeachingPlanText { get; set; }

        /// <summary>
        /// 内容要求Id
        /// </summary>
        public List<string> ContentDemandId { get; set; } = new List<string>();
    }
}
