﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model
{
    public class UniUserQo
    {
        /// <summary>
        /// UserId
        /// </summary>
        [DataMember(Name = "user_id")]
        public string UserId { get; set; }

        /// <summary>
        /// GroupId
        /// </summary>
        [DataMember(Name = "group_id")]
        public int GroupId { get; set; }

        /// <summary>
        /// UserName
        /// </summary>
        [DataMember(Name = "user_name")]
        public string UserName { get; set; }

        /// <summary>
        /// NickName
        /// </summary>
        [DataMember(Name = "nickname")]
        public string NickName { get; set; }

        /// <summary>
        /// Birthday
        /// </summary>
        [DataMember(Name = "birthday")]
        public string Birthday { get; set; }

        /// <summary>
        /// Gender
        /// </summary>
        [DataMember(Name = "gender")]
        public string Gender { get; set; }

        /// <summary>
        /// Email
        /// </summary>
        [DataMember(Name = "email")]
        public string Email { get; set; }

        /// <summary>
        /// 0: 禁用，1: 正常，2: 未通过验证。
        /// </summary>
        [DataMember(Name = "active_flag")]
        public int ActiveFlag { get; set; }

        /// <summary>
        /// -1: 公共帐号，0: 一般成员，1: 所属组组长，2: 超级管理员)
        /// </summary>
        [DataMember(Name = "admin_flag")]
        public int AdminFlag { get; set; }

        /// <summary>
        /// 用户身份代码: 有 5 位，从左往右，每一位代表一种身份，分别表示“一般注册用户”、“行
        ///政管理人员”、“教职员工”、“学生”、“家长”，对应位值为 1 时表示用户具有该身份
        /// </summary>
        [DataMember(Name = "user_type")]
        public string UserType { get; set; }

        /// <summary>
        /// 学生用户在徐汇教务系统中的自增用户ID
        /// </summary>
        [DataMember(Name = "xhjw_user_id")]
        public int JwUserId { get; set; }
    }
}
