﻿using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Collections.Concurrent;
using System;
using Uwoo.Core.Attributes;
using Uwoo.Core.Extensions;
using Uwoo.Core.ObjectActionValidator.ExpressValidator;
using Uwoo.Model;
using Microsoft.AspNetCore.Http;
using System.Linq;
using NLog;
using Microsoft.Extensions.Logging;

namespace Uwoo.Core.Filters
{
	public class ActionExecuteFilter : BaseActionFilter, IActionFilter
	{
		private readonly ILogger<ActionExecuteFilter> _logger;

		public ActionExecuteFilter(ILogger<ActionExecuteFilter> logger)
		{
			_logger = logger;
		}

		//private static readonly Logger logger = NLog.LogManager.GetCurrentClassLogger();
		static ConcurrentDictionary<HttpContext, DateTime> _requesTime { get; }
		   = new ConcurrentDictionary<HttpContext, DateTime>();

		/// <summary>
		/// Action执行之前执行
		/// </summary>
		/// <param name="context">过滤器上下文</param>
		public void OnActionExecuting(ActionExecutingContext context)
        {
			_requesTime[context.HttpContext] = DateTime.Now;
		}

        /// <summary>
        /// Action执行完毕之后执行
        /// </summary>
        /// <param name="context"></param>
        public void OnActionExecuted(ActionExecutedContext context)
        {
            if (context.ContainsFilter<NoJsonParamterAttribute>())
                return;

			var time = DateTime.Now - _requesTime[context.HttpContext];
			_requesTime.TryRemove(context.HttpContext, out _);

			string resContent = string.Empty;
			if (context.Result is EmptyResult)
                context.Result = Success();
            else if (context.Result is ObjectResult res)
            {
				if(res.Value == null)
				{
					context.Result= Success();
				}
                else if (res.Value is AjaxResult || res.Value.ToString().Contains("Wps"))
                    context.Result = JsonContent(res.Value.ToJsonString());
                else
                    context.Result = Success(res.Value);

				resContent = res.Value.ToJsonString();

			}

			if (resContent?.Length > 200)
			{
				resContent = new string(resContent.Copy(0, 200).ToArray());
				resContent += "......";
			}

			var Body = context.HttpContext.Request.Body.ReadToString();
			if (Body.Length > 500)
			{
				Body = new string(Body.Copy(0, 500).ToArray());
				Body += "......";
			}
			var request = context.HttpContext.Request;
			HttpReqResDto paras = new HttpReqResDto();
			paras.url = request.GetDisplayUrl();
			paras.method = request.Method;
			paras.contentType = request.ContentType;
			paras.body = Body;
			paras.totalms = (int)time.TotalMilliseconds;
			paras.resData = resContent;

			string log = paras.ToJsonString();
			_logger.LogInformation(log);
		}
	}
}