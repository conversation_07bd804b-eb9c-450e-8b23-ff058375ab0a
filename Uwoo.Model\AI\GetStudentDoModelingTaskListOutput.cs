﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取学生做建模任务列表输出
    /// </summary>
    public class GetStudentDoModelingTaskListOutput
    {
        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生姓名
        /// </summary>
        public string? StudentName { get; set; }

        /// <summary>
        /// 学生头像
        /// </summary>
        public string? StudentLogo { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 建模Id
        /// </summary>
        public string? ModelingId { get; set; }

        /// <summary>
        /// 建模名称
        /// </summary>
        public string? ModelingName { get; set; }

        /// <summary>
        /// 建模Logo
        /// </summary>
        public string? ModelingLogo { get; set; }

        /// <summary>
        /// 建模背景
        /// </summary>
        public string? ModelingIntroduce { get; set; }

        /// <summary>
        /// 建模阶段
        /// </summary>
        public List<GetStudentDoModelingTaskListStageInfoOutput> ModelingStageInfos { get; set; } = new List<GetStudentDoModelingTaskListStageInfoOutput>();
    }

    /// <summary>
    /// 获取学生做建模阶段信息输出
    /// </summary>
    public class GetStudentDoModelingTaskListStageInfoOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 建模阶段任务
        /// </summary>
        public List<GetStudentDoModelingTaskListStageTaskInfoOutput> ModelingStageTaskInfos { get; set; } = new List<GetStudentDoModelingTaskListStageTaskInfoOutput>();
    }

    /// <summary>
    /// 获取学生做建模阶段任务信息输出
    /// </summary>
    public class GetStudentDoModelingTaskListStageTaskInfoOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 建模阶段Id
        /// </summary>
        public string? ModelingStageId { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:问题理解、7:模型构建、8:模型假设、9:模型评价）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 开场白
        /// </summary>
        public string? Prologue { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 音色Id
        /// </summary>
        public string? ToneId { get; set; }

        /// <summary>
        /// 角色名称（用于AI展示名称）
        /// </summary>
        public string? RoleName { get; set; }

        /// <summary>
        /// 学生做建模阶段任务次数
        /// </summary>
        public List<GetStudentDoModelingTaskListNumberOutput> Numbers { get; set; } = new List<GetStudentDoModelingTaskListNumberOutput>();

        /// <summary>
        /// 任务视频
        /// </summary>
        public List<GetStudentDoModelingStageTaskVideoInfoOutput> TaskVideos { get; set; } = new List<GetStudentDoModelingStageTaskVideoInfoOutput>();

        /// <summary>
        /// 任务文档
        /// </summary>
        public List<GetStudentDoModelingStageTaskDocumentInfoOutput> TaskDocuments { get; set; } = new List<GetStudentDoModelingStageTaskDocumentInfoOutput>();

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 最后一次提交时间
        /// </summary>
        public string? SubmitTime { get; set; }
    }

    /// <summary>
    /// 学生做建模阶段任务次数
    /// </summary>
    public class GetStudentDoModelingTaskListNumberOutput
    {
        /// <summary>
        /// 次数
        /// </summary>
        public int Number { get; set; }

        /// <summary>
        /// 是否备份
        /// </summary>
        public bool IsBackups { get; set; }

        /// <summary>
        /// 任务提交记录Id
        /// </summary>
        public string? TaskSubmitId { get; set; }
    }

    /// <summary>
    /// 获取学生端建模阶段任务视频信息输出
    /// </summary>
    public class GetStudentDoModelingStageTaskVideoInfoOutput
    {
        /// <summary>
        /// 视频Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 建模阶段任务Id
        /// </summary>
        public string? ModelingStageTaskId { get; set; }

        /// <summary>
        /// 视频名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 视频地址
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// 视频大小
        /// </summary>
        public int Size { get; set; }

        /// <summary>
        /// 视频时长（秒）
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 是否观看
        /// </summary>
        public bool IsWatch { get; set; }

        /// <summary>
        /// 观看累计时长（秒）
        /// </summary>
        public int TotalWatchDuration { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Order { get; set; }
    }

    /// <summary>
    /// 获取学生端建模阶段任务文档信息输出
    /// </summary>
    public class GetStudentDoModelingStageTaskDocumentInfoOutput
    {
        /// <summary>
        /// 文档Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 建模阶段任务Id
        /// </summary>
        public string? ModelingStageTaskId { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 文件地址
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public int Size { get; set; }

        /// <summary>
        /// 是否阅读
        /// </summary>
        public bool IsRead { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Order { get; set; }
    }
}
