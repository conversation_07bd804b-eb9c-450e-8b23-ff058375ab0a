﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Student
{
    /// <summary>
    /// 学生做题答案，试卷id，题目id，学生id
    /// </summary>
    public class Exam_PaperUserAnswer
    {
        /// <summary>
        /// Id
        /// </summary>
        [Key, Column(Order = 1)]
        [SugarColumn(IsPrimaryKey = true)]
        public String Id { get; set; }

        /// <summary>
        /// 外键-做卷记录表Id
        /// </summary>
        public String DoPaperId { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public String UserId { get; set; }

        /// <summary>
        /// 试题Id
        /// </summary>
        public String ItemId { get; set; }

        /// <summary>
        /// 用户答案
        /// </summary>
        public String UserAnswer { get; set; }

        /// <summary>
        /// 得分
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 是否正确
        /// </summary>
        public bool Result { get; set; }

        /// <summary>
        /// 该题答题正确的答题处数
        /// </summary>
        public int RightAnswerCount { get; set; }

        /// <summary>
        /// 该题总作答处数 , 在匹配答案逻辑中，根据试题的答案来判定作答处数
        /// </summary>
        public int AllAnswerCount { get; set; } = 100;

        /// <summary>
        /// 做题时间
        /// </summary>
        //[SugarColumn(IsOnlyIgnoreUpdate = true)]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 单题做题时间
        /// </summary>
        public int ATime { get; set; }

        /// <summary>
        /// 试卷提交类型  0:在线练习提交   1:点阵笔作答提交;2 智慧扫描提交
        /// </summary>
        public int? SubType { get; set; } = 0;

        /// <summary>
        /// 平台来源（1专课专练、2三个助手）
        /// </summary>
        public int Source { get; set; }
    }
}
