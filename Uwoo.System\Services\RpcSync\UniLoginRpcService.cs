using log4net;
using Mapster;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UwooAgent.Entity.DomainModels.User;
using UwooAgent.Model;
using UwooAgent.System.IServices;
using UwooAgent.System.IServices.Login;

namespace UwooAgent.System.Services
{
    public class UniLoginRpcService : IUniLoginRpcService
    {
        private readonly IUniUserInfoService _uniUserInfoService;
        private readonly ILogger<UniLoginRpcService> _log;

        public UniLoginRpcService(IUniUserInfoService uniUserInfoService, ILogger<UniLoginRpcService> log)
        {
            _uniUserInfoService = uniUserInfoService;
            _log = log;
        }

        public bool AddGroup(UniGroupQo qo)
        {
            _log.LogInformation("[UniLogInfo]: {Methodname} , raw: {Raw}", nameof(AddGroup), JsonConvert.SerializeObject(qo));
            var group = _uniUserInfoService.GetGroupByGid(qo.GroupId);
            if (group == null)
            {
                group = new UniGroupInfo
                {
                    Oid = Guid.NewGuid()
                };
                qo.Adapt(group);
                _uniUserInfoService.AddGroup(group);
            }
            else
            {
                qo.Adapt(group);
                _uniUserInfoService.UpdateGroup(group);
            }

            return true;
        }

        public bool AddUser(UniUserQo qo)
        {
            var user = _uniUserInfoService.GetUserByUid(qo.UserId);
            if (user == null)
            {
                user = new UniUserInfo
                {
                    Oid = Guid.NewGuid()
                };
                qo.Adapt(user);
                _uniUserInfoService.Add(user);
            }
            else
            {
                qo.Adapt(user);
                _uniUserInfoService.Update(user);
            }

            return true;
        }

        public bool DeleteGroup(int id)
        {
            _log.LogInformation("[UniLogInfo]: {Methodname} , raw: {Raw}", nameof(DeleteGroup), id);
            return _uniUserInfoService.DeleteGroup(id);
        }

        public bool DeleteUser(string userid)
        {
            userid = userid.Replace("'", "");
            _log.LogInformation("[UniLogInfo]: {Methodname} , raw: {Raw}", nameof(DeleteUser), userid);
            return _uniUserInfoService.DeleteUser(userid);
        }

        public bool SyncGroup(UniGroupQo qo)
        {
            _log.LogInformation("[UniLogInfo]: {Methodname} , raw: {Raw}", nameof(SyncGroup), JsonConvert.SerializeObject(qo));
            var group = _uniUserInfoService.GetGroupByGid(qo.GroupId);
            if (group == null)
            {
                group = new UniGroupInfo
                {
                    Oid = Guid.NewGuid()
                };
                qo.Adapt(group);
                _uniUserInfoService.AddGroup(group);
            }
            else
            {
                qo.Adapt(group);
                _uniUserInfoService.UpdateGroup(group);
            }

            return true;
        }

        public bool SyncUser(UniUserQo qo)
        {
            var user = _uniUserInfoService.GetUserByUid(qo.UserId);
            if (user == null)
            {
                user = new UniUserInfo
                {
                    Oid = Guid.NewGuid()
                };
                qo.Adapt(user);
                _uniUserInfoService.Add(user);
            }
            else
            {
                qo.Adapt(user);
                _uniUserInfoService.Update(user);
            }

            return true;
        }

        public bool UpdateGroup(UniGroupQo qo)
        {
            _log.LogInformation("[UniLogInfo]: {Methodname} , raw: {Raw}", nameof(UpdateGroup), JsonConvert.SerializeObject(qo));
            var group = _uniUserInfoService.GetGroupByGid(qo.GroupId);
            if (group == null)
            {
                group = new UniGroupInfo
                {
                    Oid = Guid.NewGuid()
                };
                qo.Adapt(group);
                _uniUserInfoService.AddGroup(group);
            }
            else
            {
                qo.Adapt(group);
                _uniUserInfoService.UpdateGroup(group);
            }

            return true;
        }

        public bool UpdateUser(UniUserQo qo)
        {
            var user = _uniUserInfoService.GetUserByUid(qo.UserId);
            if (user == null)
            {
                user = new UniUserInfo
                {
                    Oid = Guid.NewGuid()
                };
                qo.Adapt(user);
                _uniUserInfoService.Add(user);
            }
            else
            {
                qo.Adapt(user);
                _uniUserInfoService.Update(user);
            }

            return true;
        }
    }
}
