using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 阅读理解视频任务配置表
    /// </summary>
    [SugarTable("RC_VideoTaskConfig")]
    public class RC_VideoTaskConfig
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 阅读理解项目阶段任务Id
        /// </summary>
        public string ReadingProjectStageTaskId { get; set; }

        /// <summary>
        /// 总观看时长要求（分钟）
        /// </summary>
        public int? TotalWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否要求观看所有视频
        /// </summary>
        public bool IsWatchAllVideos { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
