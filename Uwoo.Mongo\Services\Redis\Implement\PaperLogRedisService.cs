﻿// -- Function：PaperLogRedisService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/31 17:41

namespace Uwoo.Mongo.Services.Redis.Implement;

using Uwoo.Contracts.Paper;
using Uwoo.Contracts.Redis;
using Uwoo.Mongo.Interfaces.Redis.Business;
using Uwoo.Mongo.Models;
using X.PenServer.Contracts.Queue;


/// <inheritdoc />
public class PaperLogRedisService : RedisService, IPaperLogRedisService
{
    #region Overrides of RedisService

    /// <inheritdoc />
    public override string Prefix => RedisKeys.PAPER_LOG;

    /// <inheritdoc />
    public async Task<CurrentPaperId> GetCurrentPaperIdAsync(string classid)
    {
        var key = $"CurrentPaper|{classid}";
        return await GetAsync<CurrentPaperId>(key);
    }

    /// <inheritdoc />
    public async Task<WorkbookPage> GetStudentCurrentDoPaperPageInfoAsync(string userid)
    {
        var key = $"StudentCurrentWorkPage|{userid}";
        return await GetAsync<WorkbookPage>(key);
    }

    /// <inheritdoc />
    public async Task DeleteStudentCurrentDoPaperPageInfoAsync(string userid)
    {
        var key = $"StudentCurrentWorkPage|{userid}";
        await DeleteAsync(key);
    }

    /// <inheritdoc />
    public async Task SetStudentCurrentDoPaperPageInfoAsync(string userid, WorkbookPage workpage)
    {
        var key = $"StudentCurrentWorkPage|{userid}";
        await SetAsync(key, workpage, TimeSpan.FromHours(2));
    }

    /// <inheritdoc />
    public async Task HSetUserPaperNumAsync(string userid, List<PenDot> dots)
    {
        var key = $"PaperTempNum|{userid}";
        await SAddAsync(key, dots);
    }

    /// <inheritdoc />
    public async Task<List<PenDot>> HGetUserPaperNumAsync(string userid)
    {
        var key = $"PaperTempNum|{userid}";
        return await SMembersAsync<PenDot>(key);
    }

    /// <inheritdoc />
    public async Task SaveRecognitionNumberAsync(string userid, string paperno)
    {
        var key = $"RecognitionNumber|{userid}";
        await SetAsync(key, paperno, TimeSpan.FromHours(2));
    }

    /// <inheritdoc />
    public async Task HDelUserPaperNumAsync(string userid)
    {
        var key = $"PaperTempNum|{userid}";
        await SRemoveAsync(key);
    }

    /// <inheritdoc />
    public async Task<string> HGetPaperNumAsync(string paperno)
    {
        const string key = "EduwonPaperNum";
        return await HGetAsync(key, paperno);
    }

    /// <inheritdoc />
    public async Task HSetPaperNumAsync(string paperno, string paperid)
    {
        const string key = "EduwonPaperNum";
        await HSetAsync(key, paperno, paperid);
    }

    /// <inheritdoc />
    public async Task<string> HGetCorrectAsync(string classid, string paperid)
    {
        const string key = "IsEachCorrect";
        var field = $"{classid}|{paperid}";
        return await HGetAsync(key, field);
    }

    /// <inheritdoc />
    public async Task<bool> IsPageMemberAsync(string pageid)
    {
        const string key = "IsPageMember";
        return await IsSMemberAsync(key, pageid);
    }

    /// <inheritdoc />
    public async Task<string> HGetEachCorrectStudentAsync(string paperid, string userid)
    {
        const string key = "StudentEachCorrect";
        var field = $"{paperid}|{userid}";
        return await HGetAsync(key, field);
    }

    /// <inheritdoc />
    public async Task HSetEachCorrectStudentAsync(string paperid, string current_studentid, string studentid)
    {
        const string key = "StudentEachCorrect";
        var field = $"{paperid}|{current_studentid}";
        await HSetAsync(key, field, studentid);
    }

    /// <inheritdoc />
    public async Task SAddStudentCurrentPaperInfoAsync(string studentid, string paperid, string pageid, TeacherPenLog teacher_log)
    {
        var key = $"CurrentEachCorrect|{studentid}|{paperid}|{pageid}";
        await SAddAsync(key, teacher_log);
    }

    /// <inheritdoc />
    public async Task DeleteRecognitionNumberAsync(string userid)
    {
        var key = $"RecognitionNumber|{userid}";
        await DeleteAsync(key);
    }

    /// <inheritdoc />
    public void SetCurrentPaperId(string teacherid, string classid, string paperid)
    {
        var key = $"CurrentPaper|{classid}";
        var data = new CurrentPaperId
        {
            TeacherId = teacherid,
            ClassId = classid,
            PaperId = paperid
        };
        Set(key, data, TimeSpan.FromHours(2));
    }

    /// <summary>
    /// 设置当前正在作答的试题
    /// </summary>
    /// <param name="teacherid"></param>
    /// <param name="classid"></param>
    /// <param name="paperid"></param>
    /// <param name="itemid"></param>
    /// <param name="is_fullline"></param>
    public void SetCurrentItemId(string teacherid, string classid, string paperid, string itemid, bool is_fullline = false)
    {
        var key = $"CurrentPaper|{classid}|{paperid}";
        var data = new CurrentItem
        {
            TeacherId = teacherid,
            ClassId = classid,
            ItemId = itemid,
            PaperId = paperid,
            IsFullLine = is_fullline
        };
        Set(key, data, TimeSpan.FromHours(2));
    }

	/// <inheritdoc />
	public CurrentItem GetCurrentItem(string classid, string paperid)
    {
        var key = $"CurrentPaper|{classid}|{paperid}";
        return Get<CurrentItem>(key);
    }

    /// <inheritdoc />
    public void RemoveCurrentItemId(string classid, string paperid)
    {
        var key = $"CurrentPaper|{classid}|{paperid}";
        Delete(key);
    }

    /// <inheritdoc />
    public void RemoveCurrentPaperId(string classid)
    {
        var key = $"CurrentPaper|{classid}";
        Delete(key);
    }

    /// <inheritdoc />
    public CurrentPaperId GetCurrentPaperId(string classid)
    {
        var key = $"CurrentPaper|{classid}";
        return Get<CurrentPaperId>(key);
    }

    /// <inheritdoc />
    public void SetCurrentPaperIdPermanent(string teacherid, string classid, string paperid)
    {
        var key = $"CurrentPaper_Permanent|{classid}";
        var data = new CurrentPaperId
        {
            TeacherId = teacherid,
            ClassId = classid,
            PaperId = paperid
        };
        Set(key, data, new TimeSpan(10, 0, 0, 0));
    }

    /// <summary>
    /// 设置试卷的作答方式 
    /// </summary>
    /// <param name="classId"></param>
    /// <param name="paperId"></param>
    /// <param name="modeNum">1 整卷作答；2单题作答</param>
    public void SetPaperAnswerMode(string classId, string paperId, int modeNum)
    {
        var key = $"PaperAnswerMode|{classId}|{paperId}";
        Set(key, modeNum);
    }

	/// <inheritdoc />
	public int? GetPaperAnswerMode(string classId, string paperId)
    {
        var key = $"PaperAnswerMode|{classId}|{paperId}";
        var answerMode = Get<int>(key);
        if (answerMode == 0)
            return null;
        return answerMode;
    }

    /// <inheritdoc />
    public void RemovePaperAnswerMode(string classid, string paperid)
    {
        var key = $"PaperAnswerMode|{classid}|{paperid}";
        Delete(key);
    }

	/// <inheritdoc />
	public void RemoveSingleItemAnswerState(string classid,string paperid)
    {
		var hashkey = $"SingleItemAnswer|{classid}|{paperid}";
        Delete(hashkey);
	}

	/// <inheritdoc />
	public void RemoveCurrentPaperIdPermanent(string classid)
    {
        var key = $"CurrentPaper_Permanent|{classid}";
        Delete(key);
    }

    /// <inheritdoc />
    public CurrentPaperId GetCurrentPaperIdPermanent(string classid)
    {
        var key = $"CurrentPaper_Permanent|{classid}";
        return Get<CurrentPaperId>(key);
    }

	/// <inheritdoc />
	public void SetCurrentPaperIdOffline(string teacherid, string classid, string paperid)
    {
        var key = $"CurrentPaper_Offline|{classid}";
        var data = new CurrentPaperId
        {
            TeacherId = teacherid,
            ClassId = classid,
            PaperId = paperid
        };
        Set(key, data);
    }

    /// <inheritdoc />
    public void SAddPageId(string pageid)
    {
        const string key = "IsPageMember";
        SAdd(key, pageid);
    }

    /// <inheritdoc />
    public void HSetCorrect(string classid, string paperid)
    {
        const string key = "IsEachCorrect";
        var field = $"{classid}|{paperid}";
        HSet(key, field, "1");
    }

    /// <inheritdoc />
    public void HDelCorrect(string classid, string paperid)
    {
        const string key = "IsEachCorrect";
        var field = $"{classid}|{paperid}";
        HDelete(key, field);
    }

    /// <summary>
    /// 开启学生互评
    /// </summary>
    /// <param name="classid"></param>
    /// <param name="paperid"></param>
    /// <param name="type"></param>
    public void HSetAndDelComment(string classid, string paperid, int type)
    {
        const string key = "IsEachComment";
        var field = $"{classid}|{paperid}";
        if (type == 1)
        {
            HSet(key, field, "1");
        }
        else
        {
            HDelete(key, field);
        }
    }

	/// <inheritdoc />
	public int HGetCommentState(string classid, string paperid)
    {
        const string key = "IsEachComment";
        var field = $"{classid}|{paperid}";

        return HGet<int>(key, field);
    }

    /// <inheritdoc />
    public List<TeacherPenLog> SMemberStudentCurrentPaperInfo(string studentid, string paperid, string pageid)
    {
        var key = $"CurrentEachCorrect|{studentid}|{paperid}|{pageid}";
        return SMembers<TeacherPenLog>(key);
    }

    #endregion
}