﻿using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.System.IServices.Mq;

namespace Uwoo.System.Services.Mq
{
	public class MqProducer : IMqProducer
	{
		private static readonly object Lock = new object();
		private static readonly object ChannelLock = new object();

		private RabbitMQ.Client.IModel channel;

		private static readonly string RabbitHost = AppSetting.RabbitMQConfig.HostName;
		private static readonly string RabbitUserName = AppSetting.RabbitMQConfig.UserName;
		private static readonly string RabbitPassword = AppSetting.RabbitMQConfig.Password;
		private static readonly string RabbitPort = AppSetting.RabbitMQConfig.Port.ToString();

		private readonly ILogger<MqProducer> _logger;
		public MqProducer(ILogger<MqProducer> logger)
		{
			_logger = logger;
			try
			{
				var factory = new ConnectionFactory()
				{
					HostName = RabbitHost,
					UserName = RabbitUserName,
					Password = RabbitPassword,
					Port = RabbitPort.ToInt(),
					RequestedHeartbeat = TimeSpan.FromSeconds(20)
				};
				var connection = factory.CreateConnection();
				channel = connection.CreateModel();
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, ex.Message);
			}
		}

		public virtual void PushMessage(string routingKey, object message)
		{

			if (channel == null)
			{
				InitMq();
			}

			lock (ChannelLock)
			{
				channel?.QueueDeclare(queue: "message", durable: false, exclusive: false, autoDelete: false,
					arguments: null);
			}

			string msgJson = message.ToJsonString();
			var body = Encoding.UTF8.GetBytes(msgJson);
			try
			{
				if (channel == null)
				{
					return;
				}

				lock (channel)
				{
					if (channel.IsOpen)
					{
						channel.BasicPublish(exchange: "message",
							routingKey: routingKey,
							basicProperties: null,
							body: body);
					}
				}
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, ex.Message);
			}
		}

		public virtual void PushMessage(string routingKey, string exchangeName, object message)
		{

			if (channel == null)
			{
				InitMq();
			}

			lock (ChannelLock)
			{
				channel.ExchangeDeclare(exchange: exchangeName, type: "direct");
			}

			string msgJson = message.ToJsonString();
			var body = Encoding.UTF8.GetBytes(msgJson);
			try
			{
				if (channel == null)
				{
					return;
				}
				lock (channel)
				{
					var properties = channel.CreateBasicProperties();
					properties.DeliveryMode = 2; // persistent
					if (channel.IsOpen)
					{
						channel.BasicPublish(
							exchange: exchangeName,
							routingKey: routingKey,
							mandatory: true,
							basicProperties: properties,
							body: body);
					}
				}
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, ex.Message);
			}
		}

		private void InitMq()
		{
			try
			{
				lock (Lock)
				{
					if (channel == null)
					{
						var factory = new ConnectionFactory()
						{
							HostName = RabbitHost,
							UserName = RabbitUserName,
							Password = RabbitPassword,
							Port = RabbitPort.ToInt(),
							RequestedHeartbeat = TimeSpan.FromSeconds(20)
						};
						var connection = factory.CreateConnection();
						channel = connection.CreateModel();
					}
				}
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, ex.Message);
			}
		}
	}
}
