﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 创建上下文接口入参
    /// </summary>
    public class CreateContextInput
    {
        /// <summary>
        /// 创建消息
        /// </summary>
        public string? Msg { get; set; }

        /// <summary>
        /// 缓存过期时间（单位:秒）
        /// </summary>
        public int TimeOut { get; set; }

        /// <summary>
        /// 模型Id
        /// </summary>
        public string? modelId { get; set; }
    }
}
