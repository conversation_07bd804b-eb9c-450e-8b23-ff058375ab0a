﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Base
{
    /// <summary>
    /// 章节
    /// </summary>
    [Entity(TableCnName = "章节目录字典表（原：Exam_SubjectChapter）", TableName = "Base_ChapterContents")]
    public partial class Base_ChapterContents : BaseEntity
    {
        /// <summary>
        ///章节ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Key]
        [Display(Name = "章节ID")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public string Id { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "ParentId")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string ParentId { get; set; }

        /// <summary>
        ///章节名称
        /// </summary>
        [Display(Name = "章节名称")]
        [MaxLength(500)]
        [Column(TypeName = "varchar(500)")]
        [Editable(true)]
        public string ChapterName { get; set; }

        /// <summary>
        ///排序
        /// </summary>
        [Display(Name = "排序")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? OrderId { get; set; }

        /// <summary>
        ///学科ID
        /// </summary>
        [Display(Name = "学科ID")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string SubjectId { get; set; }

        /// <summary>
        ///学期
        /// </summary>
        [Display(Name = "学期")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? WeekId { get; set; }

        /// <summary>
        ///小节
        /// </summary>
        [Display(Name = "小节")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string IDNum { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "NewIdNum")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string NewIdNum { get; set; }

        /// <summary>
        ///层级
        /// </summary>
        [Display(Name = "层级")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? level { get; set; }

        /// <summary>
        ///版本( 是否还有用?)
        /// </summary>
        [Display(Name = "版本( 是否还有用?)")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? Versions { get; set; }

        /// <summary>
        ///教软对接ID
        /// </summary>
        [Display(Name = "教软对接ID")]
        [MaxLength(100)]
        [Column(TypeName = "varchar(100)")]
        [Editable(true)]
        public string OriginalId { get; set; }

        /// <summary>
        ///教材版本
        /// </summary>
        [Display(Name = "教材版本")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string TextbookId { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        [Display(Name = "备注")]
        [MaxLength(500)]
        [Column(TypeName = "varchar(500)")]
        [Editable(true)]
        public string Remark { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "起始页")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? StartPage { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? EndPage { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "CreateTime")]
        [Column(TypeName = "datetime")]
        [Editable(true)]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "Creator")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string Creator { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "ModifyTime")]
        [Column(TypeName = "datetime")]
        [Editable(true)]
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "Modifier")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string Modifier { get; set; }
    }
}
