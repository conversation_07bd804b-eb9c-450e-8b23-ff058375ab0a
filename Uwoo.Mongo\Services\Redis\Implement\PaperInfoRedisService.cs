﻿// -- Function：PaperInfoRedisService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/11/01 13:52

namespace Uwoo.Mongo.Services.Redis.Implement;

using Uwoo.Contracts.Paper;
using Uwoo.Mongo.Interfaces.Redis.Business;

/// <summary>
/// 试卷信息
/// </summary>
public class PaperInfoRedisService : RedisService, IPaperInfoRedisService
{
    #region Overrides of RedisService

    /// <inheritdoc />
    public override string Prefix => RedisKeys.PAPER_INFO;

    /// <inheritdoc />
    public async Task HSetPaperPageInfoAsync(string paperid, int page, WorkbookPage workpage)
    {
        var key = $"PaperPage|{paperid}";
        await HSetAsync(key, page.ToString(), workpage);
    }

    /// <inheritdoc />
    public async Task<WorkbookPage> HGetPaperPageInfoAsync(string paperid, int pageno)
    {
        var key = $"PaperPage|{paperid}";
        return await HGetAsync<WorkbookPage>(key, pageno.ToString());
    }

    /// <inheritdoc />
    public async Task<string> HGetPaperIdAsync(int pageid)
    {
        var key = $"PaperPageId|{pageid.ToString()}";
        return await HGetAsync<string>(key, pageid.ToString());
    }

    /// <inheritdoc />
    public async Task HSetPaperIdAsync(int pageid, string paperid)
    {
        var key = $"PaperPageId|{pageid.ToString()}";
        await HSetAsync(key, pageid.ToString(), paperid);
    }

    /// <inheritdoc />
    public async Task<PaperFinishRange> HGetPaperRangeAsync(string paperid)
    {
        const string key = "PaperFinishRange";
        return await HGetAsync<PaperFinishRange>(key, paperid);
    }

    /// <inheritdoc />
    public async Task HSetPaperRangeAsync(string paperid, PaperFinishRange data)
    {
        const string key = "PaperFinishRange";
        await HSetAsync(key, paperid, data);
    }

    /// <inheritdoc />
    public async Task<int> HGetPaperRangeCountAsync(string paperid)
    {
        const string key = "PaperRangeCount";
        return await HGetAsync<int>(key, paperid);
    }

    /// <inheritdoc />
    public async Task HSetPaperRangeCountAsync(string paperid, int count)
    {
        const string key = "PaperRangeCount";
        await HSetAsync(key, paperid, count.ToString());
    }

    /// <inheritdoc />
    public void HDeletePaperPage(string paperid)
    {
        var key = $"PaperPage|{paperid}";
        Delete(key);
    }


	/// <inheritdoc />
	public void HDeletePaperMarkInfo(string paperid)
    {
        var key = $"PaperItemMarkInfo|{paperid}";
        Delete(key);
    }

    /// <inheritdoc />
    public void HSetPaperIdIsMedia(string paperid)
    {
        const string key = "PaperIsMedia";
        HSet(key, paperid, 1);
    }

    /// <inheritdoc />
    public async Task HSetPaperIdIsMediaAsync(string paperid)
    {
        const string key = "PaperIsMedia";
        await HSetAsync(key, paperid, 1);
    }

    #endregion
}