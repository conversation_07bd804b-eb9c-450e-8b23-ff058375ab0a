﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 发布建模信息入参
    /// </summary>
    public class PublishModelingInfoInput
    {
        /// <summary>
        /// 建模Id
        /// </summary>
        public string? ModelingId { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 班级Id列表
        /// </summary>
        public List<string> ClassIds { get; set; } = new List<string>();

        /// <summary>
        /// 学生Id列表
        /// </summary>
        public List<string> StudentIds { get; set; } = new List<string>();

        /// <summary>
        /// 时间范围(下标0开始时间、下标1结束时间)
        /// </summary>
        public List<DateTime?>? TimeRange { get; set; }

        /// <summary>
        /// 评分公布类型（1提交后、2截至后、3指定时间）
        /// </summary>
        public int? ScorePublishType { get; set; }

        /// <summary>
        /// 评分公布时间（评分公布类型指定时间）
        /// </summary>
        public DateTime? ScorePublishTime { get; set; }
    }
}
