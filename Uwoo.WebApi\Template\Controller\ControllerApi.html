﻿/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果要增加方法请在当前目录下Partial文件夹{TableName}Controller编写
 */
using Microsoft.AspNetCore.Mvc;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Entity.AttributeManager;
using {Namespace}.IServices;
namespace {Namespace}.Controllers
{
    [Route("api/{TableName}")]
    [PermissionTable(Name = "{TableName}")]
    public partial class {TableName}Controller : ApiBaseController<I{TableName}Service>
    {
        public {TableName}Controller(I{TableName}Service service)
        : base(service)
        {
        }
    }
}

