﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 文多多创建token输出
    /// </summary>
    public class WeDuoDuoCreateTokenOutputDto
    {
        /// <summary>
        /// 状态码
        /// </summary>
        public int code { get; set; }

        /// <summary>
        /// 信息
        /// </summary>
        public string? message { get; set; }

        /// <summary>
        /// token信息
        /// </summary>
        public WeDuoDuoCreateTokenDataOutputDto data { get; set; } = new WeDuoDuoCreateTokenDataOutputDto();
    }

    /// <summary>
    /// 文多多创建token输出
    /// </summary>
    public class WeDuoDuoCreateTokenDataOutputDto
    {
        /// <summary>
        /// token
        /// </summary>
        public string? token { get; set; }

        /// <summary>
        /// 过期时间（秒）
        /// </summary>
        public int expireTime { get; set; }
    }
}
