﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// 智能体_学生做建模阶段任务
	/// </summary>
	[Table("AI_StudentDoModelingTask")]
    public class AI_StudentDoModelingTask
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 建模Id
        /// </summary>
        public string ModelingId { get; set; }

        /// <summary>
        /// 建模阶段Id
        /// </summary>
        public string ModelingStageId { get; set; }

        /// <summary>
        /// 建模阶段任务Id
        /// </summary>
        public string ModelingStageTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string StudentId { get; set; }

        /// <summary>
        /// 分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 等第
        /// </summary>
        public string Level { get; set; }

        /// <summary>
        /// 评估结果
        /// </summary>
        public string AssessmentResult { get; set; }

        /// <summary>
        /// 是否达到任务设定标准
        /// </summary>
        public bool IsStandard { get; set; }

        /// <summary>
        /// 提交顺序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
