using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 视频观看状态输出
    /// </summary>
    public class VideoWatchStatusOutput
    {
        /// <summary>
        /// 是否成功记录
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 是否已观看过此视频
        /// </summary>
        public bool HasWatched { get; set; }
    }

    /// <summary>
    /// 视频观看时长输出
    /// </summary>
    public class VideoWatchDurationOutput
    {
        /// <summary>
        /// 是否成功记录
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 累计观看时长（秒）
        /// </summary>
        public int TotalWatchDuration { get; set; }

        /// <summary>
        /// 要求观看时长（秒）
        /// </summary>
        public int? RequiredDuration { get; set; }

        /// <summary>
        /// 是否达到观看要求
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 完成进度（0-100）
        /// </summary>
        public decimal Progress { get; set; }

        /// <summary>
        /// 任务整体完成状态
        /// </summary>
        public bool TaskCompleted { get; set; }
    }

    /// <summary>
    /// 文档阅读状态输出
    /// </summary>
    public class DocumentReadStatusOutput
    {
        /// <summary>
        /// 是否成功记录
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 是否完成任务
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 总文档数
        /// </summary>
        public int TotalDocuments { get; set; }

        /// <summary>
        /// 已读文档数
        /// </summary>
        public int ReadDocuments { get; set; }

        /// <summary>
        /// 完成进度（0-100）
        /// </summary>
        public decimal Progress { get; set; }

        /// <summary>
        /// 未读文档列表
        /// </summary>
        public List<string> UnreadDocuments { get; set; } = new List<string>();
    }

    /// <summary>
    /// 思维导图提交输出
    /// </summary>
    public class MindMapSubmitOutput
    {
        /// <summary>
        /// 是否达标
        /// </summary>
        public bool IsStandard { get; set; }

        /// <summary>
        /// AI评估结果
        /// </summary>
        public string? AssessmentResult { get; set; }

        /// <summary>
        /// 评估分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 提交ID
        /// </summary>
        public string? SubmitId { get; set; }

        /// <summary>
        /// AI评价等第
        /// </summary>
        public string? AIGrade { get; set; }

        /// <summary>
        /// 评估维度详情
        /// </summary>
        public List<MindMapAssessmentDimensionOutput> Dimensions { get; set; } = new List<MindMapAssessmentDimensionOutput>();
    }

    /// <summary>
    /// 思维导图评估维度输出
    /// </summary>
    public class MindMapAssessmentDimensionOutput
    {
        /// <summary>
        /// 维度名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 维度分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 维度评价
        /// </summary>
        public string? Comment { get; set; }
    }

    /// <summary>
    /// 学生任务历史输出
    /// </summary>
    public class StudentTaskHistoryOutput
    {
        /// <summary>
        /// 任务历史列表
        /// </summary>
        public List<StudentTaskHistoryItemOutput> TaskHistoryList { get; set; } = new List<StudentTaskHistoryItemOutput>();

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }
    }

    /// <summary>
    /// 学生任务历史项输出
    /// </summary>
    public class StudentTaskHistoryItemOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 任务类型（1:视频任务，2:文档任务，3:思维导图任务，4:选词填空任务）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务类型名称
        /// </summary>
        public string? TaskTypeName { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompleteTime { get; set; }

        /// <summary>
        /// 评估分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 是否达标
        /// </summary>
        public bool IsStandard { get; set; }

        /// <summary>
        /// AI评价等第
        /// </summary>
        public string? AIGrade { get; set; }

        /// <summary>
        /// AI评估结果
        /// </summary>
        public string? AssessmentResult { get; set; }

        /// <summary>
        /// 提交内容（JSON格式）
        /// </summary>
        public string? SubmitContent { get; set; }
    }
}
