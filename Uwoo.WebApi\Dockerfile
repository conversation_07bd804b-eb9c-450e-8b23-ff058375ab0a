#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
RUN apt-get update; apt-get install libfontconfig1 -y

WORKDIR /app
EXPOSE 9991

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY ["Uwoo.WebApi/Uwoo.WebApi.csproj", "Uwoo.WebApi/"]
COPY ["Uwoo.System/Uwoo.System.csproj", "Uwoo.System/"]
COPY ["Uwoo.Core/Uwoo.Core.csproj", "Uwoo.Core/"]
COPY ["Uwoo.Entity/Uwoo.Entity.csproj", "Uwoo.Entity/"]
COPY ["Uwoo.Order/Uwoo.Order.csproj", "Uwoo.Order/"]
COPY ["Uwoo.AppManager/Uwoo.AppManager.csproj", "Uwoo.AppManager/"]
COPY ["Uwoo.Builder/Uwoo.Builder.csproj", "Uwoo.Builder/"]
RUN dotnet restore "Uwoo.WebApi/Uwoo.WebApi.csproj"
COPY . .
WORKDIR "/src/Uwoo.WebApi"
RUN dotnet build "Uwoo.WebApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Uwoo.WebApi.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Uwoo.WebApi.dll"]