﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI.AgentTeachingPlanAnalysis
{
    public class TeachingPlanDTO
    {
        /// <summary>
        /// 教案ID
        /// </summary>
        public string TeachingPlanId { get; set; }
        /// <summary>
        /// 教案名
        /// </summary>
        public string TeachingPlanName { get; set; }
        /// <summary>
        /// 文件关联ID
        /// </summary>

        public string DZBWordImportHistoryId { get; set; }
        /// <summary>
        /// 版本
        /// </summary>
        public int Version { get; set; }
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName{ get; set; }
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FileUrl { get; set; }
    }
    /// <summary>
    /// 作业DTO
    /// </summary>
    public class PaperDTO
    {
        /// <summary>
        /// 作业ID
        /// </summary>
        public string PaperId { get; set; }
        /// <summary>
        /// 作业名
        /// </summary>
        public string Title { get; set; }

        public int PaperType { get; set; }
    }
    /// <summary>
    /// 总分DTO
    /// </summary>
    public class PaperTotalScoreDTO
    {
        public string PaperId { get; set; }
        /// <summary>
        /// 总分
        /// </summary>
        public int TotalScore { get; set; }
    }
}
