{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\UwooEduPlatfrom\\Uwoo.Model\\Uwoo.Model\\Uwoo.Model.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\UwooEduPlatfrom\\Uwoo.Model\\Uwoo.Model\\Uwoo.Model.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\UwooEduPlatfrom\\Uwoo.Model\\Uwoo.Model\\Uwoo.Model.csproj", "projectName": "Uwoo.Model", "projectPath": "C:\\Users\\<USER>\\Documents\\UwooEduPlatfrom\\Uwoo.Model\\Uwoo.Model\\Uwoo.Model.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\UwooEduPlatfrom\\Uwoo.Model\\Uwoo.Model\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://proget.eduwon.cn/nuget/nuget/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302\\RuntimeIdentifierGraph.json"}}}}}