﻿// -- Function：TeacherCorrectRedisService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/31 11:35

namespace Uwoo.Mongo.Services.Redis.Implement;

using Uwoo.Contracts.Redis;
using Uwoo.Mongo.Interfaces.Redis.Business;


/// <inheritdoc />
public class TeacherCorrectRedisService : RedisService, ITeacherCorrectRedisService
{
    /// <inheritdoc />
    public override string Prefix => RedisKeys.TEACHER_CORRECT;

    #region Implementation of ITeacherCorrectRedisService

    /// <inheritdoc />
    public async Task<TeacherCorrectingViewModel> GetCorrectPaperAsync(string teacherid)
    {
        var key = $"TeacherCorrect|{teacherid}";
        return await GetAsync<TeacherCorrectingViewModel>(key);
    }

    /// <inheritdoc />
    public async Task SetCorrectPaperAsync(string teacherid, string paperid, string classid)
    {
        var key = $"TeacherCorrect|{teacherid}";
        var data = new TeacherCorrectingViewModel
        {
            ClassId = classid,
            PaperId = paperid
        };
        await SetAsync(key, data, TimeSpan.FromHours(2));
    }

    /// <inheritdoc />
    public TeacherCorrectingViewModel GetCorrectPaper(string teacherid)
    {
        var key = $"TeacherCorrect|{teacherid}";
        return Get<TeacherCorrectingViewModel>(key);
    }

    /// <inheritdoc />
    public void SetCorrectPaper(string teacherid, string paperid, string classid)
    {
        var key = $"TeacherCorrect|{teacherid}";
        var data = new TeacherCorrectingViewModel
        {
            ClassId = classid,
            PaperId = paperid
        };
        Set(key, data, TimeSpan.FromHours(2));
    }

    /// <inheritdoc />
    public async Task SetCorrectStudentAsync(string teacherid, string paperid, string classid, string studentid, int pageno)
    {
        var key = $"TeacherCorrectStudentNo|{teacherid}";
        var data = new TeacherCorrectingStudent
        {
            Page = pageno,
            StudentId = studentid,
            ClassId = classid,
            PaperId = paperid
        };
        await SetAsync(key, data, TimeSpan.FromHours(2));
    }

    /// <inheritdoc />
    public async Task<TeacherCorrectingStudent> GetCorrectStudentAsync(string teacherid)
    {
        var key = $"TeacherCorrectStudentNo|{teacherid}";
        return await GetAsync<TeacherCorrectingStudent>(key);
    }

    /// <inheritdoc />
    public async Task<string> HGetCorrectStudentAsync(string teacherid)
    {
        const string key = "TeacherCurrentCorrect";
        return await HGetAsync(key, teacherid);
    }

    /// <inheritdoc />
    public async Task HSetCorrectStudentAsync(string teacherid, string studentid)
    {
        const string key = "TeacherCurrentCorrect";
        await HSetAsync(key, teacherid, studentid);
    }

    /// <inheritdoc />
    public async Task HSetCorrectStudentStatusAsync(string paperid, string studentid, int status)
    {
        const string key = "CorrectStudentStatus";
        var field = $"{paperid}|{studentid}";
        await HSetAsync(key, field, status.ToString());
    }

    /// <inheritdoc />
    public async Task<int> HGetCorrectStudentStatusAsync(string paperid, string studentid)
    {
        const string key = "CorrectStudentStatus";
        var field = $"{paperid}|{studentid}";
        return await HGetAsync<int>(key, field);
    }

    /// <inheritdoc />
    public void HSetCorrectStudentStatus(string paperid, string studentid, int status)
    {
        const string key = "CorrectStudentStatus";
        var field = $"{paperid}|{studentid}";
        HSet(key, field, status.ToString());
    }

    /// <inheritdoc />
    public int HGetCorrectStudentStatus(string paperid, string studentid)
    {
        const string key = "CorrectStudentStatus";
        var field = $"{paperid}|{studentid}";
        return HGet<int>(key, field);
    }

    /// <inheritdoc />
    public async Task HSetStudentCorrectPaperAsync(string studentid, string paperid)
    {
        const string key = "StudentCorrectPaperStatus";
        var field = $"{studentid}|{paperid}";
        await HSetAsync(key, field, "1");
    }

    /// <inheritdoc />
    public bool SetCorrectClass(string teacherid, string classid)
    {
        const string key = "TeacherCorrectClass";
        HSet(key, teacherid, classid);
        return true;
    }

    /// <inheritdoc />
    public string GetCorrectClass(string teacherid)
    {
        const string key = "TeacherCorrectClass";
        return HGet(key, teacherid);
    }

    #endregion
}