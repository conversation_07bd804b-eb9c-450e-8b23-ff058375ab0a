﻿using CSRedis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.CacheManager.IService;
using Uwoo.Core.Configuration;

namespace Uwoo.Core.CacheManager.Service
{
    public abstract class RedisService : IRedisService
    {
        protected readonly CSRedisClient _redisClient;

        public abstract string Prefix { get; }

        public RedisService(int defaultDatabase = 0)
        {
            _redisClient = new CSRedisClient($"{AppSetting.RedisConnectionString},defaultDatabase={defaultDatabase}");
        }

        /// <inheritdoc />
        public async Task SetAsync(string key, object value, TimeSpan? span = null)
        {
            if (span.HasValue)
            {
                await _redisClient.SetAsync(Prefix + "|" + key, value, span.Value);
            }
            else
            {
                await _redisClient.SetAsync(Prefix + "|" + key, value);
            }
        }

        /// <inheritdoc />
        public async Task<T> GetAsync<T>(string key)
        {
            return await _redisClient.GetAsync<T>(Prefix + "|" + key);
        }

        /// <inheritdoc />
        public async Task<string> GetAsync(string key)
        {
            return await _redisClient.GetAsync(Prefix + "|" + key);
        }

        /// <inheritdoc />
        public async Task DeleteAsync(string key)
        {
            await _redisClient.DelAsync(Prefix + "|" + key);
        }

        /// <inheritdoc />
        public async Task ExpireAsync(string key, TimeSpan span)
        {
            await _redisClient.ExpireAsync(Prefix + "|" + key, span);
        }

        /// <inheritdoc />
        public async Task<bool> IsExistsAsync(string key)
        {
            return await _redisClient.ExistsAsync(Prefix + "|" + key);
        }

        /// <inheritdoc />
        public async Task HSetAsync(string key, string field, object value)
        {
            await _redisClient.HSetAsync(Prefix + "|" + key, field, value);
        }

        /// <inheritdoc />
        public async Task HSetAsync(string key, string field, string value)
        {
            await _redisClient.HSetAsync(Prefix + "|" + key, field, value);
        }

        /// <inheritdoc />
        public async Task<T> HGetAsync<T>(string key, string field)
        {
            return await _redisClient.HGetAsync<T>(Prefix + "|" + key, field);
        }

        /// <inheritdoc />
        public async Task<string> HGetAsync(string key, string field)
        {
            return await _redisClient.HGetAsync(Prefix + "|" + key, field);
        }

        /// <inheritdoc />
        public async Task HDeleteAsync(string key, string filed)
        {
            await _redisClient.HDelAsync(Prefix + "|" + key, filed);
        }

        /// <inheritdoc />
        public async Task<Dictionary<string, T>> HGetAllAsync<T>(string key)
        {
            return await _redisClient.HGetAllAsync<T>(Prefix + "|" + key);
        }

        /// <inheritdoc />
        public async Task<Dictionary<string, string>> HGetAllAsync(string key)
        {
            return await _redisClient.HGetAllAsync(Prefix + "|" + key);
        }

        /// <inheritdoc />
        public async Task SAddAsync(string key, params string[] value)
        {
            await _redisClient.SAddAsync(Prefix + "|" + key, value);
        }

        /// <inheritdoc />
        public async Task SAddAsync<T>(string key, params T[] value)
        {
            await _redisClient.SAddAsync(Prefix + "|" + key, value);
        }

        /// <inheritdoc />
        public async Task<List<string>> SMembersAsync(string key)
        {
            var result = await _redisClient.SMembersAsync<string>(Prefix + "|" + key);
            return result.ToList();
        }

        /// <inheritdoc />
        public async Task<List<T>> SMembersAsync<T>(string key)
        {
            var result = await _redisClient.SMembersAsync<T>(Prefix + "|" + key);
            return result.ToList();
        }

        /// <inheritdoc />
        public async Task SRemoveAsync(string key, params string[] value)
        {
            await _redisClient.SRemAsync(Prefix + "|" + key, value);
        }

        /// <inheritdoc />
        public async Task SRemoveAsync<T>(string key, T[] value)
        {
            await _redisClient.SRemAsync(Prefix + "|" + key, value);
        }

        /// <inheritdoc />
        public async Task<bool> IsSMemberAsync(string key, object member)
        {
            return await _redisClient.SIsMemberAsync(Prefix + "|" + key, member);
        }

        /// <inheritdoc />
        public async Task<string[]> KeysAsync(string pattern)
        {
            return await _redisClient.KeysAsync(pattern);
        }

        /// <inheritdoc />
        public void Set(string key, object value, TimeSpan? span = null)
        {
            if (span.HasValue)
            {
                _redisClient.Set(Prefix + "|" + key, value, span.Value);
            }
            else
            {
                _redisClient.Set(Prefix + "|" + key, value);
            }
        }

        /// <inheritdoc />
        public T Get<T>(string key)
        {
            return _redisClient.Get<T>(Prefix + "|" + key);
        }

        public long IncrBy(string key, long value = 1)
        {
            return _redisClient.IncrBy(Prefix + "|incrby|" + key, value);
        }

        public long IncrByNoPrefix(string key, long value = 1)
        {
            return _redisClient.IncrBy(key, value);
        }

        public long? GetIncrBy(string key)
        {
            return _redisClient.Get<long>(Prefix + "|incrby|" + key);
        }

        /// <inheritdoc />
        public string Get(string key)
        {
            return _redisClient.Get(Prefix + "|" + key);
        }

        /// <inheritdoc />
        public void Delete(string key)
        {
            _redisClient.Del(Prefix + "|" + key);
        }

        /// <inheritdoc />
        public void Expire(string key, TimeSpan span)
        {
            _redisClient.Expire(Prefix + "|" + key, span);
        }

        /// <inheritdoc />
        public bool IsExists(string key)
        {
            return _redisClient.Exists(Prefix + "|" + key);
        }

        /// <inheritdoc />
        public void HSet(string key, string field, object value)
        {
            _redisClient.HSet(Prefix + "|" + key, field, value);
        }

        public void HSet(string filed, object value)
        {
            _redisClient.HSet(Prefix, filed, value);
        }

        public void HDel(string filed)
        {
            _redisClient.HDel(Prefix, filed);
        }

        /// <inheritdoc />
        public void HSet(string key, string field, string value)
        {
            _redisClient.HSet(Prefix + "|" + key, field, value);
        }

        public void HSet(string key, string field, string value, TimeSpan ts)
        {
            var isNeedSetExpire = false;
            if (!_redisClient.Exists(Prefix + "|" + key))
            {
                isNeedSetExpire = true;
            }
            _redisClient.HSet(Prefix + "|" + key, field, value);
            if (isNeedSetExpire)
            {
                _redisClient.Expire(Prefix + "|" + key, ts);
            }
        }

        public bool HExists(string key, string field)
        {
            return _redisClient.HExists(Prefix + "|" + key, field);
        }

        /// <inheritdoc />
        public T HGet<T>(string key, string field)
        {
            return _redisClient.HGet<T>(Prefix + "|" + key, field);
        }

        /// <inheritdoc />
        public string HGet(string key, string field)
        {
            return _redisClient.HGet(Prefix + "|" + key, field);
        }

        /// <inheritdoc />
        public void HDelete(string key, string filed)
        {
            _redisClient.HDel(Prefix + "|" + key, filed);
        }

        /// <inheritdoc />
        public Dictionary<string, T> HGetAll<T>(string key)
        {
            return _redisClient.HGetAll<T>(Prefix + "|" + key);
        }

        /// <inheritdoc />
        public Dictionary<string, string> HGetAll(string key)
        {
            return _redisClient.HGetAll(Prefix + "|" + key);
        }

        /// <inheritdoc />
        public void SAdd(string key, params string[] value)
        {
            _redisClient.SAdd(Prefix + "|" + key, value);
        }

        public async Task SRemAllAsync(string key)
        {
            await _redisClient.DelAsync(Prefix + "|" + key);
        }

        /// <summary>
        ///  删除自增key
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public async Task<long> DelIncrAsync(string key)
        {
            return await _redisClient.DelAsync(Prefix + "|incrby|" + key);
        }

        /// <inheritdoc />
        public void SAdd(string key, string[] value, TimeSpan? ts = null)
        {
            _redisClient.SAdd(Prefix + "|" + key, value);
            if (ts != null)
            {
                _redisClient.Expire(Prefix + "|" + key, ts.Value);
            }
        }

        /// <inheritdoc />
        public void SAdd<T>(string key, params T[] value)
        {
            _redisClient.SAdd(Prefix + "|" + key, value);
        }

        /// <inheritdoc />
        public List<string> SMembers(string key)
        {
            return _redisClient.SMembers<string>(Prefix + "|" + key).ToList();
        }

        /// <inheritdoc />
        public List<T> SMembers<T>(string key)
        {
            return _redisClient.SMembers<T>(Prefix + "|" + key).ToList();
        }

        /// <inheritdoc />
        public void SRemove(string key, params string[] value)
        {
            _redisClient.SRem(Prefix + "|" + key, value);
        }

        /// <inheritdoc />
        public void SRemove<T>(string key, T[] value)
        {
            _redisClient.SRem(Prefix + "|" + key, value);
        }

        /// <inheritdoc />
        public bool IsSMember(string key, object member)
        {
            return _redisClient.SIsMember(Prefix + "|" + key, member);
        }

        /// <inheritdoc />
        public string[] Keys(string pattern)
        {
            return _redisClient.Keys(pattern);
        }
    }
}
