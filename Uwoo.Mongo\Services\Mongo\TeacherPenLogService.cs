﻿// -- Function：TeacherPenLogService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/26 15:30

namespace Uwoo.Mongo.Services.Mongo;

using MongoDB.Driver;
using Uwoo.Contracts.Config;
using Uwoo.Mongo.Infrastructure;
using Uwoo.Mongo.Interfaces.Business;
using Uwoo.Mongo.Models;
using Uwoo.Mongo.ViewModels;


/// <inheritdoc />
public class TeacherPenLogService : MongoAutoService<TeacherPenLog>, ITeacherPenLogService
{
    /// <inheritdoc />
    public TeacherPenLogService(IMongoConfig config) : base(config)
    {
    }

    /// <inheritdoc />
    protected override void CreateIndex(IMongoCollection<TeacherPenLog> collection)
    {
        var teacherid_builder = Builders<TeacherPenLog>.IndexKeys
            .Ascending(x => x.TeacherId)
            .Ascending(x => x.UserId)
            .Ascending(x => x.PageId)
            .Ascending(x => x.Page)
            .Ascending(x => x.AddTime)
            .Ascending(x => x.Mac);
        collection.Indexes.CreateIndex(teacherid_builder, collection.CollectionNamespace.CollectionName + "_TeacherId_Key");

        var userid_builder = Builders<TeacherPenLog>.IndexKeys
            .Ascending(x => x.UserId)
            .Ascending(x => x.PageId)
            .Ascending(x => x.Mac)
            .Ascending(x => x.Page);
        collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");
    }

    #region Implementation of ITeacherPenLogService

    /// <inheritdoc />
    public List<TeacherPenLog> GetAll(string colname, string userid, int page,int? year= null)
    {
        var mongo = GetConnection(colname, year);
        var list = mongo.Find(x => x.PageId == page && x.UserId == userid).ToList();
        return list.OrderBy(x => x.Mid).ToList();
    }

	/// <inheritdoc />
	public List<TeacherPenLog> GetAll(string colname, List<int> page, List<string> userid)
    {
        var mongo = GetConnection(colname);
        var list = mongo.AsQueryable()
                        .Where(x => page.Contains(x.PageId) && userid.Contains(x.UserId))
                        .ToList();
        return list.OrderBy(x => x.Mid).ToList();
    }

    /// <inheritdoc />
    public List<PenLogUserInfo> GetUserPageList(string colname, List<int> page, List<string> userid)
    {
        var mongo = GetConnection(colname);
        var result = mongo
                     .AsQueryable()
                     .Where(x => page.Contains(x.PageId) && userid.Contains(x.UserId))
                     .GroupBy(x => new {x.UserId, x.PageId})
                     .Select
                     (
                         x => new PenLogUserInfo
                         {
                             UserId = x.Key.UserId,
                             Page = x.Key.PageId
                         }
                     );
        return result.ToList();
    }

    /// <inheritdoc />
    public List<PenLogUserInfo> GetUserPageList(string colname, List<int> page, string userid)
    {
        var mongo = GetConnection(colname);
        var result = mongo
                     .AsQueryable()
                     .Where(x => page.Contains(x.PageId) && x.UserId == userid)
                     .GroupBy(x => new {x.UserId, x.PageId})
                     .Select
                     (
                         x => new PenLogUserInfo
                         {
                             UserId = x.Key.UserId,
                             Page = x.Key.PageId
                         }
                     );
        return result.ToList();
    }

    /// <inheritdoc />
    public void DeletePenLog(string colname, List<int> page, List<string> userid)
    {
        var mongo = GetConnection(colname);
        mongo.DeleteMany(x => page.Contains(x.PageId) && userid.Contains(x.UserId));
    }

    #endregion
}