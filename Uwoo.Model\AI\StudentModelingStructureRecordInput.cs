﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 学生建模模型构建记录输入
    /// </summary>
    public class StudentModelingStructureRecordInput
    {
        /// <summary>
        /// 建模Id
        /// </summary>
        public string? ModelingId { get; set; }

        /// <summary>
        /// 建模阶段Id
        /// </summary>
        public string? ModelingStageId { get; set; }

        /// <summary>
        /// 建模阶段任务Id
        /// </summary>
        public string? ModelingStageTaskId { get; set; }

        /// <summary>
        /// 学生做建模阶段任务提交记录Id
        /// </summary>
        public string? StudentDoModelingTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }
    }
}
