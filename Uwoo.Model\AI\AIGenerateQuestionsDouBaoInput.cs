using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI生成题目豆包入参
    /// </summary>
    public class AIGenerateQuestionsDouBaoInput
    {
        /// <summary>
        /// 调用的模型的 ID
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 响应内容是否流式返回：
        /// false：模型生成完所有内容后一次性返回结果。
        /// true：按 SSE 协议逐块返回模型生成内容，并以一条 data: [DONE] 消息结束。当 stream 为 true 时，可设置 stream_options 字段以获取 token 用量统计信息。
        /// </summary>
        public bool? stream { get; set; } = false;

        /// <summary>
        /// 流式响应的选项。当 stream 为 true 时，可设置 stream_options 字段。
        /// </summary>
        public AIGenerateQuestionsDouBaoStreamOptionsInput? stream_options { get; set; }

        /// <summary>
        /// 消息列表
        /// </summary>
        public List<AIGenerateQuestionsDouBaoMessageInput> messages { get; set; } = new List<AIGenerateQuestionsDouBaoMessageInput>();

        /// <summary>
        /// 模型输出内容须遵循此处指定的格式
        /// </summary>
        public AIGenerateQuestionsDouBaoResponseFormatInput response_format { get; set; } = new AIGenerateQuestionsDouBaoResponseFormatInput();
    }

    /// <summary>
    /// 消息列表
    /// </summary>
    public class AIGenerateQuestionsDouBaoMessageInput
    {
        /// <summary>
        /// 发送消息的角色(system、user)
        /// </summary>
        public string? role { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string? content { get; set; }
    }

    /// <summary>
    /// 模型输出内容须遵循此处指定的格式
    /// </summary>
    public class AIGenerateQuestionsDouBaoResponseFormatInput
    {
        /// <summary>
        /// 类型（json_schema）
        /// </summary>
        public string? type { get; set; } = "json_schema";

        /// <summary>
        /// JSON结构体的定义
        /// </summary>
        public AIGenerateQuestionsDouBaoJsonSchemaInput json_schema { get; set; } = new AIGenerateQuestionsDouBaoJsonSchemaInput();
    }

    /// <summary>
    /// JSON结构体的定义
    /// </summary>
    public class AIGenerateQuestionsDouBaoJsonSchemaInput
    {
        /// <summary>
        /// 用户自定义的JSON结构的名称
        /// </summary>
        public string? name { get; set; } = "QuestionGenerator";

        /// <summary>
        /// 是否在生成输出时，启用严格遵循模式
        /// </summary>
        public bool strict { get; set; } = true;

        /// <summary>
        /// 回复格式的 JSON 格式定义
        /// </summary>
        public AIGenerateQuestionsDouBaoSchemaInput schema { get; set; } = new AIGenerateQuestionsDouBaoSchemaInput();
    }

    /// <summary>
    /// 回复格式的 JSON 格式定义
    /// </summary>
    public class AIGenerateQuestionsDouBaoSchemaInput
    {
        /// <summary>
        /// 类型
        /// </summary>
        public string type { get; set; } = "object";

        /// <summary>
        /// 属性定义
        /// </summary>
        public AIGenerateQuestionsDouBaoPropertiesInput properties { get; set; } = new AIGenerateQuestionsDouBaoPropertiesInput();

        /// <summary>
        /// 必需字段
        /// </summary>
        public List<string> required { get; set; } = new List<string> { "questions" };
    }

    /// <summary>
    /// 属性定义
    /// </summary>
    public class AIGenerateQuestionsDouBaoPropertiesInput
    {
        /// <summary>
        /// 题目数组定义
        /// </summary>
        public AIGenerateQuestionsDouBaoArrayInput questions { get; set; } = new AIGenerateQuestionsDouBaoArrayInput();
    }

    /// <summary>
    /// 题目数组定义
    /// </summary>
    public class AIGenerateQuestionsDouBaoArrayInput
    {
        /// <summary>
        /// 类型
        /// </summary>
        public string type { get; set; } = "array";

        /// <summary>
        /// 数组项定义
        /// </summary>
        public AIGenerateQuestionsDouBaoItemInput items { get; set; } = new AIGenerateQuestionsDouBaoItemInput();
    }

    /// <summary>
    /// 数组项定义
    /// </summary>
    public class AIGenerateQuestionsDouBaoItemInput
    {
        /// <summary>
        /// 类型
        /// </summary>
        public string type { get; set; } = "object";

        /// <summary>
        /// 题目对象属性
        /// </summary>
        public AIGenerateQuestionsDouBaoQuestionPropertiesInput properties { get; set; } = new AIGenerateQuestionsDouBaoQuestionPropertiesInput();

        /// <summary>
        /// 必需字段
        /// </summary>
        public List<string> required { get; set; } = new List<string> { "QuestionType", "Title", "Answer", "Analysis" };
    }

    /// <summary>
    /// 题目对象属性
    /// </summary>
    public class AIGenerateQuestionsDouBaoQuestionPropertiesInput
    {
        /// <summary>
        /// 题型
        /// </summary>
        public AIGenerateQuestionsDouBaoStringInput QuestionType { get; set; } = new AIGenerateQuestionsDouBaoStringInput();

        /// <summary>
        /// 题型Id
        /// </summary>
        public AIGenerateQuestionsDouBaoStringInput QuestionTypeId { get; set; } = new AIGenerateQuestionsDouBaoStringInput();

        /// <summary>
        /// 题干
        /// </summary>
        public AIGenerateQuestionsDouBaoStringInput Title { get; set; } = new AIGenerateQuestionsDouBaoStringInput();

        /// <summary>
        /// 选项
        /// </summary>
        public AIGenerateQuestionsDouBaoOptionsInput Options { get; set; } = new AIGenerateQuestionsDouBaoOptionsInput();

        /// <summary>
        /// 正确答案
        /// </summary>
        public AIGenerateQuestionsDouBaoStringInput Answer { get; set; } = new AIGenerateQuestionsDouBaoStringInput();

        /// <summary>
        /// 答案解析
        /// </summary>
        public AIGenerateQuestionsDouBaoStringInput Analysis { get; set; } = new AIGenerateQuestionsDouBaoStringInput();

        /// <summary>
        /// 关联的知识点（可选，知识点出题时使用）
        /// </summary>
        public AIGenerateQuestionsDouBaoKnowledgePointsInput KnowledgePoints { get; set; } = new AIGenerateQuestionsDouBaoKnowledgePointsInput();
    }

    /// <summary>
    /// 字符串类型定义
    /// </summary>
    public class AIGenerateQuestionsDouBaoStringInput
    {
        /// <summary>
        /// 类型
        /// </summary>
        public string type { get; set; } = "string";
    }

    /// <summary>
    /// 选项数组定义
    /// </summary>
    public class AIGenerateQuestionsDouBaoOptionsInput
    {
        /// <summary>
        /// 类型
        /// </summary>
        public string type { get; set; } = "array";

        /// <summary>
        /// 数组项定义
        /// </summary>
        public AIGenerateQuestionsDouBaoOptionItemInput items { get; set; } = new AIGenerateQuestionsDouBaoOptionItemInput();
    }

    /// <summary>
    /// 选项项定义
    /// </summary>
    public class AIGenerateQuestionsDouBaoOptionItemInput
    {
        /// <summary>
        /// 类型
        /// </summary>
        public string type { get; set; } = "object";

        /// <summary>
        /// 选项属性
        /// </summary>
        public AIGenerateQuestionsDouBaoOptionPropertiesInput properties { get; set; } = new AIGenerateQuestionsDouBaoOptionPropertiesInput();

        /// <summary>
        /// 必需字段
        /// </summary>
        public List<string> required { get; set; } = new List<string> { "Option", "Content" };
    }

    /// <summary>
    /// 选项属性
    /// </summary>
    public class AIGenerateQuestionsDouBaoOptionPropertiesInput
    {
        /// <summary>
        /// 选项标识
        /// </summary>
        public AIGenerateQuestionsDouBaoStringInput Option { get; set; } = new AIGenerateQuestionsDouBaoStringInput();

        /// <summary>
        /// 选项内容
        /// </summary>
        public AIGenerateQuestionsDouBaoStringInput Content { get; set; } = new AIGenerateQuestionsDouBaoStringInput();
    }

    /// <summary>
    /// 知识点数组定义
    /// </summary>
    public class AIGenerateQuestionsDouBaoKnowledgePointsInput
    {
        /// <summary>
        /// 类型
        /// </summary>
        public string type { get; set; } = "array";

        /// <summary>
        /// 数组项定义
        /// </summary>
        public AIGenerateQuestionsDouBaoKnowledgePointItemInput items { get; set; } = new AIGenerateQuestionsDouBaoKnowledgePointItemInput();
    }

    /// <summary>
    /// 知识点项定义
    /// </summary>
    public class AIGenerateQuestionsDouBaoKnowledgePointItemInput
    {
        /// <summary>
        /// 类型
        /// </summary>
        public string type { get; set; } = "object";

        /// <summary>
        /// 知识点对象属性
        /// </summary>
        public AIGenerateQuestionsDouBaoKnowledgePointPropertiesInput properties { get; set; } = new AIGenerateQuestionsDouBaoKnowledgePointPropertiesInput();

        /// <summary>
        /// 必需字段
        /// </summary>
        public List<string> required { get; set; } = new List<string> { "Id", "Content" };
    }

    /// <summary>
    /// 知识点对象属性
    /// </summary>
    public class AIGenerateQuestionsDouBaoKnowledgePointPropertiesInput
    {
        /// <summary>
        /// 知识点ID
        /// </summary>
        public AIGenerateQuestionsDouBaoStringInput Id { get; set; } = new AIGenerateQuestionsDouBaoStringInput();

        /// <summary>
        /// 知识点内容
        /// </summary>
        public AIGenerateQuestionsDouBaoStringInput Content { get; set; } = new AIGenerateQuestionsDouBaoStringInput();
    }

    /// <summary>
    /// 流式响应的选项。当 stream 为 true 时，可设置 stream_options 字段。
    /// </summary>
    public class AIGenerateQuestionsDouBaoStreamOptionsInput
    {
        /// <summary>
        /// 模型流式输出时，是否在输出结束前输出本次请求的 token 用量信息。
        /// </summary>
        public bool include_usage { get; set; } = true;
    }
}
