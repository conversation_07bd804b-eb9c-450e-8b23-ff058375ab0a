﻿// -- Function：DateTimeJsonConverter.cs
// --- Project：Uwoo.Mongo
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/11/17 10:32
namespace Uwoo.Mongo.Infrastructure;

using System.Text.Json;
using System.Text.Json.Serialization;

/// <inheritdoc />
public class DateTimeJsonConverter : JsonConverter<DateTime>
{
	#region Overrides of JsonConverter<DateTime>

	/// <inheritdoc />
	public override DateTime Read(ref Utf8JsonReader reader, Type type, JsonSerializerOptions options)
	{
		return reader.GetDateTime();
	}

	/// <inheritdoc />
	public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
	{
		writer.WriteStringValue($"{value:yyyy-MM-dd HH:mm:ss}");
	}

	#endregion
}