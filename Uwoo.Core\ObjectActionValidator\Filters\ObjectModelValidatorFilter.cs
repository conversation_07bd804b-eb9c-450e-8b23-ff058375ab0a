﻿using System;
using System.Linq;
using Uwoo.Core.ObjectActionValidator.ExpressValidator;

namespace Uwoo.Core.ObjectActionValidator.Filters
{
	public class ObjectModelValidatorFilter : Attribute
	{
		public ObjectModelValidatorFilter(ValidatorModel validatorGroup)
		{
			MethodsParameters = validatorGroup.GetModelParameters()?.Select(x => x.ToLower())?.ToArray();
		}
		public string[] MethodsParameters { get; }
	}
}
