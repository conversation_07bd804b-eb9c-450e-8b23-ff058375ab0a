﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Exam
{
    /// <summary>
    /// Exam_RegexPattern
    /// </summary>
    [Table("Exam_RegexPattern")]
    public class Exam_RegexPattern
    {
        [SugarColumn(IsPrimaryKey = true)]
        /// <summary>
        /// RegId
        /// </summary>
        [Key, Column(Order = 1)]
        public string Id { get; set; }

        /// <summary>
        /// RePattern
        /// </summary>
        public String RePattern { get; set; }

        /// <summary>
        /// FunText
        /// </summary>
        public String FunText { get; set; }

        /// <summary>
        /// FunId
        /// </summary>
        public Int32 FunId { get; set; }

    }
}
