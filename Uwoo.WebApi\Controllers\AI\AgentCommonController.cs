﻿using Azure;
using Coldairarrow.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.Attributes;
using Uwoo.Core.Configuration;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Core.Extensions;
using Uwoo.Core.Utilities;
using Uwoo.Model;
using Uwoo.Model.CustomException;
using Uwoo.Model.Upload;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.Model.Upload;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_通用功能
    /// </summary>
    [Route("/AgentCommon/[controller]/[action]")]
    [ApiController]
    public class AgentCommonController : ApiBaseController<IAgentCommonService>
    {
        #region DI

        private readonly IAgentCommonService _agentCommonService;

        public AgentCommonController(IAgentCommonService agentCommonService)
        {
            _agentCommonService = agentCommonService;
        }

        #endregion

        /// <summary>
        /// AI上传文件
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public List<string> AIUploadFile([FromForm] IFormFile[] upload)
        {
            try
            {
                List<string> urls = new List<string>();
                var uploadFileToObs = new UploadHuaWeiObs();
                foreach (var file in upload)
                {
                    string responseText = uploadFileToObs.Execute(file, "aidialoguefileu");
                    var uploadResponseModel = responseText.ToObject<UploadFileModel>();
                    if (uploadResponseModel.ObjectUrl != null)
                    {
                        urls.Add(uploadResponseModel.ObjectUrl);
                    }
                }
                return urls;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// AI_生成图片
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<string> AIGenerateImage(AIGenerateImageInput input)
        {
            if (string.IsNullOrEmpty(input.Prompt))
            {
                throw new BusException("请输入提示词!", 801);
            }
            return await _agentCommonService.AIGenerateImage(input);
        }

        /// <summary>
        /// AI_生成HTML代码
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task<AIGenerateHTMLCodeOutput> AIGenerateHTMLCode(AIGenerateHTMLCodeInput input)
        {
            if (string.IsNullOrEmpty(input.Prompt))
            {
                throw new BusException("请输入提示词!", 801);
            }
            return await _agentCommonService.AIGenerateHTMLCode(input);
        }

        /// <summary>
        /// 获取AI文件消息
        /// </summary>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task<List<AIFileInfoDto>> GetAIFileInfoList()
        {
            return await _agentCommonService.GetAIFileInfoList();
        }

        /// <summary>
        /// 音频文件转文本
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task<string> AudioFileChangeText(AudioFileChangeTextInput input)
        {
            if (string.IsNullOrEmpty(input.FileUrl))
            {
                throw new BusException("参数异常!", 801);
            }
            return await _agentCommonService.AudioFileChangeText(input);
        }

        /// <summary>
        /// 文本转语音
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task TextChangeVoice(AITextChangeVoiceInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.Text))
                {
                    throw new Exception("参数异常!");
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentCommonService.NewTextChangeVoice(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                string ssePushData = "data: " + agentSSEOutput.ToJsonString() + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 获取AI对话内容记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageReturn<GetAIDialogueContentRecordOutput>> GetAIDialogueContentRecord(GetAIDialogueContentRecordInput input)
        {
            if (input.Type == 0)
            {
                if (string.IsNullOrEmpty(input.AgentId)
                    || string.IsNullOrEmpty(input.AgentTaskId)
                    || string.IsNullOrEmpty(input.StudentId))
                {
                    throw new Exception("参数异常!");
                }
            }
            else if (input.Type == 1)
            {
                if (string.IsNullOrEmpty(input.ProjectStageTaskId)
                || string.IsNullOrEmpty(input.StudentId)
                || string.IsNullOrEmpty(input.AgentId))
                {
                    throw new Exception("参数异常!");
                }
            }
            else if (input.Type == 2)
            {
                if (string.IsNullOrEmpty(input.ProjectStageTaskId)
                || string.IsNullOrEmpty(input.StudentId)
                || string.IsNullOrEmpty(input.TaskSubmitId)
                || string.IsNullOrEmpty(input.AgentId))
                {
                    throw new Exception("参数异常!");
                }
            }
            else
            {
                throw new Exception("参数异常!");
            }
            return await _agentCommonService.GetAIDialogueContentRecord(input);
        }

        /// <summary>
        /// 获取智能体模型信息
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task<List<GetAgentModelInfoOutput>> GetAgentModelInfo()
        {
            return await _agentCommonService.GetAgentModelInfo();
        }

        /// <summary>
        /// 获取音色信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<GetToneBaseInfoOutput>> GetToneBaseInfo()
        {
            return await _agentCommonService.GetToneBaseInfo();
        }

        /// <summary>
        /// PPT创建次数限制验证
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PPTCreateLimitOfTimesOutput> PPTCreateLimitOfTimes(PPTCreateLimitOfTimesInput input)
        {
            if (string.IsNullOrEmpty(input.UserId))
            {
                throw new Exception("参数异常!");
            }
            return await _agentCommonService.PPTCreateLimitOfTimes(input);
        }

        /// <summary>
        /// 保存PPT创建次数
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task SavePPTCreateLimitOfTimes(SavePPTCreateLimitOfTimesInput input)
        {
            if (string.IsNullOrEmpty(input.UserId))
            {
                throw new Exception("参数异常!");
            }
            await _agentCommonService.SavePPTCreateLimitOfTimes(input);
        }

        /// <summary>
        /// 获取创建PPT文多多token
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetCreatePPTWeDuoDuoTokenOutput> GetCreatePPTWeDuoDuoToken(GetCreatePPTWeDuoDuoTokenInput input)
        {
            if (string.IsNullOrEmpty(input.UserId))
            {
                throw new Exception("参数异常!");
            }
            return await _agentCommonService.GetCreatePPTWeDuoDuoToken(input);
        }

        /// <summary>
        /// 文件分析（保存分析结果）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<List<SaveFileAnalysisOutput>> SaveFileAnalysisResult(SaveFileAnalysisInput input)
        {
            if (input.Files.Count <= 0)
            {
                throw new Exception("参数异常!");
            }
            if (input.Files.Count > 3)
            {
                throw new Exception("每次支持上传三个文件!");
            }
            foreach (var item in input.Files)
            {
                if (string.IsNullOrEmpty(item.FileName) || string.IsNullOrEmpty(item.FileUrl) || item.Length <= 0)
                {
                    throw new Exception("参数异常!");
                }
            }
            if (input.Type == 1)
            {
                if (string.IsNullOrEmpty(input.StudentId)
                    || string.IsNullOrEmpty(input.AgentId)
                    || string.IsNullOrEmpty(input.ProjectStageTaskId))
                {
                    throw new Exception("参数异常!");
                }
            }
            else
            {
                throw new Exception("参数异常!");
            }
            return await _agentCommonService.SaveFileAnalysisResult(input);
        }

        /// <summary>
        /// 删除文件分析结果
        /// </summary>
        /// <param name="input">文件Id</param>
        /// <returns></returns>
        [HttpPost]
        public async Task DelFileAnalysisResult(DelFileAnalysisResultInput input)
        {
            if (string.IsNullOrEmpty(input.FileId))
            {
                throw new Exception("参数异常!");
            }
            await _agentCommonService.DelFileAnalysisResult(input);
        }
    }
}
