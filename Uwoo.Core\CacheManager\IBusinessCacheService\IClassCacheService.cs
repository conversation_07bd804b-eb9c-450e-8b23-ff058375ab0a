﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.CacheManager.IService;
using UwooAgent.Entity.DomainModels;

namespace UwooAgent.Core.CacheManager.IBusinessCacheService
{
    /// <summary>
    /// 班级信息缓存
    /// </summary>
    public interface IClassCacheService : IRedisService
    {
        /// <summary>
        /// 保存班级信息
        /// </summary>
        /// <param name="classInfo"></param>
        void SaveClassInfo(Exam_Class classInfo);

        /// <summary>
        /// 获取班级信息
        /// </summary>
        /// <param name="classId"></param>
        Exam_Class GetClassInfo(string classId);
    }
}
