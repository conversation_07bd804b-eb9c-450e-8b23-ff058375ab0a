﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 豆包我的应用流式输出
    /// </summary>
    public class DouBaoMyAppStreamOutput
    {
        /// <summary>
        /// 
        /// </summary>
        public string? id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<DouBaoMyAppStreamChoices> choices { get; set; } = new List<DouBaoMyAppStreamChoices>();
    }

    /// <summary>
    /// 
    /// </summary>
    public class DouBaoMyAppStreamChoices
    {
        /// <summary>
        /// 
        /// </summary>
        public int index { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DouBaoMyAppStreamDelta delta { get; set; } = new DouBaoMyAppStreamDelta();
    }

    /// <summary>
    /// 
    /// </summary>
    public class DouBaoMyAppStreamDelta
    {
        /// <summary>
        /// 
        /// </summary>
        public string? content { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? role { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? reasoning_content { get; set; }
    }
}
