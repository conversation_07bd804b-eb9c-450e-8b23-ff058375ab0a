using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IRepositories.AI
{
    /// <summary>
    /// 智能体_智能出题仓储接口
    /// </summary>
    public interface IAgentIntelligentQuestionRepository : IDependency, IRepository<AI_AgentBaseInfo>
    {
        
    }
}
