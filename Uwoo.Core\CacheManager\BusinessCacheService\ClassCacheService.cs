﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.CacheManager.Service;
using Uwoo.Core.Utilities;
using UwooAgent.Core.CacheManager.IBusinessCacheService;
using UwooAgent.Entity.DomainModels;
using UwooAgent.Entity.DomainModels.School;

namespace UwooAgent.Core.CacheManager.BusinessCacheService
{
    /// <summary>
    /// 班级信息缓存
    /// </summary>
    public class ClassCacheService : RedisService, IClassCacheService
    {
        /// <summary>
        /// 如果缓存为db1,就要在构造函数中传1
        /// </summary>
        public ClassCacheService() : base(15)
        { }

        public override string Prefix => RedisKeys.ClassCache;

        /// <summary>
        /// 保存班级信息
        /// </summary>
        /// <param name="classInfo"></param>
        public void SaveClassInfo(Exam_Class classInfo)
        {
            if (classInfo != null && !string.IsNullOrEmpty(classInfo.Id))
            {
                string key = RedisKeys.ClassInfoCache;
                HSet(key, classInfo.Id, classInfo);
            }
        }

        /// <summary>
        /// 获取班级信息
        /// </summary>
        /// <param name="classId"></param>
        public Exam_Class GetClassInfo(string classId)
        {
            if (string.IsNullOrEmpty(classId))
            {
                return null;
            }
            string key = RedisKeys.ClassInfoCache;
            return HGet<Exam_Class>(key, classId);
        }
    }
}
