﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Entity.DomainModels;
using Uwoo.System.IRepositories.Login;
using UwooAgent.Entity.DomainModels.User;
using UwooAgent.System.IRepositories.Login;

namespace UwooAgent.System.Repositories.Login
{
    public class UniUserInfoRepository : RepositoryBase<UniUserInfo>, IUniUserInfoRepository
    {
        public UniUserInfoRepository(VOLContext dbContext)
: base(dbContext)
        {

        }
    }
}
