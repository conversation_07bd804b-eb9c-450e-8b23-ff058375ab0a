﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 发布口语交际接口入参
    /// </summary>
    public class PublishOralCommunicationInput
    {
        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 发布班级Id
        /// </summary>
        public List<string> ClassId { get; set; } = new List<string>();

        /// <summary>
        /// 发布时间范围(下标0开始、下标1结束)
        /// </summary>
        public List<DateTime> TimeRange { get; set; } = new List<DateTime>();
    }
}
