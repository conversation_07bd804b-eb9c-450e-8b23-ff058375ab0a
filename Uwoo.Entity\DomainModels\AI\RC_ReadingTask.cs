using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 阅读理解任务主表
    /// </summary>
    [SugarTable("RC_ReadingTask")]
    public class RC_ReadingTask : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string TeacherId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; }

        /// <summary>
        /// 任务描述
        /// </summary>
        public string TaskDescription { get; set; }

        /// <summary>
        /// 任务类型（1:视频任务，2:文档任务，3:思维导图任务，4:选词填空任务）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 角色设定
        /// </summary>
        public string RoleSetting { get; set; }

        /// <summary>
        /// 评分标准
        /// </summary>
        public string ScoreStandard { get; set; }

        /// <summary>
        /// 任务目标
        /// </summary>
        public string TaskTarget { get; set; }

        /// <summary>
        /// 任务要求
        /// </summary>
        public string TaskRequirement { get; set; }

        /// <summary>
        /// 任务范围
        /// </summary>
        public string TaskScope { get; set; }

        /// <summary>
        /// 是否发布
        /// </summary>
        public bool IsPublished { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
