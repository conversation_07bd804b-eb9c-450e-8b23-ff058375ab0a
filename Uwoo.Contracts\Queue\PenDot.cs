﻿//  -- Function：PenDot.cs
//  --- Project：PenServer
//  ---- Remark：
//  ---- Author：Lucifer
//  ------ Date：2023/02/22 14:45

// ReSharper disable once CheckNamespace
namespace X.PenServer.Contracts.Queue
{
	using System.Text.Json.Serialization;

	/// <inheritdoc />
	public class PenDot
	{
		/// <summary>
		/// 雪花id
		/// </summary>
		[JsonPropertyName(nameof(Oid))]
		[JsonInclude]
		public long Oid { get; set; }

		/// <summary>
		/// X
		/// </summary>
		[JsonPropertyName(nameof(X))]
		[JsonInclude]
		public int X { get; set; }

		/// <summary>
		/// Y
		/// </summary>
		[JsonPropertyName(nameof(Y))]
		[JsonInclude]
		public int Y { get; set; }

		/// <summary>
		/// 点位类型: 1.常规点位 2.结束点位
		/// </summary>
		[JsonPropertyName(nameof(Type))]
		[JsonInclude]
		public int Type { get; set; }

		/// <summary>
		/// 采集到的页码
		/// </summary>
		[JsonPropertyName(nameof(Page))]
		[JsonInclude]
		public int Page { get; set; }

		/// <summary>
		/// 时间
		/// </summary>
		[JsonPropertyName(nameof(Time))]
		[JsonInclude]
		public DateTime Time { get; set; }
	}
}