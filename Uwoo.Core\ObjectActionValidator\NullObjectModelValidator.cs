﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Uwoo.Core.ObjectActionValidator.ExpressValidator;

namespace Uwoo.Core.ObjectActionValidator
{
	public class NullObjectModelValidator : IObjectModelValidator
	{

		public void Validate(
		   ActionContext actionContext,
		   ValidationStateDictionary validationState,
		   string prefix,
		   object model)
		{
			if (string.IsNullOrEmpty(prefix))
			{
				actionContext.ModelValidator(prefix, model);
				return;
			}
		}
	}
}
