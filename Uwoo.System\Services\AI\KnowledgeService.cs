using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Core.UserManager;
using Uwoo.Core.Utilities;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 知识库管理服务
    /// </summary>
    public class KnowledgeService : ServiceBase<AI_KnowledgeInfo, IKnowledgeRepository>, IKnowledgeService, IDependency
    {
        public KnowledgeService(IKnowledgeRepository repository) : base(repository)
        {
        }

        public static IKnowledgeService Instance
        {
            get { return AutofacContainerModule.GetService<IKnowledgeService>(); }
        }

        /// <summary>
        /// 获取知识库分页列表
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>分页结果</returns>
        public async Task<KnowledgePageResult> GetKnowledgeListAsync(KnowledgeQueryInput input)
        {
            try
            {
                var query = DBSqlSugar.Queryable<AI_KnowledgeInfo>();

                // 关键词搜索
                if (!string.IsNullOrEmpty(input.Keyword))
                {
                    query = query.Where(p => p.Name.Contains(input.Keyword) || p.Description.Contains(input.Keyword));
                }

                // 数据类型筛选
                if (!string.IsNullOrEmpty(input.DataType))
                {
                    query = query.Where(p => p.DataType == input.DataType);
                }

                // 创建人筛选
                if (!string.IsNullOrEmpty(input.Creator))
                {
                    query = query.Where(p => p.Creator == input.Creator);
                }

                // 排序
                switch (input.SortField?.ToLower())
                {
                    case "name":
                        query = input.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(p => p.Name)
                            : query.OrderByDescending(p => p.Name);
                        break;
                    case "createtime":
                    default:
                        query = input.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(p => p.CreateTime)
                            : query.OrderByDescending(p => p.CreateTime);
                        break;
                }

                // 分页查询
                var total = await query.CountAsync();
                var items = await query
                    .Skip((input.Page - 1) * input.PageSize)
                    .Take(input.PageSize)
                    .ToListAsync();

                // 转换为DTO
                var dtoItems = items.Select(ConvertToListDto).ToList();

                return new KnowledgePageResult
                {
                    Items = dtoItems,
                    Total = total,
                    Page = input.Page,
                    PageSize = input.PageSize
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"获取知识库列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据ID获取知识库详情
        /// </summary>
        /// <param name="id">知识库ID</param>
        /// <returns>知识库详情</returns>
        public async Task<KnowledgeDetailDto?> GetKnowledgeByIdAsync(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return null;
                }

                var knowledge = await DBSqlSugar.Queryable<AI_KnowledgeInfo>()
                    .Where(p => p.Id == id)
                    .FirstAsync();

                return knowledge == null ? null : ConvertToDetailDto(knowledge);
            }
            catch (Exception ex)
            {
                throw new Exception($"获取知识库详情失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建知识库
        /// </summary>
        /// <param name="input">创建参数</param>
        /// <returns>操作结果</returns>
        public async Task<KnowledgeOperationResult> CreateKnowledgeAsync(CreateKnowledgeInput input)
        {
            try
            {
                // 检查名称是否已存在
                var nameExists = await IsKnowledgeNameExistsAsync(input.Name);
                if (nameExists)
                {
                    return new KnowledgeOperationResult
                    {
                        Success = false,
                        Message = "知识库名称已存在，请使用其他名称"
                    };
                }

                // 生成豆包知识库名称（系统自动创建）
                var doubaoName = GenerateDoubaoName();

                // 调用VikingDB API创建知识库
                var vikingResult = await CreateVikingDBKnowledgeAsync(input, doubaoName);
                if (!vikingResult.Success)
                {
                    return vikingResult;
                }

                // 创建数据库记录
                var knowledge = new AI_KnowledgeInfo
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = input.Name,
                    DoubaoName = doubaoName,
                    Description = input.Description ?? string.Empty,
                    DataType = input.DataType,
                    Creator = UserContext.Current?.UserName ?? "System",
                    CreateTime = DateTime.Now
                };

                await DBSqlSugar.Insertable(knowledge).ExecuteCommandAsync();

                return new KnowledgeOperationResult
                {
                    Success = true,
                    Message = "知识库创建成功",
                    Data = ConvertToDetailDto(knowledge),
                    CollectionId = vikingResult.CollectionId
                };
            }
            catch (Exception ex)
            {
                return new KnowledgeOperationResult
                {
                    Success = false,
                    Message = $"创建知识库失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 更新知识库
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>操作结果</returns>
        public async Task<KnowledgeOperationResult> UpdateKnowledgeAsync(UpdateKnowledgeInput input)
        {
            try
            {
                var knowledge = await DBSqlSugar.Queryable<AI_KnowledgeInfo>()
                    .Where(p => p.Id == input.Id)
                    .FirstAsync();

                if (knowledge == null)
                {
                    return new KnowledgeOperationResult
                    {
                        Success = false,
                        Message = "知识库不存在"
                    };
                }

                // 检查名称是否已存在（排除当前记录）
                if (!string.IsNullOrEmpty(input.Name) && input.Name != knowledge.Name)
                {
                    var nameExists = await IsKnowledgeNameExistsAsync(input.Name, input.Id);
                    if (nameExists)
                    {
                        return new KnowledgeOperationResult
                        {
                            Success = false,
                            Message = "知识库名称已存在，请使用其他名称"
                        };
                    }
                    knowledge.Name = input.Name;
                }

                if (!string.IsNullOrEmpty(input.Description))
                {
                    knowledge.Description = input.Description;
                }

                await DBSqlSugar.Updateable(knowledge).ExecuteCommandAsync();

                return new KnowledgeOperationResult
                {
                    Success = true,
                    Message = "知识库更新成功",
                    Data = ConvertToDetailDto(knowledge)
                };
            }
            catch (Exception ex)
            {
                return new KnowledgeOperationResult
                {
                    Success = false,
                    Message = $"更新知识库失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 删除知识库
        /// </summary>
        /// <param name="id">知识库ID</param>
        /// <returns>操作结果</returns>
        public async Task<KnowledgeOperationResult> DeleteKnowledgeAsync(string id)
        {
            try
            {
                var knowledge = await DBSqlSugar.Queryable<AI_KnowledgeInfo>()
                    .Where(p => p.Id == id)
                    .FirstAsync();

                if (knowledge == null)
                {
                    return new KnowledgeOperationResult
                    {
                        Success = false,
                        Message = "知识库不存在"
                    };
                }

                // 删除数据库记录
                await DBSqlSugar.Deleteable<AI_KnowledgeInfo>()
                    .Where(p => p.Id == id)
                    .ExecuteCommandAsync();

                // TODO: 调用VikingDB API删除知识库

                return new KnowledgeOperationResult
                {
                    Success = true,
                    Message = "知识库删除成功"
                };
            }
            catch (Exception ex)
            {
                return new KnowledgeOperationResult
                {
                    Success = false,
                    Message = $"删除知识库失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 检查知识库名称是否存在
        /// </summary>
        /// <param name="name">知识库名称</param>
        /// <param name="excludeId">排除的ID（用于更新时检查）</param>
        /// <returns>是否存在</returns>
        public async Task<bool> IsKnowledgeNameExistsAsync(string name, string? excludeId = null)
        {
            try
            {
                var query = DBSqlSugar.Queryable<AI_KnowledgeInfo>()
                    .Where(p => p.Name == name);

                if (!string.IsNullOrEmpty(excludeId))
                {
                    query = query.Where(p => p.Id != excludeId);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"检查知识库名称失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取知识库统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public async Task<Dictionary<string, int>> GetKnowledgeStatisticsAsync()
        {
            try
            {
                var total = await DBSqlSugar.Queryable<AI_KnowledgeInfo>().CountAsync();
                var unstructuredCount = await DBSqlSugar.Queryable<AI_KnowledgeInfo>()
                    .Where(p => p.DataType == "unstructured_data").CountAsync();
                var structuredCount = await DBSqlSugar.Queryable<AI_KnowledgeInfo>()
                    .Where(p => p.DataType == "structured_data").CountAsync();

                return new Dictionary<string, int>
                {
                    ["total"] = total,
                    ["unstructured_data"] = unstructuredCount,
                    ["structured_data"] = structuredCount
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"获取知识库统计失败: {ex.Message}", ex);
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 转换为列表DTO
        /// </summary>
        /// <param name="knowledge">知识库实体</param>
        /// <returns>列表DTO</returns>
        private KnowledgeListDto ConvertToListDto(AI_KnowledgeInfo knowledge)
        {
            return new KnowledgeListDto
            {
                Id = knowledge.Id,
                Name = knowledge.Name,
                DoubaoName = knowledge.DoubaoName,
                Description = knowledge.Description,
                DataType = knowledge.DataType,
                Creator = knowledge.Creator,
                CreateTime = knowledge.CreateTime
            };
        }

        /// <summary>
        /// 转换为详情DTO
        /// </summary>
        /// <param name="knowledge">知识库实体</param>
        /// <returns>详情DTO</returns>
        private KnowledgeDetailDto ConvertToDetailDto(AI_KnowledgeInfo knowledge)
        {
            return new KnowledgeDetailDto
            {
                Id = knowledge.Id,
                Name = knowledge.Name,
                DoubaoName = knowledge.DoubaoName,
                Description = knowledge.Description,
                DataType = knowledge.DataType,
                Creator = knowledge.Creator,
                CreateTime = knowledge.CreateTime
            };
        }

        /// <summary>
        /// 生成豆包知识库名称
        /// </summary>
        /// <returns>豆包知识库名称</returns>
        private string GenerateDoubaoName()
        {
            // 生成唯一的豆包知识库名称，格式：原名称_时间戳
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            return $"uwoo_{timestamp}";
        }

        /// <summary>
        /// 调用VikingDB API创建知识库
        /// </summary>
        /// <param name="input">创建参数</param>
        /// <param name="doubaoName">豆包知识库名称</param>
        /// <returns>操作结果</returns>
        private async Task<KnowledgeOperationResult> CreateVikingDBKnowledgeAsync(CreateKnowledgeInput input, string doubaoName)
        {
            try
            {
                // 获取配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string baseUrl = AppSetting.DouBaoAI.KnowLedgeBaseUrl;

                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(baseUrl))
                {
                    return new KnowledgeOperationResult
                    {
                        Success = false,
                        Message = "VikingDB API配置异常，请检查APIKey和KnowLedgeBaseUrl配置"
                    };
                }

                // 构建完整的API地址
                string url = $"{baseUrl.TrimEnd('/')}/api/knowledge/collection/create";

                // 构建VikingDB API请求参数
                var request = new VikingDBCreateRequest
                {
                    Name = doubaoName,
                    ProjectId = "default", // 可以从配置中获取
                    Description = input.Description,
                    DataType = input.DataType,
                    Preprocessing = input.Preprocessing,
                    VectorModel = input.VectorModel,
                    IndexConfig = input.IndexConfig
                };

                string jsonData = JsonSerializer.Serialize(request, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                string ak = AppSetting.DouBaoAI.AccessKey;
                string sk = AppSetting.DouBaoAI.SecertAccessKey;
                string requestUrl = "/api/knowledge/collection/create";
                string timestamp = ((Int32)(DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1))).TotalSeconds).ToString();
               var sign = new Sign(region: "cn-north-1",
                    service: "air",
                    schema: "https",
                    host: baseUrl.Replace("https://", ""),
                    path: requestUrl,
                    ak: ak,
                    sk: sk
                );
                var req1 = sign.Request(HttpMethod.Post, new List<KeyValuePair<string, string>>(), Encoding.UTF8.GetBytes(jsonData), "application/json", DateTimeOffset.UtcNow, "UpdateHttps", "2025-07-25");

                // HTTP请求调用VikingDB API
                using (var httpClient = new HttpClient())
                {
                    var httpRequest = new HttpRequestMessage(HttpMethod.Post, url);
                    httpRequest.Headers.Add("Authorization", apiKey);
                    httpRequest.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (var response = await httpClient.SendAsync(httpRequest))
                    {
                        string responseContent = await response.Content.ReadAsStringAsync();

                        if (response.IsSuccessStatusCode)
                        {
                            try
                            {
                                var vikingResponse = JsonSerializer.Deserialize<VikingDBCreateResponse>(responseContent, new JsonSerializerOptions
                                {
                                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                                });

                                if (vikingResponse?.Status == 200 && vikingResponse.Data != null)
                                {
                                    return new KnowledgeOperationResult
                                    {
                                        Success = true,
                                        Message = "VikingDB知识库创建成功",
                                        CollectionId = vikingResponse.Data.CollectionId
                                    };
                                }
                                else
                                {
                                    return new KnowledgeOperationResult
                                    {
                                        Success = false,
                                        Message = $"VikingDB API返回错误: {vikingResponse?.Message ?? "未知错误"}"
                                    };
                                }
                            }
                            catch (JsonException jsonEx)
                            {
                                return new KnowledgeOperationResult
                                {
                                    Success = false,
                                    Message = $"VikingDB API响应解析失败: {jsonEx.Message}。响应内容: {responseContent.Substring(0, Math.Min(500, responseContent.Length))}"
                                };
                            }
                        }
                        else
                        {
                            return new KnowledgeOperationResult
                            {
                                Success = false,
                                Message = $"VikingDB API调用失败: {response.StatusCode} - {responseContent}"
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果VikingDB API调用失败，记录日志但不阻止本地数据库操作
                // 可以考虑异步重试机制
                return new KnowledgeOperationResult
                {
                    Success = true, // 暂时返回成功，允许本地创建
                    Message = $"VikingDB API调用异常，但本地创建将继续: {ex.Message}",
                    CollectionId = null
                };
            }
        }

        #endregion
    }
}