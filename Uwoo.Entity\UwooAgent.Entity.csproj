﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName></SccProjectName>
    <SccProvider></SccProvider>
    <SccAuxPath></SccAuxPath>
    <SccLocalPath></SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
	<ImplicitUsings>enable</ImplicitUsings>
	<GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;NU1902;NU1903;NU1904;</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;NU1902;NU1903;NU1904;</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="MappingConfiguration\Core\**" />
    <Compile Remove="System\**" />
    <EmbeddedResource Remove="MappingConfiguration\Core\**" />
    <EmbeddedResource Remove="System\**" />
    <None Remove="MappingConfiguration\Core\**" />
    <None Remove="System\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.112" />
  </ItemGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="DomainModels\News\" />
  </ItemGroup>

</Project>
