﻿// -- Function：TeacherInfo.cs
// --- Project：X.PenServer.Models
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/31 15:13

using System.Text.Json.Serialization;

namespace Uwoo.Mongo.Models;

/// <summary>
/// 教师信息
/// </summary>
public class TeacherInfo : MongoBaseModel
{
    /// <summary>
    /// 教师id
    /// </summary>
    [JsonPropertyName(nameof(UserId))]
    [JsonInclude]
    public string UserId { get; set; }

    /// <summary>
    /// 真实姓名
    /// </summary>
    [JsonPropertyName(nameof(RealName))]
    [JsonInclude]
    public string RealName { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    [JsonPropertyName(nameof(UserName))]
    [JsonInclude]
    public string UserName { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    [JsonPropertyName(nameof(NickName))]
    [JsonInclude]
    public string NickName { get; set; }

    /// <summary>
    /// 学校id
    /// </summary>
    [JsonPropertyName(nameof(SchoolId))]
    [JsonInclude]
    public string SchoolId { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    [JsonPropertyName(nameof(Mobile))]
    [JsonInclude]
    public string Mobile { get; set; }

    /// <summary>
    /// 区域
    /// </summary>
    [JsonPropertyName(nameof(Area))]
    [JsonInclude]
    public int Area { get; set; }
}