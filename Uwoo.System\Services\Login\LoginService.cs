﻿using Coldairarrow.Util;
using Dm.filter.log;
using log4net;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.DBManager;
using Uwoo.Core.Enums;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Core.Services;
using Uwoo.Core.Utilities;
using Uwoo.Entity.DomainModels;
using Uwoo.Model;
using Uwoo.Model.CustomException;
using Uwoo.Model.JWT;
using Uwoo.Model.Login;
using Uwoo.System.IRepositories;
using Uwoo.System.IRepositories.Login;
using Uwoo.System.IServices;
using Uwoo.System.IServices.Login;
using UwooAgent.Entity.DomainModels.Base;
using UwooAgent.Entity.DomainModels.School;
using UwooAgent.Entity.DomainModels.User;
using UwooAgent.Model.Login;
using UwooAgent.Model.UniLogin;
using static Dapper.SqlMapper;

namespace Uwoo.System.Services.Login
{
    public class LoginService : ServiceBase<Base_User, ILoginRepository>, ILoginService, IDependency
    {
        private readonly IOptions<XmlRpcClientOptions> _options;
        private readonly ILogger<LoginService> _log;
        public LoginService(IOptions<XmlRpcClientOptions> options, ILogger<LoginService> log)
        {
            _options = options;
            _log = log;
        }

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public SubmitLoginOutput SubmitLogin(SubmitLoginInput input)
        {
            try
            {
                //if (input.Type == 0)
                //{
                //    //获取学生信息
                //    Base_User student = DBSqlSugar.Queryable<Base_User>().Where(p => p.UserName == input.PhoneNo && p.Deleted == false).First();
                //    if (student == null)
                //    {
                //        student = DBSqlSugar.Queryable<Base_User>().Where(p => p.PhoneNo == input.PhoneNo && p.Deleted == false).First();
                //        if (student == null)
                //        {
                return new SubmitLoginOutput()
                {
                    IsLogin = false,
                    Msg = "登录不可用"
                };
                //        }
                //    }
                //    //验证密码
                //    if (input.Password.Trim().EncryptDES(AppSetting.Secret.User) != (student.PassWord ?? ""))
                //    {
                //        return new SubmitLoginOutput()
                //        {
                //            IsLogin = false,
                //            Msg = "密码不正确!"
                //        };
                //    }
                //    //获取学生信息
                //    Sys_UserAssociation_Mapping studentInfoId = DBSqlSugar.Queryable<Sys_UserAssociation_Mapping>().Where(p => p.UserId == student.Id && p.UserType == 1 && p.Deleted == false).First();
                //    if (studentInfoId == null)
                //    {
                //        return new SubmitLoginOutput()
                //        {
                //            IsLogin = false,
                //            Msg = "当前用户未绑定学生信息，请联系管理员!"
                //        };
                //    }

                //    // Exam_Student state = DBSqlSugar.QueryList<Exam_Student>($@"SELECT s.* FROM Base_User b INNER JOIN  Sys_UserAssociation_Mapping m ON b.Id=m.UserID INNER JOIN Exam_Student s ON s.Id=m.RassociationId where b.id=@ID",new {id= student.Id }).FirstOrDefault();
                //    //目前不需要验证审核状态
                //    ////验证账号状态
                //    //if (state.Status == 1)
                //    //{
                //    //    return new SubmitLoginOutput()
                //    //    {
                //    //        IsLogin = false,
                //    //        Msg = "您的帐号注册信息在审核中，待审核通过即可登录！"
                //    //    };
                //    //}
                //    //if (student.UserState == 3)
                //    //{
                //    //    //获取审核记录
                //    //    List<string> userAuditRecords = DBSqlSugar.Queryable<Sys_UserAuditRecord>()
                //    //        .Where(p => p.UserId == student.Id && p.UserType == 1 && p.AuditState == 2)
                //    //        .OrderByDescending(p => p.CreateTime).Select(p => p.Content)
                //    //        .ToList();
                //    //    string msg = "您的帐号注册信息审核被拒绝";
                //    //    if (userAuditRecords.Count > 0)
                //    //    {
                //    //        msg += "[";
                //    //        foreach (var item in userAuditRecords)
                //    //        {
                //    //            msg += userAuditRecords.IndexOf(item) + 1 + ":" + item + "、";
                //    //        }
                //    //        msg = msg.Substring(0, msg.Length - 1);
                //    //        msg += "]";
                //    //    }
                //    //    return new SubmitLoginOutput()
                //    //    {
                //    //        IsLogin = false,
                //    //        Msg = msg
                //    //    };
                //    //}

                //    //生成token
                //    JWTPayload jWTPayload = new JWTPayload
                //    {
                //        UserId = studentInfoId.RassociationId,
                //        UserName = student.UserName,
                //        //RealName = student.RealName,
                //        Platform = input.Source,
                //        Expire = DateTime.Parse(DateTime.Now.AddDays(2).ToString("yyyy-MM-dd") + " 03:00:00")
                //    };
                //    string token = UwooJwtHelper.GetToken(jWTPayload.ToJsonString());
                //    Logger.Add(LoggerType.Login.ToString(), input.Serialize(), "登陆成功", null, LoggerStatus.Info, student.Id, input.Source.ToString());
                //    Task.Run(() =>
                //    {
                //        DBSqlSugar.Insertable(new Sys_UserLoginLogs
                //        {
                //            UserId = student.Id,
                //            UserType = 0,
                //            CreateTime = DateTime.Now,
                //            Platform = input.Source.ToString(),
                //            LoginDate = DateTime.Now.ToString("yyyy-MM-dd"),
                //            RequestAgent = input.RequestAgent
                //        }).ExecuteCommand();
                //    });
                //    return new SubmitLoginOutput()
                //    {
                //        IsLogin = true,
                //        Msg = "登录成功!",
                //        Token = token
                //    };
                //}
                //else if (input.Type == 1)
                //{
                //    //获取教师信息
                //    Base_User user = DBSqlSugar.Queryable<Base_User>().Where(x => x.UserName == input.PhoneNo && x.Deleted == false).First();
                //    if (user == null)
                //    {
                //        user = DBSqlSugar.Queryable<Base_User>().Where(x => x.PhoneNo == input.PhoneNo && x.Deleted == false).First();
                //        if (user == null)
                //        {
                //            return new SubmitLoginOutput()
                //            {
                //                IsLogin = false,
                //                Msg = "账号未注册!"
                //            };
                //        }
                //    }
                //    //验证密码
                //    if (input.Password.Trim().EncryptDES(AppSetting.Secret.User) != (user.PassWord ?? ""))
                //    {
                //        return new SubmitLoginOutput()
                //        {
                //            IsLogin = false,
                //            Msg = "密码不正确!"
                //        };
                //    }
                //    //获取教师信息
                //    Sys_UserAssociation_Mapping teacherInfoId = DBSqlSugar.Queryable<Sys_UserAssociation_Mapping>().Where(p => p.UserId == user.Id && p.UserType != 1 && p.Deleted == false).First();
                //    if (teacherInfoId == null)
                //    {
                //        return new SubmitLoginOutput()
                //        {
                //            IsLogin = false,
                //            Msg = "当前用户未绑定教师信息，请联系管理员!"
                //        };
                //    }
                //    ////验证账号状态
                //    //if (user.AuditStatus == 1)
                //    //{
                //    //    return new SubmitLoginOutput()
                //    //    {
                //    //        IsLogin = false,
                //    //        Msg = "您的帐号注册信息在审核中，待审核通过即可登录！"
                //    //    };
                //    //}
                //    //if (user.AuditStatus == 3)
                //    //{
                //    //    //获取审核记录
                //    //    List<string> userAuditRecords = DBSqlSugar.Queryable<Sys_UserAuditRecord>()
                //    //        .Where(p => p.UserId == user.Id && p.UserType == 2 && p.AuditState == 2)
                //    //        .OrderByDescending(p => p.CreateTime).Select(p => p.Content)
                //    //        .ToList();
                //    //    string msg = "您的帐号注册信息审核被拒绝";
                //    //    if (userAuditRecords.Count > 0)
                //    //    {
                //    //        msg += "[";
                //    //        foreach (var item in userAuditRecords)
                //    //        {
                //    //            msg += userAuditRecords.IndexOf(item) + 1 + "：" + item + "、";
                //    //        }
                //    //        msg = msg.Substring(0, msg.Length - 1);
                //    //        msg += "]";
                //    //    }
                //    //    return new SubmitLoginOutput()
                //    //    {
                //    //        IsLogin = false,
                //    //        Msg = msg
                //    //    };
                //    //}

                //    //生成token
                //    JWTPayload jWTPayload = new JWTPayload
                //    {
                //        UserId = teacherInfoId.RassociationId,
                //        UserName = user.UserName,
                //        //RealName = user.RealName,
                //        Platform = input.Source,
                //        Expire = DateTime.Parse(DateTime.Now.AddDays(2).ToString("yyyy-MM-dd") + " 03:00:00")
                //    };
                //    string token = UwooJwtHelper.GetToken(jWTPayload.ToJsonString());
                //    //string token = JwtHelper.IssueJwt(jWTPayload);

                //    Logger.Add(LoggerType.Login.ToString(), input.Serialize(), "登陆成功", null, LoggerStatus.Info, user.Id, input.Source.ToString());
                //    Task.Run(() =>
                //    {
                //        DBSqlSugar.Insertable(new Sys_UserLoginLogs
                //        {
                //            UserId = user.Id,
                //            UserType = 1,
                //            CreateTime = DateTime.Now,
                //            Platform = input.Source.ToString(),
                //            LoginDate = DateTime.Now.ToString("yyyy-MM-dd"),
                //            RequestAgent = input.RequestAgent
                //        }).ExecuteCommand();
                //    });
                //    return new SubmitLoginOutput()
                //    {
                //        IsLogin = true,
                //        Msg = "登录成功!",
                //        Token = token
                //    };
                //}
                //else
                //{
                //    return new SubmitLoginOutput()
                //    {
                //        IsLogin = false,
                //        Msg = "登录类型错误!",
                //    };
                //}
            }
            catch (Exception ex)
            {
                return new SubmitLoginOutput()
                {
                    IsLogin = false,
                    Msg = ex.Message
                };
            }
        }

        /// <inheritdoc />
        public async Task<string> IsUserLogin(string sessid)
        {
            var rpc_client_option = _options.Value;
            var declara = new XDeclaration("1.0", "gbk", null);
            var doc = new XDocument(declara);
            var method = new XElement("methodCall",
                new XElement("methodName", "user.isUserLogin"),
                new XElement("params",
                    new XElement("param",
                        new XElement("value",
                            new XElement("string", sessid)
                        )
                    )
                )
            );
            doc.Add(method);
            using var stream = new MemoryStream();
            await doc.SaveAsync(stream, SaveOptions.None, default);
            var bites = stream.ToArray();
            var request_content = Encoding.GetEncoding("gbk").GetString(bites);
            _log.LogInformation("xml-rpc request info: {Request}", request_content);
            using var request_msg = new HttpRequestMessage(HttpMethod.Post, rpc_client_option.BaseUrl);
            request_msg.Content = new StringContent(request_content, Encoding.GetEncoding("gbk"), "application/xml");
            var auth = $"{rpc_client_option.UserName}:{rpc_client_option.Password}";
            auth = Convert.ToBase64String(Encoding.UTF8.GetBytes(auth));
            var xauth = $"Basic {auth}";
            request_msg.Headers.Add("Authorization", xauth);
            using var client = new HttpClient();
            using var response_msg = await client.SendAsync(request_msg);
            var response_content = await response_msg.Content.ReadAsStringAsync();
            _log.LogInformation("xml-rpc response info: {Response}", response_content);
            var xdoc = XDocument.Parse(response_content, LoadOptions.None);
            xdoc.Declaration = declara;
            var fault = xdoc.Element("methodResponse")?.Element("fault");
            if (fault != null)
            {
                return "";
            }

            var result = xdoc.Element("methodResponse")?.Element("params")?.Element("param")?.Element("value")
                ?.Element("string")?.Value ?? "";
            return result;
        }

        public async Task<UniUserInfo> GetUserByUidAsync(string uid)
        {
            return await DBSqlSugar.Queryable<UniUserInfo>().Where(p => p.UserId == uid).FirstAsync();
        }

        public async Task<Base_User> GetTokenInfoByUniUid(string uid)
        {
            return await DBSqlSugar.Queryable<Base_User>().Where(p => p.XhId == uid).Select(x => new Base_User { RealName = x.RealName, UserName = x.UserName, Id = x.Id }).FirstAsync();
        }

        public async Task AddorUpdateXHUser(Base_User userInfo,Base_UserRole base_UserRole, int groupId)
        {
            var groupInfo = DBSqlSugar.Ado.SqlQuery<XHTreeDataDto>(@"SELECT 
    CASE
        WHEN ugi2.GroupFlag = 1 THEN ugi2.GroupName
        WHEN ugi3.GroupFlag = 1 THEN ugi3.GroupName
        WHEN ugi4.GroupFlag = 1 THEN ugi4.GroupName
        ELSE NULL
    END AS SchoolName,
    CASE
        WHEN ugi2.GroupFlag = 2 THEN ugi2.GroupName
        WHEN ugi3.GroupFlag = 2 THEN ugi3.GroupName
        ELSE NULL
    END AS GradeName,
    CASE
        WHEN ugi1.GroupFlag = 3 THEN ugi1.GroupName        
        WHEN ugi2.GroupFlag = 3 THEN ugi2.GroupName
        WHEN ugi3.GroupFlag = 3 THEN ugi3.GroupName
        ELSE NULL
    END AS ClassName                
FROM UniGroupInfos ugi1
LEFT JOIN UniGroupInfos ugi2 ON ugi1.ParentId = ugi2.GroupId
LEFT JOIN UniGroupInfos ugi3 ON ugi2.ParentId = ugi3.GroupId
LEFT JOIN UniGroupInfos ugi4 ON ugi3.ParentId = ugi4.GroupId
WHERE ugi1.GroupId = @groupId;", new { groupId = groupId }).FirstOrDefault();

            if (groupInfo != null)
            {
                var gradeId = 0;
                if (!string.IsNullOrEmpty(groupInfo.GradeName))
                {
                    if (int.TryParse(XHGradeAnalyzing(groupInfo.GradeName), out int grade))
                    {
                        gradeId = XHBaseYearReplaceGrade(grade);
                        if (groupInfo.GradeName.Contains("高"))
                        {
                            gradeId += 9;
                        }
                        else if (groupInfo.GradeName.Contains("初"))
                        {
                            gradeId += 5;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(groupInfo.SchoolName))
                {
                    var school = DBSqlSugar.Ado.SqlQuery<Exam_School>(@"select * from Exam_School where SchoolName=@schoolName", new { schoolName = groupInfo.SchoolName }).FirstOrDefault();
                    if (school == null)
                    {
                        school = new Exam_School
                        {
                            Id = IdHelper.GetId(),
                            SchoolName = groupInfo.SchoolName,
                            CreateTime = DateTime.Now,
                            AreaId = userInfo.AreaId,
                            AuditStatus = 2,
                        };
                        if (gradeId < 6)
                        {
                            school.SchoolType = "2";
                        }
                        else if (gradeId < 10)
                        {
                            school.SchoolType = "3";
                        }
                        else
                        {
                            school.SchoolType = "5";
                        }
                        await DBSqlSugar.Insertable(school).ExecuteCommandAsync();
                    }
                    userInfo.SchoolId = school.Id;
                }
            }
            if (string.IsNullOrEmpty(userInfo.Password))
            {
                DBSqlSugar.Ado.ExecuteCommand(@"update Base_User set  SchoolId=@school where Id=@id", new { id = userInfo.Id, school = userInfo.SchoolId });
            }
            else
            {
                await DBSqlSugar.Insertable(userInfo).ExecuteCommandAsync();
            }

            if(base_UserRole!=null)
            {
                await DBSqlSugar.Insertable(base_UserRole).ExecuteCommandAsync();
            }
        }

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="tempid">临时授权码id</param>
        /// <returns></returns>
        public async Task<AjaxResult<LoginVo>> UniLogin(string tempid)
        {
            var api = new AjaxResult<LoginVo>();
            var oid = await RedisHelper.GetAsync<string>(tempid);
            if (string.IsNullOrWhiteSpace(oid))
            {
                return Error<LoginVo>("授权码不能为空");
            }

            // oid 就是Base_User 表中的Id
            var user = await GetTokenInfoByUniUid(oid);
            if (user == null)
            {
                return Error<LoginVo>("用户不存在");
            }

           //生成token
            JWTPayload jWTPayload = new JWTPayload
            {
                UserId = user.Id,
                UserName = user.UserName,
                //RealName = user.RealName,
                Platform = 3,
                Expire = DateTime.Parse(DateTime.Now.AddDays(2).ToString("yyyy-MM-dd") + " 03:00:00")
            };
            string token = UwooJwtHelper.GetToken(jWTPayload.ToJsonString());

            //登录成功清除授权码
            await RedisHelper.DelAsync(tempid);
            return Success(new LoginVo
            {
                Token = token,
                LoginId= user.Id
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="year"></param>
        /// <returns></returns>
        public int XHBaseYearReplaceGrade(int year)
        {
            int gradeId = 0;
            if (DateTime.Now.Month >= 9)
            {
                gradeId = (DateTime.Now.Year - year) + 1;
            }
            else
            {
                gradeId = DateTime.Now.Year - year;
            }
            return gradeId;
        }

        /// <summary>
        /// 解析徐汇年级
        /// </summary>
        /// <param name="gradename"></param>
        /// <returns></returns>
        public string XHGradeAnalyzing(string gradename)
        {
            //年级解析验证
            string grade_name = gradename.Replace("级", "").Replace("小", "").Replace("初", "").Replace("高", "");
            return grade_name;
        }
    }
}
