﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// 智能体_学生做项目化实践任务高频主题
	/// </summary>
	[Table("AI_StudentDoProjectTaskQuestion")]
    public class AI_StudentDoProjectTaskQuestion
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 项目化实践Id
        /// </summary>
        public string ProjectId { get; set; }

        /// <summary>
        /// 项目化实践阶段Id
        /// </summary>
        public string ProjectStageId { get; set; }

        /// <summary>
        /// 项目化实践阶段任务Id
        /// </summary>
        public string ProjectStageTaskId { get; set; }

        /// <summary>
        /// 项目化实践阶段任务提交记录Id
        /// </summary>
        public string StudentDoProjectTaskId { get; set; }

        /// <summary>
        /// 高频主题Id
        /// </summary>
        public string QuestionId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string StudentId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
