﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Utilities
{
    public struct RedisKeys
    {
        /// <summary>
        /// 学校缓存key
        /// </summary>
        public const string SchoolCache = "SchoolCache";

        /// <summary>
        /// 统一登录临时code
        /// </summary>
        public const string UNI_LOGIN_CODE = "Uni_Login_Code_";

        /// <summary>
        /// 学校缓存信息key
        /// </summary>
        public const string SchoolCacheInfo = "SchoolCacheInfo";

        /// <summary>
        /// 班级缓存key
        /// </summary>
        public const string ClassCache = "ClassCache";

        /// <summary>
        /// 班级信息缓存key
        /// </summary>
        public const string ClassInfoCache = "ClassInfoCache";

        /// <summary>
        /// Office文件
        /// </summary>
        public const string Office = "OFFICE";

        /// <summary>
        /// Office文件版本
        /// </summary>
        public const string OfficeVersion = "OFFICEVERSION";

        /// <summary>
        /// 智能体通用缓存key
        /// </summary>
        public const string AgentCommonCache = "AgentCommonCache";

        /// <summary>
        /// 智能体通用缓存学生提交锁key
        /// </summary>
        public const string AgentCommonStudentSubmitLockCache = "AgentCommonStudentSubmitLockCache";
    }
}
