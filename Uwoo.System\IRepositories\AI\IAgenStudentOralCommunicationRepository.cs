﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;

namespace UwooAgent.System.IRepositories.AI
{
    /// <summary>
    /// 智能体_学生端口语交际
    /// </summary>
    public interface IAgenStudentOralCommunicationRepository : IDependency, IRepository<AI_OralCommunicationTask>
    {
    }
}
