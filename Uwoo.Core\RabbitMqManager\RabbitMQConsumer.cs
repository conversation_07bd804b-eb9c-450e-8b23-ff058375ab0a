using System;
using System.Text;
using Newtonsoft.Json;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System.Threading.Tasks;

namespace Uwoo.Core.RabbitMQ
{
    public class RabbitMQConsumer
    {
        private readonly RabbitMQConnectionFactory _connectionFactory;

        public RabbitMQConsumer(RabbitMQConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public void ConsumeMessages(string exchangeName, string queueName, Action<string> messageHandler)
        {
            if (string.IsNullOrEmpty(exchangeName))
            {
                throw new ArgumentException("Exchange name cannot be null or empty.", nameof(exchangeName));
            }

            if (string.IsNullOrEmpty(queueName))
            {
                throw new ArgumentException("Queue name cannot be null or empty.", nameof(queueName));
            }

            if (messageHandler == null)
            {
                throw new ArgumentNullException(nameof(messageHandler), "Message handler cannot be null.");
            }

            using (var connection = _connectionFactory.CreateConnection())
            using (var channel = connection.CreateModel())
            {
                // 声明交换机
                channel.ExchangeDeclare(exchange: exchangeName, type: ExchangeType.Fanout, durable: true, autoDelete: false);

                // 声明队列
                channel.QueueDeclare(queue: queueName, durable: true, exclusive: false, autoDelete: false, arguments: null);

                // 将队列绑定到 Fanout 交换机，无需指定路由键
                channel.QueueBind(queue: queueName, exchange: exchangeName, routingKey: "");

                // 创建消费者
                var consumer = new EventingBasicConsumer(channel);

                channel.BasicQos(0, 30,false);
                // 注册消息接收事件
                consumer.Received += (model, ea) =>
                {
                    try
                    {
                        var body = ea.Body.ToArray();
                        var message = Encoding.UTF8.GetString(body);

                        // 处理消息
                        messageHandler(message);

                        // 手动确认消息
                        channel.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error processing message: {ex.Message}");
                        // 如果处理消息失败，可以拒绝消息，并将其重新放入队列
                        channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: true);
                    }
                };

                // 开始消费消息
                channel.BasicConsume(queue: queueName, autoAck: false, consumer: consumer);

                Console.WriteLine(" [*] Waiting for messages.");
                Console.ReadLine(); // Keep the consumer running
            }
        }
    }
}