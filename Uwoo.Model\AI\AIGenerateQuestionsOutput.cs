using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI生成题目接口输出
    /// </summary>
    public class AIGenerateQuestionsOutput
    {
        /// <summary>
        /// 题目列表
        /// </summary>
        public List<AIGeneratedQuestion> Questions { get; set; } = new List<AIGeneratedQuestion>();

        /// <summary>
        /// 创建时间
        /// </summary>
        public string? CreateTime { get; set; }
    }

    /// <summary>
    /// AI生成的单个题目
    /// </summary>
    public class AIGeneratedQuestion
    {
        /// <summary>
        /// 题型
        /// </summary>
        public string? QuestionType { get; set; }

        /// <summary>
        /// 题型ID
        /// </summary>
        public string? QuestionTypeId { get; set; }

        /// <summary>
        /// 题干
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 选项（选择题、多项选择题、判断题需要）
        /// </summary>
        public List<AIQuestionOption>? Options { get; set; }

        /// <summary>
        /// 正确答案
        /// </summary>
        public string? Answer { get; set; }

        /// <summary>
        /// 答案解析
        /// </summary>
        public string? Analysis { get; set; }

        /// <summary>
        /// 关联的知识点列表（知识点出题时使用）
        /// </summary>
        public List<QuestionKnowledgePoint>? KnowledgePoints { get; set; }

        /// <summary>
        /// 关联的章节列表（章节出题时使用）
        /// </summary>
        public List<QuestionChapter>? Chapters { get; set; }
    }

    /// <summary>
    /// 题目关联的知识点信息
    /// </summary>
    public class QuestionKnowledgePoint
    {
        /// <summary>
        /// 知识点ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 知识点内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 知识点层级
        /// </summary>
        public int Level { get; set; }
    }

    /// <summary>
    /// 题目关联的章节信息
    /// </summary>
    public class QuestionChapter
    {
        /// <summary>
        /// 章节ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 章节名称
        /// </summary>
        public string ChapterName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 题目选项
    /// </summary>
    public class AIQuestionOption
    {
        /// <summary>
        /// 选项标识（A、B、C、D）
        /// </summary>
        public string? Option { get; set; }

        /// <summary>
        /// 选项内容
        /// </summary>
        public string? Content { get; set; }
    }

    /// <summary>
    /// AI返回的题目包装类
    /// </summary>
    public class AIQuestionsWrapper
    {
        /// <summary>
        /// 题目列表
        /// </summary>
        public List<AIGeneratedQuestion> questions { get; set; } = new List<AIGeneratedQuestion>();
    }
}
