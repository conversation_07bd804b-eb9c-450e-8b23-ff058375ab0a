﻿using {StartName}.Entity.MappingConfiguration;
using {StartName}.Entity.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace {Namespace}.MappingConfiguration
{
    public class {TableName}MapConfig : EntityMappingConfiguration<{TableName}>
    {
        public override void Map(EntityTypeBuilder<{TableName}>
        builderTable)
        {
          //b.Property(x => x.StorageName).HasMaxLength(45);
        }
     }
}

