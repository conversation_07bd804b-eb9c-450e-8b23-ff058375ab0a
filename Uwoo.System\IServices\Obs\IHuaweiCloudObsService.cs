﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.Extensions.AutofacManager;

namespace Uwoo.System.IServices.Obs
{
	public interface IHuaweiCloudObsService:IDependency
	{
		/// <summary>
		/// 通过文件上传到对象中。
		/// </summary>
		/// <param name="bucketName">存储桶名称。</param>
		/// <param name="objectName">存储桶里的对象名称。</param>
		/// <param name="filePath">要上传的本地文件名。</param>
		/// <param name="contentType">mime类型</param>
		/// <returns></returns>
		Task<string> PutObjectAsync(string bucketName, string objectName, string filePath, string contentType = null);
	}
}
