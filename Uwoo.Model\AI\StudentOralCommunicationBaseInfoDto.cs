﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 学生端口语交际基本信息数据传输
    /// </summary>
    public class StudentOralCommunicationBaseInfoDto
    {
        /// <summary>
        /// 口语交际Id
        /// </summary>
        public string? OralCommunicationId { get; set; }

        /// <summary>
        /// 互动模式(1指令式、2对话式、3辩论式)
        /// </summary>
        public int? InteractiveMode { get; set; }

        /// <summary>
        /// 学科名称
        /// </summary>
        public string? SubjectName { get; set; }

        /// <summary>
        /// 章节名称
        /// </summary>
        public string? ChapterName { get; set; }

        /// <summary>
        /// 教学目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 场景
        /// </summary>
        public string? Scene { get; set; }

        /// <summary>
        /// 场景细节
        /// </summary>
        public string? SceneDetail { get; set; }

        /// <summary>
        /// 模型Id
        /// </summary>
        public string? Modelkey { get; set; }
    }
}
