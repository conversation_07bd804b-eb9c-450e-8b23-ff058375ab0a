﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.CacheManager.Service;
using Uwoo.Core.Utilities;
using UwooAgent.Core.CacheManager.IBusinessCacheService;
using UwooAgent.Entity.DomainModels.School;

namespace UwooAgent.Core.CacheManager.BusinessCacheService
{
    public class SchoolCacheService : RedisService, ISchoolCacheService
    {
        /// <summary>
        /// 如果缓存为db1,就要在构造函数中传1
        /// </summary>
        public SchoolCacheService() : base(15)
        { }

        public override string Prefix => RedisKeys.SchoolCache;

        /// <summary>
        /// 保存学校信息
        /// </summary>
        /// <param name="school"></param>
        public void SaveSchoolInfo(Exam_School school)
        {
            if (school != null && !string.IsNullOrEmpty(school.Id))
            {
                string key = RedisKeys.SchoolCacheInfo;
                HSet(key, school.Id, school);
            }
        }

        /// <summary>
        /// 获取学校信息
        /// </summary>
        /// <param name="schoolId"></param>
        public Exam_School GetSchoolInfo(string schoolId)
        {
            if (string.IsNullOrEmpty(schoolId))
            {
                return null;
            }
            string key = RedisKeys.SchoolCacheInfo;
            return HGet<Exam_School>(key, schoolId);
        }

        /// <summary>
        /// 获取学校信息
        /// </summary>
        /// <param name="schoolId"></param>
        public Exam_School GetSchoolInfo(string schoolId, Func<string, Exam_School> GetSchoolById)
        {
            if (string.IsNullOrEmpty(schoolId))
            {
                return null;
            }
            string key = RedisKeys.SchoolCacheInfo;
            var schoolInfo = HGet<Exam_School>(key, schoolId);
            if (schoolInfo == null)
            {
                schoolInfo = GetSchoolById.Invoke(schoolId);
                if (schoolInfo == null)
                    throw new Exception("学校不存在");
                HSet(key, schoolId, schoolInfo);
            }
            return schoolInfo;
        }
    }
}
