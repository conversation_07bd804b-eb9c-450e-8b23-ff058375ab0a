using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 阅读理解智能体流式输出结构
    /// </summary>
    public class ReadingComprehensionSSEOutput
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string? Content { get; set; }

        /// <summary>
        /// 是否完成（用于标识流式输出结束）
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 是否需要重新作答
        /// </summary>
        public bool NeedRetry { get; set; }

        /// <summary>
        /// 正确数量（选词填空使用）
        /// </summary>
        public int CorrectCount { get; set; }

        /// <summary>
        /// 总题目数量（选词填空使用）
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 提交ID（任务完成后返回）
        /// </summary>
        public string? SubmitId { get; set; }

        /// <summary>
        /// AI评价等第（选词填空使用）
        /// </summary>
        public string? AIGrade { get; set; }

        /// <summary>
        /// 任务状态（1:未开始，2:进行中，3:已完成）
        /// </summary>
        public int TaskStatus { get; set; }

        /// <summary>
        /// 评估分数
        /// </summary>
        public decimal Score { get; set; }
    }
}
