<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI指令管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .toolbar {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .search-box {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
            max-width: 500px;
        }

        .search-input {
            flex: 1;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 65, 108, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .content {
            padding: 30px;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.total { border-left-color: #4facfe; }
        .stat-card.active { border-left-color: #56ab2f; }
        .stat-card.deleted { border-left-color: #ff416c; }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 1.1em;
        }

        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .table-header {
            background: #f8f9fa;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #495057;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr {
            transition: background-color 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-deleted {
            background: #f8d7da;
            color: #721c24;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 5px 12px;
            font-size: 12px;
            border-radius: 15px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 20px;
            background: #f8f9fa;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: white;
            color: #495057;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .pagination button:hover:not(:disabled) {
            background: #e9ecef;
        }

        .pagination button.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }

        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.3em;
            font-weight: 600;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .close:hover {
            opacity: 1;
        }

        .modal-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .form-control.is-invalid {
            border-color: #dc3545;
        }

        .invalid-feedback {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        .modal-footer {
            padding: 20px 30px;
            background: #f8f9fa;
            border-radius: 0 0 15px 15px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .alert {
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .alert-success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .alert-info {
            background-color: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .toolbar {
                padding: 15px 20px;
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                max-width: none;
            }
            
            .content {
                padding: 20px;
            }
            
            .stats-cards {
                grid-template-columns: 1fr;
            }
            
            .table-container {
                overflow-x: auto;
            }
            
            .table {
                min-width: 800px;
            }
            
            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
            
            .modal-body {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>🤖 AI指令管理系统</h1>
            <p>智能管理和配置AI指令，提升教学效率</p>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="search-box">
                <input type="text" id="searchInput" class="search-input" placeholder="搜索指令关键词、类型...">
                <button class="btn btn-primary" onclick="searchDirectives()">
                    🔍 搜索
                </button>
            </div>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="showCreateModal()">
                    ➕ 新建指令
                </button>
                <button class="btn btn-warning" onclick="batchDelete()" id="batchDeleteBtn" disabled>
                    🗑️ 批量删除
                </button>
                <button class="btn btn-secondary" onclick="refreshData()">
                    🔄 刷新
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content">
            <!-- 统计卡片 
            <div class="stats-cards" id="statsCards">
                <div class="stat-card total">
                    <div class="stat-number" id="totalCount">-</div>
                    <div class="stat-label">总指令数</div>
                </div>
                <div class="stat-card active">
                    <div class="stat-number" id="activeCount">-</div>
                    <div class="stat-label">活跃指令</div>
                </div>
                <div class="stat-card deleted">
                    <div class="stat-number" id="deletedCount">-</div>
                    <div class="stat-label">已删除</div>
                </div>
            </div>
            -->

            <!-- 数据表格 -->
            <div class="table-container">
                <div class="table-header">
                    <div class="table-title">AI指令列表</div>
                    <div>
                        <label>
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()"> 全选
                        </label>
                    </div>
                </div>
                
                <div id="tableContent">
                    <div class="loading">
                        <div class="spinner"></div>
                        <div>正在加载数据...</div>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div class="pagination" id="pagination">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑指令模态框 -->
    <div id="directiveModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">新建AI指令</div>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="directiveForm">
                    <input type="hidden" id="directiveId">

                    <div class="form-group">
                        <label class="form-label" for="directiveKey">指令标识 *</label>
                        <input type="text" id="directiveKey" class="form-control" placeholder="请输入指令的唯一标识，如：TeachingPlan_Directive" required>
                        <div class="invalid-feedback" id="keyError"></div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="directiveContent">指令内容 *</label>
                        <textarea id="directiveContent" class="form-control" rows="6" placeholder="请输入详细的AI指令内容..." required></textarea>
                        <div class="invalid-feedback" id="contentError"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveDirective()" id="saveBtn">
                    <span id="saveText">保存</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 详情查看模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">AI指令详情</div>
                <span class="close" onclick="closeDetailModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">指令标识</label>
                    <div id="detailKey" class="form-control" style="background: #f8f9fa; cursor: default;"></div>
                </div>

                <div class="form-group">
                    <label class="form-label">指令类型</label>
                    <div id="detailType" class="form-control" style="background: #f8f9fa; cursor: default;"></div>
                </div>

                <div class="form-group">
                    <label class="form-label">指令内容</label>
                    <textarea id="detailContent" class="form-control" rows="8" readonly style="background: #f8f9fa; cursor: default;"></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">创建时间</label>
                    <div id="detailCreateTime" class="form-control" style="background: #f8f9fa; cursor: default;"></div>
                </div>

                <div class="form-group">
                    <label class="form-label">状态</label>
                    <div id="detailStatus"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeDetailModal()">关闭</button>
                <button type="button" class="btn btn-primary" onclick="editFromDetail()" id="editFromDetailBtn">编辑</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 0;
        let selectedIds = new Set();
        let allDirectives = [];
        let isEditMode = false;

        // API基础URL
        const API_BASE_URL = 'http://localhost:9910/api/AIDirective';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            //loadStatistics();
            loadDirectives();

            // 绑定搜索框回车事件
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchDirectives();
                }
            });
        });

        // 加载统计数据
        async function loadStatistics() {
            try {
                const response = await fetch(`${API_BASE_URL}/GetTypeStatistics`);
                const result = await response.json();

                if (result.success && result.data) {
                    let totalCount = 0;
                    let activeCount = 0;
                    let deletedCount = 0;

                    result.data.forEach(stat => {
                        totalCount += stat.count;
                        // 这里可以根据实际的统计数据结构调整
                    });

                    document.getElementById('totalCount').textContent = totalCount;
                    document.getElementById('activeCount').textContent = activeCount;
                    document.getElementById('deletedCount').textContent = deletedCount;
                } else {
                    // 如果统计接口失败，显示默认值
                    document.getElementById('totalCount').textContent = '0';
                    document.getElementById('activeCount').textContent = '0';
                    document.getElementById('deletedCount').textContent = '0';
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
                document.getElementById('totalCount').textContent = '0';
                document.getElementById('activeCount').textContent = '0';
                document.getElementById('deletedCount').textContent = '0';
            }
        }

        // 加载指令列表
        async function loadDirectives() {
            try {
                showLoading();

                const requestData = {
                    page: currentPage,
                    pageSize: pageSize,
                    keyword: document.getElementById('searchInput').value.trim(),
                    isDeleted: null  // null表示查询所有状态的数据
                };

                const response = await fetch(`${API_BASE_URL}/GetList`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                console.log('API返回数据:', result);

                if (result.Success && result.Data) {
                    allDirectives = result.Data.items || result.Data.Items || [];
                    totalPages = Math.ceil((result.Data.total || result.Data.Total || 0) / pageSize);
                    console.log('解析后的数据:', { allDirectives, totalPages });
                    renderTable();
                    renderPagination();
                   // updateStatistics();
                } else {
                    console.error('API返回错误:', result);
                    showError('加载数据失败: ' + (result.Data.Msg || result.Msg || '未知错误'));
                    renderEmptyTable();
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                showError('网络错误，请检查连接后重试');
                renderEmptyTable();
            }
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('tableContent').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <div>正在加载数据...</div>
                </div>
            `;
        }

        // 渲染表格
        function renderTable() {
            if (!allDirectives || allDirectives.length === 0) {
                renderEmptyTable();
                return;
            }

            let tableHtml = `
                <table class="table">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th width="200">指令标识</th>
                            <th width="150">指令类型</th>
                            <th>指令内容</th>
                            <th width="120">状态</th>
                            <th width="150">创建时间</th>
                            <th width="200">操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            allDirectives.forEach(directive => {
                const isDeleted = directive.isDeleted || directive.IsDeleted;
                const statusClass = isDeleted ? 'status-deleted' : 'status-active';
                const statusText = isDeleted ? '已删除' : '正常';
                const directiveContent = directive.directive || directive.Directive || directive.directivePreview || directive.DirectivePreview || '';
                const contentPreview = directiveContent && directiveContent.length > 50
                    ? directiveContent.substring(0, 50) + '...'
                    : directiveContent;
                const directiveKey = directive.key || directive.Key || '';
                const directiveId = directive.id || directive.Id || '';
                const createTime = directive.createTime || directive.CreateTime || '';

                tableHtml += `
                    <tr>
                        <td>
                            <input type="checkbox" value="${directiveId}" onchange="toggleSelect('${directiveId}')">
                        </td>
                        <td>
                            <div style="font-weight: 500; color: #495057;">${directiveKey}</div>
                        </td>
                        <td>
                            <span class="badge badge-info">${getDirectiveType(directiveKey)}</span>
                        </td>
                        <td>
                            <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                                 title="${directiveContent}">
                                ${contentPreview}
                            </div>
                        </td>
                        <td>
                            <span class="status-badge ${statusClass}">${statusText}</span>
                        </td>
                        <td>
                            <small class="text-muted">${formatDateTime(createTime)}</small>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-sm" onclick="viewDetail('${directiveId}')" title="查看详情">
                                    👁️
                                </button>
                                ${!isDeleted ? `
                                    <button class="btn btn-success btn-sm" onclick="editDirective('${directiveId}')" title="编辑">
                                        ✏️
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="deleteDirective('${directiveId}')" title="删除">
                                        🗑️
                                    </button>
                                ` : `
                                    <button class="btn btn-warning btn-sm" onclick="restoreDirective('${directiveId}')" title="恢复">
                                        ↩️
                                    </button>
                                `}
                            </div>
                        </td>
                    </tr>
                `;
            });

            tableHtml += `
                    </tbody>
                </table>
            `;

            document.getElementById('tableContent').innerHTML = tableHtml;
            updateBatchDeleteButton();
        }

        // 渲染空表格
        function renderEmptyTable() {
            document.getElementById('tableContent').innerHTML = `
                <div style="text-align: center; padding: 50px; color: #6c757d;">
                    <div style="font-size: 3em; margin-bottom: 20px;">📝</div>
                    <div style="font-size: 1.2em; margin-bottom: 10px;">暂无数据</div>
                    <div>点击"新建指令"开始创建您的第一个AI指令</div>
                </div>
            `;
            renderPagination();
        }

        // 渲染分页
        function renderPagination() {
            if (totalPages <= 1) {
                document.getElementById('pagination').innerHTML = '';
                return;
            }

            let paginationHtml = `
                <button onclick="changePage(1)" ${currentPage === 1 ? 'disabled' : ''}>首页</button>
                <button onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>
            `;

            // 显示页码
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                paginationHtml += `
                    <button onclick="changePage(${i})" ${i === currentPage ? 'class="active"' : ''}>${i}</button>
                `;
            }

            paginationHtml += `
                <button onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>下一页</button>
                <button onclick="changePage(${totalPages})" ${currentPage === totalPages ? 'disabled' : ''}>末页</button>
                <span style="margin-left: 20px; color: #6c757d;">
                    第 ${currentPage} 页，共 ${totalPages} 页
                </span>
            `;

            document.getElementById('pagination').innerHTML = paginationHtml;
        }

        // 切换页面
        function changePage(page) {
            if (page < 1 || page > totalPages || page === currentPage) return;
            currentPage = page;
            loadDirectives();
        }

        // 更新统计数据
        function updateStatistics() {
            const total = allDirectives.length;
            const active = allDirectives.filter(d => !(d.isDeleted || d.IsDeleted)).length;
            const deleted = allDirectives.filter(d => (d.isDeleted || d.IsDeleted)).length;

            document.getElementById('totalCount').textContent = total;
            document.getElementById('activeCount').textContent = active;
            document.getElementById('deletedCount').textContent = deleted;
        }

        // 获取指令类型
        function getDirectiveType(key) {
            if (!key) return '未知';

            // 从Key中提取类型
            if (key.includes('Teaching')) return '教学';
            if (key.includes('Question')) return '出题';
            if (key.includes('Communication')) return '交际';
            if (key.includes('Plan')) return '计划';
            if (key.includes('Evaluation')) return '评价';

            return '通用';
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                if (selectAll.checked) {
                    selectedIds.add(checkbox.value);
                } else {
                    selectedIds.delete(checkbox.value);
                }
            });

            updateBatchDeleteButton();
        }

        // 切换单个选择
        function toggleSelect(id) {
            const checkbox = document.querySelector(`input[value="${id}"]`);
            if (checkbox.checked) {
                selectedIds.add(id);
            } else {
                selectedIds.delete(id);
            }

            // 更新全选状态
            const allCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            const checkedCount = document.querySelectorAll('tbody input[type="checkbox"]:checked').length;
            const selectAll = document.getElementById('selectAll');

            selectAll.checked = checkedCount === allCheckboxes.length;
            selectAll.indeterminate = checkedCount > 0 && checkedCount < allCheckboxes.length;

            updateBatchDeleteButton();
        }

        // 更新批量删除按钮状态
        function updateBatchDeleteButton() {
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');
            batchDeleteBtn.disabled = selectedIds.size === 0;
            batchDeleteBtn.textContent = selectedIds.size > 0 ? `🗑️ 批量删除 (${selectedIds.size})` : '🗑️ 批量删除';
        }

        // 搜索指令
        function searchDirectives() {
            currentPage = 1;
            selectedIds.clear();
            loadDirectives();
        }

        // 刷新数据
        function refreshData() {
            selectedIds.clear();
           // loadStatistics();
            loadDirectives();
            showSuccess('数据已刷新');
        }

        // 显示创建模态框
        function showCreateModal() {
            isEditMode = false;
            document.getElementById('modalTitle').textContent = '新建AI指令';
            document.getElementById('directiveId').value = '';
            document.getElementById('directiveKey').value = '';
            document.getElementById('directiveContent').value = '';
            document.getElementById('saveText').textContent = '保存';

            // 清除验证错误
            clearValidationErrors();

            document.getElementById('directiveModal').style.display = 'block';
            document.getElementById('directiveKey').focus();
        }

        // 编辑指令
        function editDirective(id) {
            const directive = allDirectives.find(d => (d.id || d.Id) === id);
            if (!directive) {
                showError('找不到指定的指令');
                return;
            }

            isEditMode = true;
            document.getElementById('modalTitle').textContent = '编辑AI指令';
            document.getElementById('directiveId').value = directive.id || directive.Id || '';
            document.getElementById('directiveKey').value = directive.key || directive.Key || '';
            document.getElementById('directiveContent').value =   document.getElementById('detailContent').value;//directive.directive || directive.DirectivePreview || '';
            document.getElementById('saveText').textContent = '更新';

            // 清除验证错误
            clearValidationErrors();

            document.getElementById('directiveModal').style.display = 'block';
            document.getElementById('directiveKey').focus();
        }

        // 从详情页编辑
        function editFromDetail() {
            const id = document.getElementById('detailModal').dataset.directiveId;
            closeDetailModal();
            editDirective(id);
        }

        // 查看详情
        async function viewDetail(id) {
            try {
                const response = await fetch(`${API_BASE_URL}/GetDetail/${id}`);
                const result = await response.json();

                if (result.Success && result.Data) {
                    const directive = result.Data;

                    const directiveKey = directive.Key || directive.key || '';
                    const directiveContent = directive.directive || directive.Directive || '';
                    const createTime = directive.createTime || directive.CreateTime || '';
                    const isDeleted = directive.isDeleted || directive.IsDeleted || false;

                    document.getElementById('detailKey').textContent = directiveKey;
                    document.getElementById('detailType').textContent = getDirectiveType(directiveKey);
                    document.getElementById('detailContent').value = directiveContent;
                    document.getElementById('detailCreateTime').textContent = formatDateTime(createTime);

                    const statusHtml = isDeleted
                        ? '<span class="status-badge status-deleted">已删除</span>'
                        : '<span class="status-badge status-active">正常</span>';
                    document.getElementById('detailStatus').innerHTML = statusHtml;

                    // 设置编辑按钮状态
                    const editBtn = document.getElementById('editFromDetailBtn');
                    editBtn.style.display = isDeleted ? 'none' : 'inline-flex';

                    // 保存ID用于编辑
                    document.getElementById('detailModal').dataset.directiveId = id;

                    document.getElementById('detailModal').style.display = 'block';
                } else {
                    showError('获取详情失败: ' + (result.msg || '未知错误'));
                }
            } catch (error) {
                console.error('获取详情失败:', error);
                showError('网络错误，请重试');
            }
        }

        // 保存指令
        async function saveDirective() {
            const key = document.getElementById('directiveKey').value.trim();
            const content = document.getElementById('directiveContent').value.trim();

            // 清除之前的验证错误
            clearValidationErrors();

            // 验证输入
            let hasError = false;

            if (!key) {
                showFieldError('directiveKey', 'keyError', '请输入指令标识');
                hasError = true;
            }

            if (!content) {
                showFieldError('directiveContent', 'contentError', '请输入指令内容');
                hasError = true;
            }

            if (hasError) return;

            // 显示保存状态
            const saveBtn = document.getElementById('saveBtn');
            const saveText = document.getElementById('saveText');
            const originalText = saveText.textContent;

            saveBtn.disabled = true;
            saveText.textContent = '保存中...';

            try {
                let response;

                if (isEditMode) {
                    // 更新指令
                    const updateData = {
                        id: document.getElementById('directiveId').value,
                        key: key,
                        directive: content
                    };

                    response = await fetch(`${API_BASE_URL}/Update`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(updateData)
                    });
                } else {
                    // 创建新指令
                    const createData = {
                        key: key,
                        directive: content
                    };

                    response = await fetch(`${API_BASE_URL}/Create`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(createData)
                    });
                }

                const result = await response.json();

                if (result.success) {
                    showSuccess(isEditMode ? '指令更新成功' : '指令创建成功');
                    closeModal();
                    loadDirectives();
                    //loadStatistics();
                } else {
                    showError((isEditMode ? '更新失败: ' : '创建失败: ') + (result.msg || '未知错误'));
                }
            } catch (error) {
                console.error('保存失败:', error);
                showError('网络错误，请重试');
            } finally {
                saveBtn.disabled = false;
                saveText.textContent = originalText;
            }
        }

        // 删除指令
        async function deleteDirective(id) {
            if (!confirm('确定要删除这个AI指令吗？删除后可以通过恢复功能找回。')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/Delete/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('指令删除成功');
                    loadDirectives();
                    //loadStatistics();
                } else {
                    showError('删除失败: ' + (result.msg || '未知错误'));
                }
            } catch (error) {
                console.error('删除失败:', error);
                showError('网络错误，请重试');
            }
        }

        // 恢复指令
        async function restoreDirective(id) {
            if (!confirm('确定要恢复这个AI指令吗？')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/Restore/${id}`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('指令恢复成功');
                    loadDirectives();
                    //loadStatistics();
                } else {
                    showError('恢复失败: ' + (result.msg || '未知错误'));
                }
            } catch (error) {
                console.error('恢复失败:', error);
                showError('网络错误，请重试');
            }
        }

        // 批量删除
        async function batchDelete() {
            if (selectedIds.size === 0) {
                showError('请先选择要删除的指令');
                return;
            }

            if (!confirm(`确定要删除选中的 ${selectedIds.size} 个AI指令吗？删除后可以通过恢复功能找回。`)) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/BatchDelete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(Array.from(selectedIds))
                });

                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    if (data.failedCount > 0) {
                        showError(`批量删除完成，成功 ${data.successCount} 个，失败 ${data.failedCount} 个`);
                    } else {
                        showSuccess(`批量删除成功，共删除 ${data.successCount} 个指令`);
                    }

                    selectedIds.clear();
                    loadDirectives();
                    //loadStatistics();
                } else {
                    showError('批量删除失败: ' + (result.msg || '未知错误'));
                }
            } catch (error) {
                console.error('批量删除失败:', error);
                showError('网络错误，请重试');
            }
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('directiveModal').style.display = 'none';
            clearValidationErrors();
        }

        // 关闭详情模态框
        function closeDetailModal() {
            document.getElementById('detailModal').style.display = 'none';
        }

        // 清除验证错误
        function clearValidationErrors() {
            const fields = ['directiveKey', 'directiveContent'];
            fields.forEach(field => {
                document.getElementById(field).classList.remove('is-invalid');
                const errorElement = document.getElementById(field.replace('directive', '').toLowerCase() + 'Error');
                if (errorElement) {
                    errorElement.textContent = '';
                }
            });
        }

        // 显示字段错误
        function showFieldError(fieldId, errorId, message) {
            document.getElementById(fieldId).classList.add('is-invalid');
            document.getElementById(errorId).textContent = message;
        }

        // 显示成功消息
        function showSuccess(message) {
            showAlert(message, 'success');
        }

        // 显示错误消息
        function showError(message) {
            showAlert(message, 'danger');
        }

        // 显示提示消息
        function showAlert(message, type) {
            // 移除现有的提示
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            // 创建新的提示
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            // 插入到内容区域顶部
            const content = document.querySelector('.content');
            content.insertBefore(alert, content.firstChild);

            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const directiveModal = document.getElementById('directiveModal');
            const detailModal = document.getElementById('detailModal');

            if (event.target === directiveModal) {
                closeModal();
            }

            if (event.target === detailModal) {
                closeDetailModal();
            }
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
                closeDetailModal();
            }
        });
    </script>
</body>
</html>
