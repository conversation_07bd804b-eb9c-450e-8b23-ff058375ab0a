﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 语音文件转文本豆包入参
    /// </summary>
    public class AudioFileChangeTextDouBaoInput
    {
        /// <summary>
        /// 用户信息
        /// </summary>
        public AudioFileChangeTextDouBaoInputUser user { get; set; } = new AudioFileChangeTextDouBaoInputUser();

        /// <summary>
        /// 音频相关配置
        /// </summary>
        public AudioFileChangeTextDouBaoInputAudio audio { get; set; } = new AudioFileChangeTextDouBaoInputAudio();

        /// <summary>
        /// 请求相关配置
        /// </summary>
        public AudioFileChangeTextDouBaoInputRequest request { get; set; } = new AudioFileChangeTextDouBaoInputRequest();
    }

    /// <summary>
    /// 音频文件转换文本用户信息
    /// </summary>
    public class AudioFileChangeTextDouBaoInputUser
    {
        /// <summary>
        /// 用户标识
        /// </summary>
        public string? uid { get; set; }
    }

    /// <summary>
    /// 音频文件转换文本音频相关配置
    /// </summary>
    public class AudioFileChangeTextDouBaoInputAudio
    {
        /// <summary>
        /// 音频链接
        /// </summary>
        public string? url { get; set; }
    }

    /// <summary>
    /// 音频文件转换文本请求相关配置
    /// </summary>
    public class AudioFileChangeTextDouBaoInputRequest
    {
        /// <summary>
        /// 模型名称（目前只有bigmodel）
        /// </summary>
        public string? model_name { get; set; }
    }
}
