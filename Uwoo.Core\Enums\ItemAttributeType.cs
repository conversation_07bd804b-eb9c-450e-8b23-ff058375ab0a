namespace Uwoo.Core.Enums
{
    /// <summary>
    /// 题目属性类型
    /// </summary>
    public enum ItemAttributeType
    {
        /// <summary>
        /// 学习水平
        /// </summary>
        LearningLevel = 1,

        /// <summary>
        /// 难度系数
        /// </summary>
        DifficultyLevel = 2,

        /// <summary>
        /// 内容领域
        /// </summary>
        ContentDomain = 3,

        /// <summary>
        /// 核心素养
        /// </summary>
        CoreLiteracy = 4,

        /// <summary>
        /// 主题
        /// </summary>
        Theme = 5,

        /// <summary>
        /// 情景
        /// </summary>
        Scenario = 6,

        /// <summary>
        /// 单元目标
        /// </summary>
        UnitObjective = 7,

        /// <summary>
        /// 章节目标
        /// </summary>
        ChapterObjective = 8,

        /// <summary>
        /// 题目目标
        /// </summary>
        QuestionObjective = 9
    }
}