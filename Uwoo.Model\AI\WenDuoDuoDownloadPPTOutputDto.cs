﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 文多多下载PPT输出
    /// </summary>
    public class WenDuoDuoDownloadPPTOutputDto
    {
        /// <summary>
        /// 状态码
        /// </summary>
        public int code { get; set; }

        /// <summary>
        /// 信息
        /// </summary>
        public string? message { get; set; }

        /// <summary>
        /// 文件信息
        /// </summary>
        public WenDuoDuoDownloadPPTDataOutputDto data { get; set; } = new WenDuoDuoDownloadPPTDataOutputDto();
    }

    /// <summary>
    /// 文多多下载PPT输出
    /// </summary>
    public class WenDuoDuoDownloadPPTDataOutputDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string? id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? name { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string? subject { get; set; }

        /// <summary>
        /// 文件链接（有效期：2小时）
        /// </summary>
        public string? fileUrl { get; set; }
    }
}
