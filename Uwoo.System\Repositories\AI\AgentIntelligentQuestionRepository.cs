using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.System.IRepositories.AI;

namespace UwooAgent.System.Repositories.AI
{
    /// <summary>
    /// 智能体_智能出题仓储实现
    /// </summary>
    public class AgentIntelligentQuestionRepository : RepositoryBase<AI_AgentBaseInfo>, IAgentIntelligentQuestionRepository
    {
        public AgentIntelligentQuestionRepository(VOLContext dbContext) : base(dbContext)
        {

        }

        public static IAgentIntelligentQuestionRepository Instance
        {
            get
            {
                return AutofacContainerModule.GetService<IAgentIntelligentQuestionRepository>();
            }
        }
    }
}
