﻿using System.Text.Json.Serialization;
namespace X.PenServer.Contracts.Queue
{
	/// <inheritdoc />
	public class TeacherCorrectData
    {
        /// <summary>
        /// 正在批改的试卷Id
        /// </summary>
        [JsonPropertyName(nameof(PaperId))]
        [JsonInclude]
        public string PaperId { get; set; }

        /// <summary>
        /// 批改的学生的Id
        /// </summary>
        [JsonPropertyName(nameof(StudentId))]
        [JsonInclude]
        public string StudentId { get; set; }

		/// <inheritdoc />
		[JsonPropertyName(nameof(PageId))]
        [JsonInclude]
        public int PageId { get; set; }

		/// <inheritdoc />
		[Json<PERSON>ropertyName(nameof(StartTime))]
        [JsonInclude]
        public DateTime? StartTime { get; set; }

		/// <inheritdoc />
		[JsonPropertyName(nameof(EndTime))]
        [JsonInclude]
        public DateTime? EndTime { get; set; }
    }

	/// <inheritdoc />
	public class TeacherReviewStateDto
    {
		/// <inheritdoc />
		public string PaperId { get; set; }

		/// <inheritdoc />
		public string ClassId { get; set; }

		/// <inheritdoc />
		public DateTime FirstReviewTime { get; set; }
    }

	/// <inheritdoc />
	public class TeacherCorrectRecognitionInputDto
    {
		/// <inheritdoc />
		public int PageId { get; set; }

		/// <inheritdoc />
		public string PaperId { get; set; }

		/// <inheritdoc />
		public string StudentId { get; set; }

		/// <inheritdoc />
		public int OcrType { get; set; }

		/// <inheritdoc />
		public DateTime? StartTime { get; set; }

		/// <inheritdoc />
		public DateTime? EndTime { get; set; }

		/// <inheritdoc />
		public string Remark { get; set; }

		/// <inheritdoc />
		public bool IsTestLog { get; set; }
    }

	/// <inheritdoc />
	public class TestTeacherOcrLogInputDto
    {
		/// <inheritdoc />
		public int OcrType { get; set; }

		/// <inheritdoc />
		public string ClassId { get; set; }

		/// <inheritdoc />
		public string PaperId { get; set; }

		/// <inheritdoc />
		public string Remark { get; set; }

		/// <inheritdoc />
		public DateTime? StartTime { get; set; }

		/// <inheritdoc />
		public DateTime? EndTime { get; set; }

    }
}

