using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 题目生成SSE输出
    /// </summary>
    public class QuestionGenerationSSEOutput
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息类型：progress(进度), question(题目), complete(完成), error(错误), statistics(统计)
        /// </summary>
        public string MessageType { get; set; } = string.Empty;

        /// <summary>
        /// 进度信息
        /// </summary>
        public QuestionGenerationProgress? Progress { get; set; }

        /// <summary>
        /// 生成的题目
        /// </summary>
        public AIGeneratedQuestion? Question { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string? Content { get; set; }

        /// <summary>
        /// 统计文本描述
        /// </summary>
        public string? StatisticsText { get; set; }
    }

    /// <summary>
    /// 题目生成进度
    /// </summary>
    public class QuestionGenerationProgress
    {
        /// <summary>
        /// 当前进度（已推送题目数）
        /// </summary>
        public int Current { get; set; }

        /// <summary>
        /// 总题目数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 进度描述
        /// </summary>
        public string? Description { get; set; }
    }
}
