-- =============================================
-- 阅读理解智能体必要表建表SQL
-- 创建时间: 2025-01-13
-- 说明: 基于项目化实践结构的阅读理解智能体核心表
-- =============================================

-- 1. 阅读理解项目化实践项目表
CREATE TABLE RC_ReadingProject (
    Id NVARCHAR(50) NOT NULL PRIMARY KEY,
    AgentId NVARCHAR(50) NOT NULL,           -- 智能体Id
    TeacherId NVARCHAR(50) NOT NULL,         -- 教师Id
    Name NVARCHAR(200) NOT NULL,             -- 项目名称
    Introduce NTEXT,                         -- 项目背景介绍
    TaskLogo NVARCHAR(500),                  -- 任务图标
    SubjectId NVARCHAR(50),                  -- 学科Id
    Year INT,                                -- 学年
    Term INT,                                -- 学期（1:上学期，2:下学期）
    GradeId NVARCHAR(50),                    -- 年级Id
    IsPublished BIT NOT NULL DEFAULT 0,     -- 是否已发布
    PublishTime DATETIME,                    -- 发布时间
    CreateTime DATETIME DEFAULT GETDATE(),  -- 创建时间
    Creator NVARCHAR(50),                    -- 创建人
    ModifyTime DATETIME,                     -- 修改时间
    Modifier NVARCHAR(50),                   -- 修改人
    IsDeleted BIT DEFAULT 0                  -- 是否删除
);

-- 2. 阅读理解项目化实践阶段表
CREATE TABLE RC_ReadingProjectStage (
    Id NVARCHAR(50) NOT NULL PRIMARY KEY,
    ReadingProjectId NVARCHAR(50) NOT NULL,  -- 阅读理解项目Id
    StageName NVARCHAR(200) NOT NULL,         -- 阶段名称
    StageDescribe NTEXT,                      -- 阶段描述
    StageOrder INT NOT NULL,                  -- 阶段排序
    CreateTime DATETIME DEFAULT GETDATE(),   -- 创建时间
    Creator NVARCHAR(50),                     -- 创建人
    ModifyTime DATETIME,                      -- 修改时间
    Modifier NVARCHAR(50),                    -- 修改人
    IsDeleted BIT DEFAULT 0,                  -- 是否删除
    FOREIGN KEY (ReadingProjectId) REFERENCES RC_ReadingProject(Id)
);

-- 3. 阅读理解项目化实践阶段任务表
CREATE TABLE RC_ReadingProjectStageTask (
    Id NVARCHAR(50) NOT NULL PRIMARY KEY,
    ReadingProjectStageId NVARCHAR(50) NOT NULL,  -- 阅读理解项目阶段Id
    TaskType INT NOT NULL,                         -- 任务类型（1:成果评估,2:情景对话,3:知识问答,4:视频,5:文档,6:思维导图,7:选词填空）
    TaskName NVARCHAR(200) NOT NULL,               -- 任务名称
    Target NTEXT,                                  -- 目标
    ScoreStandard NTEXT,                           -- 评分标准
    Demand NTEXT,                                  -- 要求
    Scope NTEXT,                                   -- 问答范围
    RoleSetting NTEXT,                             -- 角色设定
    GroupIsSubmit BIT NOT NULL DEFAULT 0,         -- 是否开启提交条件（组间任务设置）
    GroupIsAssessment BIT NOT NULL DEFAULT 0,     -- 是否开启AI评估条件（组间任务设置）
    GroupAssessmentScore DECIMAL(5,2) DEFAULT 0,  -- AI评估条件分数（组间任务设置）
    TaskIsSubmit BIT NOT NULL DEFAULT 0,          -- 是否开启提交条件（任务点设置）
    TaskIsAssessment BIT NOT NULL DEFAULT 0,      -- 是否开启AI评估条件（任务点设置）
    TaskAssessmentScore DECIMAL(5,2) DEFAULT 0,   -- AI评估条件分数（任务点设置）
    TaskOrder INT NOT NULL,                       -- 任务排序
    -- 视频任务特殊字段
    TotalWatchDurationLimit INT,                  -- 总观看时长要求（分钟）
    IsWatchAllVideos BIT DEFAULT 0,               -- 是否要求观看所有视频
    -- 文档任务特殊字段
    IsReadAllDocuments BIT DEFAULT 0,             -- 是否需要阅读全部文档
    -- 选词填空任务特殊字段
    QuestionContent NTEXT,                        -- 题目内容
    CorrectAnswers NTEXT,                         -- 正确答案（JSON格式）
    DistractorWords NTEXT,                        -- 干扰项（JSON格式）
    CustomBackgroundImage NVARCHAR(500),         -- 自定义背景图片地址
    CreateTime DATETIME DEFAULT GETDATE(),       -- 创建时间
    Creator NVARCHAR(50),                         -- 创建人
    ModifyTime DATETIME,                          -- 修改时间
    Modifier NVARCHAR(50),                        -- 修改人
    IsDeleted BIT DEFAULT 0,                      -- 是否删除
    FOREIGN KEY (ReadingProjectStageId) REFERENCES RC_ReadingProjectStage(Id)
);

-- 4. 阅读理解项目化实践阶段任务高频问题表
CREATE TABLE RC_ReadingProjectStageTaskQuestion (
    Id NVARCHAR(50) NOT NULL PRIMARY KEY,
    ReadingProjectStageTaskId NVARCHAR(50) NOT NULL,  -- 阅读理解项目阶段任务Id
    QuestionContent NTEXT NOT NULL,                   -- 问题内容
    QuestionOrder INT NOT NULL,                       -- 问题排序
    CreateTime DATETIME DEFAULT GETDATE(),           -- 创建时间
    Creator NVARCHAR(50),                             -- 创建人
    ModifyTime DATETIME,                              -- 修改时间
    Modifier NVARCHAR(50),                            -- 修改人
    IsDeleted BIT DEFAULT 0,                          -- 是否删除
    FOREIGN KEY (ReadingProjectStageTaskId) REFERENCES RC_ReadingProjectStageTask(Id)
);

-- 5. 阅读理解视频资源表
CREATE TABLE RC_VideoResource (
    Id NVARCHAR(50) NOT NULL PRIMARY KEY,
    ReadingProjectStageTaskId NVARCHAR(50) NOT NULL,  -- 阅读理解项目阶段任务Id
    VideoTitle NVARCHAR(200) NOT NULL,                -- 视频标题
    VideoDescription NTEXT,                           -- 视频描述
    VideoUrl NVARCHAR(500) NOT NULL,                  -- 视频资源地址
    VideoOrder INT NOT NULL,                          -- 视频排序
    CreateTime DATETIME DEFAULT GETDATE(),           -- 创建时间
    Creator NVARCHAR(50),                             -- 创建人
    ModifyTime DATETIME,                              -- 修改时间
    Modifier NVARCHAR(50),                            -- 修改人
    IsDeleted BIT DEFAULT 0,                          -- 是否删除
    FOREIGN KEY (ReadingProjectStageTaskId) REFERENCES RC_ReadingProjectStageTask(Id)
);

-- 6. 阅读理解文档资源表
CREATE TABLE RC_DocumentResource (
    Id NVARCHAR(50) NOT NULL PRIMARY KEY,
    ReadingProjectStageTaskId NVARCHAR(50) NOT NULL,  -- 阅读理解项目阶段任务Id
    DocumentTitle NVARCHAR(200) NOT NULL,             -- 文档标题
    DocumentDescription NTEXT,                        -- 文档描述
    DocumentUrl NVARCHAR(500) NOT NULL,               -- 文档资源地址
    DocumentOrder INT NOT NULL,                       -- 文档排序
    CreateTime DATETIME DEFAULT GETDATE(),           -- 创建时间
    Creator NVARCHAR(50),                             -- 创建人
    ModifyTime DATETIME,                              -- 修改时间
    Modifier NVARCHAR(50),                            -- 修改人
    IsDeleted BIT DEFAULT 0,                          -- 是否删除
    FOREIGN KEY (ReadingProjectStageTaskId) REFERENCES RC_ReadingProjectStageTask(Id)
);

-- 7. 阅读理解思维导图任务配置表
CREATE TABLE RC_MindMapTaskConfig (
    Id NVARCHAR(50) NOT NULL PRIMARY KEY,
    ReadingProjectStageTaskId NVARCHAR(50) NOT NULL,  -- 阅读理解项目阶段任务Id
    MindMapTemplate NTEXT,                            -- 思维导图模板（JSON格式）
    RequiredNodes INT,                                -- 要求节点数量
    CreateTime DATETIME DEFAULT GETDATE(),           -- 创建时间
    Creator NVARCHAR(50),                             -- 创建人
    ModifyTime DATETIME,                              -- 修改时间
    Modifier NVARCHAR(50),                            -- 修改人
    IsDeleted BIT DEFAULT 0,                          -- 是否删除
    FOREIGN KEY (ReadingProjectStageTaskId) REFERENCES RC_ReadingProjectStageTask(Id)
);

-- 8. 阅读理解学生任务记录表
CREATE TABLE RC_StudentTaskRecord (
    Id NVARCHAR(50) NOT NULL PRIMARY KEY,
    StudentId NVARCHAR(50) NOT NULL,                  -- 学生Id
    ReadingProjectId NVARCHAR(50) NOT NULL,           -- 阅读理解项目Id
    ReadingProjectStageTaskId NVARCHAR(50) NOT NULL,  -- 阅读理解项目阶段任务Id
    TaskStatus INT NOT NULL DEFAULT 1,                -- 任务状态（1:未开始，2:进行中，3:已完成）
    ProjectStatus INT NOT NULL DEFAULT 1,             -- 项目状态（1:未开始，2:进行中，3:已完成）
    SubmitContent NTEXT,                              -- 提交内容（JSON格式存储）
    Score DECIMAL(5,2) DEFAULT 0,                     -- 评估分数
    IsStandard BIT DEFAULT 0,                         -- 是否达标
    AssessmentResult NTEXT,                           -- AI评估结果
    AIGrade NVARCHAR(50),                             -- AI评价等第
    CreateTime DATETIME DEFAULT GETDATE(),            -- 创建时间
    Creator NVARCHAR(50),                             -- 创建人
    ModifyTime DATETIME,                              -- 修改时间
    Modifier NVARCHAR(50),                            -- 修改人
    IsDeleted BIT DEFAULT 0,                          -- 是否删除
    FOREIGN KEY (ReadingProjectId) REFERENCES RC_ReadingProject(Id),
    FOREIGN KEY (ReadingProjectStageTaskId) REFERENCES RC_ReadingProjectStageTask(Id)
);

-- 9. 阅读理解学生视频观看记录表
CREATE TABLE RC_StudentVideoWatchRecord (
    Id NVARCHAR(50) NOT NULL PRIMARY KEY,
    StudentId NVARCHAR(50) NOT NULL,                  -- 学生Id
    ReadingProjectStageTaskId NVARCHAR(50) NOT NULL,  -- 阅读理解项目阶段任务Id
    VideoId NVARCHAR(50) NOT NULL,                    -- 视频资源Id
    TotalWatchDuration INT DEFAULT 0,                 -- 累计观看时长（秒）
    HasWatched BIT DEFAULT 0,                         -- 是否已观看（标记视频被点击播放）
    FirstWatchTime DATETIME,                          -- 首次观看时间
    LastWatchTime DATETIME,                           -- 最后观看时间
    CreateTime DATETIME DEFAULT GETDATE(),           -- 创建时间
    Creator NVARCHAR(50),                             -- 创建人
    ModifyTime DATETIME,                              -- 修改时间
    Modifier NVARCHAR(50),                            -- 修改人
    IsDeleted BIT DEFAULT 0,                          -- 是否删除
    FOREIGN KEY (ReadingProjectStageTaskId) REFERENCES RC_ReadingProjectStageTask(Id),
    FOREIGN KEY (VideoId) REFERENCES RC_VideoResource(Id)
);

-- 10. 阅读理解学生文档阅读记录表
CREATE TABLE RC_StudentDocumentReadRecord (
    Id NVARCHAR(50) NOT NULL PRIMARY KEY,
    StudentId NVARCHAR(50) NOT NULL,                  -- 学生Id
    ReadingProjectStageTaskId NVARCHAR(50) NOT NULL,  -- 阅读理解项目阶段任务Id
    DocumentId NVARCHAR(50) NOT NULL,                 -- 文档资源Id
    HasRead BIT DEFAULT 0,                            -- 是否已阅读
    ReadTime DATETIME,                                -- 阅读时间
    CreateTime DATETIME DEFAULT GETDATE(),           -- 创建时间
    Creator NVARCHAR(50),                             -- 创建人
    ModifyTime DATETIME,                              -- 修改时间
    Modifier NVARCHAR(50),                            -- 修改人
    IsDeleted BIT DEFAULT 0,                          -- 是否删除
    FOREIGN KEY (ReadingProjectStageTaskId) REFERENCES RC_ReadingProjectStageTask(Id),
    FOREIGN KEY (DocumentId) REFERENCES RC_DocumentResource(Id)
);

-- 11. 阅读理解学生选词填空操作记录表
CREATE TABLE RC_StudentWordFillOperationRecord (
    Id NVARCHAR(50) NOT NULL PRIMARY KEY,
    StudentId NVARCHAR(50) NOT NULL,                  -- 学生Id
    ReadingProjectStageTaskId NVARCHAR(50) NOT NULL,  -- 阅读理解项目阶段任务Id
    OperationOrder INT NOT NULL,                      -- 操作序号（按时间顺序递增）
    QuestionIndex INT NOT NULL,                       -- 题目索引（从0开始）
    SelectedWord NVARCHAR(100),                       -- 选择的词汇
    IsCorrect BIT,                                    -- 是否正确
    OperationTime DATETIME DEFAULT GETDATE(),        -- 操作时间
    CreateTime DATETIME DEFAULT GETDATE(),           -- 创建时间
    Creator NVARCHAR(50),                             -- 创建人
    ModifyTime DATETIME,                              -- 修改时间
    Modifier NVARCHAR(50),                            -- 修改人
    IsDeleted BIT DEFAULT 0,                          -- 是否删除
    FOREIGN KEY (ReadingProjectStageTaskId) REFERENCES RC_ReadingProjectStageTask(Id)
);

-- =============================================
-- 索引创建
-- =============================================

-- 项目表索引
CREATE INDEX IX_RC_ReadingProject_AgentId ON RC_ReadingProject(AgentId);
CREATE INDEX IX_RC_ReadingProject_TeacherId ON RC_ReadingProject(TeacherId);
CREATE INDEX IX_RC_ReadingProject_IsPublished ON RC_ReadingProject(IsPublished);
CREATE INDEX IX_RC_ReadingProject_IsDeleted ON RC_ReadingProject(IsDeleted);

-- 项目阶段表索引
CREATE INDEX IX_RC_ReadingProjectStage_ProjectId ON RC_ReadingProjectStage(ReadingProjectId);
CREATE INDEX IX_RC_ReadingProjectStage_Order ON RC_ReadingProjectStage(StageOrder);
CREATE INDEX IX_RC_ReadingProjectStage_IsDeleted ON RC_ReadingProjectStage(IsDeleted);

-- 项目阶段任务表索引
CREATE INDEX IX_RC_ReadingProjectStageTask_StageId ON RC_ReadingProjectStageTask(ReadingProjectStageId);
CREATE INDEX IX_RC_ReadingProjectStageTask_TaskType ON RC_ReadingProjectStageTask(TaskType);
CREATE INDEX IX_RC_ReadingProjectStageTask_Order ON RC_ReadingProjectStageTask(TaskOrder);
CREATE INDEX IX_RC_ReadingProjectStageTask_IsDeleted ON RC_ReadingProjectStageTask(IsDeleted);

-- 学生任务记录表索引
CREATE INDEX IX_RC_StudentTaskRecord_StudentId ON RC_StudentTaskRecord(StudentId);
CREATE INDEX IX_RC_StudentTaskRecord_ProjectId ON RC_StudentTaskRecord(ReadingProjectId);
CREATE INDEX IX_RC_StudentTaskRecord_TaskId ON RC_StudentTaskRecord(ReadingProjectStageTaskId);
CREATE INDEX IX_RC_StudentTaskRecord_TaskStatus ON RC_StudentTaskRecord(TaskStatus);
CREATE INDEX IX_RC_StudentTaskRecord_ProjectStatus ON RC_StudentTaskRecord(ProjectStatus);
CREATE INDEX IX_RC_StudentTaskRecord_IsDeleted ON RC_StudentTaskRecord(IsDeleted);

-- 学生视频观看记录表索引
CREATE INDEX IX_RC_StudentVideoWatchRecord_StudentId ON RC_StudentVideoWatchRecord(StudentId);
CREATE INDEX IX_RC_StudentVideoWatchRecord_TaskId ON RC_StudentVideoWatchRecord(ReadingProjectStageTaskId);
CREATE INDEX IX_RC_StudentVideoWatchRecord_VideoId ON RC_StudentVideoWatchRecord(VideoId);
CREATE INDEX IX_RC_StudentVideoWatchRecord_IsDeleted ON RC_StudentVideoWatchRecord(IsDeleted);

-- 学生文档阅读记录表索引
CREATE INDEX IX_RC_StudentDocumentReadRecord_StudentId ON RC_StudentDocumentReadRecord(StudentId);
CREATE INDEX IX_RC_StudentDocumentReadRecord_TaskId ON RC_StudentDocumentReadRecord(ReadingProjectStageTaskId);
CREATE INDEX IX_RC_StudentDocumentReadRecord_DocumentId ON RC_StudentDocumentReadRecord(DocumentId);
CREATE INDEX IX_RC_StudentDocumentReadRecord_IsDeleted ON RC_StudentDocumentReadRecord(IsDeleted);

-- 学生选词填空操作记录表索引
CREATE INDEX IX_RC_StudentWordFillOperationRecord_StudentId ON RC_StudentWordFillOperationRecord(StudentId);
CREATE INDEX IX_RC_StudentWordFillOperationRecord_TaskId ON RC_StudentWordFillOperationRecord(ReadingProjectStageTaskId);
CREATE INDEX IX_RC_StudentWordFillOperationRecord_IsDeleted ON RC_StudentWordFillOperationRecord(IsDeleted);

-- =============================================
-- 表注释说明
-- =============================================

/*
表结构说明：

1. 核心业务流程：
   RC_ReadingProject (项目)
   -> RC_ReadingProjectStage (阶段)
   -> RC_ReadingProjectStageTask (任务)
   -> RC_VideoResource/RC_DocumentResource (资源)

2. 学生操作记录：
   RC_StudentTaskRecord (任务完成记录)
   RC_StudentVideoWatchRecord (视频观看记录)
   RC_StudentDocumentReadRecord (文档阅读记录)
   RC_StudentWordFillOperationRecord (选词填空操作记录)

3. 任务类型说明：
   1: 成果评估(作品评估)
   2: 情景对话
   3: 知识问答
   4: 视频任务
   5: 文档任务
   6: 思维导图任务
   7: 选词填空任务

4. 发布机制：
   使用 AI_AgentTaskPublish 表统一管理发布，不再使用独立的班级关联表

5. 配置整合：
   视频、文档、选词填空任务的配置已整合到 RC_ReadingProjectStageTask 表中
   思维导图任务仍使用独立的配置表 RC_MindMapTaskConfig
*/
