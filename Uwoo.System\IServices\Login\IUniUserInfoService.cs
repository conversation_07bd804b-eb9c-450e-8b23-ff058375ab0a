﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.User;

namespace UwooAgent.System.IServices.Login
{
    public interface IUniUserInfoService : IService<UniUserInfo>
    {
        void Add(UniUserInfo user);

        void Update(UniUserInfo user);

        UniUserInfo GetUserByUid(string uid);

        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        bool DeleteUser(string uid);

        /// <summary>
        /// 获取组信息
        /// </summary>
        /// <param name="groupid">组id</param>
        /// <returns></returns>
        UniGroupInfo GetGroupByGid(int? groupid);

        /// <summary>
        /// 删除分组
        /// </summary>
        /// <param name="groupid"></param>
        /// <returns></returns>
        bool DeleteGroup(int groupid);

        void UpdateGroup(UniGroupInfo group);

        void AddGroup(UniGroupInfo group);
    }
}
