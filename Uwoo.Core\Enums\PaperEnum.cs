﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Enums
{
    public static class PaperEnum
    {

        // 单项选择题 = 1  id:2 ,多项选择题 = 2  id:10,判断题 = 3 id:11 ,填空题 = 4 id:5 ,问答=5 
        // 题型Id  2：单选  11：判断 10：多选  5：填空   23：应用题 36：主观题   37：应用题 上传图片（不判断对错）；  特殊题型：（ 40:可变行列表  41 可变行填空 42 不可变行列表 ） 43 可增加填空题 44 单选填空题 45 可为空填空 50连线题   51表格题  52选词填空   53拖拽题
        public enum ItemTypeId
        {
            /// <summary>
            /// 单项选择题
            /// </summary>
            SingleChoise = 1,

            /// <summary>
            /// 单选选择题
            /// </summary>
            NewSingleChoise = 2,

            /// <summary>
            /// 多项选择题
            /// </summary>
            MultipleChoise = 10,
            /// <summary>
            /// 判断题
            /// </summary>
            JudgmentQuestion = 11,
            /// <summary>
            /// 36 主观题
            /// </summary>
            Subjective = 36,
            /// <summary>
            /// 37：应用题 上传图片（不判断对错）
            /// </summary>
            WordProblems = 37,
            /// <summary>
            /// 填空题
            /// </summary>
            Completion = 5,

            /// <summary>
            /// 图片填空题
            /// </summary>
            ImageCompletion = 60,

            /// <summary>
            /// 可变行列表
            /// </summary>
            VariableRowsList = 40,
            /// <summary>
            /// 可变行填空
            /// </summary>
            VariableRows = 41,

            /// <summary>
            /// 不可变行列表
            /// </summary>
            NoVariableRowsList = 42,

            /// <summary>
            /// 可增加填空题 特殊题型
            /// </summary>
            FillBlanksAdded = 43,

            /// <summary>
            /// 可选择填空
            /// </summary>
            CanChooseVacancy = 44,
            /// <summary>
            /// 可为空填空
            /// </summary>
            CanBeEmpty = 45,
            /// <summary>
            /// 可下拉选择题
            /// </summary>
            CanPullChoice = 46,

            /// <summary>
            /// 思考问答题
            /// </summary>
            ReflectionsQuestions = 47,

            /// <summary>
            /// 连线题
            /// </summary>
            LigatureItem = 50,

            /// <summary>
            /// 表格题
            /// </summary>
            TableItem = 51,

            /// <summary>
            /// 选词填空
            /// </summary>
            SelectFillBlank = 52,

            /// <summary>
            /// 拖拽题
            /// </summary>
            DragAndDrop = 53,

            /// <summary>
            /// 多项单选题
            /// </summary>
            MultitermSingleChoice = 70,

            /// <summary>
            /// 特殊选择题
            /// </summary>
            SpecialSingleChoice = 71,

            /// <summary>
            /// 组合题型
            /// </summary>
			CombinationItem = 73
        }
    }
}
