﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Base
{
    /// <summary>
    /// 学科
    /// </summary>
    [Entity(TableCnName = "科目表", TableName = "Exam_Subject")]
    public partial class Exam_Subject : BaseEntity
    {
        /// <summary>
        /// 学科
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 学科名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 学段
        /// </summary>
        public string Stage { get; set; }

        /// <summary>
        /// 缩写
        /// </summary>
        public string JX { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 图标地址
        /// </summary>
        public string ImgUrl { get; set; }

        /// <summary>
        /// 是否其他
        /// </summary>
        public bool? IsRests { get; set; }

        /// <summary>
        /// 禁用状态图标地址
        /// </summary>
        public string DisabledImgUrl { get; set; }

        /// <summary>
        /// 颜色
        /// </summary>
        public string Color { get; set; }

        /// <summary>
        /// 禁用状态颜色
        /// </summary>
        public string DisabledColor { get; set; }
    }
}
