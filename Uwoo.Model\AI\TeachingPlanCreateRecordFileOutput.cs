﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取教案生成记录文件信息输出
    /// </summary>
    public class TeachingPlanCreateRecordFileOutput
    {
        /// <summary>
        /// 文件Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 教案生成记录Id
        /// </summary>
        public string? TeachingPlanCreateRecordId { get; set; }

        /// <summary>
        /// 文件地址
        /// </summary>
        public string? FileUrl { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 类型（1:word、2:PPT）
        /// </summary>
        public int FileType { get; set; }
    }
}
