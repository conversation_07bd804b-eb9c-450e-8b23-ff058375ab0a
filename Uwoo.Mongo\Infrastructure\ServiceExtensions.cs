﻿// -- Function：ServiceExtensions.cs
// --- Project：Uwoo.Mongo
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/11/29 16:03
namespace Uwoo.Mongo.Infrastructure;

using MongoDB.Driver;

/// <summary>
/// 服务扩展
/// </summary>
public static class ServiceExtensions
{
	/// <summary>
	/// 创建索引
	/// </summary>
	/// <param name="index">索引</param>
	/// <param name="keys">键</param>
	/// <param name="name">名称</param>
	/// <typeparam name="T">实体</typeparam>
	public static void CreateIndex<T>(this IMongoIndexManager<T> index, IndexKeysDefinition<T> keys, string name) where T : MongoBaseModel, new()
	{
		var options = new CreateIndexOptions
		{
			Background = true,
			Name = $"Indexs_{name}",
			Sparse = true,
			Collation = Collation.Simple
		};
		var model = new CreateIndexModel<T>(keys, options);
		index.CreateOne(model);
	}
}