using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.System.IRepositories.AI;

namespace UwooAgent.System.Repositories.AI
{
    /// <summary>
    /// AI指令管理Repository
    /// </summary>
    public class AIDirectiveRepository : RepositoryBase<AI_Directive>, IAIDirectiveRepository
    {
        public AIDirectiveRepository(VOLContext dbContext) : base(dbContext)
        {

        }

        public static IAIDirectiveRepository Instance
        {
            get
            {
                return AutofacContainerModule.GetService<IAIDirectiveRepository>();
            }
        }
    }
}
