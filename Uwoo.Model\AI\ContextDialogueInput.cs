﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 上下文对话接口入参
    /// </summary>
    public class ContextDialogueInput
    {
        /// <summary>
        /// 上下文缓存的ID，用于关联缓存的信息。
        /// </summary>
        public string? context_id { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
        public string? role { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string? content { get; set; }

        /// <summary>
        /// 模型Id
        /// </summary>
        public string? modelId { get; set; }
    }
}
