﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using NLog;
using Spire.Pdf.General.Paper.Uof;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.Attributes;
using Uwoo.Model.CustomException;

namespace Uwoo.Core.Filters
{
	public class GlobalExceptionFilter : BaseActionFilter, IExceptionFilter
	{
		private readonly ILogger<GlobalExceptionFilter> _logger;

		public GlobalExceptionFilter(ILogger<GlobalExceptionFilter> logger)
		{
			_logger = logger;
		}
		public void OnException(ExceptionContext context)
		{
			var ex = context.Exception;
			_logger.LogError(ex.StackTrace);
		}
	}
}
