﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// 智能体_教案生成记录
	/// </summary>
	[Table("AI_TeachingPlanCreateRecord")]
    public class AI_TeachingPlanCreateRecord
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 教案名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 创建类型（1：标题创建、2：文本创建、3：章节创建、4：文档创建）
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 年级
        /// </summary>
        public int Grade { get; set; }

        /// <summary>
        /// 教案标题（用于标题创建逻辑处理AI指令）
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 其他要求
        /// </summary>
        public string Demand { get; set; }

        /// <summary>
        /// 教案内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 章节ID
        /// </summary>
        public string ChapterId { get; set; }

        /// <summary>
        /// 文件地址
        /// </summary>
        public string FileUrl { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 模型ID
        /// </summary>
        public string ModelId { get; set; }

        /// <summary>
        /// 学科ID
        /// </summary>
        public string SubjectId { get; set; }

        /// <summary>
        /// 教案文本
        /// </summary>
        public string TeachingPlanText { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// 学年
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// 学期（1：上学期，2：下学期）
        /// </summary>
        public int? Term { get; set; }
    }
}
