﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using Uwoo.Core.Quartz;

namespace Uwoo.Core.Filters
{
	public interface IApiTaskFilter : IFilterMetadata
	{
		AuthorizationFilterContext OnAuthorization(AuthorizationFilterContext context);
	}
	public class ApiTaskAttribute : Attribute, IApiTaskFilter, IAllowAnonymous
	{
		public AuthorizationFilterContext OnAuthorization(AuthorizationFilterContext context)
		{
			return QuartzAuthorization.Validation(context);
		}
	}

}
