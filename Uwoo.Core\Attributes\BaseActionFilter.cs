﻿using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml.FormulaParsing.Excel.Operators;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.Extensions;
using Uwoo.Model;

namespace Uwoo.Core.Attributes
{
	public abstract class BaseActionFilter :Attribute
	{
		/// <summary>
		/// 返回JSON
		/// </summary>
		/// <param name="json">json字符串</param>
		/// <returns></returns>
		public ContentResult JsonContent(string json)
		{
			return new ContentResult { Content = json, StatusCode = 200, ContentType = "application/json; charset=utf-8" };
		}

		/// <summary>
		/// 返回成功
		/// </summary>
		/// <returns></returns>
		public ContentResult Success()
		{
			AjaxResult res = new AjaxResult
			{
				Success = true,
				Msg = "请求成功！"
			};

			return JsonContent(res.ToJsonString());
		}

		/// <summary>
		/// 返回成功
		/// </summary>
		/// <param name="msg">消息</param>
		/// <returns></returns>
		public ContentResult Success(string msg)
		{
			AjaxResult res = new AjaxResult
			{
				Success = true,
				Msg = msg
			};

			return JsonContent(res.ToJsonString());
		}

		/// <summary>
		/// 返回成功
		/// </summary>
		/// <param name="data">返回的数据</param>
		/// <returns></returns>
		public ContentResult Success<T>(T data)
		{
			AjaxResult<T> res = new AjaxResult<T>
			{
				Success = true,
				Msg = "请求成功！",
				Data = data
			};

			return JsonContent(res.ToJsonString());
		}

		/// <summary>
		/// 返回错误
		/// </summary>
		/// <returns></returns>
		public ContentResult Error()
		{
			AjaxResult res = new AjaxResult
			{
				Success = false,
				Msg = "请求失败！"
			};

			return JsonContent(res.ToJsonString());
		}

		/// <summary>
		/// 返回错误
		/// </summary>
		/// <param name="msg">错误提示</param>
		/// <returns></returns>
		public ContentResult Error(string msg)
		{
			AjaxResult res = new AjaxResult
			{
				Success = false,
				Msg = msg,
			};

			return JsonContent(res.ToJsonString());
		}

		/// <summary>
		/// 返回错误
		/// </summary>
		/// <param name="msg">错误提示</param>
		/// <param name="errorCode">错误代码</param>
		/// <returns></returns>
		public ContentResult Error(string msg, int errorCode)
		{
			AjaxResult res = new AjaxResult
			{
				Success = false,
				Msg = msg,
				ErrorCode = errorCode
			};

			return JsonContent(res.ToJsonString());
		}
	}
}
