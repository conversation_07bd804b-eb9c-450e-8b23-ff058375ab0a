﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 上下文对话豆包输出
    /// </summary>
    public class ContextDialogueDouBaoOutput
    {
        /// <summary>
        /// 一次 chat completion 接口调用的唯一标识，一次流式调用所有的 chunk 有相同的 id
        /// </summary>
        public string? id { get; set; }

        /// <summary>
        /// 实际使用的模型名称和版本
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 本次对话生成时间戳（秒）
        /// </summary>
        public long created { get; set; }

        /// <summary>
        /// choices
        /// </summary>
        public List<ContextDialogueDouBaoOutputChoices> choices { get; set; } = new List<ContextDialogueDouBaoOutputChoices>();
    }

    /// <summary>
    /// choices
    /// </summary>
    public class ContextDialogueDouBaoOutputChoices
    {
        /// <summary>
        /// 消息
        /// </summary>
        public ContextDialogueDouBaoOutputMessage delta { get; set; } = new ContextDialogueDouBaoOutputMessage();

        /// <summary>
        /// 该元素在 choices 列表的索引
        /// </summary>
        public int index { get; set; }

        /// <summary>
        /// 模型生成结束原因:stop表示正常生成结束，length 表示已经到了生成的最大 token 数量，content_filter 表示命中审核提前终止
        /// </summary>
        public string? finish_reason { get; set; }
    }

    /// <summary>
    /// 消息体
    /// </summary>
    public class ContextDialogueDouBaoOutputMessage
    {
        /// <summary>
        /// 内容
        /// </summary>
        public string? content { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? role { get; set; }
    }
}
