﻿using Coldairarrow.Util;
using MongoDB.Bson;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Model.CustomException;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.School;
using UwooAgent.Model.AI;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_教师端首页
    /// </summary>
    public class AgentTeacherHomePageService : ServiceBase<AI_AgentBaseInfo, IAgentTeacherHomePageRepository>, IAgentTeacherHomePageService, IDependency
    {
        /// <summary>
        /// 获取智能体列表信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<AgentTeacherHomePageListOutPut> GetAgentListInfo(AgentTeacherHomePageListInput input)
        {
            try
            {
                //获取学校信息
                Exam_School schoolInfo = await DBSqlSugar.Queryable<Exam_School>().Where(p => p.Id == input.SchoolId).FirstAsync();
                if (schoolInfo == null)
                {
                    throw new BusException("学校Id异常!");
                }

                //获取智能体类型
                List<AI_AgentType> agentTypes = await DBSqlSugar.Queryable<AI_AgentType>().OrderBy(p => p.OrderId).With(SqlWith.NoLock).ToListAsync();

                //智能体信息 - 添加授权验证逻辑
                string sql = @"SELECT distinct
                            	agentInfo.Id,
                            	agentInfo.AgentName,
                            	agentInfo.AgentBotCode,
                            	agentInfo.AgentTypeId,
                            	agentInfo.Summarize,
                            	agentInfo.Logo,
                                agentInfo.CreateTime,
                            	( CASE WHEN collection.Id IS NULL THEN 0 ELSE 1 END ) AS IsCollection
                            FROM
                            	AI_AgentBaseInfo agentInfo WITH ( NOLOCK )
                            	-- ✅ 必须有有效的授权记录
                            	INNER JOIN AI_AgentAuthorization auth WITH ( NOLOCK ) ON agentInfo.Id = auth.AgentId
                            		AND auth.IsDeleted = 0
                            		AND auth.Status = 1
                            		AND auth.StartTime <= GETDATE()
                            		AND auth.EndTime >= GETDATE()
                            	-- ✅ 当前用户的学校信息
                            	INNER JOIN Exam_School currentSchool WITH ( NOLOCK ) ON currentSchool.Id = @schoolId
                            	LEFT JOIN Exam_Subject sub WITH ( NOLOCK ) ON agentInfo.SubjectId = sub.Id
                            	LEFT JOIN Base_TextbookDict textbook WITH ( NOLOCK ) ON agentInfo.TextbookId = textbook.Id
                            	LEFT JOIN AI_UserCollectionAgent collection WITH ( NOLOCK ) ON agentInfo.Id = collection.AgentId
                            		AND collection.UserId = @teacherId
                                	AND collection.IsDeleted = 0
                            WHERE
                            	agentInfo.IsDeleted = 0
                            	-- ✅ 兼顾原来的逻辑（学校类型+学科匹配）
                            	AND ( agentInfo.SchoolType = '0' OR agentInfo.SchoolType = @schoolType )
                            	AND ( agentInfo.SubjectId = '0' OR agentInfo.SubjectId = @subjectId )
                            	-- ✅ 授权层级匹配逻辑
                            	AND (
                            		-- 市级授权：检查市ID匹配（如果需要的话，这里可以进一步细化）
                            		auth.AuthorizationType = 1
                            		OR
                            		-- 区级授权：检查授权的区ID与当前学校的区ID一致
                            		(auth.AuthorizationType = 2 AND auth.DistrictId = currentSchool.AreaId)
                            		OR
                            		-- 校级授权：精确匹配学校ID
                            		(auth.AuthorizationType = 3 AND auth.SchoolId = @schoolId)
                            	) ";
                List<AgentTeacherHomePageAgentInfo> agentInfos = await DBSqlSugar.SqlQueryable<AgentTeacherHomePageAgentInfo>(sql)
                    .AddParameters(new
                    {
                        teacherId = input.TeacherId,
                        schoolId = input.SchoolId,
                        schoolType = SchoolTypeConvertToStage(schoolInfo.SchoolType),
                        subjectId = input.SubjectId
                    }).OrderBy("CreateTime").ToListAsync();

                //父级类型
                List<AgentTeacherHomePageParentType> parentTypes = agentTypes.Where(p => p.ParentId == "0")
                    .OrderBy(p => p.OrderId)
                    .Select(p => new AgentTeacherHomePageParentType()
                    {
                        Id = p.Id,
                        Title = p.Title
                    }).ToList();

                foreach (var parentType in parentTypes)
                {
                    //获取子级类型
                    List<AgentTeacherHomePageChildrenType> childrenTypes = agentTypes.Where(p => p.ParentId == parentType.Id)
                        .OrderBy(p => p.OrderId)
                        .Select(p => new AgentTeacherHomePageChildrenType()
                        {
                            Id = p.Id,
                            Title = p.Title,
                        })
                        .ToList();

                    foreach (var childrenType in childrenTypes)
                    {
                        childrenType.AgentInfo = agentInfos.Where(p => p.AgentTypeId == childrenType.Id).OrderBy(p => p.CreateTime).ToList();
                    }
                    parentType.ChildrenTypes = childrenTypes;
                }

                //我的（收藏）
                AgentTeacherHomePageParentType agentTeacherHomePageParentType = new AgentTeacherHomePageParentType()
                {
                    Title = "我的",
                    ChildrenTypes = new List<AgentTeacherHomePageChildrenType>()
                    {
                        new AgentTeacherHomePageChildrenType()
                        {
                            Title="最近收藏",
                            AgentInfo=agentInfos.Where(p => p.IsCollection == true).OrderBy(p => p.CreateTime).ToList()
                        }
                    }
                };
                parentTypes.Insert(0, agentTeacherHomePageParentType);
                return new AgentTeacherHomePageListOutPut()
                {
                    ParentTypes = parentTypes
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        private string SchoolTypeConvertToStage(string schoolType)
        {
            return schoolType switch
            {
                "2" => "1244510235175948288",
                "3" => "1244518813626535936",
                "5" => "1244518845457108992" ,
                _ => $"未知学段({schoolType})"
            };
        }

        /// <summary>
        /// 智能体收藏
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task AgentCollection(AgentTeacherCollectionInput input)
        {
            try
            {
                //获取收藏信息
                AI_UserCollectionAgent collectionAgent = await DBSqlSugar.Queryable<AI_UserCollectionAgent>().Where(p => p.UserId == input.TeacherId && p.AgentId == input.AgentId).FirstAsync();
                if (collectionAgent == null)
                {
                    collectionAgent = new AI_UserCollectionAgent()
                    {
                        Id = IdHelper.GetId(),
                        UserId = input.TeacherId,
                        AgentId = input.AgentId,
                        CreateTime = DateTime.Now,
                        Creator = input.TeacherId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId,
                        IsDeleted = false
                    };
                    await DBSqlSugar.Insertable(collectionAgent).ExecuteCommandAsync();
                }
                else
                {
                    if (collectionAgent.IsDeleted == true)
                    {
                        collectionAgent.IsDeleted = false;
                    }
                    else
                    {
                        collectionAgent.IsDeleted = true;
                    }
                    collectionAgent.ModifyTime = DateTime.Now;
                    collectionAgent.Modifier = input.TeacherId;
                    await DBSqlSugar.Updateable(collectionAgent).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }
    }
}
