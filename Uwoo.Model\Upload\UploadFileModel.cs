﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Model.Upload
{
    public class UploadFileModel
    {
        public Responseheaders? ResponseHeaders { get; set; }
        public int StatusCode { get; set; }
        public string? BucketName { get; set; }
        public string? ObjectKey { get; set; }
        public string? Etag { get; set; }
        public object? VersionId { get; set; }
        public string? ObjectUrl { get; set; }
        public object? ObjectStorageClass { get; set; }
        public string? RequestId { get; set; }
    }

    public class Responseheaders
    {
        public string? ContentLength { get; set; }
        public DateTime Date { get; set; }
        public string? Etag { get; set; }
        public string? Id2 { get; set; }
        public string? RequestId { get; set; }
    }
}
