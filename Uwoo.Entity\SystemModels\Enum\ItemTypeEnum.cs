﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Entity.SystemModels.Enum
{
    /// <summary>
    /// 类型
    /// </summary>
    public enum ItemTypeEnum
    {
        /// <summary>
        /// 省
        /// </summary>
        [Description("省")]
        Province = 1,

        /// <summary>
        /// 市
        /// </summary>
        [Description("市")]
        City = 2,

        /// <summary>
        /// 区
        /// </summary>
        [Description("区")]
        District = 3,

        /// <summary>
        /// 校
        /// </summary>
        [Description("校")]
        School = 4,
    }
}
