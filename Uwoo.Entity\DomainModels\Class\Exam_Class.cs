﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels
{
    /// <summary>
    /// 班级信息
    /// </summary>
    [Table("Exam_Class")]
    public class Exam_Class
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public string CreatorId { get; set; }

        /// <summary>
        /// Deleted
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 学校ID
        /// </summary>
        public string SchoolId { get; set; }

        /// <summary>
        /// 班级名称
        /// </summary>
        public string ClassName { get; set; }

        /// <summary>
        /// 班级模板设置ID
        /// </summary>
        public string SchoolClassSettingId { get; set; }

        /// <summary>
        /// 班主任
        /// </summary>
        public string ClassAdviserId { get; set; }

        /// <summary>
        /// 二维码
        /// </summary>
        public string QRCode { get; set; }

        /// <summary>
        /// 二维码创建时间
        /// </summary>
        public DateTime? QRCodeLastChangeTime { get; set; }

        /// <summary>
        /// 班级学生人数
        /// </summary>
        public int? StudentCount { get; set; }

        /// <summary>
        /// 是否锁定 1  是
        /// </summary>
        public int? IsLock { get; set; }

        /// <summary>
        /// 年级
        /// </summary>
        public int GradeId { get; set; }

        /// <summary>
        /// 上海统一用户认证Id
        /// </summary>
        public string TYId { get; set; }

        /// <summary>
        /// 微校网校用户认证 Id
        /// </summary>
        public string WxId { get; set; }

        /// <summary>
        /// 黄埔Id
        /// </summary>
        public string HpId { get; set; }

        /// <summary>
        /// 徐汇网校Id
        /// </summary>
        public string XhId { get; set; }

        /// <summary>
        /// 是否开通点阵笔 0 否  1 是
        /// </summary>
        public int? IsDZB { get; set; }
    }
}
