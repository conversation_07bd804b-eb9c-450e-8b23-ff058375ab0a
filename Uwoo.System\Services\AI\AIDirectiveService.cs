using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// AI指令管理服务 
    /// </summary>
    public class AIDirectiveService : ServiceBase<AI_Directive, IAIDirectiveRepository>, IAIDirectiveService, IDependency
    {
        public static IAIDirectiveService Instance
        {
            get { return AutofacContainerModule.GetService<IAIDirectiveService>(); }
        }

        /// <summary>
        /// 获取AI指令分页列表
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>分页结果</returns>
        public async Task<AIDirectivePageResult> GetAIDirectiveListAsync(AIDirectiveQueryInput input)
        {
            try
            {
                var query = DBSqlSugar.Queryable<AI_Directive>();

                // 状态筛选
                if (input.IsDeleted.HasValue)
                {
                    query = query.Where(p => p.IsDeleted == input.IsDeleted.Value);
                }

                // 关键词搜索
                if (!string.IsNullOrEmpty(input.Keyword))
                {
                    query = query.Where(p => p.Key.Contains(input.Keyword) || p.Directive.Contains(input.Keyword));
                }

                // 类型筛选
                if (!string.IsNullOrEmpty(input.Type))
                {
                    query = query.Where(p => p.Key.Contains(input.Type));
                }

                // 排序
                switch (input.SortField?.ToLower())
                {
                    case "key":
                        query = input.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(p => p.Key)
                            : query.OrderByDescending(p => p.Key);
                        break;
                    case "createtime":
                    default:
                        query = input.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(p => p.CreateTime)
                            : query.OrderByDescending(p => p.CreateTime);
                        break;
                }

                // 分页查询
                var total = await query.CountAsync();
                var items = await query
                    .Skip((input.Page - 1) * input.PageSize)
                    .Take(input.PageSize)
                    .ToListAsync();

                // 转换为DTO
                var dtoItems = items.Select(ConvertToListDto).ToList();

                return new AIDirectivePageResult
                {
                    Items = dtoItems,
                    Total = total,
                    Page = input.Page,
                    PageSize = input.PageSize
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"获取AI指令列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据ID获取AI指令详情
        /// </summary>
        /// <param name="id">指令ID</param>
        /// <returns>指令详情</returns>
        public async Task<AIDirectiveDetailDto?> GetAIDirectiveByIdAsync(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    throw new ArgumentException("指令ID不能为空");
                }

                var directive = await DBSqlSugar.Queryable<AI_Directive>()
                    .Where(p => p.Id == id)
                    .FirstAsync();

                return directive == null ? null : ConvertToDetailDto(directive);
            }
            catch (Exception ex)
            {
                throw new Exception($"获取AI指令详情失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建AI指令
        /// </summary>
        /// <param name="input">创建参数</param>
        /// <returns>操作结果</returns>
        public async Task<AIDirectiveOperationResult> CreateAIDirectiveAsync(CreateAIDirectiveInput input)
        {
            try
            {
                // 验证Key唯一性
                var existingDirective = await DBSqlSugar.Queryable<AI_Directive>()
                    .Where(p => p.Key == input.Key && p.IsDeleted == false)
                    .FirstAsync();

                if (existingDirective != null)
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = $"Key '{input.Key}' 已存在，请使用其他Key"
                    };
                }

                // 创建新指令
                var newDirective = new AI_Directive
                {
                    Id = Guid.NewGuid().ToString(),
                    Key = input.Key.Trim(),
                    Directive = input.Directive.Trim(),
                    CreateTime = DateTime.Now,
                    IsDeleted = false
                };

                var result = await DBSqlSugar.Insertable(newDirective).ExecuteCommandAsync();

                if (result > 0)
                {
                    var createdDirective = await GetAIDirectiveByIdAsync(newDirective.Id);
                    return new AIDirectiveOperationResult
                    {
                        Success = true,
                        Message = "AI指令创建成功",
                        Data = createdDirective
                    };
                }
                else
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "AI指令创建失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return new AIDirectiveOperationResult
                {
                    Success = false,
                    Message = $"创建AI指令失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 更新AI指令
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>操作结果</returns>
        public async Task<AIDirectiveOperationResult> UpdateAIDirectiveAsync(UpdateAIDirectiveInput input)
        {
            try
            {
                // 检查指令是否存在
                var existingDirective = await DBSqlSugar.Queryable<AI_Directive>()
                    .Where(p => p.Id == input.Id)
                    .FirstAsync();

                if (existingDirective == null)
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "指令不存在"
                    };
                }

                if (existingDirective.IsDeleted)
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "无法更新已删除的指令"
                    };
                }

                // 更新指令内容
                var result = await DBSqlSugar.Updateable<AI_Directive>()
                    .SetColumns(p => p.Directive == input.Directive.Trim())
                    .Where(p => p.Id == input.Id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    var updatedDirective = await GetAIDirectiveByIdAsync(input.Id);
                    return new AIDirectiveOperationResult
                    {
                        Success = true,
                        Message = "AI指令更新成功",
                        Data = updatedDirective
                    };
                }
                else
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "AI指令更新失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return new AIDirectiveOperationResult
                {
                    Success = false,
                    Message = $"更新AI指令失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 软删除AI指令
        /// </summary>
        /// <param name="id">指令ID</param>
        /// <returns>操作结果</returns>
        public async Task<AIDirectiveOperationResult> DeleteAIDirectiveAsync(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "指令ID不能为空"
                    };
                }

                // 检查指令是否存在
                var existingDirective = await DBSqlSugar.Queryable<AI_Directive>()
                    .Where(p => p.Id == id)
                    .FirstAsync();

                if (existingDirective == null)
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "指令不存在"
                    };
                }

                if (existingDirective.IsDeleted)
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "指令已被删除"
                    };
                }

                // 软删除
                var result = await DBSqlSugar.Updateable<AI_Directive>()
                    .SetColumns(p => p.IsDeleted == true)
                    .Where(p => p.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = true,
                        Message = "AI指令删除成功"
                    };
                }
                else
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "AI指令删除失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return new AIDirectiveOperationResult
                {
                    Success = false,
                    Message = $"删除AI指令失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 恢复已删除的AI指令
        /// </summary>
        /// <param name="id">指令ID</param>
        /// <returns>操作结果</returns>
        public async Task<AIDirectiveOperationResult> RestoreAIDirectiveAsync(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "指令ID不能为空"
                    };
                }

                // 检查指令是否存在
                var existingDirective = await DBSqlSugar.Queryable<AI_Directive>()
                    .Where(p => p.Id == id)
                    .FirstAsync();

                if (existingDirective == null)
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "指令不存在"
                    };
                }

                if (!existingDirective.IsDeleted)
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "指令未被删除，无需恢复"
                    };
                }

                // 检查Key是否冲突
                var conflictDirective = await DBSqlSugar.Queryable<AI_Directive>()
                    .Where(p => p.Key == existingDirective.Key && p.IsDeleted == false && p.Id != id)
                    .FirstAsync();

                if (conflictDirective != null)
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = $"无法恢复，Key '{existingDirective.Key}' 已被其他指令使用"
                    };
                }

                // 恢复指令
                var result = await DBSqlSugar.Updateable<AI_Directive>()
                    .SetColumns(p => p.IsDeleted == false)
                    .Where(p => p.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    var restoredDirective = await GetAIDirectiveByIdAsync(id);
                    return new AIDirectiveOperationResult
                    {
                        Success = true,
                        Message = "AI指令恢复成功",
                        Data = restoredDirective
                    };
                }
                else
                {
                    return new AIDirectiveOperationResult
                    {
                        Success = false,
                        Message = "AI指令恢复失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return new AIDirectiveOperationResult
                {
                    Success = false,
                    Message = $"恢复AI指令失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取AI指令类型统计
        /// </summary>
        /// <returns>类型统计列表</returns>
        public async Task<List<AIDirectiveTypeStatistics>> GetAIDirectiveTypeStatisticsAsync()
        {
            try
            {
                var directives = await DBSqlSugar.Queryable<AI_Directive>()
                    .Where(p => p.IsDeleted == false)
                    .Select(p => p.Key)
                    .ToListAsync();

                var typeGroups = directives
                    .GroupBy(key => ExtractTypeFromKey(key))
                    .Select(g => new AIDirectiveTypeStatistics
                    {
                        Type = g.Key,
                        Count = g.Count(),
                        TypeDisplayName = GetTypeDisplayName(g.Key)
                    })
                    .OrderByDescending(x => x.Count)
                    .ToList();

                return typeGroups;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取AI指令类型统计失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 搜索AI指令
        /// </summary>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="limit">返回数量限制</param>
        /// <returns>搜索结果</returns>
        public async Task<List<AIDirectiveListDto>> SearchAIDirectivesAsync(string keyword, int limit = 10)
        {
            try
            {
                if (string.IsNullOrEmpty(keyword))
                {
                    return new List<AIDirectiveListDto>();
                }

                var directives = await DBSqlSugar.Queryable<AI_Directive>()
                    .Where(p => p.IsDeleted == false && (p.Key.Contains(keyword) || p.Directive.Contains(keyword)))
                    .OrderByDescending(p => p.CreateTime)
                    .Take(limit)
                    .ToListAsync();

                return directives.Select(ConvertToListDto).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"搜索AI指令失败: {ex.Message}", ex);
            }
        }
        #region 私有辅助方法

        /// <summary>
        /// 转换为列表DTO
        /// </summary>
        /// <param name="directive">AI指令实体</param>
        /// <returns>列表DTO</returns>
        private AIDirectiveListDto ConvertToListDto(AI_Directive directive)
        {
            return new AIDirectiveListDto
            {
                Id = directive.Id,
                Key = directive.Key,
                Name = ExtractNameFromKey(directive.Key),
                Type = ExtractTypeFromKey(directive.Key),
                DirectivePreview = GetDirectivePreview(directive.Directive),
                CreateTime = directive.CreateTime,
                IsDeleted = directive.IsDeleted
            };
        }

        /// <summary>
        /// 转换为详情DTO
        /// </summary>
        /// <param name="directive">AI指令实体</param>
        /// <returns>详情DTO</returns>
        private AIDirectiveDetailDto ConvertToDetailDto(AI_Directive directive)
        {
            return new AIDirectiveDetailDto
            {
                Id = directive.Id,
                Key = directive.Key,
                Name = ExtractNameFromKey(directive.Key),
                Type = ExtractTypeFromKey(directive.Key),
                Directive = directive.Directive,
                CreateTime = directive.CreateTime,
                IsDeleted = directive.IsDeleted
            };
        }

        /// <summary>
        /// 从Key中提取友好名称
        /// </summary>
        /// <param name="key">指令Key</param>
        /// <returns>友好名称</returns>
        private string ExtractNameFromKey(string key)
        {
            if (string.IsNullOrEmpty(key)) return "";

            // 移除 "_Directive" 后缀
            var name = key.Replace("_Directive", "");

            // 将驼峰命名转换为友好名称
            return Regex.Replace(name, "([a-z])([A-Z])", "$1 $2");
        }

        /// <summary>
        /// 从Key中提取类型
        /// </summary>
        /// <param name="key">指令Key</param>
        /// <returns>类型</returns>
        private string ExtractTypeFromKey(string key)
        {
            if (string.IsNullOrEmpty(key)) return "Unknown";

            if (key.Contains("Student")) return "Student";
            if (key.Contains("Teacher")) return "Teacher";
            if (key.Contains("IntelligentQuestion")) return "IntelligentQuestion";
            if (key.Contains("TeachingPlan")) return "TeachingPlan";
            if (key.Contains("OralCommunication")) return "OralCommunication";

            return "Other";
        }

        /// <summary>
        /// 获取类型显示名称
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>显示名称</returns>
        private string GetTypeDisplayName(string type)
        {
            return type switch
            {
                "Student" => "学生端",
                "Teacher" => "教师端",
                "IntelligentQuestion" => "智能出题",
                "TeachingPlan" => "教案生成",
                "OralCommunication" => "口语交际",
                "Other" => "其他",
                _ => type
            };
        }

        /// <summary>
        /// 获取指令内容预览
        /// </summary>
        /// <param name="directive">指令内容</param>
        /// <returns>预览内容</returns>
        private string GetDirectivePreview(string directive)
        {
            if (string.IsNullOrEmpty(directive)) return "";

            // 移除换行符并截取前100个字符
            var preview = directive.Replace("\r\n", " ").Replace("\n", " ").Replace("\r", " ");
            return preview.Length > 100 ? preview.Substring(0, 100) + "..." : preview;
        }

        #endregion
    }
}