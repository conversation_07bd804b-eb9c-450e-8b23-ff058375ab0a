﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI.AgentTeachingPlanAnalysis
{
    /// <summary>
    /// 分布数据统计结果
    /// </summary>
    public class StudentsGradeDistributionModel
    {
        /// <summary>
        /// 数据类型，0前测，1为后测
        /// </summary>
        public int DataType { get; set; }
        /// <summary>
        /// 学生成绩分布数据
        /// </summary>
        public List<StudentGradeDataModel>? StudentsGradeData { get; set; }
    }
    public class StudentGradeDataModel
    {
        /// <summary>
        /// 分数段，例如：0-20,21-40,41-60,61-80,81-100
        /// </summary>
        public string? FractionalSegments { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int StudentCount { get; set; }
    }
}
