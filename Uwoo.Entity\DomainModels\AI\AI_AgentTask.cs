﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// 智能体任务
	/// </summary>
	[Table("AI_AgentTask")]
    public class AI_AgentTask : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string AgentId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string Introduce { get; set; }

        /// <summary>
        /// 智能体任务类型（1口语交际、2项目化实践、3阅读理解、4建模）
        /// </summary>
        public int AgentTaskType { get; set; }

        /// <summary>
        /// 任务图标
        /// </summary>
        public string TaskLogo { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// 学年
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// 学期（1:上学期，2:下学期）
        /// </summary>
        public int? Term { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public string SubjectId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string GradeId { get; set; }

        /// <summary>
        /// 评分公布类型（1提交后、2截至后、3指定时间）
        /// </summary>
        public int? ScorePublishType { get; set; }

        /// <summary>
        /// 评分公布时间（评分公布类型指定时间）
        /// </summary>
        public DateTime? ScorePublishTime { get; set; }
    }
}
