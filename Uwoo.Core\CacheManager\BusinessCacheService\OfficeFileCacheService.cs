﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.CacheManager.Service;
using Uwoo.Core.Utilities;
using UwooAgent.Core.CacheManager.IBusinessCacheService;
using UwooAgent.Entity.DomainModels.School;

namespace UwooAgent.Core.CacheManager.BusinessCacheService
{
    /// <summary>
    /// OfficeFile缓存
    /// </summary>
    public class OfficeFileCacheService : RedisService, IOfficeFileCacheService
    {
        /// <summary>
        /// 如果缓存为db1,就要在构造函数中传1
        /// </summary>
        public OfficeFileCacheService() : base(0)
        { }

        public override string Prefix => RedisKeys.Office;

        /// <summary>
        /// 获取Office文件版本
        /// </summary>
        /// <param name="id"></param>
        public long GetOfficeVersion(string id)
        {
            return IncrByNoPrefix(RedisKeys.OfficeVersion + id);
        }
    }
}
