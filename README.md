# UwooAgent - 智能体教育平台

## 项目简介

UwooAgent 是一个基于 .NET 6 的智能体教育平台，专注于为教师和学生提供AI驱动的教学和学习体验。该平台集成了多种AI功能，包括图像生成、HTML代码生成、语音交互等，旨在提升教育教学的智能化水平。

## 技术架构

### 核心技术栈
- **.NET 6** - 主要开发框架
- **ASP.NET Core Web API** - Web API框架
- **SqlSugar** - ORM数据访问框架
- **Autofac** - 依赖注入容器
- **SignalR** - 实时通信
- **JWT** - 身份认证
- **Swagger** - API文档
- **NLog** - 日志记录
- **Quartz.NET** - 任务调度
- **RabbitMQ** - 消息队列
- **Redis** - 缓存服务
- **MongoDB** - 文档数据库

### 项目结构

```
UwooAgent/
├── Uwoo.Core/           # 核心框架层
├── Uwoo.Entity/         # 实体模型层
├── Uwoo.Model/          # 数据传输对象层
├── Uwoo.Builder/        # 代码生成器
├── Uwoo.System/         # 业务逻辑层
├── Uwoo.Contracts/      # 契约接口层
├── Uwoo.Mongo/          # MongoDB数据访问层
├── Uwoo.WebApi/         # Web API接口层
└── UwooAgent.sln        # 解决方案文件
```

## 主要功能模块

### 1. AI智能体功能
- **图像生成**: 基于豆包AI的图像生成服务
- **HTML代码生成**: 智能生成HTML代码
- **语音交互**: 支持语音转文本和文本转语音
- **上下文对话**: 支持多轮对话的上下文管理
- **文件处理**: AI文件上传和处理

### 2. 教师端功能
- **智能体管理**: 创建和管理教学智能体
- **口语交际**: 设计口语交际任务
- **智能体收藏**: 收藏和管理智能体
- **任务发布**: 发布学习任务给学生

### 3. 学生端功能
- **智能体交互**: 与教学智能体进行对话
- **任务完成**: 完成教师布置的学习任务
- **口语练习**: 进行口语交际练习
- **学习记录**: 记录学习过程和结果

### 4. 系统管理功能
- **用户认证**: JWT身份验证
- **权限管理**: 基于角色的权限控制
- **日志管理**: 完整的操作日志记录
- **缓存管理**: Redis缓存优化
- **文件管理**: 支持华为云OBS存储

## 环境要求

- **.NET 6 SDK**
- **SQL Server** (主数据库)
- **Redis** (缓存服务)
- **MongoDB** (文档存储)
- **RabbitMQ** (消息队列)

## 快速开始

### 1. 环境配置

#### 数据库配置
在 `appsettings.json` 中配置数据库连接：

```json
{
  "Connection": {
    "DBType": "MsSql",
    "DbConnectionString": "Server=your_server;Database=your_database;User ID=your_user;Password=your_password;",
    "RedisConnectionString": "your_redis_host:6379,password=your_password",
    "UseRedis": "true"
  }
}
```

#### AI服务配置
配置豆包AI服务：

```json
{
  "DouBaoAI": {
    "APIKey": "Bearer your_api_key",
    "AIGenerateImageUrl": "https://ark.cn-beijing.volces.com/api/v3/images/generations",
    "AIGenerateImageModelId": "your_model_id",
    "AIGenerateHTMLCodeUrl": "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
    "AIGenerateHTMLCodeModeId": "your_model_id"
  }
}
```

### 2. 运行项目

#### 开发环境
```bash
# 进入项目目录
cd Uwoo.WebApi

# 运行开发环境
dotnet run --environment Development
```

或者使用提供的批处理文件：
```bash
# Windows环境
dev_run.bat
```

#### 生产环境
```bash
# 构建项目
dotnet build --configuration Release

# 运行项目
dotnet run --configuration Release
```

### 3. 代码生成器

项目提供了代码生成器来快速生成业务代码：

```bash
# 运行代码生成器
builder_run.bat
```

## API文档

项目集成了Swagger，启动后访问：
- 开发环境: `http://localhost:9910/swagger`
- 生产环境: `http://your_domain:9910/swagger`

### 主要API端点

#### AI功能相关
- `POST /api/AgentCommon/AIGenerateImage` - AI图像生成
- `POST /api/AgentCommon/AIGenerateHTMLCode` - AI HTML代码生成
- `POST /api/AgentCommon/AIUploadFile` - AI文件上传

#### 教师端
- `GET /api/AgentTeacherHomePage/GetAgentListInfo` - 获取智能体列表
- `POST /api/AgentTeacherHomePage/AgentCollection` - 智能体收藏

#### 学生端
- `GET /api/AgentStudentHomePage/GetStudentHomePageAgentTaskList` - 获取学生任务列表
- `POST /api/AgentStudentOralCommunication/AgentStudentOralCommunicationDialogue` - 口语交际对话

## 部署说明

### Docker部署

项目提供了Dockerfile：

```bash
# 构建镜像
docker build -t uwoo-agent .

# 运行容器
docker run -d -p 9910:9910 --name uwoo-agent uwoo-agent
```

### 传统部署

1. 发布项目：
```bash
dotnet publish -c Release -o ./publish
```

2. 配置IIS或Nginx反向代理

3. 确保所有依赖服务（SQL Server、Redis、MongoDB、RabbitMQ）正常运行

## 开发指南

### 项目约定

1. **命名规范**: 使用Pascal命名法
2. **代码结构**: 遵循DDD领域驱动设计
3. **异常处理**: 使用统一的异常处理机制
4. **日志记录**: 使用NLog进行结构化日志记录

### 添加新功能

1. 在 `Uwoo.Entity` 中定义实体模型
2. 在 `Uwoo.Model` 中定义DTO对象
3. 在 `Uwoo.System` 中实现业务逻辑
4. 在 `Uwoo.WebApi` 中添加API控制器
5. 使用代码生成器生成基础CRUD代码

### 数据库迁移

项目使用SqlSugar作为ORM，支持多种数据库：
- SQL Server
- MySQL
- PostgreSQL

迁移步骤参考 `SqlSugar使用说明.txt`

## 配置说明

### 核心配置项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| Connection:DBType | 数据库类型 | MsSql |
| Connection:UseRedis | 是否使用Redis | true |
| Connection:UseSignalR | 是否使用SignalR | false |
| ExpMinutes | JWT过期时间(分钟) | 120 |
| WorkId | 雪花算法工作ID | 100 |

### 第三方服务配置

- **华为云OBS**: 文件存储服务
- **豆包AI**: AI能力提供商
- **极光推送**: 消息推送服务
- **WPS**: 文档处理服务

## 监控和日志

### 日志配置
项目使用NLog进行日志记录，配置文件：`nlog.config`

### 性能监控
- SQL执行日志
- API请求响应时间
- 异常错误追踪

## 常见问题

### Q: 如何切换数据库类型？
A: 修改 `appsettings.json` 中的 `Connection:DBType` 配置项，支持 MsSql/MySql/PgSql

### Q: Redis连接失败怎么办？
A: 检查Redis服务是否启动，确认连接字符串配置正确

### Q: AI功能调用失败？
A: 检查豆包AI的API Key和模型ID配置是否正确

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

## 许可证

本项目采用私有许可证，仅供内部使用。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 本项目为教育平台系统，请确保在生产环境中正确配置所有安全相关设置。