﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Uwoo.Core.Configuration;
using Uwoo.Core.Enums;

namespace Uwoo.Core.Utilities
{
	/// <summary>
	/// wps帮助类
	/// </summary>
	public static class WpsHelper
	{
		/// <summary>
		/// 对字符串进行HMACSH1加密
		/// </summary>
		/// <param name="encryptText"></param>
		/// <returns></returns>
		public static string ToHMACSHA1(string encryptText)
		{
			HMACSHA1 hmacsha1 = new HMACSHA1();
			hmacsha1.Key = System.Text.Encoding.Default.GetBytes(AppSetting.WPSConfig.AppSecretKey);
			byte[] dataBuffer = System.Text.Encoding.Default.GetBytes(encryptText);
			byte[] hashBytes = hmacsha1.ComputeHash(dataBuffer);
			return Convert.ToBase64String(hashBytes);
		}

		/// <summary>
		/// 对参数签名
		/// </summary>
		/// <param name="paramDic"></param>
		/// <returns>签名后的字符串</returns>
		public static string Signature(Dictionary<string, string> paramDic)
		{
			var sortParam = paramDic.OrderBy(p => p.Key).ToDictionary(p => p.Key, p => p.Value);
			sortParam.Add("_w_secretkey", AppSetting.WPSConfig.AppSecretKey);
			var paramStr = string.Join("", sortParam.Select(p => $"{p.Key}={p.Value}").ToArray());
			var signature = ToHMACSHA1(paramStr);
			sortParam.Remove("_w_secretkey");
			sortParam.Add("_w_signature", HttpUtility.UrlEncode(signature));
			return string.Join("&", sortParam.Select(p => $"{p.Key}={p.Value}").ToArray());
		}

		/// <summary>
		/// 生成签名后iframe用的url
		/// </summary>
		/// <param name="fileId"></param>
		/// <param name="fileType"></param>
		/// <param name="paramDic"></param>
		/// <returns></returns>
		// 此处直接读取了配置的appid，所以生成url时不用传递appid
		public static string GenarateUrl(string fileId, WpsFileType fileType, Dictionary<string, string> paramDic = null)
		{
			if (paramDic == null)
			{
				paramDic = new Dictionary<string, string>();
			}

			paramDic.Add("_w_appid", AppSetting.WPSConfig.AppId);
			var paramStr = Signature(paramDic);
			return $"https://wwo.wps.cn/office/{fileType.ToString()}/{fileId}?{paramStr}";
		}
	}
}
