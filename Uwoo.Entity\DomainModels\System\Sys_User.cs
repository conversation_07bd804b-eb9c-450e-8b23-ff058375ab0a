﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Entity.DomainModels.System
{
    /// <summary>
    /// 
    /// </summary>
    [Entity(TableCnName = "用户管理", TableName = "Sys_User")]
    public partial class Sys_User : BaseEntity
    {
        /// <summary>
        ///
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Key]
        [Display(Name = "Id")]
        [Column(TypeName = "varchar(50)")]
        [Required(AllowEmptyStrings = false)]
        public string Id { get; set; }

        /// <summary>
        ///帐号
        /// </summary>
        [Display(Name = "帐号")]
        [MaxLength(100)]
        [Column(TypeName = "nvarchar(100)")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public string UserName { get; set; }

        /// <summary>
        ///姓名
        /// </summary>
        [Display(Name = "姓名")]
        [MaxLength(20)]
        [Column(TypeName = "nvarchar(20)")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public string RealName { get; set; }

        /// <summary>
        ///性别(1男、0女)
        /// </summary>
        [Display(Name = "性别")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? Gender { get; set; }

        /// <summary>
        ///头像
        /// </summary>
        [Display(Name = "头像")]
        [MaxLength(500)]
        [Column(TypeName = "nvarchar(500)")]
        [Editable(true)]
        public string Photo { get; set; }

        /// <summary>
        ///不用
        /// </summary>
        [Display(Name = "不用")]
        [Column(TypeName = "int")]
        public int? Dept_Id { get; set; }

        /// <summary>
        ///不用
        /// </summary>
        [Display(Name = "不用")]
        [MaxLength(150)]
        [Column(TypeName = "nvarchar(150)")]
        [Editable(true)]
        public string DeptName { get; set; }

        /// <summary>
        ///角色
        /// </summary>
        [Display(Name = "角色")]
        [Column(TypeName = "int")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public int Role_Id { get; set; }

        /// <summary>
        ///不用
        /// </summary>
        [Display(Name = "不用")]
        [MaxLength(200)]
        [Column(TypeName = "nvarchar(200)")]
        [Editable(true)]
        public string RoleName { get; set; }

        /// <summary>
        ///Token
        /// </summary>
        [Display(Name = "Token")]
        [MaxLength(500)]
        [Column(TypeName = "nvarchar(500)")]
        [Editable(true)]
        public string Token { get; set; }

        /// <summary>
        ///类型
        /// </summary>
        [Display(Name = "类型")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? AppType { get; set; }

        /// <summary>
        ///组织构架
        /// </summary>
        [Display(Name = "组织构架")]
        [MaxLength(2000)]
        [Column(TypeName = "nvarchar(2000)")]
        [Editable(true)]
        public string DeptIds { get; set; }

        /// <summary>
        ///密码
        /// </summary>
        [Display(Name = "密码")]
        [MaxLength(200)]
        [SugarColumn(NoSerialize = true)]
        [Column(TypeName = "nvarchar(200)")]
        public string Password { get; set; }

        /// <summary>
        ///注册时间
        /// </summary>
        [Display(Name = "注册时间")]
        [Column(TypeName = "datetime")]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        ///手机用户
        /// </summary>
        [Display(Name = "手机用户")]
        [Column(TypeName = "int")]
        public int? IsRegregisterPhone { get; set; }

        /// <summary>
        ///手机号
        /// </summary>
        [Display(Name = "手机号")]
        [MaxLength(11)]
        [Column(TypeName = "nvarchar(11)")]
        public string PhoneNo { get; set; }

        /// <summary>
        ///创建人
        /// </summary>
        [Display(Name = "创建人")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        public string Creator { get; set; }

        /// <summary>
        ///是否可用
        /// </summary>
        [Display(Name = "是否可用")]
        [Column(TypeName = "tinyint")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public byte Enable { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [Display(Name = "修改人")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        public string Modifier { get; set; }

        /// <summary>
        ///修改时间
        /// </summary>
        [Display(Name = "修改时间")]
        [Column(TypeName = "datetime")]
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        ///审核状态（1审核中、2审核已通过、3审核未通过）
        /// </summary>
        [Display(Name = "审核状态")]
        [Column(TypeName = "int")]
        public int AuditStatus { get; set; }

        /// <summary>
        ///最后登陆时间
        /// </summary>
        [Display(Name = "最后登陆时间")]
        [Column(TypeName = "datetime")]
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        ///最后密码修改时间
        /// </summary>
        [Display(Name = "最后密码修改时间")]
        [Column(TypeName = "datetime")]
        public DateTime? LastModifyPwdDate { get; set; }

        /// <summary>
        ///地址
        /// </summary>
        [Display(Name = "地址")]
        [MaxLength(200)]
        [Column(TypeName = "varchar(200)")]
        [Editable(true)]
        public string Address { get; set; }

        /// <summary>
        ///Email
        /// </summary>
        [Display(Name = "Email")]
        [MaxLength(100)]
        [Column(TypeName = "varchar(100)")]
        [Editable(true)]
        public string Email { get; set; }

        /// <summary>
        ///备注
        /// </summary>
        [Display(Name = "备注")]
        [MaxLength(100)]
        [Column(TypeName = "varchar(100)")]
        [Editable(true)]
        public string Notes { get; set; }

        /// <summary>
        /// 1 正式，2 试用，3测试
        /// </summary>
        [Display(Name = "使用类型")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? UseType { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        [Display(Name = "来源")]
        [Column(TypeName = "int")]
        [Editable(true)]
        public int? Source { get; set; }

        /// <summary>
        /// 基础教育Id
        /// </summary>
        [Display(Name = "基础教育Id")]
        [MaxLength(50)]
        [Column(TypeName = "varchar(50)")]
        [Editable(true)]
        public string BaseEduId { get; set; }

        /// <summary>
        ///省Id
        /// </summary>
        [Display(Name = "Province")]
        [MaxLength(20)]
        [Column(TypeName = "varchar(20)")]
        [Editable(true)]
        public string Province { get; set; }

        /// <summary>
        ///市Id
        /// </summary>
        [Display(Name = "City")]
        [MaxLength(20)]
        [Column(TypeName = "varchar(20)")]
        [Editable(true)]
        public string City { get; set; }

        /// <summary>
        ///区/县Id
        /// </summary>
        [Display(Name = "区/县Id")]
        [MaxLength(20)]
        [Column(TypeName = "varchar(20)")]
        [Editable(true)]
        public string District { get; set; }

        /// <summary>
        ///状态0禁用1启用
        /// </summary>
        [Display(Name = "状态0禁用1启用")]
        [Column(TypeName = "int")]
        public int Status { get; set; }

        /// <summary>
        ///软删除
        /// </summary>
        [Display(Name = "软删除")]
        [Column(TypeName = "bit")]
        public bool Deleted { get; set; }
    }
}
