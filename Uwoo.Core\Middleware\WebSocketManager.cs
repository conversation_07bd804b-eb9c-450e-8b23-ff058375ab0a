﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.WebSockets;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Core.Middleware
{
	public class WebSocketManager
	{
		private ConcurrentDictionary<string, ConcurrentDictionary<string, WebSocket>> _connections = new ConcurrentDictionary<string, ConcurrentDictionary<string, WebSocket>>();

		public static ConcurrentDictionary<string, WebSocket> _ClientQRScanWebSocket = new ConcurrentDictionary<string, WebSocket>();

		public void AddSocket(string userId, string connectionId, WebSocket socket)
		{
			if (!_connections.ContainsKey(userId))
			{
				_connections[userId] = new ConcurrentDictionary<string, WebSocket>();
			}
			_connections[userId][connectionId] = socket;
		}

		public async Task<WebSocket> AddSocket(HttpContext context)
		{
			string ipAddress = ((Microsoft.AspNetCore.Http.DefaultHttpContext)context).HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString();
			WebSocket outWS;
			WebSocket webSocket = await context.WebSockets.AcceptWebSocketAsync();

			if ("/ws/qrscanlogin" == context.Request.Path)
			{
				if (_ClientQRScanWebSocket.ContainsKey(ipAddress))
				{
					if ("*******" == ipAddress)
					{
						_ClientQRScanWebSocket.TryRemove("127.0.0.1", out outWS);
					}
					_ClientQRScanWebSocket.TryRemove(ipAddress, out outWS);
				}
				_ClientQRScanWebSocket.TryAdd(ipAddress, webSocket);
			}
			return webSocket;
		}


		public void RemoveSocket(string userId, string connectionId)
		{
			if (_connections.ContainsKey(userId))
			{
				_connections[userId].TryRemove(connectionId, out _);
				if (_connections[userId].Count == 0)
				{
					_connections.TryRemove(userId, out _);
				}
			}
		}

		public async Task SendMessageToUserAsync(string userId, string message)
		{
			if (_connections.ContainsKey(userId))
			{
				var userConnections = _connections[userId];
				foreach (var socketPair in userConnections)
				{
					if (socketPair.Value.State == WebSocketState.Open)
					{
						await SendMessageAsync(socketPair.Value, message);
					}
				}
			}
		}

		private async Task SendMessageAsync(WebSocket socket, string message)
		{
			var bytes = System.Text.Encoding.UTF8.GetBytes(message);
			await socket.SendAsync(new ArraySegment<byte>(bytes, 0, bytes.Length), WebSocketMessageType.Text, true, System.Threading.CancellationToken.None);
		}
	}
}
