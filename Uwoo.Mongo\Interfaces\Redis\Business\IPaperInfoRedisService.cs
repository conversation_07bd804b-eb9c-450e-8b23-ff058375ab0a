﻿// -- Function：IPaperInfoRedisService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/11/01 13:52

namespace Uwoo.Mongo.Interfaces.Redis.Business;

using Uwoo.Contracts.Paper;

/// <summary>
/// 试卷信息
/// </summary>
public interface IPaperInfoRedisService : IRedisService
{
    /// <summary>
    /// 获取试卷子页面数据
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="pageno">页码</param>
    /// <returns></returns>
    Task<WorkbookPage> HGetPaperPageInfoAsync(string paperid, int pageno);

    /// <summary>
    /// 缓存试卷子页面数据
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="page">页码</param>
    /// <param name="workpage">试卷子页面</param>
    /// <returns></returns>
    Task HSetPaperPageInfoAsync(string paperid, int page, WorkbookPage workpage);

    /// <summary>
    /// 根据页码id获取试卷信息
    /// </summary>
    /// <param name="pageid">页码id</param>
    /// <returns></returns>
    Task<string> HGetPaperIdAsync(int pageid);

    /// <summary>
    /// 设置页码对应试卷信息
    /// </summary>
    /// <param name="pageid">页码id</param>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task HSetPaperIdAsync(int pageid, string paperid);

    /// <summary>
    /// 获取完成提交页面数据
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task<PaperFinishRange> HGetPaperRangeAsync(string paperid);

    /// <summary>
    /// 设置完成提交页面区域数据
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="data">区域数据</param>
    /// <returns></returns>
    Task HSetPaperRangeAsync(string paperid, PaperFinishRange data);

    /// <summary>
    /// 获取完成提交区域是否为首次查询
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task<int> HGetPaperRangeCountAsync(string paperid);

    /// <summary>
    /// 设置完成提交区域是否为首次查询
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="count">数量</param>
    /// <returns></returns>
    Task HSetPaperRangeCountAsync(string paperid, int count);

    /// <summary>
    /// 移除试卷页面缓存
    /// </summary>
    /// <param name="paperid"></param>
    void HDeletePaperPage(string paperid);

	/// <inheritdoc />
	void HDeletePaperMarkInfo(string paperid);

	/// <summary>
	/// 当前试卷是否包含语音题
	/// </summary>
	/// <param name="paperid">试卷id</param>
	void HSetPaperIdIsMedia(string paperid);

    /// <summary>
    /// 当前试卷是否包含语音题
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task  HSetPaperIdIsMediaAsync(string paperid);
}