using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI生成题目豆包输出
    /// </summary>
    public class AIGenerateQuestionsDouBaoOutput
    {
        /// <summary>
        /// 本次请求的模型输出内容
        /// </summary>
        public List<AIGenerateQuestionsDouBaoChoicesOutput> choices { get; set; } = new List<AIGenerateQuestionsDouBaoChoicesOutput>();

        /// <summary>
        /// 本次请求创建时间的 Unix 时间戳（秒）
        /// </summary>
        public long created { get; set; }

        /// <summary>
        /// 本次请求的唯一标识
        /// </summary>
        public string? id { get; set; }

        /// <summary>
        /// 本次请求实际使用的模型名称和版本
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 本次请求的token用量
        /// </summary>
        public AIGenerateQuestionsDouBaoUsageOutput usage { get; set; } = new AIGenerateQuestionsDouBaoUsageOutput();
    }

    /// <summary>
    /// 本次请求的模型输出内容
    /// </summary>
    public class AIGenerateQuestionsDouBaoChoicesOutput
    {
        /// <summary>
        /// 模型停止生成 token 的原因
        /// </summary>
        public string? finish_reason { get; set; }

        /// <summary>
        /// 当前元素在 choices 列表的索引
        /// </summary>
        public int index { get; set; }

        /// <summary>
        /// 模型输出的内容
        /// </summary>
        public AIGenerateQuestionsDouBaoMessageOutput message { get; set; } = new AIGenerateQuestionsDouBaoMessageOutput();
    }

    /// <summary>
    /// 模型输出的内容
    /// </summary>
    public class AIGenerateQuestionsDouBaoMessageOutput
    {
        /// <summary>
        /// 模型生成的消息内容
        /// </summary>
        public string? content { get; set; }

        /// <summary>
        /// 内容输出的角色
        /// </summary>
        public string? role { get; set; }
    }

    /// <summary>
    /// 本次请求的token用量
    /// </summary>
    public class AIGenerateQuestionsDouBaoUsageOutput
    {
        /// <summary>
        /// 模型输出内容花费的 token
        /// </summary>
        public int completion_tokens { get; set; }

        /// <summary>
        /// 输入给模型处理的内容 token 数量
        /// </summary>
        public int prompt_tokens { get; set; }

        /// <summary>
        /// 本次请求消耗的总 token 数量（输入 + 输出）
        /// </summary>
        public int total_tokens { get; set; }
    }
}
