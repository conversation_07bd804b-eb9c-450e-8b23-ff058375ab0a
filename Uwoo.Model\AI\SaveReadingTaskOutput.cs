using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 保存阅读理解任务输出
    /// </summary>
    public class SaveReadingTaskOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// 阅读理解任务详情输出
    /// </summary>
    public class ReadingTaskDetailsOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 任务类型（1:视频任务，2:文档任务，3:思维导图任务，4:选词填空任务）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 角色设定
        /// </summary>
        public string? RoleSetting { get; set; }

        /// <summary>
        /// 评分标准
        /// </summary>
        public string? ScoreStandard { get; set; }

        /// <summary>
        /// 任务目标
        /// </summary>
        public string? TaskTarget { get; set; }

        /// <summary>
        /// 任务要求
        /// </summary>
        public string? TaskRequirement { get; set; }

        /// <summary>
        /// 任务范围
        /// </summary>
        public string? TaskScope { get; set; }

        /// <summary>
        /// 是否发布
        /// </summary>
        public bool IsPublished { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        #region 视频任务相关字段

        /// <summary>
        /// 总观看时长要求（分钟）
        /// </summary>
        public int? TotalWatchDurationLimit { get; set; }

        /// <summary>
        /// 是否要求观看所有视频
        /// </summary>
        public bool IsWatchAllVideos { get; set; }

        /// <summary>
        /// 视频资源列表
        /// </summary>
        public List<VideoResourceOutput> VideoResources { get; set; } = new List<VideoResourceOutput>();

        #endregion

        #region 文档任务相关字段

        /// <summary>
        /// 是否需要阅读全部文档
        /// </summary>
        public bool IsReadAllDocuments { get; set; }

        /// <summary>
        /// 文档资源列表
        /// </summary>
        public List<DocumentResourceOutput> DocumentResources { get; set; } = new List<DocumentResourceOutput>();

        #endregion

        #region 思维导图任务相关字段

        // 思维导图任务暂无特殊配置字段

        #endregion

        #region 选词填空任务相关字段

        /// <summary>
        /// 题目内容
        /// </summary>
        public string? QuestionContent { get; set; }

        /// <summary>
        /// 正确答案
        /// </summary>
        public List<string> CorrectAnswers { get; set; } = new List<string>();

        /// <summary>
        /// 干扰项
        /// </summary>
        public List<string> DistractorWords { get; set; } = new List<string>();

        /// <summary>
        /// 自定义背景图片地址
        /// </summary>
        public string? CustomBackgroundImage { get; set; }

        #endregion
    }

    /// <summary>
    /// 视频资源输出
    /// </summary>
    public class VideoResourceOutput
    {
        /// <summary>
        /// 视频Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 视频标题
        /// </summary>
        public string? VideoTitle { get; set; }

        /// <summary>
        /// 视频描述
        /// </summary>
        public string? VideoDescription { get; set; }

        /// <summary>
        /// 视频资源地址
        /// </summary>
        public string? VideoUrl { get; set; }

        /// <summary>
        /// 视频时长（秒）
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 视频排序
        /// </summary>
        public int VideoOrder { get; set; }
    }

    /// <summary>
    /// 文档资源输出
    /// </summary>
    public class DocumentResourceOutput
    {
        /// <summary>
        /// 文档Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 文档标题
        /// </summary>
        public string? DocumentTitle { get; set; }

        /// <summary>
        /// 文档描述
        /// </summary>
        public string? DocumentDescription { get; set; }

        /// <summary>
        /// 文档资源地址
        /// </summary>
        public string? DocumentUrl { get; set; }

        /// <summary>
        /// 文档类型
        /// </summary>
        public string? DocumentType { get; set; }

        /// <summary>
        /// 文档排序
        /// </summary>
        public int DocumentOrder { get; set; }
    }
}
