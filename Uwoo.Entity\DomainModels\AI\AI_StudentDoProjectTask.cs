﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// 智能体_学生做项目化实践任务信息
	/// </summary>
	[Table("AI_StudentDoProjectTask")]
    public class AI_StudentDoProjectTask
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 项目化实践ID
        /// </summary>
        public string ProjectId { get; set; }

        /// <summary>
        /// 项目化实践阶段ID
        /// </summary>
        public string ProjectStageId { get; set; }

        /// <summary>
        /// 项目化实践阶段任务ID
        /// </summary>
        public string ProjectStageTaskId { get; set; }

        /// <summary>
        /// 学生ID
        /// </summary>
        public string StudentId { get; set; }

        /// <summary>
        /// 分数
        /// </summary>
        public decimal? Score { get; set; }

        /// <summary>
        /// 等第
        /// </summary>
        public string Level { get; set; }

        /// <summary>
        /// 评估结果
        /// </summary>
        public string AssessmentResult { get; set; }

        /// <summary>
        /// 是否达到任务设定标准
        /// </summary>
        public bool? IsStandard { get; set; }

        /// <summary>
        /// 提交顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
