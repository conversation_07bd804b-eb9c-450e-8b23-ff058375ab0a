using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;

namespace UwooAgent.System.IRepositories.AI
{
    /// <summary>
    /// 知识库管理Repository接口
    /// </summary>
    public interface IKnowledgeRepository : IDependency, IRepository<AI_KnowledgeInfo>
    {

    }
}
