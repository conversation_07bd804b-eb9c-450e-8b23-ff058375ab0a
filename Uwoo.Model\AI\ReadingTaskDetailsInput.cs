using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取阅读理解任务详情输入
    /// </summary>
    public class ReadingTaskDetailsInput
    {
        /// <summary>
        /// 阅读理解项目Id
        /// </summary>
        public string? ProjectId { get; set; }
    }

    /// <summary>
    /// 获取阅读理解任务列表输入
    /// </summary>
    public class ReadingTaskListInput
    {
        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 任务类型（可选筛选）
        /// </summary>
        public int? TaskType { get; set; }

        /// <summary>
        /// 是否已发布（可选筛选）
        /// </summary>
        public bool? IsPublished { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 发布状态（0全部、1已发布、2未发布）默认0全部
        /// </summary>
        public int PublishStatus { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public string? SchoolId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string? GradeId { get; set; }
    }

    /// <summary>
    /// 删除阅读理解任务输入
    /// </summary>
    public class DeleteReadingTaskInput
    {
        /// <summary>
        /// 阅读理解项目Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }
    }

    /// <summary>
    /// 发布阅读理解任务到班级输入
    /// </summary>
    public class PublishReadingTaskToClassInput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 班级Id列表
        /// </summary>
        public List<string> ClassIds { get; set; } = new List<string>();

        /// <summary>
        /// 时间范围(下标0开始时间、下标1结束时间)
        /// </summary>
        public List<DateTime?>? TimeRange { get; set; }
    }

    /// <summary>
    /// 获取学生完成情况统计输入
    /// </summary>
    public class ReadingTaskStudentStatisticsInput
    {
        /// <summary>
        /// 阅读理解项目Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 班级Id（可选筛选）
        /// </summary>
        public string? ClassId { get; set; }
    }
}
