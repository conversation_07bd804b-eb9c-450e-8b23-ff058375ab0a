using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 学生端选词填空任务输出
    /// </summary>
    public class StudentWordFillOutput
    {
        /// <summary>
        /// 是否达标
        /// </summary>
        public bool? IsStandard { get; set; }

        /// <summary>
        /// AI评估结果
        /// </summary>
        public string? AssessmentResult { get; set; }

        /// <summary>
        /// 评估分数
        /// </summary>
        public decimal? Score { get; set; }

        /// <summary>
        /// 正确数量
        /// </summary>
        public int CorrectCount { get; set; }

        /// <summary>
        /// 总题目数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 正确率
        /// </summary>
        public decimal AccuracyRate { get; set; }

        /// <summary>
        /// 提交ID
        /// </summary>
        public string? SubmitId { get; set; }

        /// <summary>
        /// 详细答题结果
        /// </summary>
        public List<WordFillResultDetail> Details { get; set; } = new List<WordFillResultDetail>();

        /// <summary>
        /// AI评价等第
        /// </summary>
        public string? AIGrade { get; set; }

        /// <summary>
        /// AI评价内容
        /// </summary>
        public string? AIEvaluation { get; set; }

        /// <summary>
        /// 是否需要重新作答（答案不满足要求时）
        /// </summary>
        public bool NeedRetry { get; set; }

        /// <summary>
        /// 提示消息
        /// </summary>
        public string? HintMessage { get; set; }
    }

    /// <summary>
    /// 选词填空结果详情
    /// </summary>
    public class WordFillResultDetail
    {
        /// <summary>
        /// 题目序号
        /// </summary>
        public int QuestionIndex { get; set; }

        /// <summary>
        /// 学生答案
        /// </summary>
        public string? StudentAnswer { get; set; }

        /// <summary>
        /// 正确答案
        /// </summary>
        public string? CorrectAnswer { get; set; }

        /// <summary>
        /// 是否正确
        /// </summary>
        public bool IsCorrect { get; set; }

        /// <summary>
        /// 解析说明
        /// </summary>
        public string? Explanation { get; set; }
    }
}
