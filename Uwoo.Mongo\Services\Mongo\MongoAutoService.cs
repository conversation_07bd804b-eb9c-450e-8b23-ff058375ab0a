﻿// -- Function：MongoAutoService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/11/07 17:52

namespace Uwoo.Mongo.Services.Mongo;

using System.Linq.Expressions;
using MongoDB.Driver;
using Uwoo.Mongo.Interfaces;

/// <summary>
/// mongo服务
/// </summary>
/// <remarks>自动根据年份分表</remarks>
/// <typeparam name="T">mongo模型</typeparam>
public abstract partial class MongoAutoService<T> : IMongoAutoService<T> where T : MongoBaseModel, new()
{
    #region Implementation of IMongoAutoService<T>

    /// <inheritdoc />
    public virtual T Get(long id, string colname = "")
    {
        var mongo = GetConnection(colname);
        return mongo.Find(x => x.Mid.Equals(id)).Limit(1).FirstOrDefault();
    }

    /// <inheritdoc />
    public virtual T Get(Expression<Func<T, bool>> predicate = null, string colname = "")
    {
        Expression<Func<T, bool>> xpredicate = exp => true;
        predicate ??= xpredicate;
        var mongo = GetConnection(colname);
        return mongo.Find(predicate).Limit(1).FirstOrDefault();
    }

    /// <inheritdoc />
    public virtual List<T> GetAll(Expression<Func<T, bool>> predicate = null, string colname = "", FindOptions options = null,int? year= null)
    {
        Expression<Func<T, bool>> xpredicate = exp => true;
        predicate ??= xpredicate;
        var mongo = GetConnection(colname, year);
        var list = mongo.Find(predicate, options).ToList();
        return list.OrderBy(x => x.Mid).ToList();
    }

    /// <inheritdoc />
    public virtual void Add(T entity, string colname = "")
    {
        var mongo = GetConnection(colname);
        mongo.InsertOne(entity);
    }

    /// <inheritdoc />
    public virtual void AddMany(IEnumerable<T> entities, string colname = "")
    {
        var mongo = GetConnection(colname);
        mongo.InsertMany(entities);
    }

    /// <inheritdoc />
    public virtual bool Update(Expression<Func<T, bool>> predicate, T entity, string colname = "")
    {
        var mongo = GetConnection(colname);
        var result = mongo.ReplaceOne(predicate, entity);
        return result.ModifiedCount > 0;
    }

    /// <inheritdoc />
    public virtual bool Update(long id, T entity, string colname = "")
    {
        var mongo = GetConnection(colname);
        var result = mongo.ReplaceOne(x => x.Mid.Equals(id), entity);
        return result.ModifiedCount > 0;
    }

    /// <inheritdoc />
    public virtual bool Delete(long id, string colname = "")
    {
        var mongo = GetConnection(colname);
        var result = mongo.DeleteOne(x => x.Mid.Equals(id));
        return result.DeletedCount > 0;
    }

    /// <inheritdoc />
    public virtual bool Delete(Expression<Func<T, bool>> predicate, string colname = "")
    {
        var mongo = GetConnection(colname);
        var result = mongo.DeleteOne(predicate);
        return result.DeletedCount > 0;
    }

    /// <inheritdoc />
    public virtual bool DeleteMany(Expression<Func<T, bool>> predicate, string colname = "")
    {
        var mongo = GetConnection(colname);
        var result = mongo.DeleteMany(predicate);
        return result.DeletedCount > 0;
    }

	#endregion
}