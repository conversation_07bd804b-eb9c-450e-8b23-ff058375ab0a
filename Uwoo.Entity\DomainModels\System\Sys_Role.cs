﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Entity.DomainModels
{
    [Entity(TableCnName = "角色管理", TableName = "Sys_Role")]
    public partial class Sys_Role : BaseEntity
    {
        /// <summary>
        ///角色ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        [Key]
        [Display(Name = "角色ID")]
        [Column(TypeName = "int")]
        [Required(AllowEmptyStrings = false)]
        public int Role_Id { get; set; }

        /// <summary>
        ///父级ID
        /// </summary>
        [Display(Name = "父级ID")]
        [Column(TypeName = "int")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public int ParentId { get; set; }

        /// <summary>
        ///角色名称
        /// </summary>
        [Display(Name = "角色名称")]
        [MaxLength(100)]
        [Column(TypeName = "nvarchar(100)")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public string RoleName { get; set; }

        /// <summary>
        ///部门ID
        /// </summary>
        [Display(Name = "部门ID")]
        [Column(TypeName = "int")]
        public int? Dept_Id { get; set; }

        /// <summary>
        ///部门名称
        /// </summary>
        [Display(Name = "部门名称")]
        [MaxLength(100)]
        [Column(TypeName = "nvarchar(100)")]
        [Editable(true)]
        public string DeptName { get; set; }

        /// <summary>
        ///是否启用
        /// </summary>
        [Display(Name = "是否启用")]
        [Column(TypeName = "tinyint")]
        [Editable(true)]
        public byte? Enable { get; set; }

        /// <summary>
        ///排序
        /// </summary>
        [Display(Name = "排序")]
        [Column(TypeName = "int")]
        public int? OrderNo { get; set; }

        /// <summary>
        ///创建人
        /// </summary>
        [Display(Name = "创建人")]
        [MaxLength(100)]
        [Column(TypeName = "nvarchar(100)")]
        [Editable(true)]
        public string Creator { get; set; }

        /// <summary>
        ///创建时间
        /// </summary>
        [Display(Name = "创建时间")]
        [Column(TypeName = "datetime")]
        [Editable(true)]
        public DateTime? CreateDate { get; set; }

        /// <summary>
        ///修改人
        /// </summary>
        [Display(Name = "修改人")]
        [MaxLength(100)]
        [Column(TypeName = "nvarchar(100)")]
        [Editable(true)]
        public string Modifier { get; set; }

        /// <summary>
        ///修改时间
        /// </summary>
        [Display(Name = "修改时间")]
        [Column(TypeName = "datetime")]
        [Editable(true)]
        public DateTime? ModifyDate { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "DeleteBy")]
        [MaxLength(100)]
        [SugarColumn(NoSerialize = true)]
        [Column(TypeName = "nvarchar(100)")]
        public string DeleteBy { get; set; }

        /// <summary>
        ///角色对应的管理等级
        /// </summary>
        [Display(Name = "Level")]
        [MaxLength(100)]
        [SugarColumn(NoSerialize = true)]
        [Column(TypeName = "int")]
        public int Level { get; set; }

        /// <summary>
        ///角色对应的管理等级
        /// </summary>
        [Display(Name = "Level")]
        [SugarColumn(NoSerialize = true)]
        [Column(TypeName = "bit")]
        public bool Deleted { get; set; }

        /// <summary>
        ///
        /// </summary>
        [Display(Name = "Notes")]
        [MaxLength(500)]
        [Column(TypeName = "nvarchar(500)")]
        public string Notes { get; set; }


    }
}
