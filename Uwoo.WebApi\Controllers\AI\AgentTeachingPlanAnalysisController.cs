﻿using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Uwoo.Core.Attributes;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Core.Utilities;
using Uwoo.Model;
using UwooAgent.Model.AI.AgentTeachingPlanAnalysis;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 教案相关接口
    /// </summary>
    [ApiController]
    [Route("/AgentTeachingPlanAnalysis/[controller]/[action]")]
    public class AgentTeachingPlanAnalysisController : ApiBaseController<IAgentTeachingPlanAnalysisService>
    {
        public AgentTeachingPlanAnalysisController(IAgentTeachingPlanAnalysisService service) : base(service)
        {

        }
        /// <summary>
        /// 获取教案库列表
        /// </summary>
        /// <returns>教案库列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetTeachingPlanLibrary(string teacherId,string SubjectId,string GradeId)
        {
            if (string.IsNullOrEmpty(teacherId))
                return BadRequest("教师ID不能为空");
            var result = await Service.GetTeachingPlanLibrary(teacherId, SubjectId, GradeId);
            return Ok(new AjaxResult<List<TeachingPlanDTO>>() { Data = result, Total = result.Count });
        }

        /// <summary>
        /// 选择教案 (废弃)
        /// </summary>
        /// <param name="model">教案选择模型</param>
        /// <returns>选择结果</returns>
        [HttpPost]
        [Obsolete]
        public async Task<IActionResult> SelectTeachingPlan(TeachingPlanSelectionModel model)
        {
            if (model.UploadedFile != null)
            {
                List<IFormFile> files = new List<IFormFile>() { model.UploadedFile };
                UploadHuaWeiObs uploadHuaWeiObs = new UploadHuaWeiObs();
                var result = await uploadHuaWeiObs.ExecuteAsync(model.UploadedFile, "TeachingPlan");
                if (result == null)
                    return BadRequest("上传失败");
            }

            // 处理教案选择逻辑
            return Ok(new {
                success = true,
                message = "教案选择成功",
                selectedPlanId = model.SelectedPlanId,
                isUploaded = model.UploadedFile != null
            });
        }

        /// <summary>
        /// 获取专课专练作业列表
        /// </summary>
        /// <returns>作业列表</returns>
        [HttpPost]
        public async Task<IActionResult> GetStudentPaperList(TeacherClassPaperInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId))
                return BadRequest("教师ID不能为空");
            var result = await Service.GetStudentPaperList(input.TeacherId, input.SubjectId, input.ClassId);
            return Ok(new AjaxResult<List<PaperDTO>>() { Data = result, Total = result.Count });
        }
        /// <summary>
        /// 学生成绩分布情况
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [Obsolete]
        public IActionResult StudentsGradeDistribution(StudentGradeDistributionInput input)
        {
            var result = Service.StudentsGradeDistribution(input);
            return Ok(new AjaxResult<List<StudentsGradeDistributionModel>>() { Data = result, Total = result.Count });
        }
        /// <summary>
        /// 生成教案分析
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GenerateTeachingPlanAnalysis(GenerateTeachingPlanAnalysisInput input)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new AjaxResult<string>() { Data = "参数错误" ,Success=false});
                }
                var result = await Service.GenerateTeachingPlanAnalysis(input);
                if(result==null||!result.Contains("http"))
                    return BadRequest(new AjaxResult<string>() { Data=result,Success=false});
                var serviceProvider = new ServiceCollection().AddHttpClient().BuildServiceProvider();
                IHttpClientFactory _httpClientFactory = serviceProvider.GetService<IHttpClientFactory>();
                var _httpClient = _httpClientFactory.CreateClient("CTCCMonitor");
                HttpResponseMessage response = await _httpClient.GetAsync(result);
                response.EnsureSuccessStatusCode();
                string htmlContent = await response.Content.ReadAsStringAsync();
                //return Content(htmlContent, "text/html; charset=utf-8");
                return Ok(new AjaxResult<string>() { Data=htmlContent, Success = true });
            }
            catch (Exception ex)
            {
                return BadRequest(new AjaxResult<string>() { Data = ex.Message, Success = false });
            }
        }
        [HttpPost]
        public async Task<string> GetTeachingPlanAnalysis(string wordFileUrl)
        {
            return await Service.GetXmlDocumentContent(wordFileUrl);
        }
    }

}