﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 豆包我的应用非流式输出
    /// </summary>
    public class DouBaoMyAppNoStreamOutput
    {
        /// <summary>
        /// 
        /// </summary>
        public string? id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<DouBaoMyAppNoStreamChoices> choices { get; set; } = new List<DouBaoMyAppNoStreamChoices>();

        /// <summary>
        ///     
        /// </summary>
        public DouBaoMyAppNoStreamBotUsage bot_usage { get; set; } = new DouBaoMyAppNoStreamBotUsage();
    }

    /// <summary>
    /// 
    /// </summary>
    public class DouBaoMyAppNoStreamChoices
    {
        /// <summary>
        /// 
        /// </summary>
        public string? finish_reason { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int index { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DouBaoMyAppNoStreamMessage message { get; set; } = new DouBaoMyAppNoStreamMessage();
    }

    /// <summary>
    /// 
    /// </summary>
    public class DouBaoMyAppNoStreamMessage
    {
        /// <summary>
        /// 
        /// </summary>
        public string? content { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? role { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class DouBaoMyAppNoStreamBotUsage
    {
        /// <summary>
        /// 
        /// </summary>
        public List<DouBaoMyAppNoStreamModelUsage> model_usage { get; set; } = new List<DouBaoMyAppNoStreamModelUsage>();
    }

    /// <summary>
    /// 
    /// </summary>
    public class DouBaoMyAppNoStreamModelUsage
    {
        /// <summary>
        /// 输入的 prompt token 数量
        /// </summary>
        public int prompt_tokens { get; set; }

        /// <summary>
        /// 模型生成的 token 数量
        /// </summary>
        public int completion_tokens { get; set; }
    }
}
