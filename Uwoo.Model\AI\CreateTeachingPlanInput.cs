﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 创建教案入参
    /// </summary>
    public class CreateTeachingPlanInput
    {
        /// <summary>
        /// 创建类型（1：标题创建、2：文本创建、3：章节创建、4：文档创建）
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 年级
        /// </summary>
        public int Grade { get; set; }

        /// <summary>
        /// 学科ID
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 模型ID
        /// </summary>
        public string? ModelId { get; set; }

        /// <summary>
        /// 内容要求Id
        /// </summary>
        public List<string> ContentDemandId { get; set; } = new List<string>();

        /// <summary>
        /// 教案标题
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 其他要求
        /// </summary>
        public string? Demand { get; set; }

        /// <summary>
        /// 教案内容
        /// </summary>
        public string? Content { get; set; }

        /// <summary>
        /// 章节ID
        /// </summary>
        public string? ChapterId { get; set; }

        /// <summary>
        /// 文件地址
        /// </summary>
        public string? FileUrl { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public string? SchoolId { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }
    }
}
