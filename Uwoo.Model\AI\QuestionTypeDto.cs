using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 题型数据传输对象
    /// </summary>
    public class QuestionTypeDto
    {
        /// <summary>
        /// 题型ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 题型名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 题型描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 出题方式枚举
    /// </summary>
    public enum QuestionGenerationMode
    {
        /// <summary>
        /// 知识点出题
        /// </summary>
        KnowledgePoint = 1,

        /// <summary>
        /// 文本出题
        /// </summary>
        Text = 2,

        /// <summary>
        /// 附件出题
        /// </summary>
        Attachment = 3,

        /// <summary>
        /// 章节出题
        /// </summary>
        Chapter = 4
    }

    /// <summary>
    /// 智能出题请求参数
    /// </summary>
    public class IntelligentQuestionGenerationInput
    {
        /// <summary>
        /// 出题方式
        /// </summary>
        public QuestionGenerationMode Mode { get; set; }

        /// <summary>
        /// Ai模型Id
        /// </summary>
        public string? AIModeId { get; set; }

        /// <summary>
        /// 年级
        /// </summary>
        public int Grade { get; set; } = 1;

        /// <summary>
        /// 题型ID列表（可多选）
        /// </summary>
        public List<int> QuestionTypeIds { get; set; } = new List<int>();

        /// <summary>
        /// 难度等级名称（前端不要传，只传DifficultyId）
        /// </summary>
        public string DifficultyLevelName { get; set; } = string.Empty;

        /// <summary>
        /// 出题方向名称（也就是学习水平）（前端不要传，只传LearningLevelId）
        /// </summary>
        public string QuestionDirectionName { get; set; } = string.Empty;

        /// <summary>
        /// 难度等级id
        /// </summary>
        public string ? DifficultyId { get; set; }

        /// <summary>
        /// 学习水平Id
        /// </summary>
        public string ? LearningLevelId { get; set; }

        /// <summary>
        /// 出题数量
        /// </summary>
        public int QuestionCount { get; set; }

        /// <summary>
        /// 补充内容（出题要求）
        /// </summary>
        public string? AdditionalRequirements { get; set; }

        /// <summary>
        /// 知识点ID列表（知识点出题时使用）
        /// </summary>
        public List<string>? KnowledgePointIds { get; set; }

        /// <summary>
        /// 出题范围文本（文本出题时使用）
        /// </summary>
        public string? TextContent { get; set; }

        /// <summary>
        /// 文件出题时使用的文件URL（附件出题时使用，兼容旧版本）
        /// </summary>
        public string? FileUrl { get;set; }

        /// <summary>
        /// 文件出题时使用的文件URL列表（附件出题时使用，支持多个文件，最多3个）
        /// </summary>
        public List<string>? FileUrls { get; set; }

        /// <summary>
        /// 传 单元-章节名称（章节出题时使用，兼容旧版本）
        /// </summary>
        public string? ChapterName { get; set; } = string.Empty;

        /// <summary>
        /// 章节ID列表（章节出题时使用，支持多个章节）
        /// </summary>
        public List<string>? ChapterIds { get; set; }

        /// <summary>
        /// 用户ID，不需要前端传参
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 科目Id，不需要前端传参
        /// </summary>
        public string SubjectId { get; set; } = string.Empty;   
    }

    /// <summary>
    /// 题目生成结果
    /// </summary>
    public class QuestionGenerationResult
    {
        /// <summary>
        /// 题目ID
        /// </summary>
        public string QuestionId { get; set; } = string.Empty;

        /// <summary>
        /// 题目序号
        /// </summary>
        public int QuestionNumber { get; set; }

        /// <summary>
        /// 题型ID
        /// </summary>
        public int QuestionTypeId { get; set; }

        /// <summary>
        /// 题型名称
        /// </summary>
        public string QuestionTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 题目内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 选项（选择题使用）
        /// </summary>
        public List<QuestionOption>? Options { get; set; }

        /// <summary>
        /// 正确答案
        /// </summary>
        public string CorrectAnswer { get; set; } = string.Empty;

        /// <summary>
        /// 答案解析
        /// </summary>
        public string? AnswerExplanation { get; set; }

        /// <summary>
        /// 难度等级
        /// </summary>
        public string DifficultyLevel { get; set; } = string.Empty;

        /// <summary>
        /// 出题方向
        /// </summary>
        public string QuestionDirection { get; set; } = string.Empty;
    }

    /// <summary>
    /// 题目选项
    /// </summary>
    public class QuestionOption
    {
        /// <summary>
        /// 选项标识（A、B、C、D）
        /// </summary>
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// 选项内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 是否为正确答案
        /// </summary>
        public bool IsCorrect { get; set; }
    }

    /// <summary>
    /// 出题统计结果
    /// </summary>
    public class QuestionGenerationStatistics
    {
        /// <summary>
        /// 总题目数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 各题型统计
        /// </summary>
        public List<QuestionTypeStatistic> TypeStatistics { get; set; } = new List<QuestionTypeStatistic>();

        /// <summary>
        /// 统计文本描述
        /// </summary>
        public string StatisticsText { get; set; } = string.Empty;

        /// <summary>
        /// 生成状态
        /// </summary>
        public QuestionGenerationStatus Status { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 题型统计
    /// </summary>
    public class QuestionTypeStatistic
    {
        /// <summary>
        /// 题型ID
        /// </summary>
        public int TypeId { get; set; }

        /// <summary>
        /// 题型名称
        /// </summary>
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 数量
        /// </summary>
        public int Count { get; set; }
    }

    /// <summary>
    /// 题目生成状态
    /// </summary>
    public enum QuestionGenerationStatus
    {
        /// <summary>
        /// 开始生成
        /// </summary>
        Started = 1,

        /// <summary>
        /// 生成中
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// 生成完成
        /// </summary>
        Completed = 3,

        /// <summary>
        /// 生成失败
        /// </summary>
        Failed = 4
    }

    /// <summary>
    /// 知识点DTO
    /// </summary>
    public class KnowledgePointDto
    {
        /// <summary>
        /// 知识点ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 知识点内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 父级ID
        /// </summary>
        public string? ParentId { get; set; }

        /// <summary>
        /// 层级（1-3）
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 排序值
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        /// 子知识点列表
        /// </summary>
        public List<KnowledgePointDto> Children { get; set; } = new List<KnowledgePointDto>();

        /// <summary>
        /// 是否展开（前端使用）
        /// </summary>
        public bool Expanded { get; set; } = false;
    }
}
