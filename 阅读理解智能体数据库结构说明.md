# 阅读理解智能体数据库表关联关系结构说明

## 📊 **总体架构概述**

阅读理解智能体采用**项目化实践**的五层数据结构设计，支持完整的教学业务流程：

```
🏗️ 项目层 → 📋 阶段层 → ⚙️ 任务层 → 📁 资源层 → 📊 记录层
```

## 🗄️ **核心表结构（11张必要表）**

### **🏗️ 项目层（2张表）**

#### **1. RC_ReadingProject - 项目主表**
- **作用**：存储项目化实践的基本信息
- **关键字段**：
  - `Id` (PK) - 项目主键
  - `AgentId` (FK) - 关联智能体
  - `TeacherId` - 创建教师
  - `Name` - 项目名称
  - `IsPublished` - 发布状态
- **业务逻辑**：一个项目包含多个阶段，通过AI_AgentTaskPublish发布到班级

#### **2. AI_AgentTaskPublish - 发布机制表**
- **作用**：统一管理所有智能体任务的发布
- **关键字段**：
  - `AgentTaskId` (FK) - 关联项目ID
  - `PublishBusinessId` - 班级ID
  - `PublishType` - 发布类型
- **业务逻辑**：项目通过此表发布到指定班级，学生通过班级权限查看

### **📋 阶段层（1张表）**

#### **3. RC_ReadingProjectStage - 项目阶段表**
- **作用**：项目的阶段划分
- **关键字段**：
  - `Id` (PK) - 阶段主键
  - `ReadingProjectId` (FK) - 关联项目
  - `StageName` - 阶段名称
  - `StageOrder` - 阶段排序
- **业务逻辑**：一个项目包含多个有序阶段，每个阶段包含多个任务

### **⚙️ 任务层（3张表）**

#### **4. RC_ReadingProjectStageTask - 阶段任务表**
- **作用**：具体的学习任务定义
- **关键字段**：
  - `Id` (PK) - 任务主键
  - `ReadingProjectStageId` (FK) - 关联阶段
  - `TaskType` - 任务类型（1-7）
  - `TaskName` - 任务名称
  - **配置整合字段**：
    - `TotalWatchDurationLimit` - 视频观看时长要求
    - `IsWatchAllVideos` - 是否观看所有视频
    - `IsReadAllDocuments` - 是否阅读所有文档
    - `QuestionContent` - 选词填空题目
    - `CorrectAnswers` - 正确答案JSON
- **业务逻辑**：支持7种任务类型，配置信息直接存储在任务表中

#### **5. RC_ReadingProjectStageTaskQuestion - 高频问题表**
- **作用**：存储任务相关的常见问题
- **关键字段**：
  - `ReadingProjectStageTaskId` (FK) - 关联任务
  - `QuestionContent` - 问题内容
  - `QuestionOrder` - 问题排序

#### **6. RC_MindMapTaskConfig - 思维导图配置表**
- **作用**：思维导图任务的特殊配置
- **关键字段**：
  - `ReadingProjectStageTaskId` (FK) - 关联任务
  - `MindMapTemplate` - 思维导图模板JSON
  - `RequiredNodes` - 要求节点数量

### **📁 资源层（2张表）**

#### **7. RC_VideoResource - 视频资源表**
- **作用**：存储视频任务的资源文件
- **关键字段**：
  - `ReadingProjectStageTaskId` (FK) - 关联任务
  - `VideoTitle` - 视频标题
  - `VideoUrl` - 视频地址
  - `VideoOrder` - 视频排序

#### **8. RC_DocumentResource - 文档资源表**
- **作用**：存储文档任务的资源文件
- **关键字段**：
  - `ReadingProjectStageTaskId` (FK) - 关联任务
  - `DocumentTitle` - 文档标题
  - `DocumentUrl` - 文档地址
  - `DocumentOrder` - 文档排序

### **📊 记录层（4张表）**

#### **9. RC_StudentTaskRecord - 学生任务记录表**
- **作用**：记录学生任务完成情况
- **关键字段**：
  - `StudentId` - 学生ID
  - `ReadingProjectId` (FK) - 关联项目
  - `ReadingProjectStageTaskId` (FK) - 关联任务
  - `TaskStatus` - 任务状态（1:未开始，2:进行中，3:已完成）
  - `ProjectStatus` - 项目状态
  - `Score` - 评估分数
  - `IsStandard` - 是否达标
  - `AssessmentResult` - AI评估结果

#### **10. RC_StudentVideoWatchRecord - 视频观看记录表**
- **作用**：记录学生视频观看详情
- **关键字段**：
  - `StudentId` - 学生ID
  - `ReadingProjectStageTaskId` (FK) - 关联任务
  - `VideoId` (FK) - 关联视频
  - `TotalWatchDuration` - 累计观看时长
  - `HasWatched` - 是否已观看

#### **11. RC_StudentDocumentReadRecord - 文档阅读记录表**
- **作用**：记录学生文档阅读情况
- **关键字段**：
  - `StudentId` - 学生ID
  - `ReadingProjectStageTaskId` (FK) - 关联任务
  - `DocumentId` (FK) - 关联文档
  - `HasRead` - 是否已阅读

#### **12. RC_StudentWordFillOperationRecord - 选词填空操作记录表**
- **作用**：记录学生选词填空的详细操作
- **关键字段**：
  - `StudentId` - 学生ID
  - `ReadingProjectStageTaskId` (FK) - 关联任务
  - `OperationOrder` - 操作序号
  - `QuestionIndex` - 题目索引
  - `SelectedWord` - 选择的词汇
  - `IsCorrect` - 是否正确

## 🔗 **关联关系说明**

### **核心三层结构**
```
RC_ReadingProject (1) ──→ (N) RC_ReadingProjectStage
RC_ReadingProjectStage (1) ──→ (N) RC_ReadingProjectStageTask
```

### **资源关联**
```
RC_ReadingProjectStageTask (1) ──→ (N) RC_VideoResource
RC_ReadingProjectStageTask (1) ──→ (N) RC_DocumentResource
RC_ReadingProjectStageTask (1) ──→ (1) RC_MindMapTaskConfig
```

### **学生记录关联**
```
RC_ReadingProject (1) ──→ (N) RC_StudentTaskRecord
RC_ReadingProjectStageTask (1) ──→ (N) RC_StudentTaskRecord
RC_ReadingProjectStageTask (1) ──→ (N) RC_StudentVideoWatchRecord
RC_VideoResource (1) ──→ (N) RC_StudentVideoWatchRecord
```

### **发布机制关联**
```
RC_ReadingProject (1) ──→ (N) AI_AgentTaskPublish
```

## 🎯 **任务类型说明**

| 类型 | 名称 | 说明 | 相关表 |
|------|------|------|--------|
| 1 | 成果评估 | 作品评估任务 | RC_StudentTaskRecord |
| 2 | 情景对话 | AI对话任务 | RC_StudentTaskRecord |
| 3 | 知识问答 | 智能问答任务 | RC_StudentTaskRecord |
| 4 | 视频任务 | 观看视频任务 | RC_VideoResource, RC_StudentVideoWatchRecord |
| 5 | 文档任务 | 阅读文档任务 | RC_DocumentResource, RC_StudentDocumentReadRecord |
| 6 | 思维导图 | 绘制思维导图 | RC_MindMapTaskConfig, RC_StudentTaskRecord |
| 7 | 选词填空 | 词汇练习任务 | RC_StudentWordFillOperationRecord |

## 🔐 **权限控制机制**

### **发布权限**
1. **项目级发布**：教师通过 `AI_AgentTaskPublish` 将项目发布到指定班级
2. **班级权限**：`PublishBusinessId` 存储班级ID，控制学生可见性
3. **任务继承**：任务权限从项目继承，无需单独发布

### **学生访问流程**
```sql
-- 学生查看可见项目
SELECT p.* 
FROM RC_ReadingProject p
INNER JOIN AI_AgentTaskPublish pub ON p.Id = pub.AgentTaskId
WHERE pub.PublishBusinessId = @StudentClassId
  AND p.IsPublished = 1;
```

## 📈 **数据流向**

### **教师端流程**
```
创建项目 → 创建阶段 → 创建任务 → 上传资源 → 配置任务 → 发布项目
```

### **学生端流程**
```
查看项目列表 → 选择项目 → 查看阶段任务 → 执行任务 → 提交记录
```

## 🚀 **优化特点**

1. **配置整合**：视频、文档、选词填空配置直接存储在任务表中
2. **发布统一**：使用系统统一的发布机制表
3. **权限简化**：项目级权限控制，任务权限继承
4. **性能优化**：合理的索引设计，减少表关联
5. **扩展性强**：支持新增任务类型和配置字段
