﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Entity.SystemModels.Enum;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 智能体_教师首页列表输出
    /// </summary>
    public class AgentTeacherHomePageListOutPut
    {
        /// <summary>
        /// 智能体_教师首页父级类型
        /// </summary>
        public List<AgentTeacherHomePageParentType> ParentTypes { get; set; } = new List<AgentTeacherHomePageParentType>();
    }

    /// <summary>
    /// 智能体_教师首页父级类型
    /// </summary>
    public class AgentTeacherHomePageParentType
    {
        /// <summary>
        /// 类型Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 类型名称
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 智能体_教师首页子级类型
        /// </summary>
        public List<AgentTeacherHomePageChildrenType> ChildrenTypes { get; set; } = new List<AgentTeacherHomePageChildrenType>();
    }

    /// <summary>
    /// 智能体_教师首页子级类型
    /// </summary>
    public class AgentTeacherHomePageChildrenType
    {
        /// <summary>
        /// 类型Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 类型名称
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 智能体信息
        /// </summary>
        public List<AgentTeacherHomePageAgentInfo> AgentInfo { get; set; } = new List<AgentTeacherHomePageAgentInfo>();
    }

    /// <summary>
    /// 智能体_教师首页智能体信息
    /// </summary>
    public class AgentTeacherHomePageAgentInfo
    {
        /// <summary>
		/// 智能体Id
		/// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 智能体名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 智能体应用编码
        /// </summary>
        public string? AgentBotCode { get; set; }

        /// <summary>
        /// 智能体类型
        /// </summary>
        public string? AgentTypeId { get; set; }

        /// <summary>
        /// 智能体概述
        /// </summary>
        public string? Summarize { get; set; }

        /// <summary>
        /// 图标Logo
        /// </summary>
        public string? Logo { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 是否收藏
        /// </summary>
        public bool? IsCollection { get; set; }
    }
}
