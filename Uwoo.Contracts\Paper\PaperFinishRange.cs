﻿// -- Function：PaperFinishRange.cs
// --- Project：X.PenServer.Models
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/11/01 17:57

namespace Uwoo.Contracts.Paper
{
	/// <inheritdoc />
	public class PaperFinishRange
    {
        /// <summary>
        /// ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 试卷id
        /// </summary>
        public string PaperId { get; set; }

        /// <summary>
        /// 选项对应的坐标位置
        /// </summary>
        public string Range { get; set; }

        /// <summary>
        /// 1 框选 答案  2 画圈题
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageId { get; set; }
    }
}