﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 智能体授权信息
    /// </summary>
    [SugarTable("AI_AgentAuthorization")]
    public class AIAgentAuthorization
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true, Length = 50)]
        public string Id { get; set; }

        /// <summary>
        /// 智能体ID
        /// </summary>
        [SugarColumn(ColumnName = "AgentId", Length = 50, IsNullable = false)]
        public string AgentId { get; set; }

        /// <summary>
        /// 授权类型 (1=市, 2=区, 3=校)
        /// </summary>
        [SugarColumn(ColumnName = "AuthorizationType", IsNullable = false)]
        public int AuthorizationType { get; set; }

        /// <summary>
        /// 市ID
        /// </summary>
        [SugarColumn(ColumnName = "CityId", Length = 50, IsNullable = true)]
        public string CityId { get; set; }

        /// <summary>
        /// 区ID
        /// </summary>
        [SugarColumn(ColumnName = "DistrictId", Length = 50, IsNullable = true)]
        public string DistrictId { get; set; }

        /// <summary>
        /// 校ID
        /// </summary>
        [SugarColumn(ColumnName = "SchoolId", Length = 50, IsNullable = true)]
        public string SchoolId { get; set; }

        /// <summary>
        /// 授权开始时间
        /// </summary>
        [SugarColumn(ColumnName = "StartTime", IsNullable = false)]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 授权结束时间
        /// </summary>
        [SugarColumn(ColumnName = "EndTime", IsNullable = false)]
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 状态 (0=禁用, 1=启用)
        /// </summary>
        [SugarColumn(ColumnName = "Status", IsNullable = false, DefaultValue = "1")]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "CreateTime", IsNullable = false)]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(ColumnName = "Creator", Length = 50, IsNullable = false)]
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [SugarColumn(ColumnName = "ModifyTime", IsNullable = true)]
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        [SugarColumn(ColumnName = "Modifier", Length = 50, IsNullable = true)]
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        [SugarColumn(ColumnName = "IsDeleted", IsNullable = false, DefaultValue = "0")]
        public bool IsDeleted { get; set; } = false;
    }
}
