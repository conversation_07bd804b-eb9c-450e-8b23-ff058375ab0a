﻿// -- Function：IMongoAsyncService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/27 10:13

using System.Linq.Expressions;
using MongoDB.Driver;
using Uwoo.Mongo.Models;

namespace Uwoo.Mongo.Interfaces;

/// <summary>
/// mongodb服务
/// </summary>
/// <typeparam name="T">实体</typeparam>
public partial interface IMongoService<T> where T : MongoBaseModel, new()
{
    /// <summary>
    /// 通过主键获取数据
    /// </summary>
    /// <param name="id">主键id</param>
    /// <returns></returns>
    Task<T> GetAsync(long id);

    /// <summary>
    /// 通过条件查询单条数据
    /// </summary>
    /// <param name="predicate">条件</param>
    /// <returns></returns>
    Task<T> GetAsync(Expression<Func<T, bool>> predicate = null);

    /// <summary>
    /// 通过条件查询数据列表
    /// </summary>
    /// <param name="predicate">条件</param>
    /// <param name="options">查询选项</param>
    /// <returns></returns>
    Task<List<T>> GetAllAsync(Expression<Func<T, bool>> predicate = null, FindOptions options = null);

    /// <summary>
    /// 添加单条数据
    /// </summary>
    /// <param name="entity">数据实体</param>
    /// <returns></returns>
    Task AddAsync(T entity);

    /// <summary>
    /// 批量添加数据
    /// </summary>
    /// <param name="entities">数据实体列表</param>
    /// <returns></returns>
    Task AddManyAsync(IEnumerable<T> entities);

    /// <summary>
    /// 根据条件更新单条数据
    /// </summary>
    /// <param name="predicate">条件</param>
    /// <param name="entity">数据实体</param>
    /// <returns></returns>
    Task<bool> UpdateAsync(Expression<Func<T, bool>> predicate, T entity);

    /// <summary>
    /// 根据主键更新单条数据
    /// </summary>
    /// <param name="id">主键id</param>
    /// <param name="entity">数据实体</param>
    /// <returns></returns>
    Task<bool> UpdateAsync(long id, T entity);

    /// <summary>
    /// 根据id删除单条数据
    /// </summary>
    /// <param name="id">主键id</param>
    /// <returns></returns>
    Task<bool> DeleteAsync(long id);

    /// <summary>
    /// 根据条件删除单条数据
    /// </summary>
    /// <param name="predicate">条件</param>
    /// <returns></returns>
    Task<bool> DeleteAsync(Expression<Func<T, bool>> predicate);

    /// <summary>
    /// 根据条件批量删除数据
    /// </summary>
    /// <param name="predicate">条件</param>
    /// <returns></returns>
    Task<bool> DeleteManyAsync(Expression<Func<T, bool>> predicate);
}