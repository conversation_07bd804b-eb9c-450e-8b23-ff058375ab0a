﻿using Microsoft.EntityFrameworkCore.Diagnostics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;
using Uwoo.Entity.SystemModels;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    ///  知识库基础信息
    /// AI_KnowledgeInfo
    ///    Id
    ///  Name 知识库名称，用户端显示名称
    ///  Description 知识库描述信息
    ///  DoubaoName 豆包知识库名称，系统自动创建
    ///  DataType  知识库内的数据类型
    ///  Creator 创建人
    ///  CreateTime 创建时间
    /// </summary>
    [SugarTable]
    public class AI_KnowledgeInfo : BaseEntity
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 知识库名称（用户端显示名称）
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 豆包知识库名称（系统自动创建）
        /// </summary>
        public string DoubaoName { get; set; }

        /// <summary>
        /// 知识库描述信息
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; } = false;
    }
}
