﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// 智能体_项目化实践阶段
	/// </summary>
	[Table("AI_ProjectStage")]
    public class AI_ProjectStage
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 项目化实践Id
        /// </summary>
        public string ProjectId { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string Describe { get; set; }

        /// <summary>
        /// 排序序号
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }
    }
}
