﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Uwoo.Model.JWT
{
	/// <summary>
	/// JWT
	/// </summary>
	public class JWTPayload
	{
		/// <summary>
		/// 用户id
		/// </summary>
		public string? UserId { get; set; }

		/// <summary>
		/// 用户账号
		/// </summary>
		public string? UserName { get; set; }

		/// <summary>
		/// 用户姓名
		/// </summary>
		public string? RealName { get; set; }

		/// <summary>
		/// 学校id
		/// </summary>
		public string? SchoolId { get; set; }

		/// <summary>
		/// 平台登录人口（1专课专练、2三个助手、3徐汇基座）
		/// </summary>
		public int Platform { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public DateTime Expire { get; set; }
	}
}
