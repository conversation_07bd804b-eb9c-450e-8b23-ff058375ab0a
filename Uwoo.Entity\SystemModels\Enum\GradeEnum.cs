﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.DirectoryServices.ActiveDirectory;

namespace Uwoo.Entity.SystemModels.Enum
{
    /// <summary>
    /// 年级
    /// </summary>
    public enum GradeEnum
    {
        /// <summary>
        /// 一年级
        /// </summary>
        [Description("一年级")]
        FirstGrade = 1,

        /// <summary>
        /// 二年级
        /// </summary>
        [Description("二年级")]
        SecondGrade = 2,

        /// <summary>
        /// 三年级
        /// </summary>
        [Description("三年级")]
        ThirdGrade = 3,

        /// <summary>
        /// 四年级
        /// </summary>
        [Description("四年级")]
        FourthGrade = 4,

        /// <summary>
        /// 五年级
        /// </summary>
        [Description("五年级")]
        FifthGrade = 5,

        /// <summary>
        /// 六年级
        /// </summary>
        [Description("六年级")]
        SixthGrade = 6,

        /// <summary>
        /// 七年级
        /// </summary>
        [Description("七年级")]
        SeventhGrade = 7,

        /// <summary>
        /// 八年级
        /// </summary>
        [Description("八年级")]
        EighthGrade = 8,

        /// <summary>
        /// 九年级
        /// </summary>
        [Description("九年级")]
        NinthGrade = 9,

        /// <summary>
        /// 高一
        /// </summary>
        [Description("高一")]
        TenGrade = 10,

        /// <summary>
        /// 高二
        /// </summary>
        [Description("高二")]
        ElevenGrade = 11,

        /// <summary>
        /// 高三
        /// </summary>
        [Description("高三")]
        TwelveGrade = 12,
    }
}
