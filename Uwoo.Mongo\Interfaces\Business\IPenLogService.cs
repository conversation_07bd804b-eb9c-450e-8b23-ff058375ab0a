﻿// -- Function：IPenLogService.cs
// --- Project：X.PenServer.Interfaces
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/24 17:59

using System.Threading.Tasks;
using Uwoo.Mongo.Models;
using Uwoo.Mongo.ViewModels;

namespace Uwoo.Mongo.Interfaces.Business;

/// <summary>
/// 点阵笔点位数据服务
/// </summary>
public interface IPenLogService : IMongoAutoService<PenLog>
{
	/// <summary>
	/// 获取学生笔迹列表
	/// </summary>
	/// <param name="colname">集合名称</param>
	/// <param name="page">页码集合</param>
	/// <param name="userid">用户id</param>
	/// <param name="year"></param>
	/// <returns></returns>
	List<PenLog> GetAll(string colname, List<int> page, string userid, int? year = null);

	/// <summary>
	/// 获取笔迹列表
	/// </summary>
	/// <param name="colname">集合名称</param>
	/// <param name="page">页码</param>
	/// <param name="userid">用户id</param>
	/// <param name="year">学年</param>
	/// <returns></returns>
	List<PenLog> GetAll(string colname, string userid, int page, int? year = null);

    /// <summary>
    /// 获取时间
    /// </summary>
    /// <param name="colname">集合名称</param>
    /// <param name="page">页码集合</param>
    /// <param name="userid">用户id</param>
    /// <returns></returns>
    Tuple<DateTime,DateTime> GetAggregateTime(string colname, List<int> page, string userid);

	/// <summary>
	/// 获取已作答用户列表
	/// </summary>
	/// <param name="colname">集合名称</param>
	/// <param name="page">页码集合</param>
	/// <param name="userid">用户id集合</param>
	/// <param name="year">学年</param>
	/// <returns></returns>
	List<string> GetUserList(string colname, List<int> page, List<string> userid, int? year = null);

    /// <summary>
    /// 获取已作答页面列表
    /// </summary>
    /// <param name="colname">集合名称</param>
    /// <param name="page">页码集合</param>
    /// <param name="userid">用户id集合</param>
    /// <returns></returns>
    List<PenLogUserInfo> GetUserPageList(string colname, List<int> page, List<string> userid);

    /// <summary>
    /// 删除学生作答笔迹
    /// </summary>
    /// <param name="colname">集合名称</param>
    /// <param name="page">页码集合</param>
    /// <param name="userid">用户id集合</param>
    void DeletePenLog(string colname, List<int> page, List<string> userid);

	/// <inheritdoc />
	Task CorrectMDLog(string colname,int page,string userId, int moveX,int moveY);
}