﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 学生观看建模视频入参
    /// </summary>
    public class StudentWatchModelingVideoInput
    {
        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 视频Id
        /// </summary>
        public string? VideoId { get; set; }

        /// <summary>
        /// 时长（秒）
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 建模Id
        /// </summary>
        public string? ModelingId { get; set; }

        /// <summary>
        /// 建模阶段Id
        /// </summary>
        public string? ModelingStageId { get; set; }

        /// <summary>
        /// 建模阶段任务Id
        /// </summary>
        public string? ModelingStageTaskId { get; set; }
    }
}
