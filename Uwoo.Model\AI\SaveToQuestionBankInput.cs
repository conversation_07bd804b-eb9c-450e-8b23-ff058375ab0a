using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 单题保存到题库请求参数
    /// </summary>
    public class SaveSingleQuestionToBankInput
    {
        /// <summary>
        /// 题目信息
        /// </summary>
        public AIGeneratedQuestion Question { get; set; } = new AIGeneratedQuestion();

        /// <summary>
        /// 章节ID
        /// </summary>
        public string ChapterId { get; set; } = string.Empty;

        /// <summary>
        /// 年级
        /// </summary>
        public int GradeId { get; set;} = 1;

        /// <summary>
        /// 学科Id
        /// </summary>
        public string SubjectId { get; set; } = string.Empty;

        /// <summary>
        /// 难度ID
        /// </summary>
        public string DifficultyId { get; set; } = string.Empty;

        /// <summary>
        /// 学习水平ID（出题方向ID）
        /// </summary>
        public string LearningLevelId { get; set; } = string.Empty;

        /// <summary>
        /// 不需要前端传参
        /// </summary>
        public string UserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 批量保存到题库请求参数
    /// </summary>
    public class SaveBatchQuestionsToBankInput
    {
        /// <summary>
        /// 题目列表
        /// </summary>
        public List<AIGeneratedQuestion> Questions { get; set; } = new List<AIGeneratedQuestion>();

        /// <summary>
        /// 章节ID
        /// </summary>
        public string ChapterId { get; set; } = string.Empty;

        /// <summary>
        /// 难度ID
        /// </summary>
        public string DifficultyId { get; set; } = string.Empty;

        /// <summary>
        /// 年级
        /// </summary>
        public int GradeId { get; set; } = 1;

        /// <summary>
        /// 学科Id
        /// </summary>
        public string SubjectId { get; set; } = string.Empty;

        /// <summary>
        /// 学习水平ID（出题方向ID）
        /// </summary>
        public string LearningLevelId { get; set; } = string.Empty;

        /// <summary>
        /// 不需要前端传参
        /// </summary>
        public string UserId { get; set; }
    }

    /// <summary>
    /// 保存到题库输出结果
    /// </summary>
    public class SaveToQuestionBankOutput
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 成功保存的题目数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败的题目数量
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 保存成功的题目ID列表
        /// </summary>
        public List<string> SavedQuestionIds { get; set; } = new List<string>();
    }
}
