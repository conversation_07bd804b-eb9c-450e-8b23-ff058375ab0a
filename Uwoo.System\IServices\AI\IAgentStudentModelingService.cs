﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_学生端建模
    /// </summary>
    public interface IAgentStudentModelingService : IService<AI_AgentTask>
    {
        /// <summary>
        /// 获取学生端建模信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentModelingInfoOutput> GetStudentModelingInfo(GetStudentModelingInfoInput input);

        /// <summary>
        /// 学生阅读建模文档
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task StudentReadModelingDocument(StudentReadModelingDocumentInput input);

        /// <summary>
        /// 学生观看建模视频
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task StudentWatchModelingVideo(StudentWatchModelingVideoInput input);

        /// <summary>
        /// 学生端建模作品评估
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentModelingAssessOutput> StudentModelingAssess(StudentModelingAssessInput input);

        /// <summary>
        /// 学生端建模情景对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task StudentModelingDialogue(StudentModelingDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 学生端建模情景对话提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentModelingDialogueSubmitOutput> StudentModelingDialogueSubmit(StudentModelingDialogueSubmitInput input);

        /// <summary>
        /// 学生端建模知识问答
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task StudentModelingKnowledge(StudentModelingKnowledgeInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 学生端建模问题理解
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task StudentModelingComprehend(StudentModelingComprehendInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 学生端建模问题理解提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentModelingComprehendSubmitOutput> StudentModelingComprehendSubmit(StudentModelingComprehendSubmitInput input);

        /// <summary>
        /// 学生端建模构建图片识别
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<StudentModelingStructureImgOcrOutput> StudentModelingStructureImgOcr(StudentModelingStructureImgOcrInput input);

        /// <summary>
        /// 学生端建模模型构建提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentModelingStructureSubmitOutput> StudentModelingStructureSubmit(StudentModelingStructureSubmitInput input);

        /// <summary>
        /// 学生端建模模型假设
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task StudentModelingHypothesis(StudentModelingHypothesisInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 学生端建模模型假设提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentModelingHypothesisSubmitOutput> StudentModelingHypothesisSubmit(StudentModelingHypothesisSubmitInput input);

        /// <summary>
        /// 学生端建模模型评价
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task StudentModelingEvaluate(StudentModelingEvaluateInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 获取学生模型构建最新版本模型构建信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentModelingStructureNewestOutput> StudentModelingStructureNewest(StudentModelingStructureNewestInput input);

        /// <summary>
        /// 学生端建模模型构建优化
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task StudentModelingStructureOptimize(StudentModelingStructureOptimizeInput input);

        /// <summary>
        /// 学生端建模模型评价提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentModelingEvaluateSubmitOutput> StudentModelingEvaluateSubmit(StudentModelingEvaluateSubmitInput input);

        /// <summary>
        /// 获取学生建模相关任务对话内容记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PageReturn<StudentModelingDialogueRecordOutput>> StudentModelingDialogueRecord(StudentModelingDialogueRecordInput input);

        /// <summary>
        /// 获取学生端建模模型构建记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StudentModelingStructureRecordOutput> StudentModelingStructureRecord(StudentModelingStructureRecordInput input);

        /// <summary>
        /// 学生端建模提交未达标对话记录备份
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task StudentModelingSubmitNoStandardBackups(StudentModelingSubmitNoStandardBackupsInput input);

        /// <summary>
        /// 获取学生做建模阶段任务结果
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentDoModelingTaskResultOutput> GetStudentDoModelingTaskResult(GetStudentDoModelingTaskResultInput input);

        /// <summary>
        /// 获取学生做建模未达标列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentDoModelingNoStandardListOutput> GetStudentDoModelingNoStandardList(GetStudentDoModelingNoStandardListInput input);
    }
}
