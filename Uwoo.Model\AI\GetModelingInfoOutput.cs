﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取建模信息输出
    /// </summary>
    public class GetModelingInfoOutput
    {
        /// <summary>
        /// 建模Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 任务图标
        /// </summary>
        public string? TaskLogo { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string? GradeId { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 建模阶段
        /// </summary>
        public List<GetModelingStageInfoOutput> ModelingStageInfos { get; set; } = new List<GetModelingStageInfoOutput>();
    }

    /// <summary>
    /// 建模阶段
    /// </summary>
    public class GetModelingStageInfoOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string? Describe { get; set; }

        /// <summary>
        /// 默认阶段类型(1问题理解、2假设分析、3模型评价)
        /// </summary>
        public int DefaultStageType { get; set; }

        /// <summary>
        /// 是否默认(默认禁止删除)
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 建模阶段任务
        /// </summary>
        public List<GetModelingStageTaskInfoOutput> ModelingStageTaskInfos { get; set; } = new List<GetModelingStageTaskInfoOutput>();
    }

    /// <summary>
    /// 建模阶段任务
    /// </summary>
    public class GetModelingStageTaskInfoOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:问题理解、7:模型构建、8:模型假设、9:模型评价）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 评分标准
        /// </summary>
        public string? ScoreStandard { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string? Demand { get; set; }

        /// <summary>
        /// 问答范围
        /// </summary>
        public string? Scope { get; set; }

        /// <summary>
        /// 评估角色设定
        /// </summary>
        public string? RoleSetting { get; set; }

        /// <summary>
        /// 引导角色设定
        /// </summary>
        public string? GuideRoleSetting { get; set; }

        /// <summary>
        /// 开场白
        /// </summary>
        public string? Prologue { get; set; }

        /// <summary>
        /// 音色Id
        /// </summary>
        public string? ToneId { get; set; }

        /// <summary>
        /// 角色名称（用于AI展示名称）
        /// </summary>
        public string? RoleName { get; set; }

        /// <summary>
        /// 是否开启提交条件（任务点设置）
        /// </summary>
        public bool TaskIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（任务点设置）
        /// </summary>
        public bool TaskIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（任务点设置）
        /// </summary>
        public decimal TaskAssessmentScore { get; set; }

        /// <summary>
        /// 是否观看视频（任务点设置）
        /// </summary>
        public bool TaskIsWatchVideo { get; set; }

        /// <summary>
        /// 是否开启视频观看时长条件（任务点设置）
        /// </summary>
        public bool TaskIsVideoWatchDuration { get; set; }

        /// <summary>
        /// 视频观看时长（分钟）（任务点设置）
        /// </summary>
        public int TaskVideoWatchDuration { get; set; }

        /// <summary>
        /// 是否需要阅读全部文档（任务点设置）
        /// </summary>
        public bool TaskIsReadAllDocuments { get; set; }

        /// <summary>
        /// 是否开启提交条件（组间任务设置）
        /// </summary>
        public bool GroupIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（组间任务设置）
        /// </summary>
        public bool GroupIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（组间任务设置）
        /// </summary>
        public decimal GroupAssessmentScore { get; set; }

        /// <summary>
        /// 是否观看视频（组间任务设置）
        /// </summary>
        public bool GroupIsWatchVideo { get; set; }

        /// <summary>
        /// 是否开启视频观看时长条件（组间任务设置）
        /// </summary>
        public bool GroupIsVideoWatchDuration { get; set; }

        /// <summary>
        /// 视频观看时长（分钟）（组间任务设置）
        /// </summary>
        public int GroupVideoWatchDuration { get; set; }

        /// <summary>
        /// 是否需要阅读全部文档（组间任务设置）
        /// </summary>
        public bool GroupIsReadAllDocuments { get; set; }

        /// <summary>
        /// 是否默认（默认禁止删除）
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 是否存在提交记录
        /// </summary>
        public bool IsDo { get; set; }

        /// <summary>
        /// 建模阶段任务问题
        /// </summary>
        public List<GetModelingStageTaskQuestionInfoOutput> ModelingStageTaskQuestionInfos { get; set; } = new List<GetModelingStageTaskQuestionInfoOutput>();

        /// <summary>
        /// 建模阶段任务视频
        /// </summary>
        public List<GetModelingStageTaskVideoInfo> ModelingStageTaskVideoInfos { get; set; } = new List<GetModelingStageTaskVideoInfo>();

        /// <summary>
        /// 建模阶段任务文档
        /// </summary>
        public List<GetModelingStageTaskDocumentInfo> ModelingStageTaskDocumentInfos { get; set; } = new List<GetModelingStageTaskDocumentInfo>();
    }

    /// <summary>
    /// 建模阶段任务高频问题
    /// </summary>
    public class GetModelingStageTaskQuestionInfoOutput
    {
        /// <summary>
        /// 高频问题Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 问题名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Describe { get; set; }
    }

    /// <summary>
    /// 建模阶段任务视频信息
    /// </summary>
    public class GetModelingStageTaskVideoInfo
    {
        /// <summary>
        /// Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 视频名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 视频地址
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// 视频大小
        /// </summary>
        public int Size { get; set; }

        /// <summary>
        /// 视频时长（秒）
        /// </summary>
        public int Duration { get; set; }
    }

    /// <summary>
    /// 建模阶段任务文档信息
    /// </summary>
    public class GetModelingStageTaskDocumentInfo
    {
        /// <summary>
        /// Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 文档名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 文档地址
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// 文档大小
        /// </summary>
        public int Size { get; set; }
    }
}
