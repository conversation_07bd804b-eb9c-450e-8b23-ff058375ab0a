﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取建模阶段任务统计输出
    /// </summary>
    public class GetModelingStageTaskCountOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 任务统计信息
        /// </summary>
        public List<GetModelingStageTaskInfoCountOutput> Tasks { get; set; } = new List<GetModelingStageTaskInfoCountOutput>();
    }
    /// <summary>
    /// 获取建模阶段任务统计信息输出
    /// </summary>
    public class GetModelingStageTaskInfoCountOutput
    {
        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:问题理解、7:模型构建、8:模型假设、9:模型评价）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 参与率/查询率
        /// </summary>
        public decimal Participation { get; set; }

        /// <summary>
        /// 平均对话/平均查询次数
        /// </summary>
        public decimal AvgDialogue { get; set; }

        /// <summary>
        /// 平均分
        /// </summary>
        public decimal? AvgScore { get; set; }

        /// <summary>
        /// 上传率
        /// </summary>
        public decimal Submit { get; set; }

        /// <summary>
        /// 等第
        /// </summary>
        public List<GetModelingStageTaskLevelOutput> TaskLevels { get; set; } = new List<GetModelingStageTaskLevelOutput>();

        /// <summary>
        /// 查询趋势
        /// </summary>
        public List<GetModelingStageTaskQueryCountOutput> QueryCount { get; set; } = new List<GetModelingStageTaskQueryCountOutput>();

        /// <summary>
        /// 主题
        /// </summary>
        public List<string> Themes { get; set; } = new List<string>();
    }

    /// <summary>
    /// 获取建模阶段任务统计等第输出
    /// </summary>
    public class GetModelingStageTaskLevelOutput
    {
        /// <summary>
        /// 等第名称
        /// </summary>
        public string? LevelName { get; set; }

        /// <summary>
        /// 等第数量
        /// </summary>
        public int LevelCount { get; set; }
    }

    /// <summary>
    /// 获取建模阶段任务查询统计输出
    /// </summary>
    public class GetModelingStageTaskQueryCountOutput
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 统计数量
        /// </summary>
        public int Count { get; set; }
    }

    /// <summary>
    /// 获取做建模阶段任务主题
    /// </summary>
    public class GetDoModelingStageTaskThemes
    {
        /// <summary>
        /// 阶段任务Id
        /// </summary>
        public string? ModelingStageTaskId { get; set; }

        /// <summary>
        /// 主题名称
        /// </summary>
        public string? Name { get; set; }
    }
}
