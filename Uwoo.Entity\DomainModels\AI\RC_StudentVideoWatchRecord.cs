using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 阅读理解学生视频观看记录表
    /// </summary>
    [SugarTable("RC_StudentVideoWatchRecord")]
    public class RC_StudentVideoWatchRecord
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string StudentId { get; set; }

        /// <summary>
        /// 阅读理解项目阶段任务Id
        /// </summary>
        public string ReadingProjectStageTaskId { get; set; }

        /// <summary>
        /// 视频资源Id
        /// </summary>
        public string VideoId { get; set; }

        /// <summary>
        /// 累计观看时长（秒）
        /// </summary>
        public int TotalWatchDuration { get; set; }

        /// <summary>
        /// 是否已观看（标记视频被点击播放）
        /// </summary>
        public bool HasWatched { get; set; }

        /// <summary>
        /// 首次观看时间
        /// </summary>
        public DateTime? FirstWatchTime { get; set; }

        /// <summary>
        /// 最后观看时间
        /// </summary>
        public DateTime? LastWatchTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
