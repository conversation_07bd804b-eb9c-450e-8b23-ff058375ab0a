using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.DbContext;
using Uwoo.Core.Extensions.AutofacManager;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.System.IRepositories.AI;

namespace UwooAgent.System.Repositories.AI
{
    /// <summary>
    /// 知识库管理Repository
    /// </summary>
    public class KnowledgeRepository : RepositoryBase<AI_KnowledgeInfo>, IKnowledgeRepository
    {
        public KnowledgeRepository(VOLContext dbContext) : base(dbContext)
        {

        }

        public static IKnowledgeRepository Instance
        {
            get
            {
                return AutofacContainerModule.GetService<IKnowledgeRepository>();
            }
        }
    }
}
