using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 出题方向数据传输对象
    /// </summary>
    public class QuestionDirectionDto
    {
        /// <summary>
        /// 出题方向ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 出题方向名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 出题方向描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
}
