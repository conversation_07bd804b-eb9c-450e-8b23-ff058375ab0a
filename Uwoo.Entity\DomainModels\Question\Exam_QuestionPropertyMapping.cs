﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Question
{
    /// <summary>
    /// 题库_题目属性关系实体
    /// </summary>
    [Table("Exam_QuestionPropertyMapping")]
    public class Exam_QuestionPropertyMapping
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 题库_题目Id
        /// </summary>
        public string QuestionId { get; set; }

        /// <summary>
        /// 编码属性Id
        /// </summary>
        public string PropertyId { get; set; }

        /// <summary>
        /// 编码属性类型(1题目难度、2学习水平、3内容领域、4核心素养、5情景属性、6主题、7 知识点)
        /// </summary>
        public int? PropertyType { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }
    }
}
