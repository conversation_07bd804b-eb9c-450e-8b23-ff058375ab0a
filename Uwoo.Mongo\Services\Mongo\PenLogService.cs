﻿// -- Function：PenLogService.cs
// --- Project：X.PenServer.Services
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/24 18:01

namespace Uwoo.Mongo.Services.Mongo;

using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Uwoo.Contracts.Config;
using Uwoo.Mongo.Infrastructure;
using Uwoo.Mongo.Interfaces.Business;
using Uwoo.Mongo.Models;
using Uwoo.Mongo.ViewModels;

/// <inheritdoc />
public class PenLogService : MongoAutoService<PenLog>, IPenLogService
{
    /// <inheritdoc />
    public PenLogService(IMongoConfig config) : base(config)
    {
    }

    /// <inheritdoc />
    protected override void CreateIndex(IMongoCollection<PenLog> collection)
    {
        var userid_builder = Builders<PenLog>.IndexKeys
            .Ascending(x => x.UserId)
            .Ascending(x => x.PageId)
            .Ascending(x => x.Page)
            .Ascending(x => x.AddTime)
            .Ascending(x => x.Mac);
        collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");

        var mac_builder = Builders<PenLog>.IndexKeys
            .Ascending(x => x.Mac)
            .Ascending(x => x.UserId)
            .Ascending(x => x.PageId)
            .Ascending(x => x.Page)
            .Ascending(x => x.AddTime);

        collection.Indexes.CreateIndex(mac_builder, collection.CollectionNamespace.CollectionName + "_Mac_Key");
    }

    #region Implementation of IPenLogService

    /// <inheritdoc />
    public List<PenLog> GetAll(string colname, List<int> page, string userid,int? year= null)
    {
        var mongo = GetConnection(colname, year);
        var list = mongo.Find(x => page.Contains(x.PageId) && x.UserId == userid).ToList();
        return list.OrderBy(x => x.Mid).ToList();
    }

    /// <inheritdoc />
    public List<PenLog> GetAll(string colname, string userid, int page, int? year = null)
    {
        var mongo = GetConnection(colname, year);
        var list = mongo.Find(x => x.PageId == page && x.UserId == userid).ToList();
        return list.OrderBy(x => x.Mid).ToList();
    }

    /// <inheritdoc />
    public Tuple<DateTime, DateTime> GetAggregateTime(string colname, List<int> page, string userid)
    {
        var mongo = GetConnection(colname);
        if (!mongo.AsQueryable().Any(x => x.UserId == userid && page.Contains(x.PageId)))
        {
            return null;
        }

        var mins = mongo.Find(x => page.Contains(x.PageId) && x.UserId == userid).ToList();
        var min = mins.MinBy(x => x.Mid);
        var max = mins.MaxBy(x => x.Mid);
        return Tuple.Create(min.AddTime, max.AddTime);
    }

    /// <inheritdoc />
    public List<string> GetUserList(string colname, List<int> page, List<string> userid, int? year = null)
    {
        var mongo = GetConnection(colname, year);
        if (!mongo.AsQueryable().Any(x => page.Contains(x.PageId) && userid.Contains(x.UserId)))
        {
            return new List<string>();
        }

        var builder = Builders<PenLog>.Filter;
        var filter = builder.In(x => x.PageId, page) & builder.In(x => x.UserId, userid);
        var result = mongo.Distinct(x => x.UserId, filter).ToList();
        return result;
    }

    /// <inheritdoc />
    public List<PenLogUserInfo> GetUserPageList(string colname, List<int> page, List<string> userid)
    {
        var mongo = GetConnection(colname);
        var result = mongo.AsQueryable()
                          .Where(x => page.Contains(x.PageId) && userid.Contains(x.UserId))
                          .GroupBy(x => new {x.UserId, x.PageId})
                          .Select
                          (
                              x => new PenLogUserInfo
                              {
                                  UserId = x.Key.UserId,
                                  Page = x.Key.PageId
                              }
                          );
        return result.ToList();
    }

    /// <inheritdoc />
    public void DeletePenLog(string colname, List<int> page, List<string> userid)
    {
        var mongo = GetConnection(colname);
        mongo.DeleteMany(x => page.Contains(x.PageId) && userid.Contains(x.UserId));
    }

    /// <inheritdoc />
    public async Task CorrectMDLog(string colname, int page, string user_id, int move_x, int move_y)
    {
        var mongo = GetConnection(colname);
        var result = mongo.AsQueryable().Where(x => x.PageId == page && x.UserId == user_id);
        if (move_x == 0 && move_y == 0)
        {
            return;
        }

        await Parallel.ForEachAsync(result, async (item, token) =>
        {
            foreach (var dot in item.Dots)
            {
                dot.X += (move_x != 0 ? move_x : 0);
                dot.Y += (move_y != 0 ? move_y : 0);
            }

            await mongo.FindOneAndReplaceAsync(x => x.Mid == item.Mid, item, cancellationToken: token);
        });
    }

    #endregion
}