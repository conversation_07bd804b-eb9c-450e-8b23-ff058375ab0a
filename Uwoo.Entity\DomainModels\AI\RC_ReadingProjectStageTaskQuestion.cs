using SqlSugar;
using System;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// 阅读理解项目化实践阶段任务高频问题表
    /// </summary>
    [SugarTable("RC_ReadingProjectStageTaskQuestion")]
    public class RC_ReadingProjectStageTaskQuestion : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 阅读理解项目阶段任务Id
        /// </summary>
        public string ReadingProjectStageTaskId { get; set; }

        /// <summary>
        /// 问题名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 问题描述
        /// </summary>
        public string Describe { get; set; }

        /// <summary>
        /// 问题排序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string Modifier { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDeleted { get; set; }
    }
}
