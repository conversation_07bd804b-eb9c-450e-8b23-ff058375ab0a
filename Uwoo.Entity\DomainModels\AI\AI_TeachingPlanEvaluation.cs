﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
    /// AI 教案评价记录表
    /// </summary>
    [Table("AI_TeachingPlanEvaluation")]
    public class AI_TeachingPlanEvaluation
    {
        /// <summary>
        /// 主键ID 
        /// </summary>
        [Column("Id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 教师ID
        /// </summary>
        [Column("TeacherId")]
        public string TeacherId { get; set; }

        /// <summary>
        /// 班级ID
        /// </summary>
        [Column("CLassId")]
        public string ClassId { get; set; }

        /// <summary>
        /// 教案URL
        /// </summary>
        [Column("TeachingPlanUrl")]
        public string TeachingPlanUrl { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("CreateTime")]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 前侧试卷ID
        /// </summary>
        [Column("PrePaperIds")]
        public string PrePaperIds { get; set; }

        /// <summary>
        /// 后侧试卷ID
        /// </summary>
        [Column("BackPaperIds")]
        public string BackPaperIds { get; set; }

        /// <summary>
        /// 评价HTML字符串
        /// </summary>
        [Column("EvaluationHtml")]
        public string EvaluationHtml { get; set; }
    }
}
