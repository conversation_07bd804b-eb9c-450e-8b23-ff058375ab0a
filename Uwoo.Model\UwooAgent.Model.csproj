﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Uwoo.Model\**" />
    <EmbeddedResource Remove="Uwoo.Model\**" />
    <None Remove="Uwoo.Model\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Uwoo.Entity\UwooAgent.Entity.csproj" />
    <ProjectReference Include="..\Uwoo.Mongo\UwooAgent.Mongo.csproj" />
  </ItemGroup>
</Project>
