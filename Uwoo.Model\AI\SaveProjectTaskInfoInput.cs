﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 保存/编辑项目化实践任务入参
    /// </summary>
    public class SaveProjectTaskInfoInput
    {
        /// <summary>
        /// 项目化实践任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 任务图标
        /// </summary>
        public string? TaskLogo { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string? GradeId { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public string? SchoolId { get; set; }

        /// <summary>
        /// 是否发布(默认false)
        /// </summary>
        public bool IsPublish { get; set; } = false;

        /// <summary>
        /// 发布的班级Id
        /// </summary>
        public List<string> ClassId { get; set; } = new List<string>();

        /// <summary>
        /// 发布时间范围(下标0开始、下标1结束)
        /// </summary>
        public List<DateTime> TimeRange { get; set; } = new List<DateTime>();

        /// <summary>
        /// 项目化实践阶段
        /// </summary>
        public List<ProjectStageInfoInput> ProjectStageInfos { get; set; } = new List<ProjectStageInfoInput>();
    }

    /// <summary>
    /// 项目化实践阶段入参
    /// </summary>
    public class ProjectStageInfoInput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string? Describe { get; set; }

        /// <summary>
        /// 项目化实践阶段任务
        /// </summary>
        public List<ProjectStageTaskInfoInput> ProjectStageTaskInfos { get; set; } = new List<ProjectStageTaskInfoInput>();
    }

    /// <summary>
    /// 项目化实践阶段任务入参
    /// </summary>
    public class ProjectStageTaskInfoInput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估(作品评估)、2:情景对话、3:知识问答、4:视频、5:文档、6:思维导图、7:选词填空）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 评分标准
        /// </summary>
        public string? ScoreStandard { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string? Demand { get; set; }

        /// <summary>
        /// 问答范围
        /// </summary>
        public string? Scope { get; set; }

        /// <summary>
        /// 角色设定
        /// </summary>
        public string? RoleSetting { get; set; }

        /// <summary>
        /// 是否开启提交条件（组间任务设置）
        /// </summary>
        public bool GroupIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（组间任务设置）
        /// </summary>
        public bool GroupIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（组间任务设置）
        /// </summary>
        public decimal GroupAssessmentScore { get; set; }

        /// <summary>
        /// 是否开启提交条件（任务点设置）
        /// </summary>
        public bool TaskIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（任务点设置）
        /// </summary>
        public bool TaskIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（任务点设置）
        /// </summary>
        public decimal TaskAssessmentScore { get; set; }

        /// <summary>
        /// 任务高频问题
        /// </summary>
        public List<ProjectStageTaskQuestionInfoInput> QuestionInfos { get; set; } = new List<ProjectStageTaskQuestionInfoInput>();
    }

    /// <summary>
    /// 项目化实践阶段任务高频问题入参
    /// </summary>
    public class ProjectStageTaskQuestionInfoInput
    {
        /// <summary>
        /// 高频问题Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 问题名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Describe { get; set; }
    }
}
