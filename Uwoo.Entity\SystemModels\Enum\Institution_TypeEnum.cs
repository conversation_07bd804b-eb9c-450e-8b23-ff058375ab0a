﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace Uwoo.Entity.SystemModels.Enum
{
    /// <summary>
    /// 机构类型
    /// </summary>
    public enum Institution_TypeEnum
    {
        /// <summary>
        /// 学校
        /// </summary>
        [Description("学校")]
        School = 1,

        /// <summary>
        /// 教育局
        /// </summary>
        [Description("教育局")]
        Bureau = 2,

        /// <summary>
        /// 教育学院
        /// </summary>
        [Description("教育学院")]
        College = 3,

        /// <summary>
        /// 其他
        /// </summary>
        [Description("其他")]
        Other = 9,
    }
}
