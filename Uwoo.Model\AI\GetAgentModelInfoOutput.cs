﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取智能体模型信息输出
    /// </summary>
    public class GetAgentModelInfoOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 模型名称
        /// </summary>
        public string? ModelName { get; set; }

        /// <summary>
        /// 是否支持上下文
        /// </summary>
        public bool? IsContext { get; set; }

        /// <summary>
        /// 模型描述
        /// </summary>
        public string? ModelDescribe { get; set; }
    }
}
