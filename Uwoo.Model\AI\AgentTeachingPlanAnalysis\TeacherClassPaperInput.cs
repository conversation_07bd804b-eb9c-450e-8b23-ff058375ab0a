﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI.AgentTeachingPlanAnalysis
{
    /// <summary>
    /// 教师试卷输入参数
    /// </summary>
    public class TeacherClassPaperInput
    {
        public string TeacherId { get; set; }
        public int SubjectId { get; set; }
        public string ClassId { get; set; }
    }

    public class StudentGradeDistributionInput
    {
        /// <summary>
        /// 教师ID
        /// </summary>
        public string TeacherId { get; set; }
        /// <summary>
        /// 班级ID
        /// </summary>
        public string ClassId { get; set; }
        /// <summary>
        /// 成绩分布统计模型集合，一个科目对应一个模型
        /// </summary>
        public List<StudentGradeModel> GradeModels { get; set; }
    }
    /// <summary>
    /// 学生成绩分布统计输入参数
    /// </summary>
    public class StudentGradeModel
    {
        /// <summary>
        /// 数据类型 1,前侧 2 后侧 
        /// </summary>
        public int DataType { get; set; }
        /// <summary>
        /// 科目ID
        /// </summary>
        public string SubjectId { get; set; }
        /// <summary>
        /// 试卷ID集合
        /// </summary>
        public List<string> PaperIds { get; set; }
    }

    /// <summary>
    /// 生成教学计划分析输入参数
    /// </summary>
    public class GenerateTeachingPlanAnalysisInput
    {
        /// <summary>
        /// 教案Id
        /// </summary>
        [Required(ErrorMessage = "教案Id不可为空")]
        public string TeachingPlanId { get; set; }
        public string TeachingPlanUrl { get; set; }

        /// <summary>
        ///分析类型 1数据分析  2 维度分析 3 综合评价与建议
        /// </summary>
        public int GenerateAnalysisType { get; set; }
        /// <summary>
        /// 年级ID
        /// </summary>
        [Required(ErrorMessage = "年级Id不可为空")]
        public int GradeId { get; set; }

        [Required(ErrorMessage = "学科Id不可为空")]
        public string SubjectId { get; set; }
        /// <summary>
        /// 班级ID
        /// </summary>
        public string ClassId { get; set; }
        /// <summary>
        /// 教师ID
        /// </summary>
        [Required(ErrorMessage = "教师Id不可为空")]
        public string TeacherId { get; set; }
        /// <summary>
        /// 前侧试卷ID集合
        /// </summary>
        public List<string> PrePaperIds { get; set; }
        /// <summary>
        /// 后侧试卷ID集合
        /// </summary>
        public List<string> BackPaperIds { get; set; }
    }
}
