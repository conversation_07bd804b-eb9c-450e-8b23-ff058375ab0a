﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取学生端项目化实践信息输出
    /// </summary>
    public class GetStudentProjectInfoOutput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 项目化实践Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目化实践名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目化实践Logo
        /// </summary>
        public string? ProjectLogo { get; set; }

        /// <summary>
        /// 项目化实践背景
        /// </summary>
        public string? ProjectIntroduce { get; set; }

        /// <summary>
        /// 项目化实践任务状态（1进行中、2已结束）
        /// </summary>
        public int ProjectTaskState { get; set; }

        /// <summary>
        /// 进度
        /// </summary>
        public decimal ProgressBar { get; set; }

        /// <summary>
        /// 项目化实践阶段
        /// </summary>
        public List<GetStudentProjectStageInfoOutput> ProjectStageInfos { get; set; } = new List<GetStudentProjectStageInfoOutput>();
    }

    /// <summary>
    /// 获取学生端项目化实践阶段信息输出
    /// </summary>
    public class GetStudentProjectStageInfoOutput
    {
        /// <summary>
        /// 阶段Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 阶段名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 阶段描述
        /// </summary>
        public string? Describe { get; set; }

        /// <summary>
        /// 是否锁
        /// </summary>
        public bool IsLock { get; set; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }

        /// <summary>
        /// 项目化实践阶段任务
        /// </summary>
        public List<GetStudentProjectStageTaskInfoOutput> ProjectStageTaskInfos { get; set; } = new List<GetStudentProjectStageTaskInfoOutput>();
    }

    /// <summary>
    /// 获取学生端项目化实践阶段任务信息输出
    /// </summary>
    public class GetStudentProjectStageTaskInfoOutput
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 项目化实践阶段Id
        /// </summary>
        public string? ProjectStageId { get; set; }

        /// <summary>
        /// 任务类型（1:成果评估、2:情景对话、3:知识问答、4:视频、5:文档、6:思维导图、7:选词填空）
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 是否开启提交条件（组间任务设置）
        /// </summary>
        public bool GroupIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（组间任务设置）
        /// </summary>
        public bool GroupIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（组间任务设置）
        /// </summary>
        public decimal GroupAssessmentScore { get; set; }

        /// <summary>
        /// 是否开启提交条件（任务点设置）
        /// </summary>
        public bool TaskIsSubmit { get; set; }

        /// <summary>
        /// 是否开启AI评估条件（任务点设置）
        /// </summary>
        public bool TaskIsAssessment { get; set; }

        /// <summary>
        /// AI评估条件分数（任务点设置）
        /// </summary>
        public decimal TaskAssessmentScore { get; set; }

        /// <summary>
        /// 是否锁
        /// </summary>
        public bool IsLock { get; set; }

        /// <summary>
        /// 完成状态（1未开始、2进行中、3已完成）
        /// </summary>
        public int State { get; set; }

        /// <summary>
        /// 是否已提交但未达标
        /// </summary>
        public bool IsSubmitNoStandard { get; set; }

        /// <summary>
        /// 任务提交记录Id
        /// </summary>
        public string? TaskSubmitId { get; set; }

        /// <summary>
        /// 顺序
        /// </summary>
        public int? Order { get; set; }
    }
}
