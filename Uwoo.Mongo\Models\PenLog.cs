﻿// -- Function：PenLog.cs
// --- Project：X.PenServer.Models
// ---- Remark：
// ---- Author：Lucifer
// ------ Date：2023/10/24 14:56

using System.Text.Json.Serialization;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Uwoo.Mongo.Models;

/// <summary>
/// 点阵笔点位数据
/// </summary>
public class PenLog : MongoBaseModel
{
    /// <summary>
    /// 点阵笔采集到的页码
    /// </summary>
    [BsonElement(nameof(Page))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(Page))]
    [JsonInclude]
    public int Page { get; set; }

    /// <summary>
    /// 对应业务页码id, 卷码纸为实际的卷码, 其余情况和Page字段值一致
    /// </summary>
    /// <seealso cref="Page"/>
    [BsonElement(nameof(PageId))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(PageId))]
    [JsonInclude]
    public int PageId { get; set; }

    /// <summary>
    /// 点阵笔Mac地址
    /// </summary>
    [BsonElement(nameof(Mac))]
    [BsonRepresentation(BsonType.String)]
    [JsonPropertyName(nameof(Mac))]
    [JsonInclude]
    public string Mac { get; set; }

    /// <summary>
    /// 用户id
    /// </summary>
    [BsonElement(nameof(UserId))]
    [BsonRepresentation(BsonType.String)]
    [JsonPropertyName(nameof(UserId))]
    [JsonInclude]
    public string UserId { get; set; }

    /// <summary>
    /// 点位列表
    /// </summary>
    [BsonElement(nameof(Dots))]
    [JsonPropertyName(nameof(Dots))]
    [JsonInclude]
    public List<DotBase> Dots { get; set; } = new();
}