﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取学生模型构建最新版本输出
    /// </summary>
    public class StudentModelingStructureNewestOutput
    {
        /// <summary>
        /// 变量定义
        /// </summary>
        public string? Variable { get; set; }

        /// <summary>
        /// 表达式
        /// </summary>
        public string? Expression { get; set; }
    }
}
