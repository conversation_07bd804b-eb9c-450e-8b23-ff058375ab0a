﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.Paper
{
    /// <summary>
    /// 试卷实体类
    /// </summary>
    [Table("Exam_Paper")]
    public class Exam_Paper
    {
        [SugarColumn(IsPrimaryKey = true)]
        /// <summary>
        /// Id
        /// </summary>
        [Key, Column(Order = 1)]
        public String Id { get; set; }

        /// <summary>
        /// 试卷名称
        /// </summary>
        public String Title { get; set; }

        /// <summary>
        /// 创建人，出卷人
        /// </summary>
        public String CreatorId { get; set; }

        /// <summary>
        /// 试卷类型 0 区试卷 1班试卷 18为校试卷  -2 市转型试卷(三个助手) -3 练习册(-31 为教材练习册；-32专科专练练习册) 30作废  其他值为班试卷
        /// </summary>
        public Int32? Type { get; set; }

        /// <summary>
        /// 版本也就是学年，比如2023.08.04导入的试卷那么学期就是2022下学期，学年就是2022
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// 试卷类型为练习册时，添加的标签，-31 为教材练习册；-32专科专练练习册;26 作文题型试卷;27 英语问卷；99 HomeworkType为9时，设置此值
        /// </summary>
        public Int32? PaperTag { get; set; }

        /// <summary>
        /// 订正区域 
        /// </summary>
        public Int32? ReviseAreaLevel { get; set; }

        /// <summary>
        /// 组卷方式
        /// </summary>
        public Int32? MakeType { get; set; }

        /// <summary>
        /// 年级
        /// </summary>
        public string GradeId { get; set; }

        /// <summary>
        /// 学科
        /// </summary>
        public String SubjectId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public String ChapterId { get; set; }
        /// <summary>
        /// 章节编号
        /// </summary>
        public String ChapterNum { get; set; }

        /// <summary>
        /// 章节名称
        /// </summary>
        public string ChapterName { get; set; }

        /// <summary>
        /// 试卷说明
        /// </summary>
        public String Explain { get; set; }

        /// <summary>
        /// 试卷来源 网络还是导入
        /// </summary>
        public Int32? Source { get; set; }

        /// <summary>
        /// 总分
        /// </summary>
        public Int32? TotalScore { get; set; }

        /// <summary>
        /// 试卷状态 0 新创建试卷，未发布；13 已发布
        /// </summary>
        public Int32? Status { get; set; }

        /// <summary>
        /// 标准所需要时间
        /// </summary>
        public Int32? StandardTime { get; set; }

        /// <summary>
        /// CreateTime
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 试卷内容时间
        /// </summary>
        public DateTime? PaperDateTime { get; set; }

        /// <summary>
        /// IsCheck
        /// </summary>
        public Int32? IsCheck { get; set; }

        /// <summary>
        /// 审查状态 1 未审查  2已审查  3已发布
        /// </summary>
        public int AuditState { get; set; }

        /// <summary>
        /// 审查时间 ,老师审卷时间
        /// </summary>
        public DateTime? AuditTime { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 回答截止时间
        /// </summary>
        public DateTime? AnswerEndDateTime { get; set; }

        /// <summary>
        /// 题目数量
        /// </summary>
        public int ItemCount { get; set; }

        /// <summary>
        /// 是否共享(0--本人保密，1--共享到全校，2--共享到全区)
        /// </summary>
        public int? IsShare { get; set; }

        /// <summary>
        /// 共享设置(1不共享、2同校共享)默认1
        /// </summary>
        public int? ShareSet { get; set; }

        /// <summary>
        /// 区市共享设置(1不共享、2愿意全区共享、3愿意全市共享)默认1
        /// </summary>
        public int? ShareAreaCity { get; set; }

        /// <summary>
        /// 区市共享审核（1未审核、2已审核）默认1
        /// </summary>
        public int? ShareAreaCityAudit { get; set; }

        /// <summary>
        /// 教材Id
        /// </summary>
        public string TextbookId { get; set; }

        /// <summary>
        /// 引用个数
        /// </summary>
        public int? QuoteCount { get; set; }

        /// <summary>
        /// 难易度
        /// </summary>
        public int? DifficultyStar { get; set; }
        /// <summary>
        /// 被引用的试卷Id
        /// </summary>
        public string QuotePaperId { get; set; }
        /// <summary>
        /// 区Id
        /// </summary>
        public int AreaId { get; set; }
        /// <summary>
        /// 是否高频错题 0 不是 1 全班推送 2 智能分层推送
        /// </summary>
        public int? IsHighFrequency { get; set; }

        /// <summary>
        /// 是否是市级试卷
        /// </summary>
        public int? IsCityLevel { get; set; }
        /// <summary>
        /// 是否课中练习
        /// </summary>
        public int? IsCourse { get; set; }

        /// <summary>
        /// 是否是纸笔作业（0:否、1:是）
        /// </summary>
        public int? IsDZB { get; set; }

        /// <summary>
        /// 是否是电子作业（0:否、1:是）
        /// </summary>
        public int? IsDZZY { get; set; }

        /// <summary>
        /// 0 自行铺码；1铺码纸打印  2 拍照分割  
        /// </summary>
        public int? PaperWordType { get; set; }

        /// <summary>
        /// 是否是拍照分割试卷（0否、1是）
        /// </summary>
        public int? IsPhotograph { get; set; }

        /// <summary>
        /// 学科名称
        /// </summary>
        public string SubjectName { get; set; }

        /// <summary>
        /// 难度系数
        /// </summary>
        public decimal? Level { get; set; }

        /// <summary>
        /// 试卷目标
        /// </summary>
        public string ChapterTarget { get; set; }
        /// <summary>
        /// 是否测试
        /// </summary>
        public Boolean? IsTest { get; set; }

        /// <summary>
        /// 制作时间可修改
        /// </summary>
        public DateTime? ProductionTime { get; set; }

        /// <summary>
        /// 作业类型 -> 枚举 HomeworkType
        /// </summary>
        public int? HomeworkType { get; set; }

        /// <summary>
        /// 作答方式：默认为整卷作答，1单题作答
        /// </summary>
        public int? DoAnswerMode { get; set; }

        /// <summary>
        /// 作者（可编辑，默认试卷创建人）
        /// </summary>
        public string AuthorName { get; set; }

        /// <summary>
        /// 是否设置分数(0否，1是)
        /// </summary>
        public int? IsSetScore { get; set; }

        /// <summary>
        /// 平台来源（1专课专练、2三个助手）
        /// </summary>
        public int? PlatformSource { get; set; }
        /// <summary>
        /// 是否开启助手  0关闭 1 开启
        /// </summary>
        public int IsOpenAi { get; set; }
    }
}
