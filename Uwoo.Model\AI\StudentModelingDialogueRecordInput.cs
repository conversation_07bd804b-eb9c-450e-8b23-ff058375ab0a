﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 学生端建模对话记录接口入参
    /// </summary>
    public class StudentModelingDialogueRecordInput
    {
        /// <summary>
        /// 分页_页码默认1
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 分页_每页数量默认3
        /// </summary>
        public int PageSize { get; set; } = 3;

        /// <summary>
        /// 建模Id
        /// </summary>
        public string? ModelingId { get; set; }

        /// <summary>
        /// 建模阶段Id
        /// </summary>
        public string? ModelingStageId { get; set; }

        /// <summary>
        /// 建模阶段任务Id
        /// </summary>
        public string? ModelingStageTaskId { get; set; }

        /// <summary>
        /// 学生做建模阶段任务提交记录Id
        /// </summary>
        public string? StudentDoModelingTaskId { get; set; }

        /// <summary>
        /// 是否属于备份
        /// </summary>
        public bool IsBackups { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }
    }
}
