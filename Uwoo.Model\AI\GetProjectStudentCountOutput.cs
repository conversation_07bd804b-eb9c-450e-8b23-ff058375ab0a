﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取项目化实践学生统计输出
    /// </summary>
    public class GetProjectStudentCountOutput
    {
        /// <summary>
        /// 项目化实践Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 学生信息
        /// </summary>
        public List<GetProjectStudentInfoOutput> StudentInfos { get; set; } = new List<GetProjectStudentInfoOutput>();
    }

    /// <summary>
    /// 获取项目化实践学生信息输出
    /// </summary>
    public class GetProjectStudentInfoOutput
    {
        /// <summary>
        /// 学号
        /// </summary>
        public string? StudentNum { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生姓名
        /// </summary>
        public string? StudentName { get; set; }

        /// <summary>
        /// 进度
        /// </summary>
        public decimal ProgressBar { get; set; }

        /// <summary>
        /// 未达标次数
        /// </summary>
        public int NoStandard { get; set; }

        /// <summary>
        /// 最后一次提交时间
        /// </summary>
        public string? SubmitTime { get; set; }
    }
}
