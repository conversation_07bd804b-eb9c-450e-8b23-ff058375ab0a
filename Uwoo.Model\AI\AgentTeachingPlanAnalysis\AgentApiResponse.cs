﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI.AgentTeachingPlanAnalysis
{
    public class ApiResponse
    {
        public int code { get; set; }
        public string data { get; set; }
        public string debug_url { get; set; }
        public string msg { get; set; }
        public UsageInfo Usage { get; set; }
    }

    public class UsageInfo
    {
        public int InputCount { get; set; }
        public int TokenCount { get; set; }
        public int OutputCount { get; set; }
    }

    // Data属性中的JSON可以进一步反序列化为：
    public class DataContent
    {
        public string url { get; set; }
    }

}
