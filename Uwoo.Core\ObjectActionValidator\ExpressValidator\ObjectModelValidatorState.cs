﻿namespace Uwoo.Core.ObjectActionValidator.ExpressValidator
{
	public class ObjectModelValidatorState
	{
		public ObjectModelValidatorState()
		{
			Status = true;
		}

		public bool Status { get; set; }
		public bool HasModelContent { get; set; }
		public string Code { get; set; }
		public string Message { get; set; }
	}
	public class ObjectValidatorResult
	{
		public ObjectValidatorResult()
		{

		}
		public ObjectValidatorResult(bool status)
		{
			Status = status;
		}
		public ObjectValidatorResult OK(string message)
		{
			Status = true;
			Message = message;
			return this;
		}
		public ObjectValidatorResult Error(string message)
		{
			Status = false;
			Message = message;
			return this;
		}
		public bool Status { get; set; }
		public string Message { get; set; }
	}
}
